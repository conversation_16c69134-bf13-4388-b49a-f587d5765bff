import invariant from '../utils/invariant';
import { Platform } from 'react-native'

import getScreenForRouteName from './getScreenForRouteName';
import validateScreenOptions from './validateScreenOptions';

function applyConfig(configurer, navigationOptions, configProps) {
  if (typeof configurer === 'function') {
    return {
      ...navigationOptions,
      ...configurer({
        ...configProps,
        navigationOptions,
      }),
    };
  }
  if (typeof configurer === 'object') {
    return {
      ...navigationOptions,
      ...configurer,
    };
  }

  const favorites = ['المفضلة', 'Favoriten', 'Favorites', 'Favoritos', 'Favoris', '收藏夹', '收藏夾', '我的最愛', 'Favorit', 'Preferiti', '즐겨찾기', 'Favorieten', 'Ulubione', 'Избран.', 'รายการโปรด', '<PERSON>ụ<PERSON> yê<PERSON> thích'];

  if (Platform.OS === 'android' && favorites.includes(navigationOptions.headerTitle) && navigationOptions.headerStyle && navigationOptions.headerStyle.height == 0) {
    navigationOptions.headerTitle = ''
  }

  return navigationOptions;
}

export default (routeConfigs, navigatorScreenConfig) => (
  navigation,
  screenProps
) => {
  const { state, dispatch } = navigation;
  const route = state;

  invariant(
    route.routeName && typeof route.routeName === 'string',
    'Cannot get config because the route does not have a routeName.'
  );

  const Component = getScreenForRouteName(routeConfigs, route.routeName);

  const routeConfig = routeConfigs[route.routeName];

  const routeScreenConfig =
    routeConfig === Component ? null : routeConfig.navigationOptions;
  const componentScreenConfig = Component.navigationOptions;

  const configOptions = { navigation, screenProps: screenProps || {} };

  let outputConfig = applyConfig(navigatorScreenConfig, {}, configOptions);
  outputConfig = applyConfig(
    componentScreenConfig,
    outputConfig,
    configOptions
  );
  outputConfig = applyConfig(routeScreenConfig, outputConfig, configOptions);

  validateScreenOptions(outputConfig, route);

  return outputConfig;
};
