import AlbumHelper from "../util/AlbumHelper";
import DateFormatter from "../util2/DateFormater";
import UriPlayer from "./UriPlayer";
import EventLoaderInf from "./EventLoaderInf";
import DldMgr from "./DldMgr";
import { Order } from "./EventLoaderInf";
import dayjs from 'dayjs';

const TAG = "LocalEventLoader";
class _LocalEventLoader extends EventLoaderInf {
  constructor() {
    super(false, true);
    this.mDate = (new Date()).getTime();
    this.mPlayCfg = { loader: this, player: UriPlayer };
    DldMgr.addListener((aInfo) => {
      switch (aInfo.type) {
        case "status":
          if ("save_success" == aInfo.detail || "save_faild" == aInfo.detail) {
            console.log(TAG, "notifyDataChanged");
            this.notifyDataChanged();
          }
          break;
        default:
          break;
      }
    });
  }
  async getOneDayAllEvent(aDate, aEvent, aIsMore, aOrder = Order.Desc) {
    let result = await this.getEventList(aDate, aEvent, aIsMore);

    let bgn = dayjs(aDate).hour(0).minute(0).second(0).subtract(0, "days").valueOf();
    let end = dayjs(aDate).hour(23).minute(59).second(59).valueOf();
    let items = result.items;
    items = items.filter((item) => { return item.createTime < end && item.createTime > bgn; });
    result.items = items;
    return result;
  }

  async getAllEvent(aDate, aEvent, aIsMore) {
    console.log('enter local geteventlist');
    let end = dayjs(aDate).hour(23).minute(59).second(59).valueOf();

    let result = await this.getEventList(aDate, aEvent, aIsMore);
    let items = result.items;
    items = items.filter((item) => { return item.createTime < end; });
    result.items = items;
    return result;
  }

  getEventList(aDate, aEvent, aIsMore) {
    if (aDate) {
      return new Promise((aResol, aReject) => {
        AlbumHelper.getAlbumFiles()
          .then((aRet) => {
            let items = [];
            let i = 0;
            // for test use
            // for(i = 0; i < 2; ++ i)
            // for(let j = 0; j < 10; ++ j)
            for (let itm of aRet) {
              let timeInMs = itm.modificationDate * 1000;
              let mapped = {
                createTime: timeInMs + i * 24 * 60 * 60 * 1000,
                eventTime: DateFormatter.instance().format(timeInMs),
                type: "Default",
                desc: "",
                imgStoreId: itm.url,
                duration: itm.duration / 1000,
                fileId: 2 == itm.mediaType ? itm.path : itm.url/* `${itm.url}${i}${j}` */,
                isRead: true,
                offset: 0,
                playCfg: this.mPlayCfg,
                mediaType: 2 == itm.mediaType ? "video" : "image",
                localUrl: itm.url
              };
              items.push(mapped);
            }
            aResol({ hasMore: false, nextTime: null, items });
          })
          .catch((error) => {
            aResol({ hasMore: false, nextTime: null, items: [] });
            console.log("request camera album list failed", error);
          });
      });
    } else {
      return Promise.resolve({ hasMore: false, nextTime: null, items: [] });
    }
  }

  getThumb(aRec) {
    return Promise.resolve(aRec.imgStoreId);
  }

  getSummary() {
    return Promise.resolve({ type: "local", info: { dldCnt: DldMgr.mDld.list.length } });
  }

  canDownload() {
    return false;
  }

  supportImage() {
    return true;
  }

  download(aRec, aPath, aListener) {
    // nothing to do
  }

  delete(aRecs = []) {
    let urls = aRecs.map((aRec) => { return aRec.localUrl; });
    return AlbumHelper.deleteAlbumFilesByUrl(urls);
  }

  async getVideoDates() {
    return new Promise((resolve, reject) => {
      let nowDate = dayjs(new Date()).hour(23).minute(59).second(59).valueOf();

      let result = this.getEventList(nowDate, null, false).then((result) => {
        let items = result.items;
        let dates = [];
        for(let index in items) {
          let time = items[index]['createTime'];
          let find = false;
          for (let key in dates) {
            if (dayjs(time).isSame(dayjs(dates[key]), 'day')) {
              find = true;
              break;
            }
          }
          if (!find) {
            dates.push(time);
          }
        }
        resolve(dates);
      });

    });
  }

}
const LocalEvLdr = new _LocalEventLoader();
export default LocalEvLdr;
