/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 * @flow
 */

/* eslint no-bitwise: 0 */
'use strict';

import native from '../../../../miot-sdk/native';
import { rgbToHex } from '../../../../miot-sdk/native/utils';
import DarkModeX from '../../../../miot-sdk/darkmode/index';

function shouldUseAutoDarkmode() {
  if (native.MIOTService.currentDarkMode == "dark" && !DarkModeX.darkModeStore.setDarkMode) {
    return true;
  } else {
    return false;
  }
}

function changeColor(color, flag, isText) {
  if (!shouldUseAutoDarkmode() || flag) {
    return color;
  }
  let tmp = color.toString(16);
  if (tmp.length < 8) {
    if (tmp.length == 1) {
      tmp = `0000000${ tmp }`;
    } else if (tmp.length == 2) {
      tmp = `000000${ tmp }`;
    } else if (tmp.length == 3) {
      tmp = `00000${ tmp }`;
    } else if (tmp.length == 4) {
      tmp = `0000${ tmp }`;
    } else if (tmp.length == 5) {
      tmp = `000${ tmp }`;
    } else if (tmp.length == 6) {
      tmp = `00${ tmp }`;
    } else if (tmp.length == 7) {
      tmp = `0${ tmp }`;
    } else {
      tmp = '00000000';
    }
  }
  let alpha = tmp.substr(6, 2);
  let first = `0x${ tmp.substr(0, 6) }`;
  let finalColor = getAntiColor(parseInt(first, 16), parseInt(alpha, 16), isText);
  let finalAlpha = getAntiAlpha(parseInt(first, 16), parseInt(alpha, 16));
  let final = (
    parseInt(
      finalColor.toString(16) + alpha,
      16,
    ) >>> 0
  );
  return final;
}

function getAntiAlpha(originColor, alpha, isText) {
  if (alpha <= 0x65 && alpha >= 0x4c && originColor === 0x000000) {
    return 0x99;
  }
  return alpha;
}

function getAntiColor(originColor, alpha, isText) {
  // 一些固定的颜色特殊处理
  if (originColor === 0x32bac0) {
    return 0x25A9AF;
  } else if (originColor === 0x25A9AF) {
    return 0x158B90;
  } else if (originColor === 0xF43F31) {
    return 0xD92719;
  } else if (originColor === 0xD53C32) {
    return 0xB62920;
  }
  if (!isText) {
    // 背景特殊处理
    if (originColor === 0xf6f7f8 || originColor === 0xe9e9ef || originColor === 0xEBEBEC || originColor === 0xefeff0) {
      return 0x000000;
    }
    if (originColor === 0xE3E6E7) {
      // 待确定
      return 0x1a1a1a;
    }
    if (originColor === 0xb0b0b0) {
      // 待确定
      return 0x1a1a1a;
    }
  } else {
    // 某些文字颜色特殊处理
    if (originColor === 0x23262a || originColor === 0x404346 || originColor === 0x454553) {
      return 0xffffff;
    }
  }

  // 采用hsv模型判断
  let rgbColor = hexToRgb(originColor.toString(16));
  let hsvColor = rgbToHsv(rgbColor[0], rgbColor[1], rgbColor[2]);
  let h = hsvColor.h;
  let s = hsvColor.s;
  let v = hsvColor.v;
  let flag = false;
  if (isText) {
    // 文字的情况
    if (hsvColor.h === 0 && hsvColor.s === 0) {
      // 黑或白的情况走这里
      flag = true;
      if (v >= 0 && v < 20) {
        hsvColor.v = 90;
      } else if (v >= 20 && v < 30) {
        hsvColor.v = 80;
      } else if (v >= 30 && v < 40) {
        hsvColor.v = 70;
      } else if (v >= 40 && v < 50) {
        hsvColor.v = 60;
      } else if (v >= 50 && v < 60) {
        hsvColor.v = 50;
      } else if (v >= 60 && v < 90) {
        hsvColor.v = 40;
      } else if (v >= 90 && v <= 100) {
        hsvColor.v = 90;
      } else {
        flag = false;
      }
    } else {
      console.log('Text的其他情况看下hsv', h, '-', s, '-', v, '  originColor:', originColor.toString(16));
    }
  } else {
    // view的情况
    if (hsvColor.h === 0 && hsvColor.s === 0) {
      // 黑或白的情况走这里
      flag = true;
      if (v >= 90 && v < 97) {
        hsvColor.v = 15;
      } else if (v >= 97 && v < 100) {
        hsvColor.v = 0;
      } else if (v === 100) {
        hsvColor.v = 10;
      } else {
        flag = false;
      }
    }
  }
  if (!flag) {
    // 默认处理方法
    hsvColor.s += 8;
    hsvColor.v -= 10;
  }
  // console.log('看下hsv',h,'-',s,'-',v,'  originColor:',originColor.toString(16));
  return hsvToHex(hsvColor.h, Math.min(Math.max(hsvColor.s, 0), 100), Math.min(Math.max(hsvColor.v, 0), 100));
}

function hexToRgb(color) {
  let sColorChange = [];
  let tmp = color.toString(16);
  if (tmp.length < 8) {
    if (tmp.length == 1) {
      tmp = `#00000${ tmp }`;
    } else if (tmp.length == 2) {
      tmp = `#0000${ tmp }`;
    } else if (tmp.length == 3) {
      tmp = `#000${ tmp }`;
    } else if (tmp.length == 4) {
      tmp = `#00${ tmp }`;
    } else if (tmp.length == 5) {
      tmp = `#0${ tmp }`;
    } else if (tmp.length == 6) {
      tmp = `#${ tmp }`;
    } else {
      tmp = '#000000';
    }
  }
  let rgbString = `${ tmp.slice(1).match(/.{2}/g).map((x) => parseInt(x, 16)).join() }`;
  sColorChange = rgbString.split(",");
  return sColorChange;
}

function rgbToHsv(r, g, b) {
  r = r / 255;
  g = g / 255;
  b = b / 255;
  let h, s, v;
  let min = Math.min(r, g, b);
  let max = v = Math.max(r, g, b);
  let l = (min + max) / 2;
  let difference = max - min;

  if (max == min) {
    h = 0;
  } else {
    switch (max) {
      case r:
        h = (g - b) / difference + (g < b ? 6 : 0);
        break;
      case g:
        h = 2.0 + (b - r) / difference;
        break;
      case b:
        h = 4.0 + (r - g) / difference;
        break;
    }
    h = Math.round(h * 60);
  }
  if (max == 0) {
    s = 0;
  } else {
    s = 1 - min / max;
  }
  s = Math.round(s * 100);
  v = Math.round(v * 100);
  return { "h": h, "s": s, "v": v };
}

function hsvToHex(h, s, v) {
  s = s / 100;
  v = v / 100;
  let r = 0, g = 0, b = 0;
  let i = parseInt((h / 60) % 6);
  let f = h / 60 - i;
  let p = v * (1 - s);
  let q = v * (1 - f * s);
  let t = v * (1 - (1 - f) * s);
  switch (i) {
    case 0:
      r = v; g = t; b = p;
      break;
    case 1:
      r = q; g = v; b = p;
      break;
    case 2:
      r = p; g = v; b = t;
      break;
    case 3:
      r = p; g = q; b = v;
      break;
    case 4:
      r = t; g = p; b = v;
      break;
    case 5:
      r = v; g = p; b = q;
      break;
    default:
      break;
  }
  r = parseInt(r * 255.0);
  g = parseInt(g * 255.0);
  b = parseInt(b * 255.0);
  let color = rgbToHex(r, g, b);
  return color;
}

function normalizeColor(color: string | number): ?number {
  const matchers = getMatchers();
  let match;
  if (typeof color === 'number') {
    if (color >>> 0 === color && color >= 0 && color <= 0xffffffff) {
      return changeColor(color, false, false);
    }
    return null;
  }

  let isText = false;
  if (color.indexOf('text') != -1) {
    isText = true;
    color = color.replace(new RegExp("text", "gm"), '');
  }

  let flag = false;
  if (color.indexOf('xm') != -1) {
    flag = true;
    color = color.replace(new RegExp("xm", "gm"), '');
  }

  // Ordered based on occurrences on Facebook codebase
  if ((match = matchers.hex6.exec(color))) {
    let tmp = parseInt(`${ match[1] }ff`, 16) >>> 0;
    return changeColor(tmp, flag, isText);
  }

  if (names.hasOwnProperty(color)) {
    return changeColor(names[color], flag, isText);
  }

  if ((match = matchers.rgb.exec(color))) {
    let tmp = (
      // b
      ((parse255(match[1]) << 24) | // r
      (parse255(match[2]) << 16) | // g
        (parse255(match[3]) << 8) |
        0x000000ff) >>> // a
      0
    );
    return changeColor(tmp, flag, isText);
  }

  if ((match = matchers.rgba.exec(color))) {
    let tmp = (
      // b
      ((parse255(match[1]) << 24) | // r
      (parse255(match[2]) << 16) | // g
        (parse255(match[3]) << 8) |
        parse1(match[4])) >>> // a
      0
    );
    return changeColor(tmp, flag, isText);
  }

  if ((match = matchers.hex3.exec(color))) {
    let tmp = (
      parseInt(
        `${ match[1] +
        match[1] + // r
        match[2] +
        match[2] + // g
        match[3] +
        match[3] // b
        }ff`, // a
        16,
      ) >>> 0
    );
    return changeColor(tmp, flag, isText);
  }

  // https://drafts.csswg.org/css-color-4/#hex-notation
  if ((match = matchers.hex8.exec(color))) {
    let tmp = parseInt(match[1], 16) >>> 0;
    return changeColor(tmp, flag, isText);
  }

  if ((match = matchers.hex4.exec(color))) {
    let tmp = (
      parseInt(
        match[1] +
        match[1] + // r
        match[2] +
        match[2] + // g
        match[3] +
        match[3] + // b
          match[4] +
          match[4], // a
        16,
      ) >>> 0
    );
    return changeColor(tmp, flag, isText);
  }

  if ((match = matchers.hsl.exec(color))) {
    let tmp = (
      (hslToRgb(
        parse360(match[1]), // h
        parsePercentage(match[2]), // s
        parsePercentage(match[3]), // l
      ) |
        0x000000ff) >>> // a
      0
    );
    return changeColor(tmp, flag, isText);
  }

  if ((match = matchers.hsla.exec(color))) {
    let tmp = (
      (hslToRgb(
        parse360(match[1]), // h
        parsePercentage(match[2]), // s
        parsePercentage(match[3]), // l
      ) |
        parse1(match[4])) >>> // a
      0
    );
    return changeColor(tmp, flag, isText);
  }

  return null;
}

function hue2rgb(p: number, q: number, t: number): number {
  if (t < 0) {
    t += 1;
  }
  if (t > 1) {
    t -= 1;
  }
  if (t < 1 / 6) {
    return p + (q - p) * 6 * t;
  }
  if (t < 1 / 2) {
    return q;
  }
  if (t < 2 / 3) {
    return p + (q - p) * (2 / 3 - t) * 6;
  }
  return p;
}

function hslToRgb(h: number, s: number, l: number): number {
  const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
  const p = 2 * l - q;
  const r = hue2rgb(p, q, h + 1 / 3);
  const g = hue2rgb(p, q, h);
  const b = hue2rgb(p, q, h - 1 / 3);

  return (
    (Math.round(r * 255) << 24) |
    (Math.round(g * 255) << 16) |
    (Math.round(b * 255) << 8)
  );
}

// var INTEGER = '[-+]?\\d+';
const NUMBER = '[-+]?\\d*\\.?\\d+';
const PERCENTAGE = `${ NUMBER }%`;

function call(...args) {
  return `\\(\\s*(${ args.join(')\\s*,\\s*(') })\\s*\\)`;
}

let cachedMatchers;

function getMatchers() {
  if (cachedMatchers === undefined) {
    cachedMatchers = {
      rgb: new RegExp(`rgb${ call(NUMBER, NUMBER, NUMBER) }`),
      rgba: new RegExp(`rgba${ call(NUMBER, NUMBER, NUMBER, NUMBER) }`),
      hsl: new RegExp(`hsl${ call(NUMBER, PERCENTAGE, PERCENTAGE) }`),
      hsla: new RegExp(`hsla${ call(NUMBER, PERCENTAGE, PERCENTAGE, NUMBER) }`),
      hex3: /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,
      hex4: /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,
      hex6: /^#([0-9a-fA-F]{6})$/,
      hex8: /^#([0-9a-fA-F]{8})$/
    };
  }
  return cachedMatchers;
}

function parse255(str: string): number {
  const int = parseInt(str, 10);
  if (int < 0) {
    return 0;
  }
  if (int > 255) {
    return 255;
  }
  return int;
}

function parse360(str: string): number {
  const int = parseFloat(str);
  return (((int % 360) + 360) % 360) / 360;
}

function parse1(str: string): number {
  const num = parseFloat(str);
  if (num < 0) {
    return 0;
  }
  if (num > 1) {
    return 255;
  }
  return Math.round(num * 255);
}

function parsePercentage(str: string): number {
  // parseFloat conveniently ignores the final %
  const int = parseFloat(str);
  if (int < 0) {
    return 0;
  }
  if (int > 100) {
    return 1;
  }
  return int / 100;
}

const names = {
  transparent: 0x00000000,

  // http://www.w3.org/TR/css3-color/#svg-color
  aliceblue: 0xf0f8ffff,
  antiquewhite: 0xfaebd7ff,
  aqua: 0x00ffffff,
  aquamarine: 0x7fffd4ff,
  azure: 0xf0ffffff,
  beige: 0xf5f5dcff,
  bisque: 0xffe4c4ff,
  black: 0x000000ff,
  blanchedalmond: 0xffebcdff,
  blue: 0x0000ffff,
  blueviolet: 0x8a2be2ff,
  brown: 0xa52a2aff,
  burlywood: 0xdeb887ff,
  burntsienna: 0xea7e5dff,
  cadetblue: 0x5f9ea0ff,
  chartreuse: 0x7fff00ff,
  chocolate: 0xd2691eff,
  coral: 0xff7f50ff,
  cornflowerblue: 0x6495edff,
  cornsilk: 0xfff8dcff,
  crimson: 0xdc143cff,
  cyan: 0x00ffffff,
  darkblue: 0x00008bff,
  darkcyan: 0x008b8bff,
  darkgoldenrod: 0xb8860bff,
  darkgray: 0xa9a9a9ff,
  darkgreen: 0x006400ff,
  darkgrey: 0xa9a9a9ff,
  darkkhaki: 0xbdb76bff,
  darkmagenta: 0x8b008bff,
  darkolivegreen: 0x556b2fff,
  darkorange: 0xff8c00ff,
  darkorchid: 0x9932ccff,
  darkred: 0x8b0000ff,
  darksalmon: 0xe9967aff,
  darkseagreen: 0x8fbc8fff,
  darkslateblue: 0x483d8bff,
  darkslategray: 0x2f4f4fff,
  darkslategrey: 0x2f4f4fff,
  darkturquoise: 0x00ced1ff,
  darkviolet: 0x9400d3ff,
  deeppink: 0xff1493ff,
  deepskyblue: 0x00bfffff,
  dimgray: 0x696969ff,
  dimgrey: 0x696969ff,
  dodgerblue: 0x1e90ffff,
  firebrick: 0xb22222ff,
  floralwhite: 0xfffaf0ff,
  forestgreen: 0x228b22ff,
  fuchsia: 0xff00ffff,
  gainsboro: 0xdcdcdcff,
  ghostwhite: 0xf8f8ffff,
  gold: 0xffd700ff,
  goldenrod: 0xdaa520ff,
  gray: 0x808080ff,
  green: 0x008000ff,
  greenyellow: 0xadff2fff,
  grey: 0x808080ff,
  honeydew: 0xf0fff0ff,
  hotpink: 0xff69b4ff,
  indianred: 0xcd5c5cff,
  indigo: 0x4b0082ff,
  ivory: 0xfffff0ff,
  khaki: 0xf0e68cff,
  lavender: 0xe6e6faff,
  lavenderblush: 0xfff0f5ff,
  lawngreen: 0x7cfc00ff,
  lemonchiffon: 0xfffacdff,
  lightblue: 0xadd8e6ff,
  lightcoral: 0xf08080ff,
  lightcyan: 0xe0ffffff,
  lightgoldenrodyellow: 0xfafad2ff,
  lightgray: 0xd3d3d3ff,
  lightgreen: 0x90ee90ff,
  lightgrey: 0xd3d3d3ff,
  lightpink: 0xffb6c1ff,
  lightsalmon: 0xffa07aff,
  lightseagreen: 0x20b2aaff,
  lightskyblue: 0x87cefaff,
  lightslategray: 0x778899ff,
  lightslategrey: 0x778899ff,
  lightsteelblue: 0xb0c4deff,
  lightyellow: 0xffffe0ff,
  lime: 0x00ff00ff,
  limegreen: 0x32cd32ff,
  linen: 0xfaf0e6ff,
  magenta: 0xff00ffff,
  maroon: 0x800000ff,
  mediumaquamarine: 0x66cdaaff,
  mediumblue: 0x0000cdff,
  mediumorchid: 0xba55d3ff,
  mediumpurple: 0x9370dbff,
  mediumseagreen: 0x3cb371ff,
  mediumslateblue: 0x7b68eeff,
  mediumspringgreen: 0x00fa9aff,
  mediumturquoise: 0x48d1ccff,
  mediumvioletred: 0xc71585ff,
  midnightblue: 0x191970ff,
  mintcream: 0xf5fffaff,
  mistyrose: 0xffe4e1ff,
  moccasin: 0xffe4b5ff,
  navajowhite: 0xffdeadff,
  navy: 0x000080ff,
  oldlace: 0xfdf5e6ff,
  olive: 0x808000ff,
  olivedrab: 0x6b8e23ff,
  orange: 0xffa500ff,
  orangered: 0xff4500ff,
  orchid: 0xda70d6ff,
  palegoldenrod: 0xeee8aaff,
  palegreen: 0x98fb98ff,
  paleturquoise: 0xafeeeeff,
  palevioletred: 0xdb7093ff,
  papayawhip: 0xffefd5ff,
  peachpuff: 0xffdab9ff,
  peru: 0xcd853fff,
  pink: 0xffc0cbff,
  plum: 0xdda0ddff,
  powderblue: 0xb0e0e6ff,
  purple: 0x800080ff,
  rebeccapurple: 0x663399ff,
  red: 0xff0000ff,
  rosybrown: 0xbc8f8fff,
  royalblue: 0x4169e1ff,
  saddlebrown: 0x8b4513ff,
  salmon: 0xfa8072ff,
  sandybrown: 0xf4a460ff,
  seagreen: 0x2e8b57ff,
  seashell: 0xfff5eeff,
  sienna: 0xa0522dff,
  silver: 0xc0c0c0ff,
  skyblue: 0x87ceebff,
  slateblue: 0x6a5acdff,
  slategray: 0x708090ff,
  slategrey: 0x708090ff,
  snow: 0xfffafaff,
  springgreen: 0x00ff7fff,
  steelblue: 0x4682b4ff,
  tan: 0xd2b48cff,
  teal: 0x008080ff,
  thistle: 0xd8bfd8ff,
  tomato: 0xff6347ff,
  turquoise: 0x40e0d0ff,
  violet: 0xee82eeff,
  wheat: 0xf5deb3ff,
  white: 0xffffffff,
  whitesmoke: 0xf5f5f5ff,
  yellow: 0xffff00ff,
  yellowgreen: 0x9acd32ff
};

module.exports = normalizeColor;
