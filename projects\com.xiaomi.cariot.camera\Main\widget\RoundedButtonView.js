import React from "react";
import {StyleSheet, TouchableOpacity,TouchableWithoutFeedback, View, Image, Text} from "react-native";
import {DarkMode, Host} from "miot";
/**
 * @description 通用按钮
 * @property {string} buttonText - 按钮标题
 * @property {function} onPress - 点击事件
 * @property {style} buttonTextStyle - 按钮文字样式
 * @property {style} buttonStyle - 按钮样式
 * @property {bool} disabled - 是否禁用点击，默认值 false
 * @property {string} disabledBackgroundColor - 按钮禁用时背景色
 * @property {boolean} hide - 是否隐藏此组件
 */
export default class RoundedButtonView extends React.Component {
    static defaultProps = {
        onPress:null,
        buttonText:"",
        buttonTextStyle:null,
        buttonStyle:null,
        disabled:false,
        disabledBackgroundColor:"rgba(50, 186, 192, 0.3)",
        accessibilityLabel:"",
        hide: false,
    };
    constructor(props){
        super(props);
    }

    render() {
        if(this.props.hide){
            return null;
        }
        let extraButtonStyle = {};
        if(this.props.disabled&&this.props.disabledBackgroundColor){
            extraButtonStyle.backgroundColor = this.props.disabledBackgroundColor;
        }
        return (
            <TouchableOpacity  disabled={this.props.disabled}onPress={this.props.onPress}
                               accessibilityLabel={this.props.accessibilityLabel?this.props.accessibilityLabel:""}
            >
                <View style={[styles.container,this.props.buttonStyle,extraButtonStyle]}>
                    <Text style={[styles.textStyle,this.props.buttonTextStyle]}>{this.props.buttonText}</Text>
                </View>
            </TouchableOpacity>
        );
    }

}

const styles = StyleSheet.create({
    container:{
        minHeight:45,
        backgroundColor:"#4A70A5",
        borderRadius:45,
        justifyContent:"center",
        alignItems:"center",
        marginBottom:Host.isIphoneXSeries?25:14
    },
    textStyle: {
        fontSize:15,
        fontWeight:'bold',
        color:"#FFFFFFE5",
        fontFamily:Host.isIos?null:'',
    }
});