'use strict';

import React from 'react';
import { BackHandler, View, Text, Image, StyleSheet, FlatList, Dimensions, TouchableOpacity, ActivityIndicator, Platform } from 'react-native';

import { Device, Host } from 'miot';

import { MessageDialog } from "miot/ui";

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import MultiSelectableNavigationBar from '../ui/MultiSelectableNavigationBar';
import AlbumHelper from '../util/AlbumHelper';
import { DarkMode } from 'miot/Device';
import Toast from '../components/Toast';
import { DescriptionConstants } from '../Constants';
import { NavigationBar } from 'mhui-rn';
import CommonMsgDialog from '../ui/CommonMsgDialog';
import { PermissionsAndroid } from 'react-native';

export default class SdcardPage extends React.Component {

  state = {
    isSelectMode: false,
    index: 0,
    isEmpty: true,
    isCurrentDayEmpty: false,
    calendarDays: [],
    dialogVisible: false,
    albumFiles: [],
    showLoading: true,
    showPermissionDialog: false
  };

  constructor(props) {
    super(props);
    this.isDelete = false;
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.isPageForeGround = true;

        this._onGetData();
      }
    );

    this.didBlurListener = this.props.navigation.addListener(
      'didBlur',
      () => {

        this.isPageForeGround = false;
      }
    );

    this.dateTime = new Date();
  }

  componentWillUnmount() {
    Dimensions.removeEventListener('change', this.dimensionListener);

    this.didFocusListener.remove();
    this.didBlurListener.remove();
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  dimensionListener = (args) => {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    if (Platform.OS === "ios") {
      console.log('why!, setDimensionsIos000: ', args);
      console.log('why!, Dimensions', Dimensions.get('window'));
      if (args && args.screen && args.window) {
        if (args.screen.width !== args.window.width || args.screen.height !== args.window.height) {
          setTimeout(() => Dimensions.set({ 'window': args.screen }), 10);
          console.log('纠正========');
        }
      }
    }
  }

  setNavigation(isSelectAll, isSelectMode, isDisableSelect, title) {
    if (Device.isReadonlyShared) {
      isDisableSelect = true;
    }
    this.props.navigation.setParams({

      title: title ? title : LocalizedStrings["s_photo_album"],

      left: [
        {
          key: isSelectMode ? NavigationBar.ICON.CLOSE : NavigationBar.ICON.BACK,
          onPress: () => {
            if (this.state.isSelectMode) {// 如果已经是选择模式，点左边 应该退出选择模式
              this.onSelectAllChanged(false);// 将isSelected重置
              this.setNavigation(false, false, false,);
              this.setState({ isSelectMode: false });
            } else { // 不是选择模式 就退出吧
              this.props.navigation.goBack();
            }
          }
        }
      ],
      right: !isDisableSelect ?
        [
          {
            key: !isSelectMode ? NavigationBar.ICON.EDIT : (isSelectAll ? NavigationBar.ICON.SELECTED_ALL : NavigationBar.ICON.SELECT_ALL),
            onPress: () => {
              if (!this.state.isSelectMode) { //进入选择模式
                this.setNavigation(false, true, false, LocalizedStrings["action_select"]);
                this.setState({ isSelectMode: true });
              } else if (this.state.isSelectAll) { //退出全选模式
                this.onSelectAllChanged(false);
              } else { //进入全选模式
                this.onSelectAllChanged(true);
              }
            }
          }
        ] : null,
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
  }

  render() {
    return (

      <View style={styles.container}>

        {/* {this._renderHeader()} 相册不需要day view */}
        {this._renderDayFiles()}
        {this._renderEmptyLayout()}
        {this._renderBottomSelectView()}
        {this._renderLoadingView()}
        {this._renderDialog()}
        {this._renderPermissionDialog()}
      </View>
    );
  }

  _renderDialog() {
    let title = this.isDelete ? LocalizedStrings["delete_title"] : LocalizedStrings["save_title"];
    let btn = this.isDelete ? LocalizedStrings["delete_confirm"] : LocalizedStrings["action_confirm"];
    return (
      <MessageDialog title={title}
        cancelable={true}
        cancel={LocalizedStrings["action_cancle"]}
        confirm={btn}
        onCancel={(e) => {
          console.log('onCancel', e);
        }}
        onConfirm={(e) => {
          if (this.isDelete) {
            this.onConfirmDelete();
          }
        }}
        onDismiss={() => {
          this.setState({ dialogVisible: false });
        }}
        visible={this.state.dialogVisible} />
    );
  }

  _renderLoadingView() {
    if (!this.state.showLoading) {
      return;
    }
    let isDark = DarkMode.getColorScheme() == "dark";
    return (
      <View

        style={{ position: "absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <ActivityIndicator
          style={{ width: 54, height: 54 }}
          color={isDark ? "xm#ffffff" : "xm#000000"}
          size={"large"}

        />
        <Text
          style={{ marginTop: 10, fontSize: 12, color: "#000000" }}>
          {LocalizedStrings["camera_loading"]}
        </Text>
      </View>
    );

  }
  // 这里是在相册页删除的时候
  _renderBottomSelectView() {
    // 这里是没有那个选择按钮的
    if (!this.state.isSelectMode) {

      return;
    }

    return (
      <View style={{ width: "100%", height: 69, bottom: 10, borderBottomColor: '#ffffff', borderTopColor: "#e5e5e5", borderTopWidth: 1, borderBottomWidth: 1, display: "flex", flexDirection: "row", justifyContent: "center", alignItems: "center", backgroundColor: '#ffffff' }}>

        <TouchableOpacity
          style={{ width: 50, display: "flex", flex: 1, alignItems: "center" }}
          onPress={() => { this.onPressDelete(); }}
          accessibilityLabel={DescriptionConstants.lc_14}
        >

          <Image
            style={{ width: 25, height: 25 }}
            source={!Util.isDark() ? require("../../Resources/Images/camera_icon_loc_pic_delete.png") : require("../../Resources/Images/camera_icon_loc_pic_delete_white.png")} />
          <Text
            style={{ color: "#000000", fontSize: 11 }}
          >
            {LocalizedStrings["delete_files"]}
          </Text>
        </TouchableOpacity>
      </View>
    );
  }
  // 这里是相册的渲染
  _renderDayFiles() {
    if (this.state.isCurrentDayEmpty) {
      return null;
    }
    return (
      // <View style={{ flexGrow: 1, marginTop: 22, marginHorizontal: 16 }}>
      // 简单列表组件
      <FlatList
        style={{ flexGrow: 1, marginTop: 22, marginHorizontal: 16, flex: 1 }}
        data={this.state.albumFiles}
        renderItem={({ item, index }) => this._renderDayFile(item, index)}
        numColumns={3}
        keyExtractor={(item, index) => index}
        ListFooterComponent={<View style={{ height: 20 }}></View>}
      />
      // </View>

    );

  }
  // 这里是渲染的东西 这里有判断图片还是视频
  _renderDayFile(item, index) {
    let marginHorizontal = 3.5;
    let screenWidth = self.windowWidth;
    let containerWidth = (screenWidth - 33 - marginHorizontal * 6) / 3;
    let path = item.url;
    let isVideo = item.mediaType == 2;
    let duration = item.duration;
    this.dateTime.setTime(duration);// 毫秒形式的
    let seconds = Number.parseInt(duration / 1000) % 60;// 毫秒 /1000 % 60 得到秒数
    let minutes = Number.parseInt(duration / 1000 / 60) % 60; // 毫秒/1000 / 60  % 60 =>minutes
    let hours = Number.parseInt(duration / 1000 / 60 / 60) % 24; // hour  
    let durationText = `${(hours > 0 ? (hours > 9 ? `${hours}:` : `0${hours}:`) : "") + (minutes > 9 ? minutes : (`0${minutes}`))}:${seconds > 9 ? seconds : (`0${seconds}`)}`;
    // createTime can be ignored

    return (
      <TouchableOpacity
        style={{ position: "relative", width: containerWidth, height: 70, paddingBottom: 10, marginLeft: 3.5, marginRight: 3.5 }}
        onPress={() => this._onPressVideoFileView(index)}
        accessibilityLabel={DescriptionConstants.sz_8_7}
      >
        {/* 这个小图片是选中的时候 */}
        <Image style={{ width: "100%", height: "100%", borderRadius: 5 }}
          source={{ uri: path }}
        />
        {
          this.state.isSelectMode ?
            <Image
              accessibilityLabel={DescriptionConstants.sz_810}
              style={{ width: 20, height: 20, position: "absolute", top: 4, right: 4 }}
              source={item.isSelected ? require("../../Resources/Images/icon_selected.png") : require("../../Resources/Images/icon_unselected.png")}
            /> :
            null
        }
        {/* 这里是video时的图片 */}
        {
          isVideo ?
            <View style={{ width: "100%", height: 20, position: "absolute", bottom: 10, display: "flex", flexDirection: "row", justifyContent: "space-between", alignItems: "center" }}>
              <Image
                style={{ width: 20, height: 15 }}
                source={require("../../Resources/Images/alarm_video.png")}
                accessibilityLabel={DescriptionConstants.sz_8_8}
              />

              <Text
                style={{ fontSize: 12, color: "white" }}
                accessibilityLabel={DescriptionConstants.sz_8_9}
              >
                {durationText}

              </Text>

            </View>
            : null
        }



      </TouchableOpacity>
    );
  }

  // 这里是没有文件的时候的页面
  _renderEmptyLayout() {
    if (this.state.showLoading) {
      return;
    }
    if (this.state.isCurrentDayEmpty) {
      return (
        <View
          style={{ width: "100%", flexGrow: 1, display: "flex", justifyContent: "center", alignItems: "center" }}
        >
          <Image
            accessibilityLabel={DescriptionConstants.sz_8_11}
            source={require("../../Resources/Images/icon_camera_empty_files.png")}
            style={{ width: 79, height: 79 }}
          />
          <Text
            accessibilityLabel={DescriptionConstants.sz_8_12}
            style={{ fontSize: 14, color: "#808080" }}
          >
            {LocalizedStrings["no_files"]}
          </Text>
        </View>
      );
    } else {
      return null;
    }

  }

  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 存储卡/相册
    // status == 1 麦克风
    // 
    let message = "";
    message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);

    return (
      <CommonMsgDialog
        title={message}
        confirmText={LocalizedStrings["setting"]}
        cancelText={LocalizedStrings["action_cancle"]}
        onConfirmPress={(e) => {
          Host.ui.openTerminalDeviceSettingPage(1);
          this.setState({ showPermissionDialog: false });
        }}
        onCancelPress={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog} />
    );

  }


  componentDidMount() { // 第一次进来的时候这样子设计。
    Dimensions.addEventListener('change', this.dimensionListener);

    self.windowWidth = Dimensions.get("window").width;
    self.windowHeight = Dimensions.get("window").height;
    if (self.windowHeight < self.windowWidth) {
      let sw = self.windowWidth;
      self.windowWidth = self.windowHeight;
      self.windowHeight = sw;
    }

    this.setNavigation(false, false, true);
    this.setState({ index: 1 });

    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  // android返回键处理
  onBackHandler = () => {
    if (!this.isPageForeGround) {
      return false;
    }
    if (this.state.isSelectMode) {
      this.onSelectAllChanged(false);
      this.setNavigation(false, false, false);
      this.setState({ isSelectMode: false });
      return true;
    } else {
      return false;// 不接管
    }
  }
  // 点击相册中的东西
  _onPressVideoFileView(index) {
    // 这里是没有点击选择 就直接跳到相册详情页
    if (!this.state.isSelectMode) {
      let album = this.state.albumFiles[index];
      if (album.mediaType == 2) {
        // 带上视频跳过去就行了
        // 视频
        // todo
        this.props.navigation.navigate("AlbumPhotoViewPage", { index: index, type: 2 });
        // this.props.navigation.navigate("AlbumVideoViewPage", { index: index, item: this.state.albumFiles[index] });
      } else {
        // 图片
        // 检索出所有的图片，跳过去
        // 只需要传index过去
        this.props.navigation.navigate("AlbumPhotoViewPage", { index: index });
        // 携带信息跳过去。
        // todo
      }
      // this.props.navigation.navigate('SdcardHourPage',{ tag: tag,hour: hour });
    } else {
      let albumFile = this.state.albumFiles[index];
      albumFile.isSelected = !albumFile.isSelected;
      let selectedCount = 0;
      for (let file of this.state.albumFiles) {
        if (file.isSelected) {
          selectedCount++;
        }
      }
      // 在这里重新设置标题栏 
      this.setNavigation(false, true, false, LocalizedStrings["selected_count"].replace("%1$d", selectedCount));
      if (selectedCount == 0) {
        this.onSelectAllChanged(false);
      } else if (selectedCount == this.state.albumFiles.length) {
        this.onSelectAllChanged(true);
      } else {
        this.setState({ albumFiles: this.state.albumFiles });// 刷新页面 状态不要保留在ui控件里
      }
    }
  }

  // 选择了所有
  onSelectAllChanged(isSelectAll) {
    this.setNavigation(isSelectAll, this.state.isSelectMode, this.state.isCurrentDayEmpty);
    this.setState({ index: isSelectAll ? 0 : 1 });
    for (let timeHourItem of this.state.albumFiles) {
      timeHourItem.isSelected = isSelectAll ? true : false;
    }
    this.setNavigation(isSelectAll, this.state.isSelectMode, this.state.isCurrentDayEmpty, isSelectAll ? LocalizedStrings["selected_count"].replace("%1$d", this.state.albumFiles.length) : LocalizedStrings["action_select"]);
    this.setState({ albumFiles: this.state.albumFiles, isSelectAll: isSelectAll });
  }

  _onGetData() {

    this.setNavigation(this.state.isSelectAll, this.state.isSelectMode, this.state.isCurrentDayEmpty);
    this.setState({ index: 1 });

    // 当前选中的是哪个日期
    this.updateSdcardFiles();
  }

  updateSdcardFiles() {

    AlbumHelper.checkPermission()
      .then(() => {
        this.setState({ showLoading: true });
    // 获取相册文件，要求是归类整理好了的。
        AlbumHelper.getAlbumFiles()
          .then((result) => {
            this.setState({ showLoading: false });
            let isEmpty = false;
            if (result == null || result.length == 0) {
              isEmpty = true;
            }
            this.setState({ albumFiles: result, isCurrentDayEmpty: isEmpty });
            this.setNavigation(this.state.isSelectAll, this.state.isSelectMode, false);
          })
          .catch((error) => {
            this.setState({ showLoading: false });
            console.log("request camera album list failed", error);
            this.setState({ isCurrentDayEmpty: true, isSelectAll: false, isSelectMode: false });
            this.setNavigation(this.state.isSelectAll, this.state.isSelectMode, true);
          });
      })
      .catch((result) => {
        if (result == PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
          this.setState({ showPermissionDialog: true });
        } else {
          Toast.success("camera_no_write_permission");
        }
      });

    

    // todo

  }

  onConfirmDelete() {

    AlbumHelper.checkPermission()
      .then(() => {
        let timeItems = [];
        for (let timeHourItem of this.state.albumFiles) { // 遍历所有的正在展示的内容
          if (timeHourItem.isSelected) {
            timeItems.push(timeHourItem.url);
          }
        }
        AlbumHelper.deleteAlbumFilesByUrl(timeItems)
          .then(() => {

            this.setState((state) => {
              return {
                isSelectMode: false,
                isSelectAll: false
              };
            }, () => {
              this._onGetData();

            });
            Toast.success("delete_success");
          })
          .catch((err) => {
            if (Platform.OS != "android") {
              return;
            }
            Toast.fail("delete_failed", err);
          });
      })
      .catch((result) => {
        if (result == PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
          this.setState({ showPermissionDialog: true });
        } else {
          Toast.success("camera_no_write_permission");
        }
      });


  }

  onPressDelete() {
    let timeItems = [];
    for (let timeHourItem of this.state.albumFiles) { // 遍历所有的正在展示的内容
      if (timeHourItem.isSelected) {
        timeItems = timeItems.concat(timeHourItem.timeItemList);
      }
    }
    if (timeItems == null || timeItems.length == 0) {
      Toast._showToast(LocalizedStrings["bottom_action_tip"]);
      return;
    }
    this.isDelete = true;
    if (Platform.OS == "android") {
      this.setState({ dialogVisible: true });
    } else {
      this.onConfirmDelete();
    }

  }

}



export const styles = StyleSheet.create({
  container: {
    backgroundColor: DarkMode.getColorScheme() == 'dark' ? 'xm#000' : 'xm#fff',
    width: '100%',
    height: '100%',
    display: "flex",
    flexWrap: "nowrap"
  }
});
