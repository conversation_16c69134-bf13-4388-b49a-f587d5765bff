import React from 'react';
import {
  Text,
  Dimensions,
  View,
  Image,
  TouchableWithoutFeedback,
  ActivityIndicator,
  TouchableOpacity,
  Platform,
  StatusBar,
  SafeAreaView, ScrollView
} from 'react-native';
import Toolbar from './components/Toolbar';
import Toast from '../components/Toast';
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import { Stat, StatEV } from "../util2/Stat";
import Util from "../util2/Util";
import { MessageDialog } from "miot/ui/Dialog";
import ImageButton from "miot/ui/ImageButton";
import EventGrid from "../widget/EventGrid";
import { DefFilter } from "../widget/EventList";
import BasePage, { BaseStyles } from "../BasePage";
import DldMgr from "../framework/DldMgr";
import { Service, Device, Host, API_LEVEL } from 'miot';
import Calendar from '../widget/Calendar';
import CoverLayer from '../widget/CoverLayer';
import NoSdcardPage from '../setting/NoSdcardPage';
import NetInfo from "@react-native-community/netinfo";
import { CldDldTypes } from "../framework/CloudEventLoader";
import { Tabs } from './AllStorage';
import TrackUtil from '../util/TrackUtil';
import VersionUtil from '../util/VersionUtil';
import { DescriptionConstants } from '../Constants';
import dayjs from 'dayjs';
import { DarkMode } from 'miot/Device';
import CameraConfig from '../util/CameraConfig';
import VipUtil from '../util/VipUtil';
import LoadingView from '../ui/LoadingView';
import LogUtil from '../util/LogUtil';
import CameraPlayer from '../util/CameraPlayer';
import ABTest from "../util/ABTest";
import SdcardEventLoader from "../framework/sdcard/SdcardEventLoader";
// import CloudPurchaseView from "miot/ui/CloudPurchaseView";

export const MaxSel = 50;
const LocalStorageH = 165;
const CloudStorageH = 165;
const SdcardStorageH = 165;
const kWindowHeight = Math.max(Dimensions.get("screen").height, Dimensions.get("screen").width);
const kPxScale = Math.min(kWindowHeight / 896, 1);
const TAG = "StorageUI";
export default class StorageUI extends BasePage {

  constructor(props) {
    super(props);
    this.mSelEv = [];
    this.mDld = { total: 0, list: [], allowData: false };
    this.initState({
      isEditing: false,
      showDelDlg: false,
      lstHeader: null,
      mDate: new Date(),
      marginTop: 0,
      listEmptyView: props.type === Tabs.Card ? (CameraConfig.isDeviceTemperatureHigh ? this.renderHighTemperatureView : !CameraConfig.isLocalNetwork ? this.renderLanDiffView : (CameraPlayer.getInstance().sdcardCode == 1 || CameraPlayer.getInstance().sdcardCode == 5) ? <NoSdcardPage></NoSdcardPage> : null) : null,
      emptyIcon: null,
      emptyDes: null,
      showReload: false,
      isVip: CameraConfig.isVip,
      showDownloadHint: false,
      netStatus: true, // true for connected
      emtypDataDay: false,
      showLoading: true,
      rollingInterval: -1, // 云存用户套餐有效天数
      showGlobalLoading: (this.props.type == Tabs.Cloud ? true : false),
      emptyData: false,
      isLocalNetwork: CameraConfig.isLocalNetwork
    });
    this.mHeaderInfo = null;
    this.mSelDate = null;
    this.localSelectDays = false; // 本地选中日历状态
    this.mReNonce = null;
    this.mLoader = props.loader;
    this.mSltDay = false;// 是否是用户点击选择了一天
    this.mSltDayMore = false;// 选择一天后，上滑动后的状态，显示从选择后日期开始的所有日期
    this.type = props.type;// 页面类型
    this.photoDeleted = [];
    this.mFirstTimeAfterSelectNewDate = true;
    this.isCloudServer=CameraConfig.getIsCloudServer();
    this.mSupportCloud = VersionUtil.isFirmwareSupportCloud(Device.model);
    this.mSupportCloud = this.props.type == Tabs.Cloud ? this.mSupportCloud : true; // 能进到云存管理云存页面的都是支持云存的区域，但设备可能因为版本底不支持，要提升固件升级
    this.mShowCloudForExpired = CameraConfig.showCloudForExpired;
    this.mTodayIsClicked = false;
    DldMgr.addLdrs(props.loader);
    this.networkStateListener = CameraPlayer.getInstance().addNetworkListener((state) => {
      LogUtil.logOnAll(TAG, "network change", this.type);
      CameraPlayer.getInstance().startConnect();
      if (this.type === Tabs.Card) {
        this.checkIsLocal();
      }
    });
  }

  componentDidMount() {
    super.componentDidMount();
    DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');
  }

  componentWillUnmount() {
    super.componentWillUnmount();
    this.networkStateListener && this.networkStateListener.remove();
    this.delayToLoadData && clearTimeout(this.delayToLoadData);
    this.setState = () => false;
  }

  onResume() {
    super.onResume();
    this.mPageShowStat();
    this.mDldL = DldMgr.addListener((aStatus) => {
      console.log(this.tag, "dldL", aStatus);
      if ("status" == aStatus.type) {
        // Toast.show(aStatus.detail);
      }
    });
    this.refreshHeader();
    this.mLdrL = this.mLoader.addListener(() => {
      this.refreshHeader();
    });
    if (this.type === Tabs.Card) {
      this.checkIsLocal();
    }

    // just temp;
    this.unSubNetInfo = NetInfo.addEventListener((state) => {

      LogUtil.logOnAll(TAG, "NetInfo", state.type, state.isConnected, state);
      if (!this.state.netStatus && state.isConnected) {
        this.refreshHeader();
        setTimeout(() => {
          this.setState({ netStatus: state.isConnected });
        }, 100);
      } else {
        this.setState({ netStatus: state.isConnected, showGlobalLoading: false });
      }
    });

    if (CameraConfig.isToUpdateVipStatue) {
      VipUtil.getVipStatus().then(({ isVip }) => {
        this.setState({ isVip: isVip });
      }).catch((err) => {
        console.log("get vip err", err);
      });
    }
    DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');
  }

  checkIsLocal() {
    LogUtil.logOnAll(TAG, "checkIsLocal", new Date().getTime());
    // 清除之前的超时处理
    this.timeoutLocalPing && clearTimeout(this.timeoutLocalPing);
    
    // 设置新的超时处理，无论当前是否在局域网
    this.timeoutLocalPing = setTimeout(() => {
      LogUtil.logOnAll(TAG, "checkIsLocal timeout", this.state.isLocalNetwork);
      // 超时时总是认为不在同一局域网
      if (this.state.isLocalNetwork) {
        CameraConfig.isLocalNetwork = false;
        // 先更新状态，确保differentLanAfter使用最新状态
        this.setState({ isLocalNetwork: false }, () => {
          this.differentLanAfter();
        });
      }
    }, 3000);
    
    Device.getDeviceWifi().localPing().then((response) => {
      LogUtil.logOnAll(TAG, "localPing:", response, this.state.isLocalNetwork);
      this.timeoutLocalPing && clearTimeout(this.timeoutLocalPing);
      CameraConfig.isLocalNetwork = response;
      let tempLan = this.state.isLocalNetwork;

      // 更新状态后再调用相应方法
      this.setState({ isLocalNetwork: response }, () => {
        if (tempLan && !response) {
          // 局域网变为非局域网
          this.differentLanAfter();
        } else if (!tempLan && response) {
          // 非局域网变为局域网
          this.sameLanAfter();
        }
      });

    }).catch((err) => {
      LogUtil.logOnAll(TAG, "localPing error", err, new Date().getTime());
      this.timeoutLocalPing && clearTimeout(this.timeoutLocalPing);
      CameraConfig.isLocalNetwork = false;
      let tempLan = this.state.isLocalNetwork;
      
      // 更新状态后再调用相应方法
      this.setState({ isLocalNetwork: false }, () => {
        if (tempLan) {
          this.differentLanAfter();
        }
      });
    });
  }

  sameLanAfter() {
    // 切换网络后，设备p2p会断开连接，需要加个延迟再去获取数据，
    // 否则可能会无法取得正常数据 看看是否需要添加
    this.setState({ 
      listEmptyView: null,  // 清除非局域网提示视图
      lstHeader: this.mSdcardHeader,  // 恢复header显示
      headerHeight: SdcardStorageH,  // 恢复header高度
    }, () => {
      // 然后再刷新数据和header
      this.refreshHeader();
      if (Platform.OS == "ios") {
        // 添加一个延迟获取
        this.delayToLoadData && clearTimeout(this.delayToLoadData);
        this.delayToLoadData = setTimeout(() => {
          if (this.mEvLst) {
            this.mEvLst.mRefresh();
          }
        }, 1000);
      } else {
        if (this.mEvLst) {
          this.mEvLst.mRefresh();
        }
      }

    });
  }

  differentLanAfter() {
    if (this.props.isEditing) {
      // 编辑模式下，需要退出编辑模式
      this.exitEdit();
    }
    this.refreshHeader();
    if (this.mEvLst) {
      this.mEvLst.clearListData();
    }

  }


  onPause(willFinish) {
    super.onPause();
    this.mDldL.remove();
    this.mLdrL.remove();
    this.unSubNetInfo();
    // remove
  }

  mPageShowStat() {
    let id = null;
    switch (this.type) {
      case Tabs.Cloud:
        id = 'Storage_CloudStorage_Show';// Storage_CloudStorage_Show
        break;
      case Tabs.Local:
        id = 'Storage_LocalAlbum_Show';// Storage_LocalAlbum_Show
        break;
      case Tabs.Card:
        id = 'Storage_MemoryCard_Show';// Storage_MemoryCard_Show
        break;
    }
    if (id) {
      TrackUtil.reportClickEvent(id);
      console.log('stat: ', id);
    }

  }

  refreshHeader() {
    this.mLoader.getSummary()
      .then((aInfo) => {
        this.mHeaderInfo = aInfo.info;
        console.log(this.tag, "refreshHeader", aInfo.info);
        let sdcardCode = 0;
        let days30 = 0;
        switch (aInfo.type) {
          case "local":
            this.setState({ lstHeader: this.mLocalHeader, headerHeight: LocalStorageH });
            break;
          case "cloud":
            if (!this.mHeaderInfo.vip && this.mHeaderInfo.showCloudForExpired) {
              this.mShowCloudForExpired = true;
            }
            days30 = 30 * 24 * 60 * 60 * 1000;
            this.rollingInterval = this.mHeaderInfo.rollingInterval ? (this.mHeaderInfo.rollingInterval > days30 ? this.mHeaderInfo.rollingInterval : this.mHeaderInfo.rollingInterval + 1 * 24 * 60 * 60 * 1000) : -1;
            VipUtil.getVipStatus().then(({ isVip }) => {
              this.setState({ showGlobalLoading: false, lstHeader: this.mCloudHeader, headerHeight: CloudStorageH, isVip: isVip, rollingInterval: this.rollingInterval });
            }).catch((err) => {
              this.setState({ showGlobalLoading: false });
              console.log("get vip err", err);
            });
            break;
          case "sdcard":
            LogUtil.logOnAll("SD StorageUI summary:", JSON.stringify(aInfo));
            sdcardCode = this.mHeaderInfo.sdcardCode;
            this.doSdcardViewShow(sdcardCode, this.state.isLocalNetwork);

            break;
          default:
            break;
        }
      })
      .catch((aErr) => {
        this.setState({
          showGlobalLoading: false
        });
        console.log(this.tag, "get summary failed", aErr);
        LogUtil.logOnAll("StorageUI summary error:", JSON.stringify(aErr));
      });
  }

  doSdcardViewShow(sdcardCode, isLocalNetwork) {
    if (CameraConfig.isDeviceTemperatureHigh) {
      // 没有header，empty就是高温显示的页面
      this.setState({ lstHeader: null, headerHeight: 0, listEmptyView: this.renderHighTemperatureView });
    } else if (!isLocalNetwork) {
      this.setState({ lstHeader: this.mSdcardHeader, headerHeight: SdcardStorageH, listEmptyView: this.renderLanDiffView });
    } else if (sdcardCode == 1 || sdcardCode == 5) {
      // 没有header，empty就是sdcardEmpty;
      this.setState({ lstHeader: null, headerHeight: 0, listEmptyView: <NoSdcardPage></NoSdcardPage> });
    } else if (sdcardCode == 3 || sdcardCode == 4) {
      this.setState({ lstHeader: this.mSdcardHeader, headerHeight: 90, emptyDes: LocalizedStrings["sdcard_page_desc_error"] });
    } else if (sdcardCode == -1) {
      this.setState({ lstHeader: null, headerHeight: 0, emptyDes: LocalizedStrings["sdcard_page_desc_failed"] });
    } else {
      this.setState({ lstHeader: this.mSdcardHeader, headerHeight: SdcardStorageH, emptyDes: LocalizedStrings["sdcard_page_desc_empty"], listEmptyView: null });
    }
  }

  render() {
    // return this.renderStorageHeader();
    let isCloud = this.type == Tabs.Cloud ? true : false;
    let isLocal = this.type == Tabs.Local ? true : false;
    let isSDCard = this.type == Tabs.Card ? true : false;
    let showVipCloud = this.state.isVip || this.mShowCloudForExpired;
    let isShow = (((isCloud && showVipCloud) || isSDCard) && this.state.netStatus) || isLocal ? true : false;
    LogUtil.logOnAll('storageUI', `rendering isCloud ${ isCloud }, isVip ${ this.state.isVip }, showVipCloud ${ showVipCloud }, netstatus ${ this.state.netStatus }, supportCloud ${ this.mSupportCloud }`);
    console.log('storageUI----------> ', `rendering isCloud ${ isCloud }, isVip ${ this.state.isVip }, showVipCloud ${ showVipCloud }, netstatus ${ this.state.netStatus }, supportCloud ${ this.mSupportCloud }`);
    let mShowCloudPermission = (isCloud && !showVipCloud) && !Device.isOwner && this.mSupportCloud;
    let mShowCloudBuyTip = !mShowCloudPermission && isCloud && !showVipCloud && this.state.netStatus && !this.mShowCloudForExpired;
    let mShowReload = !this.state.netStatus && this.type != Tabs.Local && this.mSupportCloud && !mShowCloudPermission;
    let mShowLoading = this.state.showLoading && !mShowCloudBuyTip && !mShowReload && !mShowCloudPermission && this.mSupportCloud;
    if (isSDCard) {
      mShowLoading = this.mHeaderInfo && (this.mHeaderInfo.sdcardCode == 5 || this.mHeaderInfo.sdcardCode == 1) ? false : mShowLoading;
    }

    if (isSDCard && !CameraConfig.isLocalNetwork) {
      mShowLoading = false;
    }
    console.log('render isshow:', isShow, 'netinfo:', this.state.netStatus);
    if (this.state.showGlobalLoading) {
      return (<View style={[BaseStyles.pageRoot, { paddingTop: 20 }]}>
        {this.renderGlobalLoading()}
      </View>);
    } else {
      return (<View style={[BaseStyles.pageRoot, { paddingTop: 20 }]}>
        {
          isShow ?
            <EventGrid
              ref={(aEvLst) => { this.mEvLst = aEvLst; }}
              loader={this.mLoader}
              loaderArgs={{ filter: DefFilter, startDate: this.state.mDate }}
              selectAll={this.props.selectAll}
              eventHeaderView={this.props.isEditing ? null : this.state.lstHeader}
              eventHeaderHeight={this.state.headerHeight}
              eventEmptyView={this.state.listEmptyView}
              onEventPress={this.mEvPressed}
              onScroll={this.mScroll}
              emptyDes={this.state.emptyDes == null ? this.props.emptyDes : this.state.emptyDes}
              emptyIcon={this.state.emptyIcon}
              isEditing={this.props.isEditing}
              isSltDay={this.mSltDay}
              // isSltDayMore={this.mSltDayMore}
              onShowCalendar={this.mShowPopupView}
              onStartEdit={this.mStartEdit}
              onGetDataDone={this.mGetDataDone}
              onSwitchSltDayMore={this.mSwitchSltDayMore}
              onSelectAllCB={this.mSelectAllCB}
              typeTab={ this.type}
              type={this.props.type == Tabs.Cloud ? CldDldTypes.Files : null}
              autoRefresh={this.props.type == Tabs.Card ? false : true}
              abType={ABTest.Types.DFT}
            /> : null
        }
        {
          isShow ?
            (this.props.isEditing ?
              <Toolbar
                showDownload={this.mLoader.canDownload()}
                deletePressed={() => {
                  if (this.mSelEv.length < 1) {
                    Toast.fail("bottom_action_tip");
                  } else {
                    this.setState({ showDelDlg: true });
                  }
                }}
                downloadPressed={() => this.downloadPressed()} /> : null) : null
        }
  
        <MessageDialog
          visible={this.state.showDelDlg}
          canDismiss={false}
          message={LocalizedStrings["delete_title"]}
          messageStyle={[BaseStyles.text17, { alignSelf: 'center', color: '#333333', fontWeight: '400' }]}
          onDismiss={() => {
            this.setState({ showDelDlg: false });
          }}
          buttons={[
            {
              text: LocalizedStrings["action_cancle"],
              callback: (_) => this.setState({ showDelDlg: false })
              // ignore
            },
            {
              text: LocalizedStrings["delete_confirm"],
              callback: (_) => {
                this.setState({ showDelDlg: false });
                this.deletePressed();
              }
              // ignore
            }
          ]}
        />
  
        {this._renderDownloadHint()}
  
        <CoverLayer CoverLayerState={this.props.CoverLayerState} ref={(ref) => this.coverLayer = ref} />
        { this.mSupportCloud ? (mShowCloudBuyTip ? this.renderCloudBuyViewNew() : null) : this.renderUpgradeView() }
        { mShowReload ? this.renderReloadView() : null}
        { mShowLoading ? this.renderLoadingView() : null}
        { mShowCloudPermission ? this.renderCloudNoPermissionView() : null}
      </View>);
    }
  }


  componentDidUpdate(aPrevProps) {
    if (this.props.isEditing !== aPrevProps.isEditing && !this.props.isEditing) {
      this.exitEdit();
    }
  }

  exitEdit() {
    this.props.startEdit(null);
    for (let ev of this.mSelEv) {
      ev.selected = undefined;
    }
    this.mSelEv = [];
  }

  mGetDataDone = (count, items = -1, dates = null) => {
    let empty = false;
    if (this.mSltDay && count == 0) {
      empty = true;
    }
    console.log("mGetDataDone",this.mSltDay,count,empty)

    this.setState({ showLoading: false, emtypDataDay: empty, items: items, emptyData: count < 1 });
    this.mScrollSwitch = true;
    if (this.mTodayIsClicked) {
      this.mTodayIsClicked = false;
      this.mEvLst.scrollTo({ animated: true,
        itemIndex: 0,
        viewOffset: 0,
        sectionIndex: 0 });
    }
    this.mLoader.getVideoDates().then((dates) => {
      if (!this.mSltDay && count > 0 && dates != null && dates.length > 0) {
        this.mDatesWithData = dates;
      }
    }).catch((err) => {
      console.log(`getVideoDates err: ${ err }`);
    });
  }
  downloadPressed = () => {
    if (this.mSelEv.length < 1) {
      Toast.success("bottom_action_tip");
      return;
    }
    this.checkStoragePerm()
      .then(() => {
        let fileIds = [];
        for (let i = 0; i < this.mSelEv.length; i++) {
          let selEv = this.mSelEv[i];
          if (selEv.mediaType === "sdcard" && selEv.subTimestamps != null) { // 针对sd卡做一点处理。
            for (let i = 0; i < selEv.subTimestamps.length; i++) {
              let timestamp = selEv.subTimestamps[i].startTime;
              let localUrls = this.mLoader.getThumbUrlByImgStoreId(timestamp);
              fileIds.push({ fileId: timestamp, mediaType: "sdcard", createTime: timestamp, imgStoreUrl: localUrls.thumbUrl, videoUrl: localUrls.videoUrl, playCfg: { loader: this.mLoader } });
            }
          } else {
            fileIds.push(selEv);
          }
        }

        this.setState({ showDownloadHint: true });
        setTimeout(() => {
          this.setState({ showDownloadHint: false });
        }, 5000);

        DldMgr.addDld(fileIds, this.mLoader);
        this.exitEdit();
      })
      .catch((err) => {
        Toast.success(err);
      });
    if (this.mSelEv.length > 0) {
      this.mSelEv[0].mediaType == 'sdcard' ? TrackUtil.reportClickEvent('Storage_MemoryCard_Download') : TrackUtil.reportClickEvent('Storage_CloudStorage_Download');
    }
  }


  deletePressed = () => {
    let selectedItems = [];
    for (let i = 0; i < this.mSelEv.length; i++) {
      let selEv = this.mSelEv[i];
      if (selEv.mediaType === "sdcard" && selEv.subTimestamps != null) { // 针对sd卡做一点处理。
        for (let i = 0; i < selEv.subTimestamps.length; i++) {
          let timestamp = selEv.subTimestamps[i].startTime;
          let save = selEv.subTimestamps[i].save;
          selectedItems.push({ startTime: timestamp, save: save });
        }
      } else {
        selectedItems.push(selEv);
      }
    }
    this.setState({ showLoading: true });
    this.mLoader.delete(selectedItems)
      .then((res) => {
        Toast.show("delete_success");
        this.mEvLst.removeEvents((aEv) => { return !aEv.selected; });
        this.mSelectAllCB(false);
        this.exitEdit();
        this.mEvLst.mOnEnd();
        this.setState({ showLoading: false });
      }).catch((aErr) => {
        console.log(this.tag, "del error", aErr);
        Toast.fail('delete_failed');
        this.setState({ showLoading: false });
      });
  }

  mScroll = (y) => {
    if (Platform.OS == "android" && this.mSltDay && this.mFirstTimeAfterSelectNewDate) {
      this.mFirstTimeAfterSelectNewDate = false;
      return;
    }
    this.mScrollSwitch = true;
    if (this.mSltDay && this.mScrollSwitch && !this.props.isEditing) {
      if (y < -85) {
        this.mScrollSwitch = false;
        this.mSwitchAllVideo();
      }
    }

  }

  mSwitchSltDayMore = () => {
    console.log("landing6 3");
    this.mSltDayMore = true;
    this.mEvLst.mSwitchSltDayMore(this.mSltDayMore);
  }

  mSelectAllCB = (allSelected) => {
    this.props.selectCB && this.props.selectCB((this.mSelEv.length === MaxSel) || allSelected, this.mSelEv.length);
  }

  mEvPressed = (aItm, aExtra = null, tapSelectAll = false) => {
    // console.log(this.tag, "mEvPressed", aItm);
    let ret = true;
    if (this.props.isEditing) {
      if (!aItm.selected) {
        if (MaxSel === this.mSelEv.length) {
          Toast.fail('max_select_noti');
          ret = false;
        } else {
          this.mSelEv.push(aItm);
          aItm.selected = true;
        }
      } else {
        this.mSelEv = this.mSelEv.filter((arg) => { return arg.fileId !== aItm.fileId; });
        aItm.selected = false;
      }
      
      let str = Util.fmtStr(LocalizedStrings["storage_sel"], this.mSelEv.length);
      this.props.startEdit({ title: str });
      console.log(this.tag, str);
      this.setState({});
    } else {
      // Stat.reportEvent(StatEV.ALL_VIDEO_PLAY_COUNT, null);

      if ("localUrl" in aItm) {
        this.naviTo("AlbumPhotoViewPage", { type: "relocate", url: aItm.localUrl, sltDate: this.mSltDay ? this.state.mDate : -1, items: this.mEvLst.getItemsFromEvents(), mCbDeleted: this.mCbDeleted, preOri: 'portrait' });
      } else if (aItm.mediaType == "sdcard") {
        aItm.extraData.deleteCb = (tag, hour) => {
          if (this.mEvLst != null) {
            this.mEvLst.removeEvents((itm) => {
              return (itm.extraData.tag != tag || itm.extraData.hour != hour);
            });
          }
        };
        this.naviTo("SdcardHourPage", aItm.extraData);
      } else {
        let items = aExtra ? aExtra.events : null;
        let nextDate = aExtra ? aExtra.nextDate : null;
        this.naviTo('AlarmVideoUI', {
          item: aItm, cfg: aItm.playCfg, event: 'Default',
          lstType: "cloud",
          items,
          loaderArgs: { startDate: this.state.mDate, filter: DefFilter, nextDate: nextDate, sltDay: this.mSltDay },
          dataFilter: this.mLoader.supportImage() ? (aDat) => { return aDat.mediaType === "video"; } : null,
          deleteCb: (aIds) => {
            if (this.mEvLst != null) {
              this.mEvLst.removeEvents((aItm) => { aIds.has(aItm.fileId); });
            }
          }
        });
        TrackUtil.reportClickEvent('CloudStorage_VideoList_ClickNum');
      }

    }
    return ret;
  }

  mCalEdt = () => {
    let isCloud = this.type == Tabs.Cloud ? true : false;
    let isSdcard = this.type == Tabs.Card ? true : false;
    // console.log('mcaledt', isCloud, Device.isReadonlyShared);
    if (this.type === Tabs.Card && !this.state.isLocalNetwork) {
      return null;
    }
    return (
      <View style={{ flexDirection: "row", justifyContent: "center" }}>
        <ImageButton
          style={[BaseStyles.icon45, { marginRight: 15 }]}
          source={Util.isDark() ? require('../../resources2/images/cld_icon_nor_w.png') : require('../../resources2/images/cld_icon_nor.png')}
          onPress={() => {
            this.mShowPopupView();
            
          }}
          accessibilityLabel={DescriptionConstants.lc_date}

        />
        {
          (this.state.emptyData || ((isCloud || isSdcard) && Device.isReadonlyShared)) ? null :
            <ImageButton style={BaseStyles.icon45} source={Util.isDark() ? require('../../resources2/images/icon_edit_storage_white.png') : require('../../resources2/images/icon_edit_storage_black.png')}
              onPress={this.mStartEdit}
              accessibilityLabel={DescriptionConstants.lc_edit}
            />
        }
      </View>
    );
  }

  mLocalHeader = () => {
    let dldCnt = this.mHeaderInfo.dldCnt;
    let dlding = dldCnt > 0;
    let arrowSrc = dlding ? require('../../resources2/images/icon_arrow_right.png') : require('../../resources2/images/icon_arrow_right_b.png');
    let dStr = this.addDateDes(dayjs(this.mSltDate), dayjs(this.mSltDate).format(LocalizedStrings["yyyymmdd"]));

    let aEptDateStr = this.mSltDay ? dStr : null;
    let mMarginBottom = this.mSltDay && !this.state.emtypDataDay ? 20 : 0;
    return (
      <View style={[BaseStyles.column, { paddingHorizontal: 20, justifyContent: "flex-start", alignItems: "flex-start", marginTop: this.state.marginTop, marginBottom: mMarginBottom }]}>
        <TouchableWithoutFeedback onPress={() => {
          this.naviTo("DldPage");
          TrackUtil.reportClickEvent('Storage_LocalAlbum_Management');
        }}>
          <View style={[BaseStyles.column, { backgroundColor: dldCnt > 0 ? "#32BAC0" : "#F6F7F8", alignItems: "flex-start", borderRadius: 16, height: 80, paddingHorizontal: 14 }]}>
            <Text style={[BaseStyles.text16, { color: dlding ? "#ffffff" : "black", marginTop: 21, fontWeight: "bold" }]}>
              {dlding ? LocalizedStrings["storage_local_dlding"] : LocalizedStrings["storage_local_dld_lst"]}</Text>
            <Text style={[BaseStyles.text12, { color: dlding ? "#ffffff" : "#7F7F7F", marginBottom: 21 }]}>
              {dlding ? Util.fmtStr(LocalizedStrings["storage_local_dld_stat"], dldCnt) : LocalizedStrings["storage_local_dld_empty"]}</Text>
            <Image style={{ position: "absolute", right: 23, top: 34, width: 12, height: 12 }}
              source={arrowSrc} />
          </View>
        </TouchableWithoutFeedback>

        {
          this.mSltDay ?
            (this.state.emtypDataDay ?
              <View style={[BaseStyles.row, { marginTop: 25 }]}>
                <Text style={[BaseStyles.text14, { fontWeight: 'bold', color: "#000000" }]}>{aEptDateStr}</Text>
                {this.mCalEdt()}
              </View> : null)
            :
            <View style={[BaseStyles.row, { marginTop: 25 }]}>
              <Text style={[BaseStyles.text22, { fontWeight: "bold" }]}>{LocalizedStrings["storage_local_dld"]}</Text>
              {this.mCalEdt()}
            </View>
        }
      </View>
    );
  }

  renderHighTemperatureView = () => {
    return (
      <View style={{ width: "100%", flex: 1, position: "relative", justifyContent: "center", alignItems: "center", backgroundColor: '#ffffff' }}>
        <Image
          style={{ alignSelf: "center", width: 92, height: 60 }}
          source={Util.isDark() ? require("../../Resources/Images/car/car_device_temperature_high_dark.png") : require("../../Resources/Images/car/car_device_temperature_high.png")} />

        <Text
          style={{ color: 'gray', textAlign: "center", paddingHorizontal: 40, marginTop: 10 }}
          numberOfLines={2}>{ LocalizedStrings['high_temperature_cannot_use'] }</Text>
      </View>
    )
  }

  renderLanDiffView = () => {
    return (
      <View style={{ width: "100%", flex: 1, position: "relative", justifyContent: "center", alignItems: "center", backgroundColor: '#ffffff' }}>
        <Image
          style={{ alignSelf: "center", width: 92, height: 60 }}
          source={Util.isDark() ? require("../../Resources/Images/car/car_device_lan_diff_dark.png") : require("../../Resources/Images/car/car_device_lan_diff.png")} />
        <Text
          style={{ textAlign: "center", paddingHorizontal: 40, marginTop: 10, color: "#00000066", fontSize: 14}}
          numberOfLines={2}>{ LocalizedStrings['phone_lan_is_different'] }</Text>
        <Text
          style={{ textAlign: "center", paddingHorizontal: 40, marginTop: 10, color: "#0000004D", fontSize: 12 }}
          numberOfLines={2}>{ LocalizedStrings['lan_is_different'] }</Text>
      </View>
    )
  }
  // 存储卡的全部视频
  mSdcardHeader = () => {
    let spaceDesc = "";
    let statusDesc = "";

    let arrowSrc = require('../../resources2/images/icon_arrow_right_b.png');
    let leftSpacePercent = 0;
    let holdSpacePercent = 0;
    let showSpaceProgress = false;
    if (this.mHeaderInfo.totalSize && (this.mHeaderInfo.sdcardCode == 2 || this.mHeaderInfo.sdcardCode == 0)) {
      holdSpacePercent = Number.parseInt(this.mHeaderInfo.videoSize * 100 / this.mHeaderInfo.totalSize);
      leftSpacePercent = 100 - holdSpacePercent;
      showSpaceProgress = true;
      let usedSize = Util._formatSize(this.mHeaderInfo.videoSize);
      let totalSize = Util._formatSize(this.mHeaderInfo.totalSize);
      spaceDesc = LocalizedStrings["sdcard_status_capcity"].replace("%1$d", usedSize).replace("%2$d", totalSize);
    }
    let sdcardCode = this.mHeaderInfo.sdcardCode;
    let showEditView = true;
    if (sdcardCode == 3 || sdcardCode == 4) {
      statusDesc = LocalizedStrings["sdcard_status_error"];
      showEditView = false;
    } else if (sdcardCode == CameraPlayer.SD_CARD_NEED_FORMAT_CODE) {
      statusDesc = LocalizedStrings["sdcard_status_need_format"];
    } else {
      statusDesc = LocalizedStrings["sdcard_status_normal"];
    }
    if (CameraConfig.supportSDCardV2(Device.model)) {
      let usedSize = Util._formatSize(this.mHeaderInfo.videoSize);
      let totalSize = Util._formatSize(this.mHeaderInfo.totalSize);
      showSpaceProgress = true;
      statusDesc = `${ LocalizedStrings['sds_status'] }:${ LocalizedStrings[`sds_status_${ sdcardCode }`] }`;
      spaceDesc = sdcardCode == 4 || sdcardCode == 0 && this.mHeaderInfo.totalSize != null && this.mHeaderInfo.totalSize != 0 
        ? this.mHeaderInfo.duration && this.mHeaderInfo.duration > 0 ? `${ LocalizedStrings["sdcard_status_capcity"].replace("%1$d", usedSize).replace("%2$d", totalSize) }，${ LocalizedStrings["sdcard_record_duration"].replace("%1$s", this.mHeaderInfo.duration) }` : LocalizedStrings["sdcard_status_capcity"].replace("%1$d", usedSize).replace("%2$d", totalSize)
        : sdcardCode == 3 || sdcardCode == 2 || sdcardCode == CameraPlayer.SD_CARD_NEED_FORMAT_CODE || sdcardCode == CameraPlayer.SD_CARD_FILE_ERROR_CODE
          ? LocalizedStrings["sds_try_format"]
          : LocalizedStrings[`sds_try_action_${ sdcardCode == CameraPlayer.SD_CARD_INCOMPATIBLE_CODE 
            ? CameraPlayer.SD_CARD_TOO_SMALL_CODE : sdcardCode }`];

    }
    if (this.mSltDay && !this.state.emtypDataDay) {
      showEditView = false;
    }

    let dStr = this.addDateDes(dayjs(this.mSltDate), dayjs(this.mSltDate).format(LocalizedStrings["yyyymmdd"]));

    let aEptDateStr = this.mSltDay ? dStr : null;
    let mMarginBottom = this.mSltDay && !this.state.emtypDataDay ? 20 : 0;
    return (
      <View style={[BaseStyles.column, { paddingHorizontal: 20, justifyContent: "flex-start", alignItems: "flex-start", marginTop: this.state.marginTop, marginBottom: mMarginBottom }]}>
        <TouchableWithoutFeedback
          onPress={() => {
            if (!CameraConfig.debugSpecialSupport()) {
              return;
            }
            if (Device.isReadonlyShared) {
              Toast.fail('share_permission_cannot_control');
              return false;
            }
            this.naviTo("SDCardSetting");
            TrackUtil.reportClickEvent('Storage_MemoryCard_Management');
          }}
        >
          <View style={[BaseStyles.row, { backgroundColor: Util.isDark() ? "#262626" : "#F6F7F8", alignItems: "center", borderRadius: 16, minHeight: 90, paddingHorizontal: 12, paddingVertical: 17 }]}>
            <View
              style={{ flex: 1, justifyContent: "center" }}
            >
              <View style={{justifyContent: "space-between", flexDirection: "row", alignItems: "center"}}>
                <Text style={[BaseStyles.text16, { color: "black", fontWeight: "bold" }]}>{statusDesc}</Text>
                {/*<Image style={{ width: 12, height: 12, position: "relative", top: 10}} source={arrowSrc} />*/}
              </View>
              <View style={{ width: "100%" }}>
                <Text style={[BaseStyles.text12, { color: "#7F7F7F", marginTop: 5 }, !showSpaceProgress ? { display: "none" } : null]}>{spaceDesc}</Text>
                <View style={[{ width: "100%", position: "relative", flexDirection: "row", height: 6, borderRadius: 3, marginTop: 10, backgroundColor: "#EEEEEE" }, !showSpaceProgress ? { display: "none" } : null]}>
                  <View style={[{ flexGrow: holdSpacePercent, borderRadius: 3, height: 6, backgroundColor: "#32BAC0" }, !showSpaceProgress ? { display: "none" } : null]}>
                  </View>
                  <View style={{ flexGrow: leftSpacePercent, height: 6 }}>
                  </View>
                </View>
              </View>
            </View>

          </View>
        </TouchableWithoutFeedback>
        {
          showEditView ?
            (this.mSltDay ?
              (this.state.emtypDataDay ?
                <View style={[BaseStyles.row, { marginTop: 25 }]}>
                  <Text style={[BaseStyles.text14, { fontWeight: 'bold', color: "#000000" }]}>{aEptDateStr}</Text>
                  {this.mCalEdt()}
                </View> : null)
              :
              <View style={[BaseStyles.row, { marginTop: 25 }]}>
                <Text style={[BaseStyles.text22, { fontWeight: "bold" }]}>{LocalizedStrings["f_all"]}</Text>
                {this.mCalEdt()}
              </View>)
            :
            null
        }



      </View>
    );
  }


  mCloudHeader = () => {
    let mMarginBottom = this.mSltDay && !this.state.emtypDataDay ? 20 : 0;
    let dStr = this.addDateDes(dayjs(this.mSltDate), dayjs(this.mSltDate).format(LocalizedStrings["yyyymmdd"]));

    let aEptDateStr = this.mSltDay ? dStr : null;
    return (
      <View style={[BaseStyles.column, { paddingHorizontal: 20, justifyContent: "flex-start", alignItems: "flex-start", marginTop: this.state.marginTop, marginBottom: mMarginBottom }]}>
        <TouchableWithoutFeedback
          onPress={() => {
            if (Device.isOwner) {
              if (this.mHeaderInfo.vip || this.mHeaderInfo.showCloudForExpired) {
                Service.miotcamera.showCloudStorageSetting();
              } else {
                API_LEVEL > 10068 ? Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: 'stroagemgt_button' }) : Service.miotcamera.showCloudStorageSetting();
                CameraConfig.isToUpdateVipStatue = true;
              }
            } else {
              Toast.success("share_user_permission_hint");
            }
            TrackUtil.reportClickEvent('Storage_CloudStorage_Management');
          }}
        >
          <View style={[BaseStyles.column, { backgroundColor: "#D6EEEF", alignItems: "flex-start", justifyContent: "center", borderRadius: 16, height: 80, paddingHorizontal: 14 }]}>
            <Text style={[BaseStyles.text16, { color: "#32BAC0", fontWeight: "bold", width: "85%" }]}>{this.mHeaderInfo.type /* LocalizedStrings["storage_state_normal"] */}</Text>
            <Text style={[BaseStyles.text12, { color: "#32BAC0", width: "85%" }]}>{this.mHeaderInfo.detail}</Text>
            <Image style={{ position: "absolute", right: 23, top: 34, width: 12, height: 12 }}
              source={require('../../resources2/images/icon_arrow_right_g_nor.png')} />

          </View>
        </TouchableWithoutFeedback>

        {
          this.mSltDay ?
            (this.state.emtypDataDay ?
              <View style={[BaseStyles.row, { marginTop: 25 }]}>
                <Text style={[BaseStyles.text14, { fontWeight: 'bold', color: "#000000" }]}>{aEptDateStr}</Text>
                {this.mCalEdt()}
              </View> : null)
            :
            <View style={[BaseStyles.row, { marginTop: 25 }]}>
              <Text style={[BaseStyles.text22, { fontWeight: "bold" }]}>{LocalizedStrings["f_all"]}</Text>
              {this.mCalEdt()}
            </View>
        }
      </View>
    );
  }

  mStartEdit = () => {
    console.log(this.tag, "start edit");
    if (this.state.emptyData) {
      return;
    }
    if (!this.props.isEditing) {
      this.mSelectAllCB(false);
      this.props.startEdit({ title: LocalizedStrings["storage_sel_init"] });
    } else {
      this.exitEdit();
    }
  }

  mShowPopupView = () => {
    this.coverLayer.showWithContent(
      () => {
        // let nowDate = new Date();
        console.log('calendar input: ', this.state.mDate,Dimensions.get("window").height,Dimensions.get("window").width);
        let mWidth = Math.min(Dimensions.get("window").height, Dimensions.get("window").width);
        let mHeight = Math.max(Dimensions.get("window").height, Dimensions.get("window").width);
        let height = 530;
        if (mHeight < 530) {
          height = mHeight - 30;
        }
        return (
          <View style={{ height: height, width: mWidth }}>
            <Calendar
              ref={(hdl) => this.mCalendar = hdl}
              visible={this.showCalendar}
              y={this.state.mDate.getFullYear()}
              m={this.state.mDate.getMonth() + 1}
              d={this.state.mDate.getDate()}
              interval={this.state.rollingInterval}
              width={mWidth}
              onDateChanged={this.mSwitchOneDay}
              onCancel={() => { this.coverLayer.hide(); }}
              onAllVideo={this.mSwitchAllVideo}
              dates={this.props.type != Tabs.Cloud ? (this.mDatesWithData ? this.mDatesWithData : []) : null}
            >
            </Calendar>
          </View>
        );
      },
      () => this.coverLayer.hide(),
      CoverLayer.popupMode.bottom
    );
  }

  mSwitchAllVideo = () => {
    console.log("get selected");
    this.mTodayIsClicked = true;
    this.mFirstTimeAfterSelectNewDate = true;
    let sltDate = new Date();
    this.mSltDay = false;
    this.mSltDayMore = false;
    this.mEvLst.mSwitchSltDayMore(this.mSltDayMore);
    this.mSltDate = sltDate;
    this.mEvLst.switchDay(sltDate, this.mSltDay);// remove all exist and refresh
    this.setState({ mDate: sltDate, showLoading: true });
    this.coverLayer.hide();
    this.mEvLst.scrollTo({ animated: true,
      itemIndex: 0,
      viewOffset: 0,
      sectionIndex: 0 });
  }
  mSwitchOneDay = (items) => {
    console.log(`get selected: ${ items }`);
    this.mEvLst.scrollTo({ animated: true,
      itemIndex: 0,
      viewOffset: 0,
      sectionIndex: 0 });
    this.mFirstTimeAfterSelectNewDate = true;
    let sltDate = new Date(Date.parse(`${ items[0] }/${ items[1] }/${ items[4] }`));
    this.mSltDay = true;
    this.mSltDayMore = false;
    this.mEvLst.mSwitchSltDayMore(this.mSltDayMore);
    this.mSltDate = sltDate;
    this.mCalendar.setDate(sltDate);
    if (this.type === 1) {
      this.localSelectDays = true;
    }
    this.setState({ mDate: sltDate, showLoading: true });
    this.mEvLst.switchDay(sltDate, this.mSltDay, this.localSelectDays);// remove all exist and refresh
    this.coverLayer.hide();

  }

  _renderDownloadHint() {
    if (!this.state.showDownloadHint) {
      return null;
    }
    return (
      <TouchableOpacity style={{ position: "absolute", minHeight: 40, bottom: 0, width: "100%", backgroundColor: "#32BAC0", display: "flex", justifyContent: "center", alignItems: "center" }}
        onPress={() => {
          this.props.navigation.navigate("DldPage");
        }}
      >
        <Text
          style={[BaseStyles.text13, { color: "#ffffff", paddingHorizontal: 10, textAlign: 'center', textAlignVertical: 'center' }]}
        >
          {LocalizedStrings["download_hint"]}
        </Text>
      </TouchableOpacity>
    );
  }

  renderReloadView() {
    return (
      <View style={{ flex: 1, flexDirection: 'column', width: '100%', height: '100%', alignItems: 'center' }}>
        <Image style={{ width: 138, height: 138, marginTop: 150 }}
          source={Util.isDark() ? require('../../Resources/Images/icon_empty_load_fail_w.png') : require('../../Resources/Images/icon_empty_load_fail.png')}></Image>

        <Text style={[BaseStyles.text18, { color: '#999999', fontWeight: '400', width: '100%', textAlign: 'center', paddingHorizontal: 20 }]}> {LocalizedStrings['loading_failed_try_again']}</Text>

        <View style={{ position: 'absolute', width: '80%', height: 46, borderRadius: 23, bottom: 20, backgroundColor: '#32BAC0' }}>
          < TouchableOpacity style={{ width: '100%', height: 46, justifyContent: 'center', alignItems: 'center' }}
            onPress={() => { // cancel
              if (this.mEvLst) {
                VipUtil.getVipStatus().then(({ isVip }) => { // 刷新时确定是否拥有VIP
                  this.setState({ isVip: isVip }, () => {
                    this.mEvLst.mRefresh();
                  });
                }).catch((err) => {
                  console.log("get vip err", err);
                });
              } else if (!this.netStatus) {
                console.log('请检查网络');
              }
            }}>
            < Text style={[BaseStyles.text17, { color: '#E6E6E6', fontWeight: '500' }]} > {LocalizedStrings['load_again']} </ Text >
          </ TouchableOpacity >
        </View>
      </View>
    );
  }

  renderCloudBuyView() {
    if (this.state.isVip === false) {
    return (
      <View style={{ flex: 1, flexDirection: 'column', width: '100%', height: '100%', alignItems: 'center' }}>
        <Image style={{ width: 138, height: 138, marginTop: 150 }}
          source={Util.isDark() ? require('../../Resources/Images/icon_empty_load_fail_w.png') : require('../../Resources/Images/icon_empty_load_fail.png')}></Image>

        <Text
          style={
            [BaseStyles.text22,
            {
              color: '#999',
              fontSize: 16,
              width: '100%',
              textAlign: 'center',
              paddingHorizontal: 20
            }]}
          numberOfLines={3}
        >
          {this.isCloudServer ? LocalizedStrings['eu_tip_not_buy_cloud'] : LocalizedStrings['tip_not_buy_cloud']}
        </Text>

        <View style={{ position: 'absolute', width: '80%', height: 46, borderRadius: 23, bottom: 20, backgroundColor: '#32BAC0' }}>
          < TouchableOpacity style={{ width: '100%', height: 46, justifyContent: 'center', alignItems: 'center' }}
            onPress={() => { // cancel
              console.log('click_buy_cloud clicked, Storage_CloudStorage_Purchase_ClickNum');
              TrackUtil.reportClickEvent("Storage_CloudStorage_Purchase_ClickNum"); // Storage_CloudStorage_Purchase_ClickNum
              API_LEVEL > 10068 ? Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: 'stroagemgt_button' }) : Service.miotcamera.showCloudStorage(true, true);
              CameraConfig.isToUpdateVipStatue = true;
            }}>
            < Text style={[BaseStyles.text17, { color: '#ffffff', fontWeight: '500' }]} > {this.isCloudServer?LocalizedStrings['eu_click_buy_cloud']:LocalizedStrings['click_buy_cloud']} </ Text >
          </ TouchableOpacity >
        </View>
      </View>
      );
    }
  }

  renderCloudBuyViewNew() {

    let item1 = {
      source: require("../../Resources/Images/cloud_intro_part3.png"),
      title: LocalizedStrings["cloud_banner_storage_title1"],
      text: LocalizedStrings["cloud_banner_storage_text11"]
    };
    let item2 = {
      source: require("../../Resources/Images/cloud_intro_part2.png"),
      title: LocalizedStrings["cloud_banner_storage_title2"],
      text: LocalizedStrings["cloud_banner_storage_text21"]
    };
    let item3 = {
      source: require("../../Resources/Images/cloud_intro_part4.png"),
      title: LocalizedStrings["cloud_banner_storage_title3"],
      text: LocalizedStrings["cloud_banner_storage_text31"]
    };
    let stateCover = {
      // position: 'absolute',
      width: '100%',
      // height: '100%',
      display: "flex",
      // alignItems: "flex-start",
      // justifyContent: 'space-evenly',
      marginTop: (this.isEuropeServer ? 30 : 55) * kPxScale,
      marginLeft: 10
    };
    let stateCoverTitle = {
      textAlign: "left",
      textAlignVertical: "center",
      marginLeft: 20
    };
    let isIOS = Platform.OS === 'ios';
    let isLanCN = Util.isLanguageCN();
    let textWidth = isLanCN ? 160 : 250;
    let titleFontsize = this.isEuropeServer ? 31 * kPxScale : isLanCN ? 33 : 31; // 海外都是英文xiaomi home secure, 所以都随尺寸变化
    let titleLineHeight = this.isEuropeServer ? 37 * kPxScale : 37;
    return (
      <SafeAreaView style={{ flex: 1, flexDirection: 'column', width: '100%', height: '100%', alignItems: "center", backgroundColor: "xm#F0F0F0" }}>
        <View style={{ width: '100%', height: '35%', marginTop: 0 }}>
          {/* <Image source={titleBkImg} style={{ display: "flex", flex: 1, width: "100%", height: "100%" }} resizeMode="stretch" /> */}
          <Image source={{ uri: 'https://cdn.cnbj0.fds.api.mi-img.com/miio.files/resource_package/20240117101102_mj-std-camera-cloud_intro_title_bk_cloud_intro_title_bk.png' }} style={{ display: "flex", flex: 1, width: "100%", height: "100%" }} resizeMode="stretch" />

        </View>
        <View style={{position: 'absolute', left: 0, width: '100%', height: '100%' }}>
          <View style={stateCover}>
            {/* 上面的存储暂停 */}
            <View style={{  width: textWidth, backgroundColor: "#00000000", paddingBottom: 30 }}>
              <View style={{ marginLeft: isIOS ? 0 : (isLanCN ? -5 : -1), width: this.isEuropeServer ? 160 : (isLanCN ? 170 : 220) }}>
                <Text
                  style={[stateCoverTitle, { fontSize: titleFontsize, color: "#283537", fontWeight: 'bold', marginBottom: 10, letterSpacing: isLanCN ? 3 : 1, lineHeight: titleLineHeight }]}>
                  {this.isEuropeServer ? LocalizedStrings["eu_cloud_seting"] : LocalizedStrings["s_cloud_setting"]}
                </Text>
              </View>

              <Text numberOfLines={3}
                    style={[BaseStyles.text17, stateCoverTitle, { color: "xm#000000CC", marginBottom: 5, letterSpacing: isLanCN ? 2 : 1, fontWeight: "300", lineHeight: 22 }]}>
                {LocalizedStrings["cloud_banner_storage_text1"]}
              </Text>

            </View>
          </View>
          <ScrollView style={{ display: "flex", width: "100%", height: "100%", flexGrow: 1 }}>
            <View style={{ alignItems: 'center' }}>
              {[item1, item2, item3].map((item, idx) => this._renderBuyViewItem(item, idx))}
            </View>
            { Platform.OS == 'android' ? <View style={{ height: 300 }}></View> : <View style={{ height: 100 }}></View> }
          </ScrollView>
        </View>


        <View style={{ position: 'absolute', width: '86%', height: 46, borderRadius: 23, bottom: 20, backgroundColor: '#32BAC0', marginBottom: Util.getBottomMarginWithoutSafeArea() }}>
          < TouchableOpacity style={{ width: '100%', height: 46, justifyContent: 'center', alignItems: 'center' }}
                             onPress={() => { // cancel
                               if (!Device.isOwner) {
                                 Toast.success("share_user_permission_hint");
                                 return;
                               }
                               // super.statView(true); // 携带duration
                               console.log('click_buy_cloud clicked, Storage_CloudStorage_Purchase_ClickNum');
                               TrackUtil.reportClickEvent("Storage_CloudStorage_Purchase_ClickNum"); // Storage_CloudStorage_Purchase_ClickNum
                               TrackUtil.newOneTrack(['click'], { item_name: 'cloud_new_link_button', item_type: 'button' });
                               Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: "stroagemgt_button_v1" });
                               CameraConfig.isToUpdateVipStatue = true;
                             }}>
            < Text style={[BaseStyles.text17, { color: Util.isDark() ? 'xm#ffffffe7' : '#ffffff', fontWeight: '500' }]} > {this.isEuropeServer ? LocalizedStrings['eu_click_buy_cloud'] : LocalizedStrings['cloud_banner_buy_button_title']} </ Text >
          </ TouchableOpacity >
        </View>
      </SafeAreaView>
    );

    // 此为购买页
    // if (this.setRefDone !== true) {
    //   this.setRefDone = true;
    //   this.setRefDoneTimer = setTimeout(() => {
    //     // TrackUtil.setRefInfo('camera_storage_management', 'cloud_tab', true);
    //   }, 100);
    // }
    // return (<CloudPurchaseView
    //   style={{ flex: 1, flexDirection: 'column', width: '100%', height: '100%', alignItems: "center" }}
    //   urlExtraPayload={{did: Device.deviceID, channel: "storage-manage", bind_relation: Device.isOwner ? "0" : "1", userType: VipUtil.getUserType(), isrn: "1"}}
    //   onPurchaseComplete={() => {
    //     // here switch to other page
    //     // Toast.success("cloud_purchase_complete");
    //     LogUtil.logOnAll("CloudPurchaseView onPurchaseComplete");
    //     if (!CameraConfig.getInternationalServerStatus()) {
    //       this.setState({ showBuyDoneDialog: true });
    //     }
    //     this.refreshHeader(); // 刷新UI
    //   }}
    // />);
  }

  _renderBuyViewItem(item, idx) {
    let panelOptionItemLayout = {
      display: "flex",
      position: "relative",
      width: "86%",
      height: Util.isLanguageCN() ? 110 : 155,
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 12,
      paddingLeft: 30,
      paddingRight: 22,
      borderRadius: 16,
      backgroundColor: "xm#FFFFFF"
    };
    return (
      <View key={idx}>
        <View
          style={panelOptionItemLayout}
        >
          <Image
            style={{ width: 40, height: 40, position: "relative" }}
            source={item.source}
          />

          <View style={{ display: "flex", height: "100%", flex: 1, position: "relative", flexDirection: "column", justifyContent: "center" }}>
            <Text numberOfLines={5}
                  style={[BaseStyles.text16, { marginLeft: 15, marginTop: 0, color: "xm#000000", fontWeight: "bold", lineHeight: 20 }]}
            >
              {item.title}
            </Text>

            <Text
              style={[BaseStyles.text13, { marginLeft: 15, marginTop: 4, color: "#999999", fontWeight: "400", lineHeight: 16 }]}
              numberOfLines={5}
              ellipsizeMode={"tail"}
            >
              {item.text}
            </Text>
          </View>
        </View>
      </View>
    );
  }

  renderGlobalLoading() {
    return (
      <View style={{ zIndex: 4, position: "absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center", backgroundColor: "#00000011" }}>
        <LoadingView style={{ width: 80, height: 80 }}/>
      </View>
    );
  }

  renderUpgradeView() {
    return (
      <View style={{ flex: 1, flexDirection: 'column', width: '100%', height: '100%', alignItems: 'center' }}>
        <Image style={{ width: 138, height: 138, marginTop: 150 }}
          source={Util.isDark() ? require('../../Resources/Images/icon_empty_load_fail_w.png') : require('../../Resources/Images/icon_empty_load_fail.png')}></Image>

        <Text style = {[BaseStyles.text22, { color: '#000000', fontWeight: '500', width: '100%', height: 30, textAlign: 'center', paddingHorizontal: 20 }]}> {LocalizedStrings['tip_not_support_cloud']}</Text>

        <View style={{ position: 'absolute', width: '80%', height: 46, borderRadius: 23, bottom: 20, backgroundColor: '#32BAC0' }}>
          < TouchableOpacity style={{ width: '100%', height: 46, justifyContent: 'center', alignItems: 'center' }}
            onPress={() => { // cancel
              console.log('click_update_firmware clicked');
              Host.ui.openDeviceUpgradePage();
            } }>
            < Text style= {[BaseStyles.text17, { color: '#E6E6E6', fontWeight: '500' }]} > {LocalizedStrings['click_upgrade_firmware']} </ Text >
          </ TouchableOpacity >
        </View>
      </View>
    );
  }

  renderCloudNoPermissionView() {
    return (
      <View style={{ flex: 1, flexDirection: 'column', width: '100%', height: '100%', alignItems: 'center' }}>
        <Image style={{ width: 138, height: 138, marginTop: 150 }}
          source={Util.isDark() ? require('../../Resources/Images/icon_empty_load_fail_w.png') : require('../../Resources/Images/icon_empty_load_fail.png')}></Image>

        <Text style={[BaseStyles.text22, { color: '#999', fontSize: 16, width: '100%', textAlign: 'center', paddingHorizontal: 20, numberOfLines: 3 }]}>{LocalizedStrings['share_user_permission_hint']}</Text>
      </View>
    );
  }

  renderLoadingView() {
    let isDark = DarkMode.getColorScheme() == "dark";
    return (
      <View style={{ position: "absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center", zIndex:9}}>
        <ActivityIndicator
          style={{ width: 54, height: 54 }}
          color={isDark ? "xm#ffffff" : "xm#000000"}
          size={"large"}
        />
        <Text style={[BaseStyles.text12, { marginTop: 10, color: "#000000" }]}>
          {LocalizedStrings["camera_loading"]}
        </Text>
      </View>
    );
  }

  // delete from albumphotoview
  mCbDeleted = (url) => {
    this.mEvLst.removeEvents((aItm) => aItm.url == url );
  }

  addDateDes(date, dStr) {
    if (Util.isToday(date)) {
      dStr = `${ dStr } | ${ LocalizedStrings['today'] }`;
    } else if (Util.isYestoday(date)) {
      dStr = `${ dStr } | ${ LocalizedStrings['yestoday'] }`;
    }
    return dStr;
  }
}
