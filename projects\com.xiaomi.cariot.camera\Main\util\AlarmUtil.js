import { Service, Device } from "miot";
import CameraConfig from "./CameraConfig";
import VersionUtil from '../util/VersionUtil';
import API from '../API';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import Toast from "../components/Toast";
import LogUtil from "./LogUtil";

export const MOTION_DETECTION_SIID = 5;
export const MOTION_DETECTION_PIID = 1;
export const ALARM_INTERVAL_PIID = 2;
export const DETECTION_SENSITIVITY_PIID = 3;
export const START_TIME_PIID = 4;
export const END_TIME_PIID = 5;
export const MOTION_PERIOD_PIID = 6;

export const CHUANGMI_AI_SIID = 7;
export const CHUANGMI_CLOCKS = 8;
export const CHUANGMI_FACE_SWITCH_022 = 1;
export const CHUANGMI_PET_SWITCH_022 = 3;
export const CHUANGMI_AI_FRAME_022 = 4; // 051  7.4 是手势识别开关
const LONG_TIME_ALARM_SPEC_PARAMS = [
  { did: Device.deviceID, siid: CHUANGMI_AI_SIID, piid: CHUANGMI_CLOCKS }
];
const AI_SWITCH_022_SPEC_PARAMS = [
  { did: Device.deviceID, siid: CHUANGMI_AI_SIID, piid: CHUANGMI_FACE_SWITCH_022 },
  { did: Device.deviceID, siid: CHUANGMI_AI_SIID, piid: CHUANGMI_PET_SWITCH_022 }
];
const AI_FRAME_022_SPEC_PARAMS = [
  { did: Device.deviceID, siid: CHUANGMI_AI_SIID, piid: CHUANGMI_AI_FRAME_022 }
];
const SPEC_BABY_SLEEP_PARAMS = [
  { did: Device.deviceID, siid: CHUANGMI_AI_SIID, piid: 6 },
  { did: Device.deviceID, siid: CHUANGMI_AI_SIID, piid: 7 }
];

const MOTION_DETECTION_SEPC_PARAMS = [
  { did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: MOTION_DETECTION_PIID },
  { did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: ALARM_INTERVAL_PIID },
  { did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: DETECTION_SENSITIVITY_PIID },
  { did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: START_TIME_PIID },
  { did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: END_TIME_PIID },
  { did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: MOTION_PERIOD_PIID }
];

let ai_scene_siid = 8;
if (Device.model == "chuangmi.camera.ipc022") {
  ai_scene_siid = 9;
}
export const CHUANGMI_AI2_AI_SCENE_SIID = ai_scene_siid;
export const CHUANGMI_AI2_AI_FAV_AREA = 22;
export const CHUANGMI_AI2_FAV_AREA_PIID = 1;
export const CHUANGMI_AI2_ACTIVE_FAV_AREA_PIID = 2;
const PRE_POSITIONS_SPEC_PARAMS = [
  { did: Device.deviceID, siid: CHUANGMI_AI2_AI_FAV_AREA, piid: CHUANGMI_AI2_FAV_AREA_PIID }
];
const ACTIVE_PRE_POSITIONS_SPEC_PARAMS = [
  { did: Device.deviceID, siid: CHUANGMI_AI2_AI_FAV_AREA, piid: CHUANGMI_AI2_ACTIVE_FAV_AREA_PIID }
];
export const CHUANGMI_AI2_AI_CRUISE_SIID = 10;
export const CHUANGMI_AI2_CRUISE_SWITCH_PIID = 1;
export const CHUANGMI_AI2_CRUISE_CONFIG_PIID = 2;
const CRUISE_SWITCH_SPEC_PARAMS = [
  { did: Device.deviceID, siid: CHUANGMI_AI2_AI_CRUISE_SIID, piid: CHUANGMI_AI2_CRUISE_SWITCH_PIID }
];
const CRUISE_CONFIG_SPEC_PARAMS = [
  { did: Device.deviceID, siid: CHUANGMI_AI2_AI_CRUISE_SIID, piid: CHUANGMI_AI2_CRUISE_CONFIG_PIID }
];

export const CHUANGMI_AI2_ONE_KEY_CALL_SIID = 12;
export const CHUANGMI_AI2_ONE_KEY_CALL_SWITCH_PIID = 1;
export const CHUANGMI_AI2_ONE_KEY_CALL_STATUS_PIID = 2;
const ONE_KEY_CALL_SWITCH_PARAMS = [
  { did: Device.deviceID, siid: CHUANGMI_AI2_ONE_KEY_CALL_SIID, piid: CHUANGMI_AI2_ONE_KEY_CALL_SWITCH_PIID }
];
const ONE_KEY_CALL_STATUS_PARAMS = [
  { did: Device.deviceID, siid: CHUANGMI_AI2_ONE_KEY_CALL_SIID, piid: CHUANGMI_AI2_ONE_KEY_CALL_STATUS_PIID }
];
export const CHUANGMI_AI2_SMART_CARE_SIID = 11; // 智能看护
export const CHUANGMI_AI2_SMART_CARE_SWITCH_PIID = 1;
export const CHUANGMI_AI2_SMART_CARE_AREA_PIID = 2;
export const CHUANGMI_AI2_SMART_CARE_CONFIG_PIID = 3;
export const CHUANGMI_AI2_SMART_CARE_CLOCK_KEY_PIID = 4; // 无人出现告警推送key用于服务器

const SMART_CARE_PARAMS = [
  { did: Device.deviceID, siid: CHUANGMI_AI2_SMART_CARE_SIID, piid: CHUANGMI_AI2_SMART_CARE_SWITCH_PIID },
  { did: Device.deviceID, siid: CHUANGMI_AI2_SMART_CARE_SIID, piid: CHUANGMI_AI2_SMART_CARE_AREA_PIID },
  { did: Device.deviceID, siid: CHUANGMI_AI2_SMART_CARE_SIID, piid: CHUANGMI_AI2_SMART_CARE_CONFIG_PIID }
];
export const CAMERA_SD_STATUS_SIID = 16; // 
export const CAMERA_SD_STATUS_EJECTED = 1; // 记录SD卡是否被弹出
export const CAMERA_SD_CARD_RECORD_DURATION = 2; // 记录SD卡可录制时长
const SD_STATUS_EJECTED_PARAMS = [
  { did: Device.deviceID, siid: CAMERA_SD_STATUS_SIID, piid: CAMERA_SD_STATUS_EJECTED }
];
const SD_CARD_RECORD_DURATION = [
  { did: Device.deviceID, siid: CAMERA_SD_STATUS_SIID, piid: CAMERA_SD_CARD_RECORD_DURATION }
];

export const SPEC_SIID_KEY_SPEAKER = "speaker";
export const SPEC_PIID_KEY_VOLUME = "volume";

export default class AlarmUtil {
  static _lastSpecAlarmConfig = null;
  static _lastSpecAlarmTimestamp = 0;
  static cruiseConfigure = [];

  static getSpecByKey(skey, pkey) {
    return Service.spec.getSpecByKey(Device.deviceID, { mkey: null, skey: skey, pkey: pkey });
  }

  static getSpeakerVolume(datasource = 2) {
    return this.getSpecByKey(SPEC_SIID_KEY_SPEAKER, SPEC_PIID_KEY_VOLUME).then((res) => {
      console.log("getSpeakerVolume getSpecByKey ", res[0].siid, " piid=", res[0].piid);
      return Service.spec.getPropertiesValue([{ did: Device.deviceID, siid: res[0].siid, piid: res[0].piid }], datasource);
    }).catch((err) => {
      console.log("getSpeakerVolume getSpecByKey err=", JSON.stringify(err));
      return new Promise((resolve, reject) => {
        reject(err);
      });
    });
  }
  static setSpeakerVolume(value) {
    return this.getSpecByKey(SPEC_SIID_KEY_SPEAKER, SPEC_PIID_KEY_VOLUME).then((res) => {
      console.log("setSpeakerVolume getSpecByKey ", res[0].siid, " piid=", res[0].piid);
      let params = [
        Object.assign({ did: Device.deviceID, siid: res[0].siid, piid: res[0].piid }, { value: value })
      ];
      return Service.spec.setPropertiesValue(params);
    }).catch((err) => {
      console.log("setSpeakerVolume getSpecByKey err=", JSON.stringify(err));
      return new Promise((resolve, reject) => {
        reject(err);
      });
    });
  }

  static getSmartCareConfig(datasource = 2) {
    return Service.spec.getPropertiesValue([SMART_CARE_PARAMS[2]], datasource);
  }

  static getOneKeyCallStatus(datasource = 2) {
    return Service.spec.getPropertiesValue(ONE_KEY_CALL_STATUS_PARAMS, datasource);
  }
  static putOneKeyCallStatus(value) {
    let params = [
      Object.assign(ONE_KEY_CALL_STATUS_PARAMS[0], { value: value })
    ];
    return Service.spec.setPropertiesValue(params);
  }
  static getOneKeyCallSwitch(datasource = 2) {
    return Service.spec.getPropertiesValue(ONE_KEY_CALL_SWITCH_PARAMS, datasource)
      .then((res) => {
        return new Promise((resolve, reject) => {
          resolve(res);
        });
      }).catch((err) => {
        console.error(err);
      });
  }
  static putOneKeyCallSwitch(value) {
    let params = [
      Object.assign(ONE_KEY_CALL_SWITCH_PARAMS[0], { value: value })
    ];
    return Service.spec.setPropertiesValue(params);
  }

  static getCruiseSwitch(datasource = 2) {
    return Service.spec.getPropertiesValue(CRUISE_SWITCH_SPEC_PARAMS, datasource)
      .then((res) => {
        return new Promise((resolve, reject) => {
          resolve(res);
        });
      }).catch((err) => {
        console.error(err);
      });
  }
  static putCruiseSwitch(value) {
    let params = [
      Object.assign(CRUISE_SWITCH_SPEC_PARAMS[0], { value: value })
    ];
    return Service.spec.setPropertiesValue(params);
  }
  static getCruiseConfig(datasource = 2) {
    return Service.spec.getPropertiesValue(CRUISE_CONFIG_SPEC_PARAMS, datasource)
      .then((res) => {
        return new Promise((resolve, reject) => {
          this.cruiseConfigure = res;
          resolve(res);
        });
      }).catch((err) => {
        console.error(err);
      });
  }
  static putCruiseConfig(value) {
    let params = [
      Object.assign(CRUISE_CONFIG_SPEC_PARAMS[0], { value: value })
    ];
    return Service.spec.setPropertiesValue(params);
  }
  static getPrePositions(datasource = 2) {
    return Service.spec.getPropertiesValue(PRE_POSITIONS_SPEC_PARAMS, datasource)
      .then((res) => {
        return new Promise((resolve, reject) => {
          resolve(res);
        });
      }).catch((err) => {
        console.error(err);
      });
  }
  static putPrePositions(value) {
    let params = [
      Object.assign(PRE_POSITIONS_SPEC_PARAMS[0], { value: value })
    ];
    return Service.spec.setPropertiesValue(params);
  }
  static activePrePositions(value) {
    let params = [
      Object.assign(ACTIVE_PRE_POSITIONS_SPEC_PARAMS[0], { value: value })
    ];
    return Service.spec.setPropertiesValue(params);
  }


  static getAlarmConfig() {
    let url = '/miot/camera/app/v1/get/alarmSwitch';
    if (CameraConfig.isNewChuangmi(Device.model)) {
      url = '/miot/camera/app/v2/get/alarmSwitch';
    }
    return API.get(url, 'business.smartcamera'); 
  }
  static putDailyStorySwitch(params) {
    let url = '/miot/camera/app/v1/put/dailyStorySwitch';
    return API.post(url, 'business.smartcamera', params); 
  }

  static queryAndOpenAlarmSwitch() {
    return new Promise((resolve, reject) => {
      if (VersionUtil.isUsingSpec(Device.model)){
        this.getSpecAlarmConfig(2)
          .then((result) => {
            if (result[0].code != 0) {
              // modify
              throw { code: result[0].code };
            }
            if (result[0].value == true) {
              // 中途有其他设备端打开了开关。
              Toast.success("c_set_success");
              resolve(result);
              return;
            }
            this.putSpecMotionDetectionSwitch(true)
              .then((setResult) => {
                if (setResult[0].code == 0) {
                  Toast.success("c_set_success");
                  result[0].value = true;//打开了开关。
                  // query should open or not.
                  resolve(result);
                  return;
                }
                throw { code: setResult[0].code };
              })

              .catch((err) => {
                Toast.success("action_failed");
                reject();
              })

          })
          .catch((err) => {
            Toast.success("action_failed");
            reject();
          })
      } else {
        this.getAlarmConfig()
          .then((result) => {
            if (result.code != 0) {
              throw { code: result.code };
            }
            let isSwitchOpen = result.data.motionDetectionSwitch.detectionSwitch;
            if (isSwitchOpen) {
              Toast.success("c_set_success");
              resolve(result);
              return;
            }
            result.data.motionDetectionSwitch.detectionSwitch = true;
            let interval = result.data.motionDetectionSwitch.interval;
            let startTime = result.data.motionDetectionSwitch.startTime;
            let endTime = result.data.motionDetectionSwitch.endTime;

            this.putMotionDetection({
              open: true,
              interval: interval,
              startTime: startTime,
              endTime: endTime
            })
              .then((modifyResult) => {
                if (modifyResult.code == 0) {
                  resolve(result);
                } else {
                  throw { code: modifyResult.code };
                }
              })
              .catch((err) => {
                Toast.success("action_failed");
                reject();
              });
          })
          .catch((err) => {
            Toast.success("action_failed");
            reject();
          });
      }
    });
  }

  static getSpecAlarmConfig(datasource = 1) {
    let curTime = new Date().getTime();
    let timePast = curTime - this._lastSpecAlarmTimestamp;
    if (timePast < 1000 && this._lastSpecAlarmConfig) {
      // 一分钟内再次取用缓存的值
      return new Promise((resolve, reject) => {
        resolve(this._lastSpecAlarmConfig);
      });
    } else {
      // 取到的值缓存起来
      return Service.spec.getPropertiesValue(MOTION_DETECTION_SEPC_PARAMS, datasource)
        .then((res) => {
          return new Promise((resolve, reject) => {
            if (res instanceof Array) {
              let curTime = new Date().getTime();
              this._lastSpecAlarmTimestamp = curTime;
              this._lastSpecAlarmConfig = res;
              resolve(res);
            } else {
              reject(res);
            }
          });
        }).catch((err) => {
          this._lastSpecAlarmTimestamp = 0;
          this._lastSpecAlarmConfig = null;
        });
    }
    // return Service.spec.getPropertiesValue(MOTION_DETECTION_SEPC_PARAMS, datasource);
  }

  static getLongTimeAlarmList(datasource = 1) {
    return Service.spec.getPropertiesValue(LONG_TIME_ALARM_SPEC_PARAMS, datasource)
      .then((res) => {
        return new Promise((resolve, reject) => {
          if (res instanceof Array) {
            resolve(res);
          } else {
            reject(res);
          }
        });
      }).catch((err) => {
      });
  }
  static getAiSwitch022(datasource = 2) {
    return Service.spec.getPropertiesValue(AI_SWITCH_022_SPEC_PARAMS, datasource);
  }
  static putFaceSwitch022(value) {
    let params = [
      Object.assign(AI_SWITCH_022_SPEC_PARAMS[0], { value: value })
      // { did: Device.deviceID, siid: CHUANGMI_AI_SIID, piid: CHUANGMI_FACE_SWITCH_022, value: value }
    ];
    return Service.spec.setPropertiesValue(params);
  }
  static status_ejected = -1;
  static getSD_STATUS_EJECTED(datasource = 2) {
    return Service.spec.getPropertiesValue(SD_STATUS_EJECTED_PARAMS, datasource)
      .then((res) => {
        return new Promise((resolve, reject) => {
          resolve(res);
        });
      }).catch((err) => {
        console.error(err);
      });
  }
  static getSD_SD_CARD_RECORD_DURATION(datasource = 2) {
    return Service.spec.getPropertiesValue(SD_CARD_RECORD_DURATION, datasource)
      .then((res) => {
        return new Promise((resolve, reject) => {
          resolve(res);
        });
      }).catch((err) => {
        console.error(err);
      });
  }
  static putSD_STATUS_EJECTED(value) {
    let params = [
      Object.assign(SD_STATUS_EJECTED_PARAMS[0], { value: value })
    ];
    return Service.spec.setPropertiesValue(params);
  }
  static putPetSwitch022(value) {
    let params = [
      Object.assign(AI_SWITCH_022_SPEC_PARAMS[1], { value: value })
      // { did: Device.deviceID, siid: CHUANGMI_AI_SIID, piid: CHUANGMI_FACE_SWITCH_022, value: value }
    ];
    return Service.spec.setPropertiesValue(params);
  }
  static getAiFrameSwitch022(datasource = 2) {
    return Service.spec.getPropertiesValue(AI_FRAME_022_SPEC_PARAMS, datasource);
  }
  static putAiFrameSwitch022(value) {
    let params = [
      { did: Device.deviceID, siid: CHUANGMI_AI_SIID, piid: CHUANGMI_AI_FRAME_022, value: value }
    ];
    return Service.spec.setPropertiesValue(params);
  }
  static getBabySleepParams(datasource = 2) {
    return Service.spec.getPropertiesValue(SPEC_BABY_SLEEP_PARAMS, datasource);
  }

  static getLongTimeRepeatStr(repeat) {
    if (0b00000000 == repeat) {
      return LocalizedStrings['plug_timer_onetime'];
    } else if (0b01111111 == repeat) {
      return LocalizedStrings['plug_timer_everyday'];
    } else {
      let dayList = [LocalizedStrings['monday1'], LocalizedStrings['tuesday1'], LocalizedStrings['wednesday1'],
        LocalizedStrings['thursday1'], LocalizedStrings['friday1'], LocalizedStrings['saturday1'], LocalizedStrings['sunday1']];

      let ret = "";
      let flag = 0b00000001;
      let i = 0;
      for (i = 0; i < dayList.length; i++) {
        if ((repeat & flag) != 0) {
          if (ret.length > 0) {
            ret += ", ";
          }
          ret += dayList[i];
        }
        flag = flag << 1;
      }
      return ret;
    }
  }

  static putLongTimeAlarmList(value) {
    let params = [
      { did: Device.deviceID, siid: CHUANGMI_AI_SIID, piid: CHUANGMI_CLOCKS, value: value }
    ];
    return Service.spec.setPropertiesValue(params);
  }
  static putBabySleepSwitch(value) {
    let params = [
      { did: Device.deviceID, siid: CHUANGMI_AI_SIID, piid: 6, value: value }
    ];
    return Service.spec.setPropertiesValue(params);
  }
  static putBabySleepArea(value) {
    let params = [
      { did: Device.deviceID, siid: CHUANGMI_AI_SIID, piid: 7, value: value }
    ];
    return Service.spec.setPropertiesValue(params);
  }

  static putMotionDetection(params) {
    let url = '/miot/camera/app/v1/put/motionDetectionSwitch';
    if (CameraConfig.isNewChuangmi(Device.model)) {
      url = '/miot/camera/app/v2/put/motionDetectionSwitch';
    }
    return API.post(url, 'business.smartcamera', params);
  }

  static putSpecMotionDetection(params) {
    let specParams = [
      { did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: MOTION_DETECTION_PIID, value: params.open },
      { did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: ALARM_INTERVAL_PIID, value: params.interval },
      { did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: START_TIME_PIID, value: params.startTime },
      { did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: END_TIME_PIID, value: params.endTime }
    ];
    this._lastSpecAlarmTimestamp = 0;
    this._lastSpecAlarmConfig = null;
    return Service.spec.setPropertiesValue(specParams);
  }

  static putSpecMotionDetectionSwitch(value) {
    let specParams = [
      { did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: MOTION_DETECTION_PIID, value: value }
    ];
    this._lastSpecAlarmTimestamp = 0;
    this._lastSpecAlarmConfig = null;
    return Service.spec.setPropertiesValue(specParams);
  }

  static putSpecMotionDetectionInterval(value) {
    let specParams = [
      { did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: ALARM_INTERVAL_PIID, value: value }
    ];
    this._lastSpecAlarmTimestamp = 0;
    this._lastSpecAlarmConfig = null;
    return Service.spec.setPropertiesValue(specParams);
  }

  static putSpecMotionDetectionPeriod(params) {
    let specParams = [
      { did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: START_TIME_PIID, value: params.startTime },
      { did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: END_TIME_PIID, value: params.endTime }
    ];
    this._lastSpecAlarmTimestamp = 0;
    this._lastSpecAlarmConfig = null;
    return Service.spec.setPropertiesValue(specParams);
  }

  static FACE_ACCEPT = "accept";
  static FACE_CANCEL = "cancel";
  static setFacePrivacyConfirmation(type, needSetSwitch = true) {
    let param = {
      privacyVersion: "",
      type: type,
      privacyType: 3,
      pluginPrivacyId: 302
    };
    return new Promise((resolve, reject) => {
      if (type == this.FACE_CANCEL) {
        this.putFaceSwitch022(false).then(() => {
          Device.setPrivacyConfirmation(param).then((res) => {
            if (needSetSwitch) {
              Toast.success('c_set_success');
            }
            resolve(res);
          }).catch((err) => {
            if (needSetSwitch) {
              Toast.fail('c_set_fail');
            }
            reject(err);
          });
        }).catch((err) => {
          if (needSetSwitch) {
            Toast.fail('c_set_fail');
          }
          reject(err);
        });
      } else {
        Device.setPrivacyConfirmation(param).then((res) => {
          if (needSetSwitch) {
            this.putFaceSwitch022(true).then(() => {
              Toast.success('c_set_success');
              resolve(res);
            }).catch((err) => {
              Toast.fail('c_set_fail');
              reject(err);
            });
          } else {
            resolve(res);
          }
        }).catch((err) => {
          Toast.fail('c_set_fail');
          reject(err);
        });
      }
    });
  }

  static formatTimeString(timeStr) {
    if (!timeStr || typeof (timeStr) != 'string') {
      return timeStr;
    }
    let subs = timeStr.split(':');
    if (subs.length < 3) {
      return timeStr;
    }
    let ret = '';
    for (let i = 0; i < 2; ++i) {
      let sub = subs[i];
      if (sub.length < 2) {
        sub = `0${ sub }`;
      }
      ret = ret + sub;
      if (i < 1) {
        ret = `${ ret }:`;
      }
    }
    return ret;
  }

  static getDailyStoryList(params) {
    let url = '/miot/camera/app/v1/dailyStory/playlist';
    return API.get(url, 'business.smartcamera', params);
  }

  static deleteDailyStoryVideo(params) {
    let url = '/miot/camera/app/v1/dailyStory/delete';
    return API.post(url, 'business.smartcamera', params);
  }

  static batchGetDatas(params) {
    let url = '/v2/device/batchgetdatas';
    return API.postString(url, '', params); 
  }
  static range_get_extra_data(params) {
    let url = '/v2/device/range_get_extra_data';
    return API.postString(url, '', params); 
  }
  static setProps(params) {
    let url = '/v2/device/set_props';
    return API.postString(url, '', params); 
  }

  static getThirdAlarmInfo() {
    let url = '/v2/device/get_camera_third_alarm_info';
    return Service.callSmartHomeAPI(url, { "did": Device.deviceID });
  }
  static setThirdAlarmInfo(value) {
    let url = '/v2/device/set_camera_third_alarm_info';
    return Service.callSmartHomeAPI(url, { "did": Device.deviceID, "phone_number": value });
  }
  static setPhoneServiceSwitch(value) {
    let url = '/v2/device/set_camera_phoneservice_switch';
    return Service.callSmartHomeAPI(url, { "did": Device.deviceID, "switch": value });
  }

  static sceneSize = 0;
  static loadAutomaticScenes(did) {
    return new Promise((resolve, reject) => {
      Service.scene.loadAutomaticScenes(did).then((res) => {
        LogUtil.logOnAll("loadAutomaticScenes res.size=", res.length);
        AlarmUtil.sceneSize = res.length;
        Device.getRoomInfoForCurrentHome(did).then((res) => {
          Service.scene.loadAllScenesV2(res.data.homeId, did).then((res) => {
            LogUtil.logOnAll("loadAutomaticScenesV2 res.size=", res.length);
            AlarmUtil.sceneSize += res.length;
            resolve(AlarmUtil.sceneSize);
          }).catch((err) => {
            LogUtil.logOnAll("loadAutomaticScenesV2 err=", JSON.stringify(err));
            resolve(AlarmUtil.sceneSize);
          });
        }).catch((err) => {
          LogUtil.logOnAll("loadAutomaticScenes getRoomInfoForCurrentHome err=", JSON.stringify(err));
          resolve(AlarmUtil.sceneSize);
        });
      }).catch((err) => {
        LogUtil.logOnAll("loadAutomaticScenes err=", JSON.stringify(err));
        reject(err);
      });
    });
  }
}