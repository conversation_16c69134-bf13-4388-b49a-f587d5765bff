import React from 'react';
import EventGridCard, { CardHeight } from './EventGridCard';
import { SectionList, View, Text, TouchableOpacity, Animated, ScrollView, Platform } from "react-native";
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import EventList, { LoadStatus } from "./EventList";
import { BaseStyles } from "../BasePage";
import { Order } from "../framework/EventLoaderInf";
import Separator from 'miot/ui/Separator';
import ImageButton from "miot/ui/ImageButton";
import { Device, Host } from 'miot';
import Util from '../util2/Util';
import { CldDldTypes } from "../framework/CloudEventLoader";
import { MaxSel } from "../allVideo/StorageUI";

const ColCnt = 3;
const HeaderH = 45;
export const FooterH = 23;
const TAG = "EventGrid";
import dayjs from 'dayjs';
import { DescriptionConstants } from '../Constants';
import ABTest from "../util/ABTest";
import CameraConfig from "../util/CameraConfig";
import { Tabs } from "../allVideo/AllStorage";

/*
UI data form
[
section0{
  title: XXX,
  data:[[itm1, itm2, itm3],
      [itm1, itm2, itm3],
      [itm1, itm2, itm3]]
}
.....
sectionX{
  title: XXX,
  data:[[itm1, itm2, itm3],
      [itm1, itm2, itm3],
      [itm1, itm2, itm3]]
}
]
*/
export default class EventGrid extends EventList {
  constructor(aProps) {
    super(aProps);
    this.mCardH = CardHeight;
    this.mSecHeaderBg = this.props.secHeaderBg ? this.props.secHeaderBg : "#ffffff";
  }

  delayToScroll() {
    let delay = Platform.OS == 'ios' ? 100 : 300;
    setTimeout(() => {
      let scroll = this.getInitScroll();
      console.log(TAG, "scroll", scroll,this.mActive);
      if (this.mActive && scroll != null) {
        this.scrollTo(scroll);
      }
    }, delay);
  }

  getInitScroll() {
    let ev = this.state.events;
    if (this.props.playingId) {
      for (let sectionIndex = 0; sectionIndex < ev.length; ++sectionIndex) {
        let section = ev[sectionIndex].data;
        for (let itemIndex = 0; itemIndex < section.length; ++itemIndex) {
          let tuple = section[itemIndex];
          for (let itm of tuple) {
            // let mPId = `${ itm.fileId }`;
            let mPId = itm.offset != null ? `${ itm.fileId }_${ itm.offset }` : `${ itm.fileId }`;

            if (mPId === this.props.playingId) {
              console.log("======================itemIndex",itemIndex,sectionIndex)
              let itemIndexUse = itemIndex + 1;// 加上顶部的header
              // return { animated: true, itemIndex, sectionIndex, viewOffset: 0, viewPosition: 1 };
              // viewPosition 为 0 时将这个列表项滚动到可视区顶部 (可能会被顶部粘接的 header 覆盖), 为 1 时将它滚动到可视区底部, 为 0.5 时将它滚动到可视区中央。
              // return { animated: false, itemIndex, sectionIndex, viewOffset: HeaderH * sectionIndex, viewPosition: itemIndex == 0 ? 0 : 1 };
              return { animated: false, sectionIndex, itemIndex: itemIndexUse,viewOffset: HeaderH * sectionIndex };
            }
          }
        }
      }
    }
    return null;
  }


  scrollTo(aLoc) {
    if (this.mLst && this.state.events?.length > 0) {
      this.mLst.scrollToLocation(aLoc);
    }
  }
  
  async getData(date, event, isMore = false, aOrder = Order.Desc, type = this.props.type) {
    console.log("landing1", 'isSltDayMore', this.mSltDayMore);
    if (this.sltDay && !this.mSltDayMore) {
      console.log('getOneDayAllEvent is called');
      let data = await this.mLoader.getOneDayAllEvent(date, event, isMore, aOrder, type);
      return data;
    } else {
      // console.log(this.mLoader, 'getAllEvent is called');
      let data = await this.mLoader.getAllEvent(date, event, isMore, aOrder, type);
      return data;
    }
  }
  
  appendEvents(aOldGrps, aItms, aOrder = Order.Desc) {
    if (Order.Desc == aOrder) {
      let dic = {};
      let dicSel = {};
      if (aOldGrps.length > 0) {
        // use last for fill
        let grp = aOldGrps.pop();
        dic[grp.title] = grp.data;
        dicSel[grp.title] = grp.selected;
      }
      
      for (let i = 0; i < aItms.length; i++) {
        let item = aItms[i];
        this.buildSection(dic, item);
      }
      for (let key in dic) {
        let groupItem = {
          title: key,
          data: dic[key],
          selected: dicSel[key] ? dicSel[key] : false,
          date: this.getDateInfo(dic[key])
        };

        if (this.props.abType != ABTest.Types.A) {
          delete groupItem.title;
        }
        aOldGrps.push(groupItem);
      }
      return aOldGrps;
    } else {
      throw "not support ASC";
    }
  }

  onGetDataDone(events) {
    let dates = [];
    for (let evt in events) {
      let mdt = events[evt]?.date;
      if (mdt && !dates.includes(mdt)) { // 日期已经归类，不会重复的
        dates.push(mdt);
      }
    }
    if (events?.length > 0 && (events[0]?.data[0]?.isNoSVLTips || events[0]?.data[0]?.isCloudTips)) {
      this.props.onGetDataDone(events.length - 1, events, dates);
    } else {
      this.props.onGetDataDone(events.length, events, dates);
    }
  }

  getDateInfo(grp) {
    let tuple = grp[grp.length-1];
    return tuple[0] ? tuple[0].createTime : tuple.createTime;
  }

  buildSection(aSecDict, aItm) {
    
    let dStr = dayjs.unix(aItm.createTime / 1000).format(LocalizedStrings["yyyymmdd"]);
    if (Util.isToday(dayjs.unix(aItm.createTime / 1000))) {
      dStr = `${ dStr } | ${ LocalizedStrings['today'] }`;
    } else if (Util.isYestoday(dayjs.unix(aItm.createTime / 1000))) {
      dStr = `${ dStr } | ${ LocalizedStrings['yestoday'] }`;
    }
    let grp = aSecDict[dStr];
    aItm['sectKey'] = dStr;
    if (!grp) {
      aSecDict[dStr] = [[aItm]];
    } else {
      let tuple = grp[grp.length - 1];
      if (ColCnt == tuple.length) {
        grp.push([aItm]); // make new tuple
      } else {
        tuple.push(aItm); // append to existing
      }
    }
  }



  applyFilter(aEvents, aFilter) {
    let curEv = aEvents;
    let newEv = [];
    for (let evGrp of curEv) {
      let nd = [];
      let filtered = null;
      for (let tuple of evGrp.data) {
        let nt = tuple.filter(aFilter);
        if (!filtered) {
          if (ColCnt == nt.length) {
            nd.push(nt);
          } else if (nt.length > 0) {
            filtered = nt;
          }
        } else {
          while (nt.length > 0 && filtered.length < ColCnt) {
            filtered.push(nt.shift());
          }
          if (ColCnt == filtered.length) {
            nd.push(filtered);
            filtered = null;
          }

          if (nt.length > 0) {
            filtered = nt;
          }
        }
      }
      if (filtered != null) {
        nd.push(filtered);
      }

      evGrp.data = nd;

      if (evGrp.data.length > 0) {
        newEv.push(evGrp);
      }
    }
    return newEv;
  }
  filterItems(data){
    let arr=data;
    if(data instanceof Array&&data[0]){
      if(arr[0].data[0] && typeof(arr[0].data[0].isShowImg) === 'boolean'){// 判断是否有海外云存需求
        arr[0].data = data[0].data.filter((v)=>v.isShowImg)
      }
    }
    return arr;
   }

  render() {
    let hvf = this.props.eventHeaderView;

    // console.log(TAG, "render hvf", hvf, this.state.initIdx, this.props.isEditing);
    // initialScrollIndex={this.state.initIdx}
    let paddingBm = this.props.withDailyStoryButton ? Platform.OS == 'ios' ? 106 : 76 : 50;
    return (
      this.props.isFullscreen ? null :
        (<View style={{ flex: 1 }}>
          {hvf ? hvf() : null}

          <SectionList
            ref={(ref) => { this.mLst = ref; }}
            style={this.props.style}
            sections={this.filterItems(this.state.events)}
            contentContainerStyle={[this.props.contentContainerStyle, { paddingBottom: paddingBm, flexGrow: 1 }]}
            scrollIndicatorInsets={{ top: 0, left: 0, bottom: 0, right: 0 }}
            ListEmptyComponent={this.props.eventEmptyView != null ? this.props.eventEmptyView : this.mEmptyV()}
            renderItem={this.mRItem}
            renderSectionHeader={this.mRSecH}
            renderSectionFooter={this.mRSecF}
            showsVerticalScrollIndicator={false}
            keyExtractor={(item, index) => {
            // console.log(this.tag, "calc key", index, item);
              return `${ item.fileId }_${ item.offset }_${ index }`;
            }}
            onEndReached={this.mOnEnd}
            onEndReachedThreshold={0.1}
            onRefresh={this.mRefresh}
            ListHeaderComponent={null}
            ListFooterComponent={this.mFooter}
            refreshing={LoadStatus.Loading === this.state.loadingStatus}
            getItemLayout={this.mLayoutGetter}
            stickySectionHeadersEnabled={true}
            onScroll={(e) => {
              this.props.onScroll && this.props.onScroll(e.nativeEvent.contentOffset.y);
            }}
            scrollEventThrottle={5}
          />
        </View>)

    );
  }

  componentDidUpdate(aPrevProps) {
    if (this.props.selectAll !== aPrevProps.selectAll) {

      let doSel = this.props.selectAll;
      for (let i = 0; i < this.state.events.length; ++i) {
        let section = this.state.events[i];
        if (this.selSection(section, doSel)) {
          break;
        }
      }
    } else if (this.props.isEditing !== aPrevProps.isEditing && !this.props.isEditing) {
      // for back
      for (let i = 0; i < this.state.events.length; ++i) {
        let section = this.state.events[i];
        section.selected = false;
      }
    }
    super.componentDidUpdate(aPrevProps);
  }

  sectSize(aSection) {
    let size = 0;
    for (let j = 0; j < aSection.data.length; ++j) {
      let evTuple = aSection.data[j];
      size += evTuple.length;
    }
    return size;
  }

  isAllSectSelected(aSection) {
    let size = this.sectSize(aSection);
    let cnt = 0;
    for (let j = 0; j < aSection.data.length; ++j) {
      let evTuple = aSection.data[j];
      for (let ev of evTuple) {
        if (ev.selected) {
          cnt += 1;
        }
      }
    }
    if (cnt == size || cnt == MaxSel) {
      return true;
    }
    return false;
  }

  isAllSelected() {
    for (let j = 0; j < this.state.events.length; ++j) {
      let sect = this.state.events[j];
      if (!this.isAllSectSelected(sect)) {
        return false;
      }
    }
    return true;
  }

  selSection(aSection, aDoSel) {
    let stop = false;
    let size = this.sectSize(aSection);
    let cnt = 0;
    for (let j = 0; j < aSection.data.length && !stop; ++j) {
      let evTuple = aSection.data[j];
      for (let ev of evTuple) {
        cnt += 1;
        if (aDoSel) {
          if (!ev.selected) {
            stop = !this.props.onEventPress(ev);
            if (stop) {
              cnt -= 1;
              break;
            }
          }
        } else {
          if (ev.selected) {
            this.props.onEventPress(ev);
          }
        }
      }
      if (stop) {
        break;
      }
    }
    if (cnt == size || cnt == MaxSel) {
      aSection.selected = aDoSel;
    }
    this.props.onSelectAllCB && this.props.onSelectAllCB(this.isAllSelected());
    return stop;
  }

  getAdjacentEvent(aEv) {
    let curEv = this.state.events;
    for (let i = 0; i < curEv.length; ++i) {
      let evGrp = curEv[i].data;
      for (let j = 0; j < evGrp.length; ++j) {
        let tuple = evGrp[j];
        let idx = tuple.findIndex((aItm) => {
          return aItm.fileId == aEv.fileId;
        });
        if (idx != -1) {
          if (idx + 1 < tuple.length) {
            return tuple[idx + 1];
          } else {
            if (j + 1 < evGrp.length) {
              return evGrp[j + 1][0];
            }
            // check net grp
            else if (i + 1 < curEv.length) {
              return curEv[i + 1].data[0][0];
            }
            // look back
            else if (idx - 1 >= 0) {
              return tuple[idx - 1];
            } else if (j > 0) {
              let preTuple = evGrp[j - 1];
              return preTuple[preTuple.length - 1];
            } else if (i > 0) {
              let preTuple = curEv[i - 1].data[0];
              return preTuple[preTuple.length - 1];
            } else {
              return null;
            }
          }
        }
      }
    }
  }

  mRefresh = () => {
    if (!this.props.isEditing) {
      console.log("landing5", new Date());
      if (!CameraConfig.isLocalNetwork && this.props.typeTab == Tabs.Card) {
        console.log("===========local network===================");
      } else {
        this.getEventList(this.state.startDate, 'Default', false);
      }
      if (Platform.OS == 'android' && this.sltDay) {
        this.props.onScroll && this.props.onScroll(-90);
      }
    }
  }


  mRItem = ({ section, index }) => {
    // console.log(this.tag, "mRItem", index);
    return (
      <View style={{ height: CardHeight, flexDirection: "row", paddingHorizontal: 20 }}>
        {
          section.data[index]
            .map((itm, idx) => {
              let rightPadding = ColCnt - 1 == idx ? 0 : 10;
              let mkey = itm.offset != null ? `c_${ index }_${ itm.fileId }_${ itm.offset }` : `c_${ index }_${ itm.fileId }`;
              // let mPId = `${ itm.fileId }`;
              let mPId = itm.offset != null ? `${ itm.fileId }_${ itm.offset }` : `${ itm.fileId }`;

              // console.log("key: " + mkey + ", eventtime: " + itm.eventTime + ", ctime: " + itm.createTime + ", offset:"+itm.offset);
              return (
                <EventGridCard key={mkey}
                  style = {{ marginRight: rightPadding }}
                  item={itm}
                  isEditing={this.props.isEditing}
                  isPlaying = {this.props.playingId === mPId}
                  cardPressed={(aItm) => {
                    this.mEventPress(aItm);
                  }}/>
              );
            })
        }
      </View>
    );
  }

  mEventPress = (aItm) => {
    let events = this.state.events;
    let nextDate = this.state.nextDate;
    let success = this.props.onEventPress(aItm, { events, nextDate });
    if (success) {
      let sectionKey = aItm.sectKey;
      if (events && events.length > 0) {
        for (let key in events) {
          let sect = events[key];
          if (sect.title == sectionKey) {
            if (!aItm.selected && sect.selected) {
              sect.selected = false;
            } else if (!sect.selected && aItm.selected && this.isAllSectSelected(sect)) {
              sect.selected = true;
            } else {
              sect.selected = this.isAllSectSelected(sect);
            }
            this.setState({});
            break;
          }
        }
      }
      this.props.onSelectAllCB && this.props.onSelectAllCB(this.isAllSelected());
    }
  }

  mRSecH = (aDat) => {
    if (!aDat.section?.title || aDat.section.title == "Invalid Date") {
      return null;
    }
    // console.log(TAG, "isSltDay", this.props.isSltDay, "isEditing", this.props.isEditing);
    let mSecStyle = this.props.isSltDay ? { fontSize: Util.isLanguageCN()? 14 : 12, fontWeight: 'bold', color: "#000000" } : { fontSize: Util.isLanguageCN()? 12 : 10, fontWeight: 'bold', color: "#8C93B0" };
    let mSlctWidth = 90;
    if (Host.locale.language == 'zh' || Host.locale.language == 'zh_hk' || Host.locale.language == 'zh_tw') {
      mSlctWidth = 60;
    }
    return (
      <View style = {[BaseStyles.row, { backgroundColor: this.mSecHeaderBg, height: HeaderH, paddingHorizontal: 22, paddingBottom: 10 }]}>
        <Text style = {[BaseStyles.text12, mSecStyle]}>{aDat.section.title}</Text>
        {
          this.props.isEditing ?
            (<TouchableOpacity style = {{ backgroundColor: "#F5F5F5", display: "flex", flexDirection: "column" ,justifyContent: "center", alignItems: "center", minHeight: 28, borderRadius: 17,padding:8 }}
              onPress={() => {
                this.selSection(aDat.section, !aDat.section.selected);
              }}
            >
              <Text style = {[{ fontSize: Util.isLanguageCN()? 13 : 11, fontWeight: 'bold', color: "#666666", paddingHorizontal: 7 }]}>{aDat.section.selected ? LocalizedStrings["item_unsel_all"] : LocalizedStrings["item_sel_all"]}</Text>
            </TouchableOpacity>)
            : (this.props.isSltDay ? 
              (
                <View style={{ flexDirection: "row", justifyContent: "center" }}>
                  <ImageButton style={[BaseStyles.icon45, { marginRight: 15 }]} source={Util.isDark() ? require('../../resources2/images/cld_icon_nor_w.png') : require('../../resources2/images/cld_icon_nor.png')}
                    onPress={this.props.onShowCalendar}
                    accessibilityLabel={DescriptionConstants.lc_date}
                  />
                  {
                    Device.isReadonlyShared ? null : <ImageButton style={BaseStyles.icon45} source={Util.isDark() ? require('../../resources2/images/icon_edit_storage_white.png') : require('../../resources2/images/icon_edit_storage_black.png')}
                      onPress={this.props.onStartEdit}
                      accessibilityLabel={DescriptionConstants.lc_edit}
                    />
                  }
                </View>
              ) : null)
        }
      </View>);
  }

  mRSecF = () => {
    return this.props.type && this.props.type == CldDldTypes.Events ? null :
      <View style={{ height: FooterH, paddingHorizontal: 25, paddingBottom: 8, justifyContent: "center" }}>
        <Separator/>
      </View>
    ;
  }


  mLayoutGetter = (aSections, aIdx) => {
    let pos = -1;
    let hh = this.props.eventHeaderView ? this.props.eventHeaderHeight : 0;
    let length = aIdx < 0 ? hh : HeaderH;
    let offset = aIdx >= 0 ? hh : 0;
    let i = 0;
    for (i = 0; i < aSections.length && pos < aIdx; ++i) {
      let data = aSections[i].data;
      // account for section header and footer
      let nPos = pos + data.length + 2;
      if (nPos < aIdx) {
        pos = nPos;
        offset = offset + data.length * this.mCardH + HeaderH + FooterH;
      } else if (nPos == aIdx) { // hit section footer
        pos = nPos;
        length = FooterH;
        offset = offset + HeaderH + data.length * this.mCardH;
      } else {
        ++pos;// add section header
        if (pos == aIdx) { // hit section header
          length = HeaderH;
        } else {
          offset += HeaderH;
          if (aIdx <= pos + data.length) { // in middle
            offset = offset + (aIdx - pos - 1) * this.mCardH;
            length = this.mCardH;
          } else { // last footer
            offset = offset + (aIdx - pos) * this.mCardH;
            length = FooterH;
          }
          pos = aIdx;
        }
      }
    }
    let ret = { length, offset, index: aIdx };
    // console.log(TAG, "getItemLayout", aIdx, ret, pos, "CardHeight", CardHeight, "HeaderH", HeaderH, "FooterH", FooterH, "mCardH" + this.mCardH);
    return ret;
  }

  getItemsFromEvents() {
    let items = [];
    if (this.state.events && this.state.events.length > 0) {
      for (let key in this.state.events) {
        for (let arr in this.state.events[key].data) {
          items = items.concat(this.state.events[key].data[arr]);
        }
      }
    }
    let nItems = [];
    for (let key in items) { // rever map, ref to map in localeventloader.getEventList()
      let item = {};
      item.url = items[key].localUrl;
      item.modificationDate = items[key].createTime / 1000;
      item.path = items[key].fileId;
      item.mediaType = items[key].mediaType == 'video' ? 2 : 1;
      nItems.push(item);
    }
    return nItems;
  }


}
