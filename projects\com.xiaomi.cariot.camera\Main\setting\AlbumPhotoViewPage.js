import React from 'react';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlbumHelper from '../util/AlbumHelper';
import Toast from '../components/Toast';
import { View, Text, TouchableOpacity, Image, Dimensions, ActivityIndicator, BackHandler, StatusBar, Platform, SafeAreaView } from 'react-native';
import NavigationBar from "miot/ui/NavigationBar";
import ImageViewer from 'react-native-image-zoom-viewer';
import { Host, DarkMode } from "miot";
import { MessageDialog } from "miot/ui";
import ImageButton from "miot/ui/ImageButton";
import Orientation from 'react-native-orientation';
import CameraConfig, { IMG_DARKMODE_TINT } from '../util/CameraConfig';
import dayjs from 'dayjs';
import StatusBarUtil from '../util/StatusBarUtil';
import LinearGradient from 'react-native-linear-gradient';
import { DescriptionConstants } from '../Constants';
import Util from '../util2/Util';
import CommonMsgDialog from '../ui/CommonMsgDialog';
import { PermissionsAndroid } from 'react-native';
import { PAD_SCROLL_STRATEGY } from 'miot/Host';
import Service from 'miot/Service';
import LogUtil from '../util/LogUtil';
import { AccessibilityRoles } from 'miot/utils/accessibility-helper';

const kIsCN = Util.isLanguageCN();
const iconSize = 40; // 图标尺寸
const TAG = "AlbumPhotoViewPage";

export default class AlbumPhotoViewPage extends React.Component {

  static navigationOptions = ({ navigation }) => {
    const { titleProps } = navigation.state.params || {};
    // if (!titleProps) return { header: null }
    // return {
    //   header: <NavigationBar {...titleProps} />
    // };
    return {
      headerTransparent: true,
      header: null
    };
  };
  constructor(props) {
    super(props);
    this.receiverMap = new Map();
    this.receiveUrlArray = [];
    this.dialogDeleteContent = LocalizedStrings["delete_title"];
    this.title = null;
    this.mDate = -1;
    this.mInItems = -1;
    /*   this.dataMapValue = {
          path: '',
          name: ''
      } */
    this.state = {
      currentIndex: 0,
      imageUrlArray: [],
      // showLoadingDialog: true,
      deleteDialogVisible: false,
      showLoading: true,
      items: [],
      positionX: Dimensions.get("window").width / 2 - 25,
      positionY: Dimensions.get("window").height / 2 - 25,
      showPermissionDialog: false,
      isFullscreen: (this.props.navigation.state.params?.preOri && this.props.navigation.state.params?.preOri == 'landscape') ?? false,
    };

    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        Host.setPadScrollDealStrategy({strategy: PAD_SCROLL_STRATEGY.ALWAYS_SDK_DEAL});
        if (Platform.OS === "android") {
          this.onBackListener = BackHandler.addEventListener("hardwareBackPress", this.onBack);
        }
      }
    );
    this.willBlurListener = this.props.navigation.addListener(
      "willBlur",
      () => {
        Host.setPadScrollDealStrategy({strategy: PAD_SCROLL_STRATEGY.AUTO});
        BackHandler.removeEventListener("hardwareBackPress", this.onBack);
        this.onBackListener && this.onBackListener.remove();
      }
    )

    // Service.miotcamera.enterFullscreenForPad(false);
    // Orientation.lockToPortrait();

    this.mOri = "PORTRAIT";
    if (this.props.navigation.state.params?.preOri) {
      this.mOri = this.props.navigation.state.params?.preOri.toUpperCase();
    }
    this.deleteThrottleTime = null;
  }

  getStatusBarHeight() {
    let StatusBarheight = null, haveStatus = false;
    if (Platform.OS === "android") {
      StatusBarheight = StatusBar.currentHeight;
      if (StatusBarheight <= 0) {
        haveStatus = true;
        StatusBarheight = 15;
      }
      // -16 因为返回按钮的图片留白太大
    } else {
      StatusBarheight = StatusBarUtil._getInset("top") - 16;
    }
    return StatusBarheight;
  }
  setNavigation() {
    console.log('setnavigation', LocalizedStrings["s_photo_album"]);
    this.props.navigation.setParams({

      title: LocalizedStrings["s_photo_album"],
      type: NavigationBar.TYPE.DARK,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.onBack(); }
        }
      ]
    });
  }

  componentDidMount() {
    console.log(TAG,"componentDidMount")
    let currentIndex = -1;
    // CameraConfig.lockToPortrait();// 切换回竖屏
    StatusBar.setHidden(false);//强制显示statusBar
    StatusBar.setBarStyle("dark-content");
    this.setNavigation();

    this.albumSuffix = "";
    if (this.props.navigation.state.params && this.props.navigation.state.params.albumSuffix && CameraConfig.isDualCamera()) {
      this.albumSuffix = this.props.navigation.state.params.albumSuffix;
    }

    if (this.props.navigation != null && this.props.navigation.state != null && this.props.navigation.state.params != null) {
      currentIndex = this.props.navigation.state.params.index == undefined ? -1 : this.props.navigation.state.params.index;
      this.mDate = this.props.navigation.state.params.sltDate == undefined ? -1 : this.props.navigation.state.params.sltDate;
      this.mType = this.props.navigation.state.params.type;
      this.mUrl = this.props.navigation.state.params.url;
      this.mInItems = this.props.navigation.state.params.items == undefined ? -1 : this.props.navigation.state.params.items;

    } else {
      currentIndex = -1;
    }


    this.mType == 2 ? this.mMediaTypeIsVideo = true : this.mMediaTypeIsVideo = false;

    let colorScheme = DarkMode.getColorScheme();
    if (colorScheme == 'dark') {
      this.darkMode = true;
    } else {
      this.darkMode = false;
    }

    this.loadFiles(currentIndex);
    this.restoreOri();
    Orientation.addOrientationListener(this._orientationListener);
  }
  componentWillUnmount() {
    console.log(TAG,"componentWillUnmount");
    Host.setPadScrollDealStrategy({ strategy: PAD_SCROLL_STRATEGY.ALWAYS_SDK_DEAL });

    BackHandler.removeEventListener('hardwareBackPress', this.onBack);
    this.onBackListener && this.onBackListener.remove();
    this.didFocusListener.remove();
    this.willBlurListener.remove();
  }

  onBack = (forceBack = false, forceBackBecauseOfRemainItemsCountEqual0 = false) => {
    // this.mRestorePreOrientation();
    console.log("onBack",this.props.navigation.state.params.preOri,this.state.isFullscreen, forceBack, forceBackBecauseOfRemainItemsCountEqual0);
    if (this.props.navigation.state.params.preOri) {
      if ((this.props.navigation.state.params.preOri == "landscape" && this.state.isFullscreen)
        || (this.props.navigation.state.params.preOri !== "landscape" && !this.state.isFullscreen)) {
        this.props.navigation.goBack();
        return true;
      } else {
        if (this.state.isFullscreen) {
          this.toPortrait();
          if (forceBackBecauseOfRemainItemsCountEqual0) {
            // 添加延迟，解决MIECOCMCZ-1268
            setTimeout(() => {
              this.props.navigation.goBack();
            }, 20);
          }
          return true;
        } else {
          this.toLandscape();
        }
        if (forceBack) {
          setTimeout(() => {
            this.props.navigation.goBack();
          }, 20);
        }
        return;
      }
    } else {
      this.props.navigation.goBack();
      return true;
    }
  }

  loadFiles(currentIndex, suffix = this.albumSuffix) {
    if (this.mInItems != -1) {
      this.parseItems(this.mInItems, currentIndex);
      return;
    }

    AlbumHelper.checkPermission()
      .then(() => {
        AlbumHelper.getAlbumFiles(suffix)
          .then((result) => {
            // 肯定是array了
            // let picArray = result.filter((item) => item.mediaType == 1);
            this.parseItems(result, currentIndex);
          })
          .catch((error) => {
            console.log("AlbumPhotoViewPage error:", error);
            console.log("this.props.navigation.state.params.router:",this.props.navigation.state.params.router);
            if (this.props.navigation.state?.params?.routerName == 'AlarmVideoUI') {
              this.props.navigation.goBack();// 操作失败
              return
            }
            Toast.show("action_failed");
            this.props.navigation.goBack();// 操作失败
          });
      })
      .catch((result) => {
        if (result == PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
          this.setState({ showPermissionDialog: true });
        } else {
          Toast.success("camera_no_write_permission");
        }
      });


  }

  parseItems(inItems, currentIndex) {
    // inItems是接收的整个数组
    let picArray = [];
    if (this.mDate != -1) {//这里适用于从存储管理local页面跳过来的，主要只显示当天的内容
      let bgn = dayjs(this.mDate).hour(0).minute(0).second(0).subtract(0, "days").valueOf();
      let end = dayjs(this.mDate).hour(23).minute(59).second(59).valueOf();
      picArray = inItems.filter((item) => { return item.modificationDate * 1000 < end && item.modificationDate * 1000 > bgn; });
    } else {
      picArray = inItems;
    }
    // picArray是用来接收
    if (picArray.length == 0) {
      Toast.show("action_failed");
      this.onBack();// 操作失败
      // 索引出错。
      return;
    }
    let showOnlyOne = false;
    if (this.mUrl) {
      for (let i = 0; i < picArray.length; i++) {
        if (this.mUrl == picArray[i].url) {
          currentIndex = i;
          picArray[i].mediaType == 2 ? this.mMediaTypeIsVideo = true : this.mMediaTypeIsVideo = false;
          break;
        }
      }
    }

    if (currentIndex < 0) {
      showOnlyOne = true;// 从直播页直接跳过来，只显示一个就行了。
      currentIndex = 0;// 因为是按照时间倒排序了的。
    } else {
      if (inItems.length <= currentIndex) {
        Toast.show("action_failed");
        this.onBack();// 操作失败
        // 索引出错。
        return;
      }
    }
    let picUrlArray = [];
    let width = Dimensions.get("window").width;
    let height = Number.parseInt(width / 640 * 360);
    for (let i = 0; i < picArray.length; i++) {
      if (picArray[i].pixelWidth != 0 && picArray[i].pixelHeight != 0) {
        picUrlArray.push({ url: picArray[i].url, width: width, height: height, path: picArray[i].path, createTime: picArray[i].modificationDate, type: picArray[i].mediaType });
      } else {
        picUrlArray.push({ url: picArray[i].url, path: picArray[i].path, createTime: picArray[i].modificationDate, width: width, height: height, type: picArray[i].mediaType });
      }
      if (showOnlyOne) {
        break;
      }
    }

    let createTime = picUrlArray[currentIndex].createTime;
    let title = picUrlArray[currentIndex].type == 1 ? AlbumHelper.getFileName(false, createTime * 1000) : AlbumHelper.getFileName(true, createTime * 1000);
    this.title = title;
    this.setState({ currentIndex: currentIndex, imageUrlArray: picUrlArray, showLoading: false, items: picArray });
  }

  _onChange(index) {
    // 这里修改标题
    let createTime = this.state.imageUrlArray[index].createTime;
    let title = this.state.imageUrlArray[index].type == 1 ? AlbumHelper.getFileName(false, createTime * 1000) : AlbumHelper.getFileName(true, createTime * 1000);
    this.title = title;
    this.setNavigation();
    this.state.imageUrlArray[index].type == 2 ? this.mMediaTypeIsVideo = true : this.mMediaTypeIsVideo = false;

    this.setState({
      currentIndex: index < this.state.imageUrlArray.length ? index : this.state.imageUrlArray.length - 1
    });

  }

  deleteImgClick() {
    if (Platform.OS == "ios") {
      let currentTime = new Date();
      let milliseconds = currentTime.getTime();
      if (this.deleteThrottleTime == null || (milliseconds - this.deleteThrottleTime >= 1000)) {
        this.deleteThrottleTime = milliseconds;
        this.confirmDelete();
      } else {
        return;
      }
    } else {
      this.setState({
        deleteDialogVisible: true
      });
    }

  }
  // 这里建议不要跳到新的页面就直接有播放按钮
  play() {
    console.log('ImageViewer', ImageViewer.width);
    this.props.navigation.navigate("AlbumVideoViewPage", { url: this.state.imageUrlArray[this.state.currentIndex].url, item: this.state.items[this.state.currentIndex], mCbDeleted: this.mCbDeleted, preOri: this.state.isFullscreen ? "landscape" : "portrait" });
  }
  shareImage() {
    // todo
    if (this.state.imageUrlArray.length > 0) {
      let path = this.state.imageUrlArray[this.state.currentIndex].url;
      Host.ui.openSystemShareWindow(path);// 调用系统的分享界面。
    }
  }

  confirmDelete() {

    if (this.state.imageUrlArray.length > 0) {
      let deleteImageUrl = this.state.imageUrlArray[this.state.currentIndex].url;

      AlbumHelper.checkPermission()
        .then(() => {
          AlbumHelper.deleteAlbumFilesByUrl([deleteImageUrl])
            .then(() => {
              Toast.success("delete_success");
              this.mCbDeleted();
            })
            .catch((error) => {
              if (Platform.OS != "android") {
                return;
              }
              Toast.fail("delete_failed", error);
            });
        })
        .catch((result) => {
          if (result == PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            this.setState({ showPermissionDialog: true });
          } else {
            Toast.success("camera_no_write_permission");
          }
        });
    }
  }

  render() {
    let countStyle = {
      position: 'absolute',
      left: 0,
      right: 0,
      top: 38,
      zIndex: 13,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'transparent'
    };
    let countTextStyle = {
      color: this.state.isFullscreen ? 'white' : 'black',
      fontSize: kIsCN ? 16 : 14,
      backgroundColor: 'transparent',
      textShadowColor: 'rgba(0, 0, 0, 0.3)',
      textShadowOffset: {
        width: 0,
        height: 0.5
      },
      textShadowRadius: 0
    };

    let imgStyle = {
      width: 25,
      height: 25
    };
    if (this.darkMode || this.state.isFullscreen) {
      imgStyle.tintColor = IMG_DARKMODE_TINT;
    }
    let buttonMarginTop = 10;
    let isDark = DarkMode.getColorScheme() == "dark";
    let imgHeight = Math.max(Dimensions.get("window").height, Dimensions.get("window").width) - 250;
    Host.isPad && (imgHeight = imgHeight + 65);// satusbar height
    let bgColor = !this.state.isFullscreen ? "#ffffff" : "black";

    return (

      <View style={{ display: "flex", width: "100%", height: "100%", minHeight: '100%', flexWrap: "nowrap", backgroundColor: '#ffffff' }} >
        <SafeAreaView style={{ backgroundColor: bgColor }}></SafeAreaView>
        {/* <LoadingDialog visible={this.state.showLoadingDialog} message={getString('loading')} onDismiss={() => { this.setState({ showLoadingDialog: true }) }} /> */}

        {
          this.state.isFullscreen ? null : this.renderTitleView()
        }
        <View style={[!this.state.isFullscreen ? { justifyContent: "center", flexGrow: 1, width: "100%" } : { height: "100%", width: "100%" }]}>
          <View style={{ display: "flex", flexDirection: "column", width: "100%", height: "100%", position: "absolute", alignItems: "center", justifyContent: "center" }}>

            <ImageViewer
              style={{ flex: 1, flexGrow: 1, width: '100%' }}
              backgroundColor={this.state.isFullscreen ? "#000000" : '#ffffff'}
              imageUrls={this.state.imageUrlArray}
              key={this.state.imageUrlArray}
              index={this.state.currentIndex}
              saveToLocalByLongPress={false}
              doubleClickInterval={500}
              loadingRender={() => {
                return <ActivityIndicator size="large" color={"black"} />;
              }}
              onChange={this._onChange.bind(this)}
              onClick={this.mMediaTypeIsVideo ? this.play.bind(this) : null}
              renderImage={(prop) => {
                // console.log(prop.source.uri,prop.source.uri.includes('video'),'prop')
                // 这里是想要实现滑动预览时页面播放视频
                if (!prop.source.uri.includes('video')) {
                  return (
                    <View style={{ flexGrow: 1, width: '100%' }}>
                      <Image
                        {...prop}
                        resizeMode={"contain"}
                      />
                      {this.state.isFullscreen ? null :
                        <View style={{ zIndex: 16, position: 'absolute', width: 40, height: 40, right: 0, bottom: 0 }}>
                          <ImageButton
                            onPress={() => { this.toLandscape(); }}
                            style={{ width: 40, height: 40 }}
                            source={require('../../Resources/Images/icon_camera_fullscreen_playback.png')}
                            highlightedSource={require('../../Resources/Images/icon_camera_fullscreen_playback.png')}
                            accessibilityLabel={DescriptionConstants.fullScreen }
                          />
                        </View>}
                    </View>
                  );
                } else {
                  return (
                    <View style={{ flexGrow: 1,
                      justifyContent: 'center',
                      alignItems: 'center'
                    }}>
                      <Image
                        {...prop}
                        resizeMode={"contain"}

                      />
                      {this.state.isFullscreen ? null :
                        <View style={{ zIndex: 16, position: 'absolute', width: 40, height: 40, right: 0, bottom: 0 }}>
                          <ImageButton
                            onPress={() => { this.toLandscape(); }}
                            style={{ width: 40, height: 40 }}
                            source={require('../../Resources/Images/icon_camera_fullscreen_playback.png')}
                            highlightedSource={require('../../Resources/Images/icon_camera_fullscreen_playback.png')}
                            accessibilityLabel={DescriptionConstants.fullScreen}
                          />
                        </View>}
                      <TouchableOpacity
                        style={{ zIndex: 17, position: 'absolute', height: 50, width: 50 }}
                        onPress={() => this.play()}
                        accessibilityLabel={DescriptionConstants.lc_1}
                      >
                        <Image style={{ height: 50, width: 50 }} source={require("../../Resources/Images/icon_album_video_play_nor.png")} />
                      </TouchableOpacity>
                    </View>
                  );
                }

              }}
              renderIndicator={(index, count) => {
                console.log("render indicator:", index, count);
                return React.createElement(
                  View,
                  { style: countStyle },
                  React.createElement(Text, { style: countTextStyle }, `${ index }/${ count }`)
                );
              }}
            />
            {this.state.isFullscreen ?
              <View style={{ zIndex: 16, position: 'absolute', width: 40, height: 40, right: 0, bottom: 0 }}>
                <ImageButton
                  onPress={() => { this.toPortrait(); }}
                  style={{ width: 40, height: 40 }}
                  source={require('../../Resources/Images/icon_camera_fullscreen_exit_playback.png')}
                  highlightedSource={require('../../Resources/Images/icon_camera_fullscreen_exit_playback.png')}
                  accessibilityLabel={DescriptionConstants.lc_12}
                />
              </View> : null}

          </View>
        </View>

        {/*  加position来解决 */}
        {/* <View style={{ position:'absolute', width: "100%", height: 60, display: "flex", flexDirection: "row", justifyContent: "center", alignItems: "center" , bottom:10 , backgroundColor:'pink'}}> */}
        {/* {this.renderBottomView()} */}
        {this.renderHeaderControlBar(buttonMarginTop, imgStyle)}

        {this.renderDialog()}
        {this._renderPermissionDialog()}
        {this.renderLoadingView()}
        <SafeAreaView></SafeAreaView>
      </View>
    );
  }

  renderHeaderControlBar(buttonMarginTop, imgStyle) {
    let useBlack = true;
    if (this.darkMode || this.state.isFullscreen) {
      useBlack = false;
    }
    let StatusBarheight = null;
    if (Platform.OS === "android") {
      StatusBarheight = StatusBar.currentHeight;
      if (StatusBarheight <= 0) {
        StatusBarheight = 15;
      }
      // -16 因为返回按钮的图片留白太大
    } else {
      StatusBarheight = StatusBarUtil._getInset("top") - 16;
    }
    return <LinearGradient
      colors={this.state.isFullscreen ? ['#00000099', '#00000000'] : ["#00000000", "#00000000"]}
      style={this.state.isFullscreen ? { position: "absolute", display: "flex", flexDirection: "row", width: "100%", paddingHorizontal: StatusBarheight, height: 69, alignItems: "center" } : { width: "100%", height: 69, display: "flex", flexDirection: "row", justifyContent: "center", alignItems: "center" }}>

      {this.state.isFullscreen ?
        <TouchableOpacity
          style={{ width: 50, display: "flex", alignItems: "center" }}
          onPress={() => { this.onBack(); }}>
          <Image
            style={{ width: 35, height: 35, transform: [{ scaleX: Util.isRtl() ? -1 : 1 }] }}
            source={require("../../Resources/Images/icon_back_black_nor_dark.png")}
            accessibilityLabel={DescriptionConstants.sd_1}
          />
        </TouchableOpacity> : null}

      <View style={this.state.isFullscreen ? { display: "flex", flexDirection: "row", flex: 1, height: 69, justifyContent: "flex-end", alignItems: "center" } : { width: "100%", height: 69, display: "flex", flexDirection: "row", justifyContent: "center", alignItems: "center" }}>

        <TouchableOpacity
          // accessibilityLabel={DescriptionConstants.sz_8_20}
          style={{ display: "flex", alignItems: "center" }}
          accessibilityRole={AccessibilityRoles.button}
          onPress={() => { this.shareImage(); }}>
          <Image
            style={imgStyle}
            source={useBlack ?
              require("../../Resources/Images/camera_icon_loc_pic_share.png") :
              require("../../Resources/Images/camera_icon_loc_pic_share_white.png")}
            // accessibilityLabel={DescriptionConstants.lc_13}
          />
          {
            this.state.isFullscreen ? null :
              <Text style={{ fontSize: kIsCN ? 11 : 9, color: 'black', marginTop: 3 }}>{LocalizedStrings['share_files']}</Text>
          }

        </TouchableOpacity>
        <TouchableOpacity
          style={{ marginLeft: 30, display: "flex", alignItems: "center" }}
          accessibilityRole={AccessibilityRoles.button}
          onPress={() => this.deleteImgClick()} >
          <Image
            style={imgStyle}
            source={useBlack ? require("../../Resources/Images/camera_icon_loc_pic_delete.png") : require("../../Resources/Images/camera_icon_loc_pic_delete_white.png")}
            // accessibilityLabel={DescriptionConstants.lc_14}
          />
          {
            this.state.isFullscreen ? null :
              <Text style={{ fontSize: kIsCN ? 11 : 9, color: 'black', marginTop: 3 }}>{LocalizedStrings['delete_files']}</Text>

          }
        </TouchableOpacity>
      </View>

    </LinearGradient>;
  }
  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 存储卡/相册
    // status == 1 麦克风
    //
    let message = "";
    message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);

    return (
      <CommonMsgDialog
        title={message}
        confirmText={LocalizedStrings["setting"]}
        cancelText={LocalizedStrings["action_cancle"]}
        onConfirmPress={(e) => {
          Host.ui.openTerminalDeviceSettingPage(1);
          this.setState({ showPermissionDialog: false });
        }}
        onCancelPress={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog} />
    );

  }

  renderTitleView() {
    // first change statusBar
    DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');
    if (Platform.OS == 'android') {
      // StatusBar.setTranslucent(true); // 测试过的机型几乎都无效：华为荣耀V9，红米Note4X，小米Mix2
    }

    // second get statusBar height;
    let containerHeight = StatusBar.currentHeight || 0;
    containerHeight += 52;
    let statusBarHeight = StatusBar.currentHeight;

    const textContainerStyle = {
      flexGrow: 1,
      alignSelf: 'stretch', // 控制自己填充满父类的高度
      display: "flex",
      flexDirection: "column",
      justifyContent: 'center',
      alignItems: 'stretch', // 控制子类填充满本身的宽度
      marginHorizontal: 5
    };

    const titleTextStyle = {
      fontSize: kIsCN ? 16 : 14,
      // lineHeight: 22,
      textAlignVertical: 'center',
      color: "#000000CC",
      textAlign: 'center'
    };

    let imgStyle = {
      width: iconSize,
      height: iconSize,
      position: "absolute",
      transform: [{ scaleX: Util.isRtl() ? -1 : 1 }]
    };
    if (this.darkMode || this.state.isFullscreen) {
      imgStyle.tintColor = IMG_DARKMODE_TINT;
    }

    return (
      <View style={{ width: "100%", height: containerHeight, display: "flex", flexDirection: "row", flexWrap: "nowrap", alignItems: "center", paddingTop: statusBarHeight }}>
        <View
          style={{ width: iconSize, height: iconSize, position: "relative" }}>

          <ImageButton
            style={imgStyle}
            source={require("../../Resources/Images/icon_back_black.png")}
            highlightedSource={require("../../Resources/Images/icon_back_black.png")}
            onPress={() => this.onBack()}
            accessibilityLabel={DescriptionConstants.sd_1}
          />
        </View>

        <View style={textContainerStyle}  >
          <Text
            numberOfLines={1}
            style={[titleTextStyle]}
          >
            {this.title}
          </Text>

        </View>

        <View
          style={{ width: iconSize, height: iconSize, position: "relative" }}>
        </View>
      </View>
    );
  }

  renderDialog() {
    return (
      <MessageDialog
        visible={this.state.deleteDialogVisible}
        title={this.dialogDeleteContent}
        cancel={LocalizedStrings["action_cancle"]}
        confirm={LocalizedStrings["delete_confirm"]}
        onCancel={(e) => {
          console.log('onCancel', e);
        }}
        cancelable={true}
        onConfirm={(e) => {
          console.log('onConfirm', e);
          this.confirmDelete();
        }}
        onDismiss={() => {
          console.log('onDismiss');
          this.setState({ deleteDialogVisible: false });
        }}
      />
    );
  }
  renderLoadingView() {
    if (!this.state.showLoading) {
      return;
    }
    let isDark = DarkMode.getColorScheme() === "dark";
    return (
      <View style={{ position: "absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}>
        <ActivityIndicator
          style={{ width: 54, height: 54 }}
          color={isDark ? "xm#ffffff" : "xm#000000"}
          size={"large"}
        />
        <Text style={{ marginTop: 10, fontSize: kIsCN ? 12 : 10, color: "#000000" }}>
          {LocalizedStrings["camera_loading"]}
        </Text>
      </View>
    );
  }

  mCbDeleted = () => {
    let deletedUrl = this.state.imageUrlArray[this.state.currentIndex].url;
    let i = -1;
    let arr = this.state.imageUrlArray.filter((item) => {
      i = i + 1;
      return deletedUrl == item.url ? false : true;
    });
    i = -1;
    let nItems = this.state.items.filter((item) => {
      i = i + 1;
      return deletedUrl == item.url ? false : true;
    });

    let len = this.state.imageUrlArray.length - 1;
    let currentIndex = this.state.currentIndex;

    console.log(this.state.currentIndex, len, 'this.state.currentIndex', 'len');
    if (len > 0) {
      //从数据集里删除数据后，要更新currentIndex;

      if (this.state.currentIndex > (len - 1)) {
        currentIndex = len -1;
      }

      let createTime = arr[currentIndex].createTime;
      let title = arr[currentIndex].type == 1 ? AlbumHelper.getFileName(false, createTime * 1000) : AlbumHelper.getFileName(true, createTime * 1000);
      this.title = title;
      this.setNavigation();
      arr[currentIndex].type == 2 ? this.mMediaTypeIsVideo = true : this.mMediaTypeIsVideo = false;
      this.setState({ currentIndex: currentIndex, imageUrlArray: arr, showLoading: false, items: nItems });
    } else {
      this.onBack(true, true);
    }

    if (this.props && this.props.navigation.state.params.mCbDeleted) {
      this.props.navigation.state.params.mCbDeleted(deletedUrl);
    }
  }

  restoreOri() {
    console.log(TAG, "restoreOri");

    if ("PORTRAIT" === this.mOri) {
      this.toPortrait();
    } else {
      this.toLandscape();
    }
  }

  toPortrait() {
    if (this.mRestorePreOrientationSwitch) return;
    console.log(TAG, "toPortrait");
    this.mOri = "PORTRAIT";
    Orientation.lockToPortrait();
    StatusBar.setHidden(false);
  }

  toLandscape() {
    StatusBar.setHidden(true);
    console.log(TAG, "toLandscape");
    this.mOri = "LANDSCAPE";
    if (Platform.OS === "android") {
      Orientation.lockToLandscapeLeft();
    } else {
      if (Platform.OS == 'ios' && Host.isPad) {
        Service.miotcamera.enterFullscreenForPad(true);
      } else {
        Orientation.lockToLandscapeRight();
      }
    }
    StatusBar.setHidden(true);

  }

  _orientationListener = (orientation) => {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    LogUtil.logOnAll(TAG, `device orientation changed :${ orientation } want ${ this.mOri } fullscreen ${ this.state.isFullscreen }`);
    if ((Platform.OS == "ios" || (Host.isAndroid && Host.isPad)) && ((this.mOri === 'LANDSCAPE' && this.state.isFullscreen) || (this.mOri === 'PORTRAIT' && !this.state.isFullscreen))) {
      return;
    }
    if (this.mOri === orientation) {
      if (orientation === 'LANDSCAPE') {
        // do something with landscape layout
        this.setState({ isFullscreen: true });
        if (Host.isPad) {
          Service.miotcamera.enterFullscreenForPad(true);
        }
      } else {
        // do something with portrait layout
        this.setState((state) => { return { isFullscreen: false }; }, () => {
          if (this.isToShare) {
            let path = this.state.videoPath.url;
            Host.ui.openSystemShareWindow(path);
            this.isToShare = false;
          }
        });
        if (Host.isPad) {
          Service.miotcamera.enterFullscreenForPad(false);
        }
      }
    } else {
      // ios need restore for next lock
      this.restoreOri();
    }
  }

//   mRestorePreOrientation = () => {
//     if (this.props.navigation.state.params.preOri) {
//       if (this.props.navigation.state.params.preOri == "landscape") {
//         if (Platform.OS == 'android') {
//           Orientation.lockToLandscapeLeft();
//         } else {
//           Orientation.lockToLandscapeRight();
//         }
//         Service.miotcamera.enterFullscreenForPad(true);
//       } else {
//         CameraConfig.lockToPortrait();
//       }
//     }
//   }
}