import React from 'react';
import { BaseStyles } from "../BasePage";
import Util from "../util2/Util";
import { StyleSheet, View, Text, Image, TouchableOpacity } from 'react-native';
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import { DescriptionConstants } from '../Constants';
export const CardHeight = 88;
export const CardMB = 12;
import { SCREEN_WIDTH } from '../util2/Const';
import CameraConfig from '../util/CameraConfig';
import { Device } from 'miot';

const TAG = "EventEditSingleCard";
export default class EventEditSingleCard extends React.Component {

  render() {
    let imgStoreUrl = this.props.item.data[0].imgStoreUrl;
    let imgSource = imgStoreUrl != null ? { uri: `file://${imgStoreUrl}` } : null;
    const type = this.props.type;
    // let iconSource = Util.getIconFromType(this.props.item.type, this.props.item.faceInfo ? this.props.item.faceInfo.name : null);
    let iconSource = this.props.item.selected?require("../../Resources/Images/icon_selected.png"):require("../../Resources/Images/icon_unselected.png")
    let actColor = Util.getActiveColorFromType(this.props.item.type);
    // console.log("EVENTCARD render", this.props.item.type);
    // nomal 0 read 1 active 2
    let styleIdx = this.props.item.data[0].isRead ? 1 : 0;
    let timeStyle = [[BaseStyles.text12, { marginTop: 2, fontWeight: "bold", color: "#7F7F7F" }],
    [BaseStyles.text12, { marginTop: 2, color: "#7F7F7F", fontWeight: "bold" }],
    [BaseStyles.text12, { marginTop: 2, color: actColor, fontWeight: "bold" }]
    ];

    let descStyle = [[BaseStyles.text16, { fontWeight: "bold" }],
    [BaseStyles.text16, { color: "#7F7F7F", fontWeight: "bold" }],
    [BaseStyles.text16, { color: actColor, fontWeight: "bold" }]
    ];
    // console.log(TAG, "render", this.props.item.desc, "evt", this.props.item.eventTime, "type", this.props.item.type);
    let textW = SCREEN_WIDTH - 176 - 30;
    let showImg = true;
    if (typeof(this.props.item.data[0].isShowImg) === 'boolean') {
      showImg = this.props.item.data[0].isShowImg;
    }


    return (
      <TouchableOpacity style={[BaseStyles.row, { height: CardHeight, paddingLeft: 15, marginBottom: CardMB, borderRadius: 12, backgroundColor: "#ffffff" }]}
                        activeOpacity={1}
        onPress={() => {
          this.props.cardPressed(this.props.item);
          }}>
        <Image
          style={[BaseStyles.icon22, { marginRight: 13 }]}
          source={iconSource}
          accessibilityLabel={DescriptionConstants.kj_1_9}
        >

        </Image>
        <View style={[BaseStyles.column, { flex: 1, alignItems: "flex-start", justifyContent: "space-between", marginRight: 20 }]} accessibilityLabel={ this.props.item.data[0].desc.trim() + this.props.item.data[0].eventTime }>
          <Text
            style={descStyle[styleIdx]}
            numberOfLines={3}
            ellipsizeMode={"tail"}
          >{this.props.item.data[0].desc.trim()}
          </Text>
          <Text
            style={timeStyle[styleIdx]}>
            {this.props.item.data[0].eventTime}
          </Text>
        </View>

        {
          showImg ? <View styles={styles.imgView}>
            {
              imgSource?<Image
              accessibilityLabel={!this.props.isPlaying ? DescriptionConstants.kj_2_17 : DescriptionConstants.kj_2_18}
              style={[styles.imgView, {  alignSelf: "center", marginRight: 12, resizeMode: "stretch" }]} source={imgSource} />:<View style={[styles.imgView, {  alignSelf: "center", marginRight: 12, resizeMode: "stretch" ,backgroundColor: "#EEEEEE",borderRadius: 9}]}/>
            }

        </View> : null

        }
      </TouchableOpacity>
    );
  }
}

const styles = StyleSheet.create({
  timeView: {
    flexDirection: 'row',
    alignItems: 'flex-start'
  },
  pressedTimeLabel: {
    color: 'gray'
  },
  imgView: {
    width: 106,
    height: 64,
    borderRadius: 9
  }
});
