import React from 'react';
import PropTypes from 'prop-types';
import { ViewPropTypes, View, Dimensions, ART, Animated, Easing, TouchableOpacity, Image } from 'react-native';

const {
  Surface, Shape, Path
} = ART;

const LONG_PRESS_MOVE = 30000;
const TIME_LINE_MINITUES = 5;
const MINUTES = 60000;
const MAX_SCALE = 10;
const MIN_SCALE = 0.5;

export default class ScaleableTimelineViewNew extends React.Component {


  static propTypes = {

    onCenterValueChanged: PropTypes.func, // 进度条发生了改变，通知给外部的回调。
    ...ViewPropTypes
  };

  state = {
    showTimelineView: false, // 是否显示时间轴view
    centerValue: 0 // 中间值

  }

  constructor(props) {
    super(props);
    this.topH = 2;
    this.timeBarW = 10;
    this.mOffsetCurrrentTime = 0;
    this.mCurrentTime = 0;
    this.mTimeItems = [];
    this.mOffsetPos = 0;
    this.mTouchStartX = 0;
    this.mWidthPer5MinutesBase = 15;
    this.mWidthScaleFactors = 1.0;
    this.mWidthPer5Minutes = this.mWidthPer5MinutesBase * this.mWidthScaleFactors;


    this.mHalfW = 0;
    this.mViewHeight = 0;
    this.mViewWidth = 0;

    this.isScaleBegin = false;
    this.mBeforeScaleTime = 0;

    this.mTimelineColor = "#a6b0c3";// 时间轴的颜色
    this.mTimelinPointerRes = require("../../Resources/Images/timeline_center_pointer.png");// 正中间的红色指示线 
    this.mTimelineSelectedColor = "#FF5F0026";// 有视频的颜色
    this.mTimelineMotionColor = "#EA751E50";// 有物体移动的颜色

    this.mBottomPadding = 0;
    this.mLastAndPreviousButtonWidth = 30;
    this.mLeftButtonRes = require("../../Resources/Images/button_prev_nor.png");
    this.mRightButtonRes = require("../../Resources/Images/button_next_nor.png");
    this.misPress = false;


    // todo 缩放等到后面再做。
    // 滑动的也等到后面吧

  }

  // 同步当前事件 ？？？干啥的
  syncCurrentTime(currentTime) {

  }

  // 控制时间轴滚动到该位置
  scrollToTimestamp(timestamp) { // 开放给外面的

  }

  _setPlayTime(playTime) { // 开放给内部的，内部的滚动需要同步给外面

  }

  _setJustPlayTime(playTime) { // 我也不知道干啥的，跟前面的diamagnetic有什么差别？

  }

  getCurrentSelectedTime() { // 开放给外部的，获取当前选中事件

  }

  _getTime(position) { // 获取指定位置对应的时间 position:屏幕上的位置

  }

  _getPosition(time) { // 根据时间得到在屏幕上的位置

  }

  _onLayout(event) {
    console.log(event.nativeEvent);// 宽高等信息都在这里。
    this.mHalfW = event.nativeEvent.layout.width;
    this.height = event.nativeEvent.layout.height;
  }

  render() {
    return (
      <View
        style={styles.container}
        onLayout={(event) => { // 获取宽度
          this._onLayout(event);
        }}
      >
        <TouchableOpacity
          style={{ width: 30, height: 54 }}
          onPress={() => this.onLeftPressed()}
          onPressIn={() => this.onLeftPressIn()}
          onPressOut={() => this.onLeftPressOut()}
          underlayColor={"#55555522"}
          activeOpacity={0.88}
        >
          <Image
            style={{ width: "100%", height: "100%" }}
            source={require("../../Resources/Images/button_prev_nor.png")}
          />
        </TouchableOpacity>

        <View style={{
          flexGrow: 1,
          height: "100%"
        }}>

          <Surface
            height={waveHeight}
            width={waveWidth + 8 * tempX1}
          >
            <Shape d={_path1} stroke={'#6495ED'} strokeWidth={0.5} />
          </Surface>
        </View>


        <TouchableOpacity
          style={{ width: 30, height: 54 }}
          onPress={() => this.onRightPressed()}
          onPressIn={() => this.onRightPressIn()}
          onPressOut={() => this.onRightPressOut()}
          underlayColor={"#55555522"}
          activeOpacity={0.88}
        >
          <Image
            style={{ width: "100%", height: "100%" }}
            source={require("../../Resources/Images/button_next_nor.png")}
          />
        </TouchableOpacity>

      </View>
    );
  }

}

export const styles = StyleSheet.create({
  container: {
    width: "100%",
    height: 54,
    borderLeftWidth: 0,
    borderRigthWidth: 0,
    borderTopWidth: 0.5,
    borderBottomWidth: 0.5,
    borderColor: "#E6E6E6",
    display: "flex",
    flexDirection: "row",
    alignItems: "center"
  }
});
