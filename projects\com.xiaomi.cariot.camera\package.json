{"name": "project-com-xiaomi-cariot-camera", "version": "1.0.28", "scripts": {"start": "node ../../bin/runProject.js", "postinstall": "patch-package"}, "dependencies": {"base64-js": "^1.2", "dayjs": "^1.10.5", "micariot-ui-sdk": "file:../../micariot-ui-sdk", "patch-package": "^6.4.7", "react-native-canvas": "^0.1.37", "react-native-image-pan-zoom": "2.1.12", "react-native-image-zoom-viewer": "^2.2.27", "react-native-parsed-text": "0.0.22", "react-native-root-toast": "^3.2.0", "react-native-scrollable-tab-view": "^1.0.0"}}