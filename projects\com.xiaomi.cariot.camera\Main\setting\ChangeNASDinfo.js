'use strict';

import React from 'react';
import { StyleSheet, ScrollView, View, Text, TextInput, Button, TouchableOpacity } from 'react-native';
import RPC from "../util/RPC";
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import NavigationBar from "miot/ui/NavigationBar";
import Toast from '../components/Toast';
import { DarkMode } from 'miot';
import LogUtil from '../util/LogUtil';
import Host from 'miot/Host';
import InputView from "../widget/InputView";
import RoundedButtonView from "../widget/RoundedButtonView";
export default class ChangeNASDinfo extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
      deviceName: null,
      userName: null,
      userPassword: null,
      isInputDeviceNameInvalid: false,
      inputDeviceNameErrorHint: null,
      isInputNameInvalid: false,
      inputNameErrorHint: null,
      inputNameBottomHint: LocalizedStrings.nas_input_character_hint,
      isInputPsdInvalid: false,
      inputPsdErrorHint: null,
      inputPsdBottomHint: LocalizedStrings.nas_input_character_hint
    };

  }
  render() {
    return (
      <View style={styles.container}>
        <ScrollView showsVerticalScrollIndicator={false}
                    scrollEventThrottle={1}
                    onScroll={this.scrollViewScroll}>

          <View style={{ flexDirection: "row", flexWrap: "wrap" }} key={'0'}>
            <Text style={{ fontSize: 30, color: "rgba(0, 0, 0, 0.80)", fontWeight: "300", position: "relative", marginLeft: 25, marginTop: 3, marginBottom: 23,fontFamily:'MI-LANTING--GBK1-Light' }}>
              { LocalizedStrings['change_nas_storage']}</Text>
          </View>
        {this._renderChangeStorageView()}
        </ScrollView>

        <RoundedButtonView buttonText={LocalizedStrings.nas_storage_fin}
                           disabled={this.state.isInputDeviceNameInvalid || this.state.isInputNameInvalid || this.state.isInputPsdInvalid}
                           buttonStyle={{marginHorizontal: 27, marginBottom: Host.isIphoneXSeries ? 35 : 27, height: 45, backgroundColor: 'rgba(50, 186, 192, 1)'}}
                           onPress={() => {
                             this._onComplete()
                           }}/>
      </View>
    );
  }

// 监听列表滚动事件
  scrollViewScroll = (event) => {
    const y = event.nativeEvent.contentOffset.y;
    let flag = y > 28;
    if (this.showTitle == flag) {
      return;
    }
    if (flag) {
      this.showTitle = true;
      this.props.navigation.setParams({ title: LocalizedStrings['change_nas_storage'] });
    } else {
      this.showTitle = false;
      this.props.navigation.setParams({ title: "" });
    }
  };


  componentDidMount() {
    let { deviceMessage } = this.props.navigation.state.params;
    this.props.navigation.setParams({
      title: '',
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
    this.willFocusSubscription = this.props.navigation.addListener(
      'didFocus', () => {
        this._renderChangeStorageView();
      }
    );

  }
  componentWillUnmount() {
    this.willFocusSubscription.remove();
  }
  _renderChangeStorageView() {
    let { deviceMessage } = this.props.navigation.state.params;
    return (
        <View>

          <Text style={styles.inputTitleStyle}>
            {LocalizedStrings['nas_storage_name']}
          </Text>
          <InputView
              type={InputView.TYPE.DELETE}
              style={{marginHorizontal: 24,marginTop:4,marginBottom:17}}
              placeholder={LocalizedStrings['long_time_name']}
              defaultValue={deviceMessage.share.name}
              onChangeText={text => this._onChangeDeviceNameText(text)}
              textInputProps={{autoFocus: false}}
              isCorrect={!this.state.isInputDeviceNameInvalid}
              errorHint={this.state.inputDeviceNameErrorHint}
          />

          <Text style={styles.inputTitleStyle}>
            {LocalizedStrings['nas_user_setting']}
          </Text>
          <InputView
              type={InputView.TYPE.DELETE}
              style={{marginHorizontal: 24,marginVertical:4}}
              placeholder={LocalizedStrings['nas_storage_user']}
              defaultValue={deviceMessage.share.user}
              onChangeText={text => this._onChangeUserNameText(text)}
              textInputProps={{autoFocus: false}}
              isCorrect={!this.state.isInputNameInvalid}
              errorHint={this.state.inputNameErrorHint}
              inputBottomHint={this.state.inputNameBottomHint}
          />

          <InputView
              type={InputView.TYPE.DELETE}
              style={{marginHorizontal: 24,marginVertical:4}}
              placeholder={LocalizedStrings['nas_storage_pas']}
              defaultValue={deviceMessage.share.pass}
              onChangeText={text => this._onChangeUserPasswordText(text)}
              textInputProps={{autoFocus: false}}
              isCorrect={!this.state.isInputPsdInvalid}
              errorHint={this.state.inputPsdErrorHint}
              inputBottomHint={this.state.inputPsdBottomHint}
          />


          {/*<View>
            <Text style={styles.tipsBack}>
              {LocalizedStrings['nas_storage_tip']}
            </Text>
          </View>*/}


        </View>
    )
  }
  _onChangeDeviceNameText(result) {
    let length = result.length;
    if (length <= 0) {
      this.setState({ isInputDeviceNameInvalid: true, inputDeviceNameErrorHint: LocalizedStrings["add_feature_empty_tips"] });
    } else {
      this.setState({
        isInputDeviceNameInvalid: false,
        inputDeviceNameErrorHint: null
      });
    }
    this.setState({
      deviceName: result
    });
  }
  _onChangeUserNameText(result) {
    this.setState({
      isInputNameInvalid: false,
      inputNameErrorHint: null,
      // inputNameBottomHint: test ? null: LocalizedStrings.nas_input_character_hint
      inputNameBottomHint: null,
      userName: result
    });
  }
  _onChangeUserPasswordText(result) {
    this.setState({
      isInputPsdInvalid: false,
      inputPsdErrorHint: null,
      // inputPsdBottomHint: test ? null : LocalizedStrings.nas_input_character_hint
      inputPsdBottomHint:  null,
      userPassword: result
    });
  }
  _onComplete() {
    // 这里要判断是修改还是添加还要看java中是怎么区分修改还是添加
    // 这里要重新获取整体的数据 然后改一下名字
    Toast.loading('c_setting');
    let { deviceMessage } = this.props.navigation.state.params;
    deviceMessage.share.name = this.state.deviceName != null ? this.state.deviceName : deviceMessage.share.name;
    deviceMessage.share.user = this.state.userName != null ? this.state.userName : deviceMessage.share.user;
    deviceMessage.share.pass = this.state.userPassword != null ? this.state.userPassword : deviceMessage.share.pass;
    LogUtil.logOnAll("nas_set_config==", JSON.stringify(deviceMessage));
    RPC.callMethod("nas_set_config", deviceMessage).then((res) => {
      LogUtil.logOnAll("nas_set_config res==", JSON.stringify(res));
      if (res.result[0] == 'OK') {
        Toast.success('save_success');
        this.props.navigation.goBack();
      } else {
        Toast.fail('c_set_fail');
      }
    }).catch((err) => {
      LogUtil.logOnAll("changenas", `nas_set_config failed${ JSON.stringify(err) }`);

      Toast.fail('c_set_fail', err);
    });


  }

  isEmojiCharacterV2(codePoint) {
    return !((codePoint == 0x0) ||
        (codePoint == 0x9) ||
        (codePoint == 0xA) ||
        (codePoint == 0xD) ||
        ((codePoint >= 0x20) && (codePoint <= 0xD7FF)) ||
        ((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) ||
        ((codePoint >= 0x10000))) ||
        (codePoint == 0xa9 || codePoint == 0xae || codePoint == 0x2122 ||
            codePoint == 0x3030 || (codePoint >= 0x25b6 && codePoint <= 0x27bf) ||
            codePoint == 0x2328 || (codePoint >= 0x23e9 && codePoint <= 0x23fa))
        || ((codePoint >= 0x1F000 && codePoint <= 0x1FFFF))
        || ((codePoint >= 0x2702) && (codePoint <= 0x27B0))
        || ((codePoint >= 0x1F601) && (codePoint <= 0x1F64F));
  }


}
export const styles = StyleSheet.create({
  container: {
    backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : '#fff',
    flexDirection: 'column',
    height: '100%',
    width: '100%',
  },

  inputStyle: {
    width: '100%',
    // height: 40,
    borderBottomColor: '#e5e5e5',
    borderBottomWidth: 1,
    paddingTop: Host.isIOS ? 15 : 0,
    paddingBottom: Host.isIOS ? 15 : 0,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center'
  },
  inputTitleStyle: {
    marginHorizontal:27,
    marginVertical:9,
    fontSize:12,
    color:"#8C93B0",
    fontWeight:'400'
  },
  tipsBack: {
    paddingHorizontal:35,
    color: 'rgba(0,0,0,0.4)',
    fontSize: 12,
    marginTop: 5
  }
});
