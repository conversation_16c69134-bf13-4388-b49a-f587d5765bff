import { Package } from 'miot';

import CarIndex from "./Main/CarIndex";
import PhoneIndex from "./Main/PhoneIndex";


switch (Package.entryInfo.mobileType) {
  case 'car':
    // 车机插件进入
    Package.entry(CarIndex, () => {
      console.disableYellowBox = true;
    });
    break;
  default:
    Package.entry(PhoneIndex, () => {
      console.disableYellowBox = true;
    });
    break;
}


// switch (Package.entrance) {
//   // Entrance.Scene 表示扩展程序进入智能场景的页面；为Entrance.Main 时，表示进入扩展程序的首页
//   case Entrance.Scene:
//     Package.entry(SceneMain, () => {
//       console.disableYellowBox = true;
//     });
//     break;
//   default:
//     TrackConnectionHelper.clear();
//     TrackConnectionHelper.trackEnterPlugin();
//
//     Package.entry(App, () => {
//       console.disableYellowBox = true;
//
//     });
//     break;
// }

