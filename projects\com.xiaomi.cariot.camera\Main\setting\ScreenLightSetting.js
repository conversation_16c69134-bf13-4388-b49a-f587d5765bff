'use strict';

import { DarkMode } from "miot";
import React from 'react';
import { ScrollView, Image, Text, View, TouchableWithoutFeedback } from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import Toast from '../components/Toast';
import NavigationBar from "miot/ui/NavigationBar";
import ChoiceItemCustom from "../ui/ChoiceItemCustom";
import Util from "../util2/Util";
import SlideGear from "../ui/SlideGear";
import BaseSettingPage from "../BaseSettingPage";
import AlarmUtilV2, {
  SCREEN_SETTING_BRIGHTNESS_PIID,
  SCREEN_SETTING_DISPLAY_STATE_PIID,
  SCREEN_SETTING_SIID
} from "../util/AlarmUtilV2";

export default class ScreenLightSetting extends BaseSettingPage {

  constructor(props, context) {
    super(props, context);
    this.options = [25,50,75,100];
    const mode = this.props.navigation.getParam("screenLightMode") ? this.props.navigation.getParam("screenLightMode") : 0;
    const brightValue = this.props.navigation.getParam("screenLightValue") ? this.props.navigation.getParam("screenLightValue") : 0;
    const index = this.options.indexOf(brightValue) == -1 ? 0 : this.options.indexOf(brightValue);
    this.brightCallback = this.props.navigation.getParam("selectScreenLightCallBack");
    this.state = {
      glimmerColor: false,
      screenLightValue: mode,
      screenValueIndex: index
    };

  }

  getTitle() {
    return LocalizedStrings['cs_screen_light'];
  }

  renderSettingContent() {
    let darkMode = Util.isDark();
    let detectionDistanceValueArray = [1, 2, 3, 4];

    return (
      <View style={ styles.container }>
          <View
            style={ styles.featureSetting }
            key={ 5 }
          >

            <ChoiceItemCustom
              checked={ this.state.screenLightValue == 0 }
              containerStyle={ {
                display: "flex",
                height: 70,
                marginHorizontal: 22,
                marginTop: 20,
                marginVertical: 6,
                justifyContent: 'center'
              } }
              selectedIconLeft={ require("../../Resources/Images/icon_smart_monitor_tick.png") }
              title={ LocalizedStrings['camera_quality_auto'] }
              subtitle={ LocalizedStrings['screen_light_auto_desc'] }
              onlyChecked={true}
              onValueChange={ (value) => {
                console.log("=========value",value)
                value && this._onSelectedItem(0);
              } }
            />

            <TouchableWithoutFeedback
              // onPress={ () => this.state.screenLightValue != 0 ? null : this._onSelectedItem(1) }
              onPress={ () => this._onSelectedItem(1) }
              // disabled={ this.state.screenLightValue != 0 }
            >
              <View
                style={ [styles.singleChoiceItemContainer, { backgroundColor: this.state.screenLightValue != 0 ? darkMode ? "#25A9AF4D" : "#32BAC01A" : darkMode ? "xm#FFFFFF1F" : "#0000000F" }, {
                  marginTop: 12,
                  marginHorizontal: 22,
                  borderRadius: 10
                }] }>
                <View style={ { margin: 15, flexDirection: 'row', alignItems: "center" } }>
                  <Image
                    style={ { width: 13, height: 11 } }
                    source={ this.state.screenLightValue != 0 ? require("../../Resources/Images/icon_smart_monitor_tick.png") : null }/>
                  <View style={ { marginLeft: 10 } }>
                    <Text
                      numberOfssLines={ 3 }
                      style={ {
                        lineHeight: 22,
                        fontSize: 15,
                        fontWeight: "bold",
                        color: this.state.screenLightValue != 0 ? darkMode ? "#25A9AF" : "#32BAC0" : darkMode ? "#FFFFFFE5" : "#000000"
                      } }>{ LocalizedStrings['alarm_time_user'] }</Text>
                    {/*<Text numberOfssLines={6}*/ }
                    {/*      style={{ lineHeight: 18, fontSize: 13, color: this.state.screenLightValue != 0 ? darkMode ? "#25A9AF" : "#32BAC0" : darkMode ? "#FFFFFF80" : "#00000099" }}>{subtitle}</Text>*/ }
                  </View>
                </View>

                <SlideGear
                  options={ [0, 1, 2, 3] }
                  containerStyle={ { marginBottom: 5, marginLeft: 38, marginRight: 35, height: 30 } }
                  maximumTrackTintColor={ DarkMode.getColorScheme() == 'dark' ? 'xm#2D2D2D' : "#E2E2E2" }
                  minimumTrackTintColor={ this.state.screenLightValue == 0 ? DarkMode.getColorScheme() == 'dark' ? 'xm#3A3A3A': '#D4D4D4' : "#32BAC0" }
                  type={SlideGear.TYPE.RECTANGLE}
                  blockStyle={{backgroundColor: this.state.screenLightValue == 0 ? DarkMode.getColorScheme() == 'dark' ? 'xm#3A3A3A' : '#D4D4D4' : '#32BAC0', width: 20}}
                  indicatorTextStyle={ { color: DarkMode.getColorScheme() == 'dark' ? 'white' : 'black' } }
                  value={ this.state.screenValueIndex }
                  stopAtIndicatorText={ true }
                  onValueChange={ (index) => {
                  } }
                  onSlidingComplete={ (index) => {
                    console.log('-------', index);
                    if (this.state.screenLightValue == 1 && index == this.state.screenValueIndex) {
                      console.log("{{{{{{{{{{{{{{{{same value");
                      this.setState({
                        screenValueIndex: index,
                        screenLightValue: 1
                      });
                      return;
                    }
                    this.setScreenBright(index);
                  } }
                />
                <View style={{ flex: 1, flexDirection: 'row',justifyContent: 'space-between', marginBottom: 26, marginTop: 0, marginLeft: 38,marginRight: 35 }}>
                  <Text style={{fontSize: 13, color: '#00000066'}}>{"0"}</Text>
                  <Text style={{fontSize: 13, color: '#00000066'}}>{"100%"}</Text>
                </View>
              </View>
            </TouchableWithoutFeedback>

          </View>
      </View>
    );
  }

  componentDidMount() {
    super.componentDidMount();
    // this.props.navigation.setParams({
    //   title: LocalizedStrings['cs_screen_light'],
    //   titleNumberOfLines: 2,
    //   type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
    //   left: [
    //     {
    //       key: NavigationBar.ICON.BACK,
    //       onPress: () => {
    //         this.props.navigation.goBack();
    //       }
    //     }
    //   ],
    //   titleStyle: {
    //     fontSize: 18,
    //     color: '#333333',
    //     fontWeight: 500
    //   }
    // });
    this.getScreenSetting();
  }

  getScreenSetting() {
    let params = [
      { sname: SCREEN_SETTING_SIID, pname: SCREEN_SETTING_BRIGHTNESS_PIID },
      { sname: SCREEN_SETTING_SIID, pname: SCREEN_SETTING_DISPLAY_STATE_PIID }
    ];
    AlarmUtilV2.getSpecPValue(params,2)
      .then((res) => {
        console.log("[[[[[",res);
        if (res[0].code === 0) {
          // 25 50 75 100
          let bright = res[0].value;
          this.setState({ screenValueIndex: this.options.indexOf(bright), screenLightValue: res[1].value });
          this.brightCallback({ value: bright, mode: res[1].value });
        } else {
          Toast.fail('c_get_fail');
        }
      }).catch((err) => {
        Toast.fail('c_get_fail', err);
      });
  }

  _onSelectedItem(value) {
    if (value ==1 && this.state.screenLightValue != 0) {
      return;
    }
    Toast.loading('c_setting');
    let lightValueMode = this.state.screenLightValue;
    this.setState({
      screenLightValue: value
    });
    AlarmUtilV2.setSpecPValue([{ sname: SCREEN_SETTING_SIID, pname: SCREEN_SETTING_DISPLAY_STATE_PIID, value: value }])
      .then((res) => {
        if (res[0].code ==0) {
          Toast.success("c_set_success");
          this.brightCallback({ value: this.options[this.state.screenValueIndex], mode: value });
        } else {
          this.setState({
            screenLightValue: lightValueMode
          });
          Toast.fail('c_set_fail');
        }
      }).catch((err) => {
        Toast.fail('c_set_fail', err);
      this.setState({
        screenLightValue: lightValueMode
      });
      });
  }

  setScreenBright(index) {
    let brightValue = this.options[index];
    let params = [
      { sname: SCREEN_SETTING_SIID, pname: SCREEN_SETTING_BRIGHTNESS_PIID, value: brightValue },
      { sname: SCREEN_SETTING_SIID, pname: SCREEN_SETTING_DISPLAY_STATE_PIID, value: 1 }
    ];
    AlarmUtilV2.setSpecPValue(params)
      .then((res) => {
        if (res[0].code === 0) {
          Toast.success("c_set_success");
          this.setState({
            screenValueIndex: index,
            screenLightValue: 1
          });
          this.brightCallback({ value: brightValue, mode: 1 });
        } else {
          Toast.fail('c_set_fail');
        }
      }).catch((err) => {
      Toast.fail('c_set_fail', err);
    });

  }
}