import React from 'react';
import { <PERSON><PERSON>View, View, BackHandler, FlatList, Platform, Text, StyleSheet, Image, TouchableWithoutFeedback, TouchableOpacity, ActivityIndicator, Dimensions } from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { ChoiceDialog, NavigationBar } from 'mhui-rn';
import AlarmUtil from '../util/AlarmUtil';
import StorageKeys from '../StorageKeys';
import Toast from '../components/Toast';
import CameraConfig from '../util/CameraConfig';
import { Service, Host, Device, Entrance } from 'miot';
import LogUtil from '../util/LogUtil';
import { PackageEvent } from 'miot/Package';
import API from '../API';
import { ListItem, ListItemWithSwitch } from "miot/ui/ListItem";
import { strings as I18n } from "miot/resources";
import { styles } from "../setting/SettingStyles";
import AlarmUtilV2, {
  PIID_COUGH_SENSITIVITY,
  PIID_COUGH_SWITCH,
  PIID_CRY_SENSITIVITY,
  PIID_CRY_SWITCH,
  PIID_MOVE_SENSITIVITY,
  PIID_MOVE_SWITCH,
  PIID_PEOPLE_SWITCH,
  PIID_SOUND_SENSITIVITY,
  PIID_SOUND_SWITCH, SIID_AI_DETECTION, SIID_AI_SENSITIVITY
} from "../util/AlarmUtilV2";
import { Event } from '../config/base/CfgConst';
import BaseSettingPage from "../BaseSettingPage";

const TAG = "MotionDetectionPage";
const sensitiveOptions = [
  { title: LocalizedStrings['alarm_level_high_title'], value: LocalizedStrings['pss_h'] },
  { title: LocalizedStrings['alarm_level_middle_title'], value: LocalizedStrings['pss_m'] },
  { title: LocalizedStrings['alarm_level_low_title'], value: LocalizedStrings['pss_l'] }
];
const optionsMove = [
  { text: LocalizedStrings['detect_operation_record'], icon: require('../../Resources/Images/aircon_band_scan.png') },
  { text: LocalizedStrings['ev_filter_title'], icon: require('../../Resources/Images/aircon_band_scan.png') },
  { text: LocalizedStrings['detect_operation_push_msg'], icon: require('../../Resources/Images/aircon_band_scan.png') },
  { text: LocalizedStrings['detect_operation_sense'], icon: require('../../Resources/Images/aircon_band_scan.png') }
];

const viewHeight = (Dimensions.get('window').width - 48) * 9 / 16;
/**
 * 多检测公用
 */
export default class MotionDetectionPage extends BaseSettingPage {

  constructor(props, context) {
    super(props, context);
    this.pageType = this.props.navigation.getParam("type");
    this.state = {
      isLoading: false,
      isVip: false,
      switchValue: false,
      sensitiveIndex: 0,
      sensitivityVisible: false
    };
    this.isReadOnlyShared = Device.isReadonlyShared;
    this.titleStr = LocalizedStrings['detect_move'];
    this.detectionDesc = LocalizedStrings['ai_move_desc'];
    this.attentionDesc = LocalizedStrings['ai_note_attention'];
    this.topImageSrc = require("../../Resources/Images/faceRecognition/ai_pic_move.webp");
  }

  getTitle() {
    switch (this.pageType) {
      case Event.ObjectMotion:
        this.titleStr = LocalizedStrings['detect_move'];
        break;
      case Event.PeopleMotion:
        this.titleStr = LocalizedStrings['detect_people'];
        break;
      case Event.LouderSound:
        this.titleStr = LocalizedStrings['detect_sound'];
        break;
    }
    return  this.titleStr;
  }

  componentDidMount() {
    super.componentDidMount();
    switch (this.pageType) {
      case Event.ObjectMotion:
        this.titleStr = LocalizedStrings['detect_move'];
        this.detectionDesc = LocalizedStrings['ai_move_desc'];
        this.attentionDesc = LocalizedStrings['ai_note_attention'];
        this.topImageSrc = require("../../Resources/Images/faceRecognition/ai_pic_move.webp");
        break;
      case Event.PeopleMotion:
        this.titleStr = LocalizedStrings['detect_people'];
        this.detectionDesc = LocalizedStrings['ai_people_desc'];
        this.topImageSrc = require("../../Resources/Images/faceRecognition/ai_pic_people.webp");
        this.attentionDesc = "";
        break;
      case Event.LouderSound:
        this.titleStr = LocalizedStrings['detect_sound'];
        this.detectionDesc = LocalizedStrings['ai_sound_desc'];
        this.attentionDesc = LocalizedStrings['ai_error_attention'];
        this.topImageSrc = require("../../Resources/Images/faceRecognition/ai_pic_abnormal_sound.webp");
        break;
    }

    console.log(TAG, "this.isReadOnlyShared", this.isReadOnlyShared);
    // 获取vip的状态
    StorageKeys.IS_VIP_STATUS.then((res) => {
      StorageKeys.IN_CLOSE_WINDOW.then((res1) => {
        this.setState({ isVip: res || res1 });
      })
      console.log("看看vip的状态：", this.state.isVip)
    }).catch(() => {
      this.setState({ isVip: false });
    });

    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {
        this._getSetting();
      }
    );
    this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {//// 原生页面关闭后回调
      // this.autoPlay();
      LogUtil.logOnAll(TAG, "did resume ios");
      this._onResume();
    });
    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      LogUtil.logOnAll(TAG, "did resume");
      this._onResume();
    });
  }
  componentWillUnmount() {
    console.log("DailyStoryFirstEnter componentWillUnmount.....");
    this.willFocusSubscription && this.willFocusSubscription.remove();
    this.didResumeListener && this.didResumeListener.remove();
    this.willAppearListener && this.willAppearListener.remove();
  }

  _onResume() {
    this._fetchVipStatus();
  }
  _fetchVipStatus() {
    console.log(TAG, "_fetchVipStatus start");
    // todo:需要框架端提供能够指定hostName前缀的api
    API.get("/miot/camera/app/v1/vip/status", "business.smartcamera")
      .then((result) => {
        if (result.code != 0) {
          return;
        }
        let data = result.data;
        if (data == null) {
          return;
        }
        console.log(TAG, "_fetchVipStatus", JSON.stringify(result));
        let vip = data["vip"];
        let status = data["status"];
        let inWindow = !data["closeWindow"]; // false表示在窗口内，true表示不在
        let endTime = data["endTime"];
        StorageKeys.IS_VIP_STATUS = vip;
        StorageKeys.IN_CLOSE_WINDOW = inWindow;
        StorageKeys.VIP_DETAIL = data;
        this.setState({ isVip: vip || inWindow });
      })
      .catch((err) => {
        console.log(err);
      });
  }
  // 通过后端获取开关信息
  _getSetting() {
    //多个侦测共用一个页面
    let params = this._getSpecParams();
    AlarmUtilV2.getSpecPValue(params, 2, TAG).then((res) => {
      if (res[0]?.code != 0) {
        console.log(TAG, "getSpecPValue:", JSON.stringify(-1));
        Toast.fail('c_get_fail');
        return;
      }
      let sensitiveIndex = 0;
      if (this.pageType != Event.PeopleMotion) {
        sensitiveIndex = res[1]?.value == 0 ? 2 : res[1]?.value == 2 ? 0 : 1;
      }
      this.setState({
        switchValue: res[0].value,
        sensitiveIndex: sensitiveIndex
      });
    }).catch((err) => {
      console.log(TAG, "getSpecPValue:", JSON.stringify(err));
      Toast.fail('c_get_fail', err);
      LogUtil.logOnAll(TAG, "getSpecPValue:", JSON.stringify(err));
    });
  }

  _getSpecParams() {
    let params = [
      { "sname": SIID_AI_DETECTION, "pname": PIID_MOVE_SWITCH },
      { "sname": SIID_AI_SENSITIVITY, "pname": PIID_MOVE_SENSITIVITY }];
    switch (this.pageType) {
      case Event.ObjectMotion:
        params = [
          { "sname": SIID_AI_DETECTION, "pname": PIID_MOVE_SWITCH },
          { "sname": SIID_AI_SENSITIVITY, "pname": PIID_MOVE_SENSITIVITY }
        ];
        break;
      case Event.PeopleMotion:
        params = [{ "sname": SIID_AI_DETECTION, "pname": PIID_PEOPLE_SWITCH }];
        break;
      case Event.LouderSound:
        params = [
          { "sname": SIID_AI_DETECTION, "pname": PIID_SOUND_SWITCH },
          { "sname": SIID_AI_SENSITIVITY, "pname": PIID_SOUND_SENSITIVITY }
        ];
        break;
      case Event.BabyCry:
        params = [
          { "sname": SIID_AI_DETECTION, "pname": PIID_CRY_SWITCH },
          { "sname": SIID_AI_SENSITIVITY, "pname": PIID_CRY_SENSITIVITY }
        ];
        break;
      case Event.PeopleCough:
        params = [
          { "sname": SIID_AI_DETECTION, "pname": PIID_COUGH_SWITCH },
          { "sname": SIID_AI_SENSITIVITY, "pname": PIID_COUGH_SENSITIVITY }
        ];
        break;
    }
    return params;
  }

  _renderSensitivityDialog() {
    return (
      <ChoiceDialog
        visible={this.state.sensitivityVisible}
        title={LocalizedStrings['sensitivity_settings']}
        useNewType={true}
        dialogStyle={{
          allowFontScaling: true,
          unlimitedHeightEnable: false,
          titleStyle: {
            fontSize: 18
          },
          itemSubtitleNumberOfLines: 0,
          itemSubtitleStyle: {
            marginRight: 10
          }
        }}
        buttons={[{
          text: I18n.cancel,
          callback: () => {
            this.setState({ sensitivityVisible: false });
          }
        }, {
          text: I18n.ok,
          callback: (res) => {
            console.log("ssssss", res);
            const newPluginType = res?.[0];
            if (newPluginType === this.state.sensitiveIndex) {
              this.setState({ sensitivityVisible: false });
              return;
            }
            //设置灵敏度
            this.setSensitivity(newPluginType);
          }
        }]}
        onDismiss={() => {this.setState({ sensitivityVisible: false });}}
        options={sensitiveOptions}
        itemStyleType={2}
        selectedIndexArray={[this.state.sensitiveIndex]}
      />
    )
  }
  setSensitivity(value) {
    let params = this.getSetSensitivityParams(value);
    AlarmUtilV2.setSpecPValue(params, TAG).then((res) => {
      console.log("setSensitivity success", res);
      if (res[0].code != 0) {
        Toast.fail("c_set_fail");
      } else {
        this.setState({ sensitiveIndex: value, sensitivityVisible: false });
      }
    }).catch((err) => {
      console.log("setSensitivity error", err);
      Toast.fail("c_set_fail");
      this.setState({ sensitivityVisible: false });
    })

  }
  _onSwitchValue(value) {
    let params = this.getSetParams(value);
    AlarmUtilV2.setSpecPValue(params, TAG).then((res) => {
      console.log("_onSwitchValue success", res);
      if (res[0].code != 0) {
        this.setState({ switchValue: !value });
        Toast.fail("c_set_fail");
      } else if (!value) {
        // AlarmUtilV2.setAISettingEventClose(this.pageType);
      }
      if (res[0].code == 0){
        Toast.success("c_set_success");
        this.setState({ switchValue: value });
      }
    }).catch((err) => {
      this.setState({ switchValue: !value });
      Toast.fail("c_set_fail", err);
      console.log("_onSwitchValue error", err);
    })
  }

  getSetParams(value) {
    let params = [];
    switch (this.pageType) {
      case Event.ObjectMotion:
        params = [{ "sname": SIID_AI_DETECTION, "pname": PIID_MOVE_SWITCH, value: value }]
        break;
      case Event.PeopleMotion:
        params = [{ "sname": SIID_AI_DETECTION, "pname": PIID_PEOPLE_SWITCH, value: value }];
        break;
      case Event.LouderSound:
        params = [{ "sname": SIID_AI_DETECTION, "pname": PIID_SOUND_SWITCH, value: value }];
        break;
      case Event.BabyCry:
        params = [{ "sname": SIID_AI_DETECTION, "pname": PIID_CRY_SWITCH, value: value }];
        break;
      case Event.PeopleCough:
        params = [{ "sname": SIID_AI_DETECTION, "pname": PIID_COUGH_SWITCH, value: value }];
        break;
    }
    return params;
  }

  getSetSensitivityParams(value) {
    value = value == 0 ? 2 : value == 2 ? 0 : 1;
    let params = [];
    switch (this.pageType) {
      case Event.ObjectMotion:
        params = [{ "sname": SIID_AI_SENSITIVITY, "pname": PIID_MOVE_SENSITIVITY, value: value }]
        break;
      case Event.LouderSound:
        params = [{ "sname": SIID_AI_SENSITIVITY, "pname": PIID_SOUND_SENSITIVITY, value: value }];
        break;
      case Event.BabyCry:
        params = [{ "sname": SIID_AI_SENSITIVITY, "pname": PIID_CRY_SENSITIVITY, value: value }];
        break;
      case Event.PeopleCough:
        params = [{ "sname": SIID_AI_SENSITIVITY, "pname": PIID_COUGH_SENSITIVITY, value: value }];
        break;
    }
    return params;
  }

  renderSettingContent() {


    return (
      <View style={{ display: "flex", height: "100%", flex: 1, flexDirection: "column", alignItems: "center" }}>
        {/*{this.renderTitleBar()}*/}
          <View style={styles.featureSetting} key={102}>

            <View style={{ alignItems: "center", marginHorizontal: 24, marginTop: 0 }}>
              <Image style={{ width: '100%', height: viewHeight, borderRadius: 9 }}
                source={this.topImageSrc} />
            </View>

            <View style={stylesDetection.white_blank} />
            <Text style={styles.desc_title}>{LocalizedStrings['algorithm_desc']}</Text>
            <Text style={styles.desc_subtitle}>{this.detectionDesc}</Text>
            {
              this.attentionDesc ? <Text style={styles.desc_subtitle}>{this.attentionDesc}</Text> : null
            }

            <View style={[styles.whiteblank, {marginTop: 38}]} />

            <ListItemWithSwitch
              titleNumberOfLines={3}
              unlimitedHeightEnable={true}
              showSeparator={false}
              title={this.titleStr}
              value={this.state.switchValue}
              onValueChange={(val) => this._onSwitchValue(val)}
              onPress={() => {
              }}
              titleStyle={{ fontWeight: 'bold' }}
              accessibilitySwitch={{
                accessibilityLabel: LocalizedStrings['care_screen_close_show_protect']
              }}
            />

            {
              this.state.switchValue && this.pageType != Event.PeopleMotion ? <ListItem
              title={LocalizedStrings['sensitivity_settings']}
              showSeparator={false}
              value={sensitiveOptions[this.state.sensitiveIndex].value}
              onPress={() =>
                this.setState({ sensitivityVisible: true })
              }
              titleStyle={{ fontWeight: 'bold' }}
              titleNumberOfLines={3} /> : null
            }
            {/*{*/}
            {/*  this.state.switchValue && this.pageType == Event.PeopleMotion ?*/}
            {/*    <View style={ [styles.whiteblank, { marginTop: 30 }] }/> : null*/}
            {/*}*/}
            {/*{*/}
            {/*  this.state.switchValue && this.pageType == Event.PeopleMotion ?*/}
            {/*    <ListItem*/}
            {/*      title={LocalizedStrings['family_detection']}*/}
            {/*      containerStyle={{ marginBottom: 20 }}*/}
            {/*      showSeparator={false}*/}
            {/*      titleStyle={{ fontWeight: 'bold' }}*/}
            {/*      subtitle={LocalizedStrings['family_detection_subtitle']}*/}
            {/*      onPress={() => {*/}
            {/*        this.props.navigation.navigate('FamilyDetectionSetting');*/}
            {/*      }} /> : null*/}
            {/*}*/}
            {/*啼哭安抚先不做*/}
            {/*{this.pageType == 'cry'?*/}
            {/*  <ListItem*/}
            {/*    title={ LocalizedStrings['cry_soothe'] }*/}
            {/*    showSeparator={ false }*/}
            {/*    value={LocalizedStrings['already_open'] }*/}
            {/*    onPress={ () =>*/}
            {/*      this.props.navigation.navigate('CrySoothePage')*/}
            {/*    }*/}
            {/*    titleStyle={ { fontWeight: 'bold' } }*/}
            {/*    titleNumberOfLines={ 3 }/>:null*/}
            {/*}*/}



          </View>
        {this._renderSensitivityDialog()}
      </View>
    )
  }
  renderTitleBar() {
    let titleBarContent = {
      title: this.titleStr,
      type: this.state.darkMode ? NavigationBar.TYPE.DARK : NavigationBar.TYPE.LIGHT,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => {
            this.props.navigation.goBack();
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    };
    return (
      <NavigationBar {...titleBarContent} />
    );
  }
}

const stylesDetection = StyleSheet.create({
  white_blank: {
    height: 0.5,
    marginTop: 20,
  },
  whiteblank: {
    height: 0.5,
    marginHorizontal: 24,
    backgroundColor: "#e5e5e5",
    marginBottom: 30,
    marginTop: 20

  },
  algorithm_title: {
    color: "#999",
    fontSize: 18,
    paddingHorizontal: 24
  },
  algorithm_subtitle: {
    color: "#999",
    fontSize: 12,
    marginTop: 10,
    paddingHorizontal: 24
  },
  optionsPage: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 20,
    paddingHorizontal: 24
  },
  optionContainer: {
    minHeight: 80,
    alignItems: 'center'
  },
  icon: {
    width: 48,
    height: 48
  },
  optionText: {
    marginTop: 4,
    marginBottom: 10,
    width: 48,
    textAlign: 'center',
    fontSize: 12
  },

});