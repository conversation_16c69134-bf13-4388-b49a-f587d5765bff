import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity, FlatList } from 'react-native';
import Util from '../../util2/Util';
import { BaseStyles } from "../../BasePage";
import { localStrings as LocalizedStrings } from "../../MHLocalizableString";
import { DescriptionConstants } from '../../Constants';
import Toast from "../../components/ToastCar";
import { DarkMode } from 'miot'
import { carStyles } from "../../car/common/Styles";
import { styles } from "micariot-ui-sdk";
import LogUtil from "../../util/LogUtil";

const Padding = 12;
const NumColumns = 7;
const itemSpace = 12;
const TAG = "DateFilterViewForCar";
export default class DateFilterViewForCar extends React.Component {
  flatListRef = null
  beginOffset = 0;
  endOffset = 0;
  constructor(props, context) {
    super(props, context);

    this.isDark = DarkMode.getColorScheme() === "dark";
    this.LastIndex = Math.floor(this.props.dataSource.length / 7) - 1;
    this.currentIndex = this.LastIndex;
  }

  componentDidUpdate(prevProps) {
    
    if (prevProps.currentStopIndex != this.props.currentStopIndex) {
      this.scrollToSelect();
    }
  }

  componentDidMount() {
    console.log("+++++++componentDidMount+++++++++",this.props.currentStopIndex);
    this.scrollToSelect();
  }

  componentWillUnmount() {
    this.delayToDo && clearTimeout(this.delayToDo);
  }

  render() {
    let itemWidth = (this.props.totalWidth - 2 * Padding - itemSpace * (NumColumns + 1)) / NumColumns;

    if (this.props.dataSource && this.props.dataSource.length > 0) {
      const chunkedData = [];
      const dataSource = this.props.dataSource;
      for (let i = 0; i < dataSource.length; i += 7) {
        const temp = dataSource.slice(i, i + 7);
        chunkedData.push(temp)
      }
      // console.log("� ~ file: DateFilterViewV2.js ~ line 18 ~ DateFilterViewV2 ~ render ~ dataSource", chunkedData)
      let now = (new Date()).getTime();
      let date = null;
      let fmt = LocalizedStrings["mmdd"];
      
      
      for (let item of this.props.dataSource) {
        if (item.selected) {
          date = item.ts;
          break;
        }
      }
      let dayM = Util.getMoment(date / 1000);
      let nowM = Util.getMoment(now / 1000);
      let extra = dayM.dayOfYear() == nowM.dayOfYear() ? ` ${ LocalizedStrings["date_filter_today"] }` : "";
      // console.log("🚀 ~ file: DateFilterViewV2.js ~ line 32 ~ DateFilterViewV2 ~ render ~ extra", extra)
      let dStr = dayM.format(fmt) + extra;
      return (
        <View style={[{width: this.props.totalWidth}, { flexDirection: 'column', alignItems: "center", justifyContent: 'center' }]} onLayout={this.props.onLayout}>
          {/* add addtional view to fix ios lack textAlignVertical */}
          {
            // if this.props.dataSource change too fast can cause FlatList only render partial
            this.props.dataSource == null || 0 == this.props.dataSource.length ? null :
              <FlatList
                onLayout={() => {
                  console.log("+++++++++++++++++++++=")
                  let offset = this.props.type == 0 ? this.props.totalWidth * 8 : this.props.totalWidth * 4;
                  // this.flatListRef.scrollToOffset({animated: false, offset: offset});
                }}
                showsHorizontalScrollIndicator={false}
                style={{ width: "100%", marginVertical: 24}}
                // ListFooterComponent={() => this.mFooter(itemWidth / 3)}
                // data={this.props.dataSource}  使用下面的，7天一组
                data={chunkedData}
                inverted={true}
                renderItem={(item) => this.renderDate(itemWidth, item)}
                horizontal={true}
                keyExtractor={(item, index) => index.toString()}
                // initialScrollIndex={5}
                /* 这个代码去掉可能会有问题，以后要注意一下 
                getItemLayout={(data, index) =>
                  ({ length: itemWidth, offset: (itemWidth + itemSpace) * index, index })
                } */
                pagingEnabled={true}
                ref={(ref) => { this.flatListRef = ref; }}
                onScrollBeginDrag={(e) => {
                  // 记录起始偏移量
                  this.beginOffset = e.nativeEvent.contentOffset.x;
                  console.log('beginOffset:', this.beginOffset);
                }}
                onScrollToIndexFailed={(info) => {
                  // LogUtil.logOnAll(TAG, "onScrollToIndexFaifled", info);
                  console.log("===========onScrollToIndexFailed==========",info);
                } }
                // // onMomentumScrollEnd在滚动动画结束触发，这样避免了使用onScrollEndDrag时用户抬手时仍滚动的场景带来的endOffset=0的问题（这会导致停在中间）
                // // onScrollEndDrag={}
                onMomentumScrollEnd={(e) => {
                  // 记录抬手时的偏移量
                  this.endOffset = e.nativeEvent.contentOffset.x;
                  console.log('endOffset:', this.endOffset);
                  // this.props.scrollModel ? this.scrollByWeek(itemWidth) : null;
                  this._onScrollEnd(e);
                }}
                onScroll={(e) => {
                  this._onScrolling(e);
                }
                }
              />
          }
          {this.props.extraHeader ? this.props.extraHeader() : null}
        </View>
      );
    } else {
      return null;
    }
  }

  renderDate = (aItmW, wkDat) => {
    // console.log(TAG, "render item", wkDat.index, wkDat.item);
    aItmW = 72

    let textStyle = [
      styles.itemHighlightTextStyle,
      carStyles.dayItemStyle,
      carStyles.dayItemUnableStyle
    ];
    // 日期是从大网小的，给反一下
    wkDat.item.reverse();
    const wkDaysCpn = wkDat.item.map((aDat, index) => {
      let item = aDat;
      let fmt = LocalizedStrings["yyyymmdd"];
      let date = item.ts || 0;
      let dayM = Util.getMoment(date / 1000);
      let dStr = dayM.format(fmt);
      return (
        <TouchableOpacity
          key={index}
          // disabled={!item.enabled}
          style={[item.selected ? carStyles.daySelectStyle : carStyles.dayUnselectStyle, (wkDat.index === 0 && index === 0) ? { marginRight: 0 } : { marginRight: 12 }]}
          onPress={() => {
            if (!item.enabled) {
              Toast.success('car_no_video_in_day');
              return;
            }
            this.props.dayButtonPressed(item);
          }}
          accessible={true}
          accessibilityState={{
          selected:item.selected
          }}
        >
          <Text
            style={[item.selected ? textStyle[0] : item.enabled ? textStyle[1] : textStyle[2], {fontSize: 18, fontWeight: "300"}]}
            accessibilityLabel={DescriptionConstants.kj_1_5+item.wkDay}
          >
            {item.wkDay}
          </Text>
          <Text
            accessibilityLabel={dStr}
            style={[BaseStyles.text16, item.selected ? textStyle[0] : item.enabled ? textStyle[1] : textStyle[2], {fontWeight: "300"}]}>
            {item.date}
          </Text>
        </TouchableOpacity>
      )
    })
    return (
      <View style={{flexDirection: 'row', width: this.props.totalWidth }}>
        {wkDaysCpn}
      </View>
    )
  }

  mFooter = (itemWidth) => {
    return (<View style={{ width: itemWidth }}>
      <Text> </Text>
    </View>);
  }

  scrollByWeek = (itemWidth) => {
    if (this.endOffset >= this.props.totalWidth * 3.5) {
      // 滑到最后时，再触底不换页
      return
    }

    let coefficient = 0;
    // 滑动超过屏幕宽度2/7时才切换，利用coefficient做系数判断向左还是向右
    if (Math.abs(this.endOffset - this.beginOffset) > (itemWidth + itemSpace) * 2) {
      this.endOffset > this.beginOffset ? coefficient = -1 : coefficient = 1;
    } else {
      coefficient = 0;
    }
    // let finalOffset = coefficient === 0 ? this.beginOffset : this.beginOffset - coefficient * (itemWidth * 7 + itemSpace * 7)
    // this.flatListRef.scrollToOffset({ offset: finalOffset })
    console.log("++++++++++++++++++", coefficient, NumColumns * coefficient);
    // this.flatListRef.scrollToIndex({ animated:false, index: NumColumns * coefficient });
  }
  _onScrollEnd(e) {
    let contentOffset = e.nativeEvent.contentOffset;
    let viewSize = e.nativeEvent.layoutMeasurement;
    //如果横向滑动则是
    // Math.floor(contentOffset.x/viewSize.width);
    let pageNum = Math.floor(contentOffset.x/viewSize.width);
    console.log('scrolled to page ', pageNum,viewSize,contentOffset.x);
    this.currentIndex = pageNum;
    this.isScrolling = false;
    this.props.onScrollEnd && this.props.onScrollEnd(this.currentIndex);
  }
  _onScrolling(e) {
    let contentOffset = e.nativeEvent.contentOffset;
    let viewSize = e.nativeEvent.layoutMeasurement;
    //如果横向滑动则是
    // Math.floor(contentOffset.x/viewSize.width);
    let pageNum = Math.floor(contentOffset.x/viewSize.width);
    console.log('scrolling to page ', pageNum,viewSize,contentOffset.x);

    // const scrollOffset = nativeEvent.contentOffset.y;
    // const firstVisibleItemIndex = Math.floor(scrollOffset / itemHeight);
    // const lastVisibleItemIndex = Math.floor((scrollOffset + screenHeight) / itemHeight);
    // const middleItemIndex = Math.round((firstVisibleItemIndex + lastVisibleItemIndex) / 2);
    // console.log('Middle item index:', middleItemIndex);
    this.delayToClearState && clearTimeout(this.delayToClearState);
    this.delayToClearState = setTimeout(() => {
      this.isScrolling = false;
      this.props.onScrollEnd && this.props.onScrollEnd(pageNum);
    }, 300);

    if (!this.isScrolling) {
      this.isScrolling = true;
      this.props.onScrolling && this.props.onScrolling();
    }

  }

  previewWeek() {
    console.log("{{{{{{{{{{{", this.currentIndex);
    if (this.currentIndex === this.LastIndex) {
      return;
    }
    if (this.currentIndex + 1 > this.LastIndex) {
      return;
    }
    this.flatListRef && this.flatListRef.scrollToIndex({ animated:false, index: this.currentIndex + 1 });
    this.currentIndex = this.currentIndex + 1;
  }

  nextWeek() {
    if (this.currentIndex === 0) {
      return;
    }
    if (this.currentIndex - 1 < 0) {
      return;
    }
    this.flatListRef && this.flatListRef.scrollToIndex({ animated:false, index: this.currentIndex - 1 });
    this.currentIndex = this.currentIndex -1;
  }

  scrollToSelect() {
    if (this.props.currentStopIndex > -1 && this.flatListRef && this.flatListRef.scrollToIndex) {
      // 因为7天为一屏显示，所以只需要计算当前index是7的几倍就可以了
      // const remainder = (this.props.currentStopIndex+1) % 7;
      // const index = remainder > 0 ? Math.floor(this.props.currentStopIndex / 7) : Math.floor(this.props.currentStopIndex / 7) - 1  ;
      // 0-6
      // 7-13
      // 14-20
      // 21-27
      const index = Math.floor(this.props.currentStopIndex / 7);
      console.log("===========================",this.props.currentStopIndex,index,this.props.dataSource.length, this.props.dataSource.length / 7);
      if (index > (this.props.dataSource.length / 7 -1)) {
        return;
      }
      this.currentIndex = index;
      this.delayToDo && clearTimeout(this.delayToDo);
      this.delayToDo = setTimeout(() => {
        if (this.flatListRef) {
          this.flatListRef.scrollToIndex({ animated: false, index });
        }
      }, 30);
    }
  }
}
