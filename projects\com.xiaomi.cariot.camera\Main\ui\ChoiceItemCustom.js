import PropTypes from 'prop-types';
import React from 'react';
import {Image, Platform, StyleSheet, Text, TouchableWithoutFeedback, View,TouchableOpacity} from 'react-native';
import Util from "../util2/Util";

const ICON_SIZE = 25; // 当android设置24的时候，图形会挤压形成锯齿

const BACKGROUNDCOLOR = 'rgba(50, 186, 192, 0.1)';
const UNCHECKED_BACKGROUNDCOLOR = 'rgba(0, 0, 0, 0.06)';
const UNCHECKED_DARK_BACKGROUNDCOLOR = 'xm#FFFFFF1F';

const UNCHECKED_TITLECOLOR = 'rgba(0, 0, 0, 1)';
const CHECKED_TITLECOLOR = 'rgba(50, 186, 192, 1)';

const UNCHECKED_SUBTITLECOLOR = 'rgba(0, 0, 0, 1)';
const CHECKED_SUBTITLECOLOR = 'rgba(50, 186, 192, 1)';
/**
 * @description 米家新版卡片化、支持显示左侧图标、大标题子标题、右侧单选按钮、以及右侧自定义图片数组设置项
 * @property {string} title - 主标题
 * @property {string} subtitle - 子标题
 * @property {bool} checked 设置项是否勾选
 * @property {style} containerStyle - 列表项的自定义样式
 * @property {style} titleStyle - 主标题的自定义样式
 * @property {style} subtitleStyle - 子标题的自定义样式
 * @property {function} onValueChange - 设置项被勾选的回调函数

 * @property {object[]} rightImagesArray - 右侧自定义 支持点击事件的图片数组
 */
export default class ChoiceItemCustom extends React.Component {
    static propTypes = {
        title: PropTypes.string.isRequired,
        subtitle: PropTypes.string,
        checked: PropTypes.bool,
        onlyChecked: PropTypes.bool,
        containerStyle: PropTypes.object,
        titleStyle: PropTypes.object,
        subtitleStyle: PropTypes.object,
        selectedIconLeft: PropTypes.any,
        selectIcon:PropTypes.any,
        unselectIcon:PropTypes.any,
        backgroundColor:PropTypes.string,
        titleColor:PropTypes.string,
        subtitleColor: PropTypes.string,
        accessibilityLabel:PropTypes.string,
        rightImagesArray: PropTypes.arrayOf(PropTypes.object),
        onValueChange: PropTypes.func,
        onLongPress: PropTypes.func
    }
    static defaultProps = {
        title: '',
        subtitle: '',
        checked: false,
        onlyChecked:false,
        containerStyle: {},
        titleStyle: {},
        subtitleStyle: {},
        selectIcon:null,
        unselectIcon:null,
        rightImagesArray:null,
        accessibilityLabel:""
    }

    constructor(props, context) {
        super(props, context);
        this.state = {
            checked: this.props.checked
        }
    }

    UNSAFE_componentWillReceiveProps(newProps) {
        console.log("newProps------", newProps);
        if (newProps.checked !== this.state.checked) {
            this.setState({checked: newProps.checked});
        }

    }

    render() {
        let itemColor = this.props.backgroundColor?this.props.backgroundColor:(this.state.checked ? BACKGROUNDCOLOR : Util.isDark()?UNCHECKED_DARK_BACKGROUNDCOLOR:UNCHECKED_BACKGROUNDCOLOR);
        let titleColor = this.props.titleColor?this.props.titleColor:(this.state.checked ? CHECKED_TITLECOLOR : UNCHECKED_TITLECOLOR);
        let subtitleColor = this.props.subtitleColor?this.props.subtitleColor:(this.state.checked ? CHECKED_SUBTITLECOLOR : UNCHECKED_SUBTITLECOLOR);
        let imageSource = this.state.checked ? this.props.selectIcon: this.props.unselectIcon;

        return (
            <View >

                    <View
                        style={[styles.container, {backgroundColor: itemColor}, this.props.containerStyle]}>
                        {this.props.selectedIconLeft?
                            <View style={[styles.selectedIconLeftView, {flex: 0}]}>
                                <Image style={[styles.selectedIconLeft,this.state.checked?{}:{tintColor:"transparent"}]} source={this.props.selectedIconLeft}/>
                            </View>
                            :null
                        }

                        <TouchableWithoutFeedback
                            disabled={this.props.disabled}
                            onPress={() => {
                                if(this.props.onlyChecked&&this.state.checked){
                                    return;
                                }
                                let checkValue = !this.state.checked;
                                this.setState({checked: checkValue});
                                if (this.props.onValueChange) {
                                    this.props.onValueChange(checkValue);
                                }
                            }}
                            onLongPress={()=>this.props.onLongPress && this.props.onLongPress()}
                            accessibilityLabel={this.props.accessibilityLabel?this.props.accessibilityLabel:""}
                        >
                        <View style={{flex: 8}}>
                            <View style={{paddingVertical: 2}}>
                                <Text
                                    numberOfLines={3}
                                    style={{
                                        fontSize: 15,
                                        fontWeight: 'bold',
                                        color: titleColor,
                                        fontFamily: Platform.OS === 'android' ? "" : null
                                    }}
                                >
                                    {this.props.title}
                                </Text>
                            </View>
                            {this.props.subtitle ? <Text
                                numberOfLines={6}
                                style={[{fontSize: 12, color: subtitleColor, marginTop: 3,lineHeight:15},this.props.subtitleStyle]}
                            >
                                {this.props.subtitle}
                            </Text> :null
                            }

                        </View>
                        </TouchableWithoutFeedback>

                        { this.props.rightImagesArray && this.props.rightImagesArray.map(vo=>{
                            return(
                                <View style={[styles.right, {flex: 0}]}>
                                    <TouchableOpacity onPress={vo.onPress}>
                                    <Image style={styles.icon} source={vo.imageSource}/>
                                    </TouchableOpacity>
                                </View>
                            )
                        })}


                        {this.props.selectIcon || this.props.unselectIcon ?
                            <View style={[styles.right, {flex: 0}]}>
                                <TouchableOpacity onPress={() => {
                                    if (this.props.onlyChecked && this.state.checked) {
                                        return;
                                    }
                                    let checkValue = !this.state.checked;
                                    this.setState({checked: checkValue});
                                    if (this.props.onValueChange) {
                                        this.props.onValueChange(checkValue);
                                    }
                                }}>
                                    <Image style={styles.icon} source={imageSource}/>
                                </TouchableOpacity>
                            </View> : null}
                    </View>


            </View>
        );
    }
}
var styles = StyleSheet.create({
    container: {
        borderRadius: 10,
        paddingVertical: 18,
        paddingHorizontal: 14,
        flexDirection: 'row',
        alignItems: 'center'
    },
    left: {
        flex: 8,
    },
    right: {
        marginLeft: 18,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
    },
    fourChoice: {
        flexDirection: 'row',
        flexWrap: 'wrap'
    },
    icon: {
        width: ICON_SIZE,
        height: ICON_SIZE,
    },
    selectedIconLeftView: {
        flex: 8,
        marginRight:10,
    },
    selectedIconLeft: {
        width: 13,
        height: 11,
    },
});
