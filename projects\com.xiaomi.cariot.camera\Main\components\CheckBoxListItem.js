import React from "react";
import { View, Text, TouchableOpacity, Image } from "react-native";

import { strings, Styles } from "miot/resources";
import Separator from "miot/ui/Separator";

export default class CheckBoxListItem extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      selected: this.props.selected
    };
  }

  render() {
    const selectedImage = require("../../../resources/images/doorcamera_setting_select.png");
    const unselectedImage = require("../../../resources/images/doorcamera_setting_unselect.png");

    return (
      <TouchableOpacity onPress={this.props.onPress}>
        <View
          style={{
            height: 50,
            paddingLeft: 23,
            justifyContent: "center",
            alignItems: "stretch",
            backgroundColor: "#ffffff"
          }}
        >
          <Text
            style={{
              textAlign: "left",
              textAlignVertical: "center",
              color: "#000000",
              fontSize: 15
            }}
          >
            {this.props.title}
          </Text>
          {this.props.subtitle && (
            <Text
              style={{
                textAlign: "left",
                textAlignVertical: "center",
                color: "rgba(0,0,0,0.6)",
                fontSize: 12,
                marginTop: 2,
                marginRight: 70
              }}
            >
              {this.props.subtitle}
            </Text>
          )}

          <Image
            source={this.props.selected ? selectedImage : unselectedImage}
            style={{
              width: 20,
              height: 20,
              position: "absolute",
              alignSelf: "center",
              right: 20
            }}
          />
        </View>
        {
        // <Separator style={{ marginLeft: Styles.common.padding, height: 0.5 }}/>
        }
      </TouchableOpacity>
    );
  }
}
