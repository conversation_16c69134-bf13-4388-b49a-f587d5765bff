'use strict';
import React from 'react';
import { <PERSON><PERSON>, BackHandler, View, Text, Image, StyleSheet, FlatList, Dimensions, TouchableOpacity, ActivityIndicator, Platform, SectionList } from 'react-native';
import { Device, Service, DarkMode } from 'miot';
import { MessageDialog } from "miot/ui";
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import MultiSelectableNavigationBar from '../ui/MultiSelectableNavigationBar';
import AlbumHelper from '../util/AlbumHelper';
import Toast from '../components/Toast';
import API from '../API';
import { getOrientation } from 'react-native-orientation';
import { log } from 'miot/utils/fns';
import InputDlgEx from '../widget/InputDlgEx';
import { AbstractDialog } from "miot/ui/Dialog";
import Util from "../util2/Util";
import { NavigationBar } from 'mhui-rn';
import JSONbig from 'json-bigint';
import dayjs from 'dayjs';
import Singletons from '../framework/Singletons';
import UriPlayer from '../framework/UriPlayer';
import EventList, { applyChange } from "../widget/EventList";
import LogUtil from '../util/LogUtil';
const kIsCN = Util.isLanguageCN();

export default class FaceEvents extends React.Component {
  state = {
    isSelectMode: false,
    index: 0,
    isEmpty: true,
    isCurrentDayEmpty: false,
    calendarDays: [],
    dialogVisible: false,
    albumFiles: [],
    showLoading: true,
    // figureInfos: []
    coverFaceInfosList: [],
    faceList: [],
    addMarkedFaceDialogVisible: false,
    commentDlg: false,
    rightKey: true,
    faceListLength: "",
    orignalArray: []
  };
  constructor(props) {
    super(props);
    this.isAllDelete = false;
    this.dateTime = new Date();
    this.figureInfos = [];
    this.coverFaceInfosList = [];
    this.coverImags = {};
  }




  dimensionListener = (args) => {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    if (Platform.OS === "ios") {
      console.log('why!, setDimensionsIos000: ', args);
      console.log('why!, Dimensions', Dimensions.get('window'));
      if (args && args.screen && args.window) {
        if (args.screen.width !== args.window.width || args.screen.height !== args.window.height) {
          setTimeout(() => Dimensions.set({ 'window': args.screen }), 10);
          console.log('纠正========');
        }
      }
    }
  }

  setNavigation(rightKey) {
    this.props.navigation.setParams({
      title: LocalizedStrings["lowpower_face_manager"],//"人脸管理"
      type: NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack() }
        }
      ],
      right: [
        {
          key: this.state.orignalArray.length == 0 ? NavigationBar.ICON.DETAIL : (rightKey ? NavigationBar.ICON.COMPLETE : NavigationBar.ICON.DETAIL),
          // disable:this.state.orignalArray.length == 0 ? true:false,
          onPress: () => {
            if (this.state.rightKey) {
              this.setState({
                rightKey: false
              });
            }
            if (!this.state.rightKey) {
              this.setState({
                rightKey: true
              })
            }
            this.setNavigation(this.state.rightKey)
          }

        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#000000',
        fontWeight: 500,
        fontFamily: "MILanPro_MEDIUM--GB1-4"
      }
    });
  }
  componentDidMount() { // 第一次进来的时候这样子设计。
    console.log(this.props.navigation.state, 'this.props.navigation.state')
    this.setNavigation()
    Dimensions.addEventListener('change', this.dimensionListener);
    self.windowWidth = Dimensions.get("window").width;
    self.windowHeight = Dimensions.get("window").height;
    if (self.windowHeight < self.windowWidth) {
      let sw = self.windowWidth;
      self.windowWidth = self.windowHeight;
      self.windowHeight = sw;
    }
    this.setState({ index: 1 });
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.isPageForeGround = true;
        this._onGetData(this.headerInfo1, this.forceReload);
      }
    );
    this.didBlurListener = this.props.navigation.addListener(
      'didBlur',
      () => {
        this.isPageForeGround = false;
      }
    );
  }
  //获取后端数据
  _onGetData(updatedFaceName = null, forceUpdate = false) {
    let imageUrl = this.props.navigation.state.params.figureInfo.faceUrl
    let faceId = this.props.navigation.state.params.figureInfo.faceId
    let faceIds = this.props.navigation.state.params.figureInfo.faceIds;
    let figureId = this.props.navigation.state.params.figureInfo.figureId;
    let headerInfo = this.props.navigation.state.params.figureInfo;
    let figureName = "";
    if (!updatedFaceName && !forceUpdate) {
      figureName = this.props.navigation.state.params.figureInfo.figureName
    } else {
      figureName = updatedFaceName;
    }

    this.setState({
      headerInfo: headerInfo,
      imageUrl: imageUrl,
      figureName: figureName,
      faceId: faceId,
      faceIds: headerInfo.faceIds,
      figureId:figureId
    })
    console.log(this.props.navigation.state.params.figureInfo, 'this.props.navigation.state.params.figureInfo')
    console.log(figureName, "传过来的");
    console.log(headerInfo, "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa");
    console.log(this.state.faceIds, "ccccccccccccccccccccccc");
    // 这里是为了完成名字的转换，让点击管理以后把这个数据传下去
    let Info = {
      faceUrl: imageUrl,
      name: figureName,
    }
    this.setState({ Info: Info })
    console.log(Info, "bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb");
    //为了弹窗
    Util.getAllFigure().then((res) => {
      console.log(res, '已备注的人脸数据')
      this.setState({
        mAllFigureInf: res,
        figureInfos: [...res],
        showMarkView: true,
        figureInfosLength: res.length,
      })
      console.log(this.figureInfos, 'this.figureInfos')
    })
      .catch((err) => {
        console.log('获取已备注的人脸err', err);
      });

    //为了获取人脸个数
    Util.getExistFigure(figureName).then((res) => {
      //这里获取到figureid
      this.setState({
        figureId: res
      })
      Util.getFigureFaces(res).then((res) => {
        this.setState({
          faceList: [...res, { faceUrl: false }],
          showFaceInfoMetaView: true,
          faceListLength: res.length,
        })
        console.log(res, "筛选");
      })
    })

    //获取人脸事件
    Util.getFaceClusterEvent(faceIds, figureId, { name: figureName })
      .then((res) => {
        LogUtil.logOnAll("FaceEvent", "getFaceClusterEven" + res);
        // console.log(res, "faceEvents"); //JSONbig.strinify(res)

        this.setState({
          faceEvents: res,
          showLoading:false
        })
        if(res.length !=0){
          let eventArr = this._onGroupedData(this._onPickedData(res))
          console.log("eventArr", eventArr);
          this.setState({
            eventArr: eventArr,
            orignalArray: res
          })
        }else{
          let eventArr = ""
          this.setState({
            eventArr: eventArr,
            orignalArray: res,
          })
        }
        this.state.faceEvents.forEach((item) => {
          if (!this.coverImags[item.imgStoreId.toString()]) {
            Service.miotcamera.getFileIdImage(item.imgStoreId.toString()).then((res) => {
              this.coverImags[item.imgStoreId.toString()] = res;
              this.forceUpdate();
            }).catch((err) => {
              console.log("eventCoverUrl getFileIdImage err=", JSON.stringify(err));
            });
          }
        });
      })
      .catch((error) => {
        console.log("err",error);
        LogUtil.logOnAll("FaceEvent", "getFaceClusterEven" + error);
        if (forceUpdate) {
          //originalArray
          this.setState({
            originalArray: this.state.orignalArray.forEach((element) => {
              element.eventType = Util.getDescWithFaceFrom(element.originalEventType, figureName)
            })
          });
        }
      });
  }


  //日期转换
  _onPickedData(arr) {
    let temp = [];
    temp = arr.reduce((total, currentValue, currentIndex, arr2) => {
      // let appearTime = dayjs.unix(currentValue['createTime'] / 1000).format("MM月DD日,HH:mm")
      let appearTime = dayjs.unix(currentValue['createTime'] / 1000).format(LocalizedStrings["date_filter_fmt"])
      let time = dayjs.unix(currentValue['createTime'] / 1000).format("HH:mm")
      // let curDay = appearTime.split(',')[0]
      // let curHour = appearTime.split(',')[1]
      let curDay = appearTime
      let curHour = time
      currentValue['appearDay'] = curDay
      currentValue['appearHour'] = curHour
      let currentVal = currentValue['appearDay'];
      total[currentVal] || (total[currentVal] = []);//当前数组有吗没有是空，有的话就有
      total[currentVal].push(currentValue);
      return total;
    }, {});
    // console.log("日期格式修改", temp);
    return temp
  }

  //这个是sectionlist要求的格式
  _onGroupedData(temp) {
    let groupArr = []
    for (var days in temp) {
      let obj = {}
      obj['title'] = days
      obj['data'] = temp[days]
      groupArr.push(obj)
    }
    console.log("格式变sectionlist", groupArr);
    return groupArr
  }
  //在组件卸载及销毁之前掉用
  componentWillUnmount() {
    Dimensions.removeEventListener('change', this.dimensionListener);
    this.didFocusListener.remove();
    this.didBlurListener.remove();
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  render() {
    return (
      <View style={styles.container}>
        {this._renderLoadingView()}
        {this._renderFaceInfoMetaView()}
        {this._renderCommentDlg()}
      </View>
    );
  }
  //渲染数据
  _renderFaceInfoMetaView(index) {
    return (
      <View style={{ width: '100%', flex: 1 }}>
        <View
          style={{ display: 'flex', flexDirection: "row", justifyContent: 'space-between', alignItems: "center", marginHorizontal: 20, marginBottom: 10, marginTop: 20 }}
        >
          <View style={{ display: "flex", flexDirection: 'row', justifyContent: 'flex-start', alignItems: "center" }}>
            <View>
              <Image
                style={{ width: 50, height: 50, borderRadius: 25, marginRight: 10 }}
                source={this.state.imageUrl}
              />
            </View>
            {
              this.state.figureName === false ?
                <View style={{ display: "flex", flexDirection: 'column', justifyContent: 'flex-start' }}>
                  <Text style={{ fontSize: 16 }}>
                    {LocalizedStrings["face_unmarked"]}
                  </Text>
                </View>
                :
                <View style={{ display: "flex", flexDirection: 'column', justifyContent: 'flex-start' }}>
                  <Text style={{ fontSize: 16 }}>
                    {this.state.figureName}
                  </Text>
                  <Text style={{ fontSize: 12, color: '#999999' }}>
                    {LocalizedStrings["figure_face_count_tips"].replace("%d", this.state.faceListLength)}
                  </Text>
                </View>
            }
          </View>
          {
            this.state.figureName === false ?
              <TouchableOpacity
                style={{ width: 76, height: 28, borderRadius: 14, backgroundColor: '#f5f5f5', color: '#666666', fontSize: 12, alignItems: 'center', justifyContent: 'center' }}
                onPress={() => { this._showCommentdlg() }}
              >
                <Text>
                  {LocalizedStrings["add_notes"]}
                </Text>
              </TouchableOpacity>
              : 
              this.state.faceListLength != "" ? 
                <TouchableOpacity
                  style={{ flexDirection: 'row', justifyContent: 'flex-end', alignItems: "center" }}
                  onPress={() => this._onPressFigureInfo(index)}
                >
                  <Text style={{ fontSize: 12, color: '#666666' }}>
                    {LocalizedStrings["string_face_manager"]}
                  </Text>
                  <Image
                    style={{ width: 20, height: 40 }}
                    source={require('../../Resources/Images/button_next_nor.png')}
                  />
                </TouchableOpacity> : null
          }
        </View>
        <View style={styles.whiteblank}>
        </View>
        {this._renderFacesInfoListView(index)}
      </View>
    )
    // }
  }
  //渲染列表
  _renderFacesInfoListView() {
    if (this.state.orignalArray.length != 0) {
      return (
        <View style={{ width: "100%", height: "100%", flex: 1 }}>
          <SectionList
            sections={this.state.eventArr}
            keyExtractor={(item, index) => item + index}
            renderItem={({ item, index }) => this._renderFacesInfoView(item, index)}
            renderSectionHeader={({ section: { title } }) => (
              this._renderFacesInfo(title)
            )}
          />
        </View>
      )
    } else if(this.state.showLoading == false) {
      return (
        <View style={{ display: "flex", justifyContent: "center", alignItems: 'center', marginTop: 250 }}>
          <Image
            style={{ width: 80, height: 80 }}
            source={require('../../resources2/images/icon_ev_empty.png')}
          />
          <Text style={{ color: '#999999' }}>{LocalizedStrings["no_alarm_event"]}</Text>
        </View>
      )
    }
  }
  //列表头部
  _renderFacesInfo(title) {
    return (
      <View>
        <Text style={{ fontSize: 12, color: "#999999", marginLeft: 24, marginBottom: 29 }}>
          {title}
        </Text>
      </View>
    )
  }
  //渲染列表源
  _renderFacesInfoView(item, index) {
    return (
      <TouchableOpacity
        onPress={() => {
          this.props.navigation.navigate('AlarmVideoUI', // 跳转到视频播放页
            {
              item: {
                fileId: item.fileId, createTime: item.createTime, offset: item.offset, isAlarm: item.isAlarm,
                playCfg: { loader: "_CloudEventLoader" },
                faceInfo: { faceId: JSONbig.stringify(this.state.faceId), name: this.state.figureName, figureId:this.state.figureId }
              },
              cfg: { loader: Singletons.CloudEventLoader, player: UriPlayer },
              lstType: "list_vip",
              items: null,
              fromFaceManager: true,
              loaderArgs: { startDate: new Date(), filter: "Default", nextDate: null },
            }
          );
        }}
      >
        <View style={{ display: 'flex', flexDirection: "row", justifyContent: 'space-between', alignItems: "center", marginHorizontal: 24, marginBottom: 35 }}>
          <View style={{ display: "flex", flexDirection: 'column', justifyContent: 'flex-start' }}>
            <Text style={{ fontSize: 12, color: "#666666" }}>
              {`${ item.appearHour } | `}
              <Image
                style={{ width: 14, height: 14 }}
                source={item.faceEventIcon}
              />
            </Text>
            <Text style={{ fontSize: 15, color: "#000000", marginTop: 2 }}>
              {item.eventType}
            </Text>
          </View>
          <View style={{ display: "flex", flexDirection: 'row', justifyContent: 'center', alignItems: "center" }}>
            <Image
              style={{ width: 99, height: 63, borderRadius: 10 }}
              source={{ "uri": this.coverImags[item.imgStoreId.toString()] }}
            />
            {
              !this.state.rightKey ? <TouchableOpacity style={{ marginLeft: 20 }}
                onPress={() => {
                  // 删除                         
                  console.log(item.fileId, item.offset, item.faceId, "删除");
                  let orignalArray = this.state.orignalArray
                  console.log("orignalArray", orignalArray);
                  orignalArray = orignalArray.filter((res) => res.faceId !== item.faceId);
                  let eventArr = this._onGroupedData(this._onPickedData(orignalArray));
                  this.setState({
                    eventArr: eventArr,
                    orignalArray: orignalArray,
                  })
                  Util.faceMistake(item.fileId, item.offset, item.faceId)
                    .then((res) => {
                    })
                    .catch((err) => {
                      alert(JSON.stringify(err));
                    });
                }}>
                <View style={{ width: 52, height: 28, borderRadius: 14, backgroundColor: '#f5f5f5', color: '#666666', alignItems: 'center', justifyContent: 'center' }}>
                  <Text style={{ fontSize: kIsCN ? 12 : 10 }}>{LocalizedStrings["error_report"]}</Text>
                </View>
              </TouchableOpacity> : null
            }
          </View>

        </View>
      </TouchableOpacity>
    );
  }
  //已备注管理功能
  _onPressFigureInfo(headerInfo) {
    this.props.navigation.navigate("FacesDetailManager", {
      figureInfo: this.state.Info,
      callback: (headerInfo) => {
        this.headerInfo1 = headerInfo.name;
        this.forceReload = headerInfo.forceUpdate;
      }
    }) 
  }
  // 加载中loading...........
  _renderLoadingView() {
    if (this.state.showLoading) {
      return (
        <View
          style={{ position: "absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center", backgroundColor: "white" }}
        >
          <ActivityIndicator
            style={{ width: 54, height: 54 }}
            color={DarkMode.getColorScheme() == "dark" ? "xm#ffffff" : "#000000"}
            size={"large"}
          />
          <Text
            style={{ marginTop: 10, fontSize: 12, color: "#000000" }}>
            {LocalizedStrings["camera_loading"]}
          </Text>
        </View>
      );
    }


  }
  //点击添加备注后触发这个让commentDlg: true,
  _showCommentdlg(item) {
    console.log(item, 'item1111')
    this.setState({
      commentDlg: true,
      unMarkfaceUrl: this.state.faceUrl,
      unMarkfaceId: this.state.faceId
    })
    console.log(this.state.unMarkfaceId, "unMarkfaceId");
  }
  //点击添加备注后的弹窗
  _renderCommentDlg() {
    let obj = {}
    obj = this.state.imageUrl
    if (this.state.commentDlg)
      return (
        <InputDlgEx
          title={LocalizedStrings["cloud_comment_dlg_title"]} //备注名称
          visible={this.state.commentDlg}                     //控制是否显示
          icon={obj}                                          //传来的人脸
          listData={this.state.mAllFigureInf}                 //已备注人脸列表
          onPressed={(aDat) => {
            console.log(aDat);
            this.setState({ defVal: aDat.name });
          }}
          onDismiss={(_) => {                                 //弹框隐藏时回调
            this.renameItem = null;
            this.setState({ commentDlg: false, isRename: false });
          }}
          inputWarnText={this.state.commentErr}
          inputs={[{                                         //输入框
            onChangeText: (text) => {                        //文字变化回调
              console.log("onChangeText", text);
              if (this.state.commentErr != null) {
                this.setState({ commentErr: null });
              }
            },
            textInputProps: {
              maxLength: 8,
              returnKeyType: "done",
              autoFocus: Util.isHeightPt() ? true : false
            },
            defaultValue: this.state.defVal,               //默认初始文字
            type: 'DELETE',
            isCorrect: this.state.commentErr == null       //输入框下的红色警示文字
          }]}
          buttons={[
            {
              text: LocalizedStrings["action_cancle"],
              callback: (_) => {
                this.renameItem = null;
                this.setState({ commentDlg: false, isRename: false });
              }
            },
            {
              text: LocalizedStrings["csps_right"],
              callback: (result) => {
                let text = result.textInputArray[0].trim();
                console.log(this.tag, "input changed", text, text.length);
                if (text.length > 0 && !this.containsEmoji(text)) {
                  let cmd = null;
                  cmd = Util.commentFace(text, this.state.unMarkfaceId);
                  console.log("cmd", cmd);

                  if (cmd) {
                    cmd.then((aRet) => {
                      console.log("aRet", aRet);
                      this.setState({
                        commentDlg: false,
                        defVal: "",
                      });
                      Toast.show("修改成功");
                      this._onGetData(text, true);
                    })
                      .catch((aErr) => {
                        this.setState({ commentDlg: false });
                        // if (this.state.figureInfosLength < 10) {
                        //   Toast.show(LocalizedStrings["action_fail"]);
                        //   console.log("comment failed", aErr);
                        // } else {
                        //   Toast.show(LocalizedStrings["face_max_tips"])
                        // }
                        	// 400302 人物上限
												// 400302 人物上限
												//400303 人物名称已经存在
											  let errCode = aErr.code;
                        // 400302 人物上限
                        let errMap = { 400302: "figure_max_tips", 400305: "face_max_tips" };
                        let err = errMap[errCode] || "action_failed";
                        Toast.fail(err, err, true);
                      });
                  } else {
                    console.log("nothing changed");
                  }

                } else {
                  if (this.containsEmoji(text)) {
										this.setState({ commentErr: LocalizedStrings["no_emoij_tips"] });
									}
									else {
										this.setState({ commentErr: LocalizedStrings["cloud_comment_null"] });
									}
                }
              }
            }
          ]}
        />);
  }

  containsEmoji(str) {
    let length = str.length;
    for (let i = 0; i < length; ++i) {
      let c = str.charCodeAt(i);
      if (this.isEmojiCharacterV2(c)) {
        return true;
      }
    }
    return false;
  }

  isEmojiCharacterV2(codePoint) {
    return !((codePoint == 0x0) ||
      (codePoint == 0x9) ||
      (codePoint == 0xA) ||
      (codePoint == 0xD) ||
      ((codePoint >= 0x20) && (codePoint <= 0xD7FF)) ||
      ((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) ||
      ((codePoint >= 0x10000))) ||
      (codePoint == 0xa9 || codePoint == 0xae || codePoint == 0x2122 ||
        codePoint == 0x3030 || (codePoint >= 0x25b6 && codePoint <= 0x27bf) ||
        codePoint == 0x2328 || (codePoint >= 0x23e9 && codePoint <= 0x23fa))
      || ((codePoint >= 0x1F000 && codePoint <= 0x1FFFF))
      || ((codePoint >= 0x2702) && (codePoint <= 0x27B0))
      || ((codePoint >= 0x1F601) && (codePoint <= 0x1F64F));
  }

  // android返回键处理
  onBackHandler = () => {
    if (!this.isPageForeGround) {
      return false;
    }
    if (this.state.isSelectMode) {
      this.onSelectAllChanged(false);
      this.setNavigation(false, false, false);
      this.setState({ isSelectMode: false });
      return true;
    } else {
      return false;// 不接管
    }
  }
}

export const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    width: '100%',
    height: '100%',
    display: "flex",
    flexWrap: "nowrap"
  },
  whiteblank: {
    height: 0.5,
    marginHorizontal: 24,
    backgroundColor: "#e5e5e5",
    marginBottom: 30,
    marginTop: 20,
  }
});
