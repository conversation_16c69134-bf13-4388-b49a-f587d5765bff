
import React from 'react';
import { Package, Device, Service, Host, PackageEvent, System, DeviceEvent } from 'miot';
import { Permissions } from "miot/system/permission";
import { NativeModules, SafeAreaView, StatusBar, DeviceEventEmitter, Platform, BackHandler, PermissionsAndroid, Image, StyleSheet, View, Text, TouchableOpacity, TouchableWithoutFeedback, AppState } from 'react-native';
import { MISSCommand, AlarmEventType } from "miot/service/miotcamera";
import CameraRenderView from 'miot/ui/CameraRenderView';
import { MISSSampleRate, MISSDataBits, MISSAudioChannel } from 'miot/ui/CameraRenderView';
import base64js from 'base64-js';
import Orientation from 'react-native-orientation';
import ImageButton from "miot/ui/ImageButton";

import LinearGradient from 'react-native-linear-gradient';
import { SingleChoseDialog, CardButton } from "miot/ui";
import DeviceOfflineDialog from "../ui/DeviceOfflineDialog";
import { TouchableHighlight, <PERSON><PERSON>, Modal } from "react-native";

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import CameraPlayer from '../util/CameraPlayer';
import StorageKeys from '../StorageKeys';
import API from '../API';
// import PrivacyUtil from '../util/PrivacyUtil'; 
import dayjs from 'dayjs';

import CheckFirmwareUpdate from "../util/CheckFirmwareUpdate";
import VersionUtil from '../util/VersionUtil';
import AlbumHelper from '../util/AlbumHelper';
import SdFileManager from '../sdcard/util/SdFileManager';
import Toast from '../components/Toast';
import CameraConfig from '../util/CameraConfig';
import RPC from '../util/RPC';

import { ChoiceDialog } from 'miot/ui/Dialog';
import tr from 'miot/resources/strings/tr';
import PushHandler from '../util/PushHandler';
import LoadingView from '../ui/LoadingView';

// import NetInfo from "@react-native-community/netinfo";

const kBpsDataReceiveCallbackName = "bpsDataReceiveCallback";
const kRecordTimeCallbackName = "recordTimeCallback";
const navigationBarHeightFat = 65; // 导航栏高度，有副标题
const iconSize = 40; // 图标尺寸
const TAG = "LiveVideoPage";

/**
*@deprecated 不再维护这个页面。  请参考新页面
*
*/
export default class LiveVideoPage extends React.Component {
  static navigationOptions = (navigation) => {
    // if (true) {//不要导航条
    //   return null;
    // }
    return {
      headerTransparent: true,
      header:
        null
    };
  };

  constructor(props) {
    super(props);



    this.isPageForeGround = true;// 默认当前page在前台, 页面在后，plugin可前可后；plugin在后，页面可前可后。
    this.isPluginForeGround = true;// 默认当前插件在前台

    // see https://blog.csdn.net/u011068702/article/details/83218639
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.isPageForeGround = true;
        this.isPluginForeGround = true;
        this._onResume();
      }
    );
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'didBlur',
      () => {
        this.isPageForeGround = false;
        this._onPause();
        if (this.cameraGLView != null) {
          this.cameraGLView.hidesSurfaceView();
        }
      }
    );

    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      if (!this.isPageForeGround) { // app进入前台，ios/android都会调用。对android，从native页面返回也会调用这个页面
        return;
      }
      this.isPluginForeGround = true;// rnactivity调用了onresume
      this.restoreOri();
      this._onResume();
    });

    this.willPauseListener = PackageEvent.packageWillPause.addListener(() => {
      if (!this.isPageForeGround) { // app进入后台，ios/android都会调用。对android，进入native页面也会调用这个页面
        return;
      }
      this.isPluginForeGround = false;// rnactivity调用了onpause
      this._onPause();
    });



    if (Platform.OS == "ios") {

      this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {
        if (!this.isPageForeGround) { // 从native页回来后，ios只进入这一个状态，但是android还要走packageDidResume，所以这里要为ios调用onResume
          return;
        }
        // this.isPageForeGround = true;// only for ios jump to native view, statusbar control
        this.isPluginForeGround = true;// rnactivity调用了onresume
        this._onResume();
      });

      this.willDisappearListener = PackageEvent.packageViewWillDisappearIOS.addListener(() => {
        if (!this.isPageForeGround) { // 进入native页面，ios只调用这个页面。
          return;
        }
        this.isPluginForeGround = false;// rnactivity调用了onpause
        this._onPause();
        if (this.cameraGLView != null && !this.isCheckingPermission) {
          this.cameraGLView.hidesSurfaceView();
        }
      });
    }

    this.currentNetworkState = -1;
    this.isChangingNetwork = false;
    this.isDataUsageWarning = true;
    this.checkIsInternationalServer();
    this.sdcardCode = -1;
    this.isFirstEnter = true;
    this.connRetry = false;// for each connected have one more try when state come to 0
    this.startVideoRetry = false;

    this.videoRecordPath = null;
    if (Platform.OS === "android") {
      Service.miotcamera.setCurrentDeviceIsMissFirmware(VersionUtil.judgeIsMiss(Device));
      // Service.miotcamera.setCurrentDeviceIsMissFirmware(false);
    }

    this.exitListener = PackageEvent.packageWillExit.addListener(() => {
      this.exitListener.remove();
      Service.miotcamera.disconnectToDevice();// 只调用一次。
    });
    this.cancelAuthListener = PackageEvent.packageAuthorizationCancel.addListener(() => {
      this.cancelAuthListener.remove();
      Service.miotcamera.disconnectToDevice();
      StorageKeys.IS_PRIVACY_NEEDED = true;// 撤销授权后 应该重置状态为需要弹框
      Package.exit();
    });

    this.isAudioMuteTmp = false;

    this.cameraGLView = null;
    this.isConnecting = false;

    this.isClickSleep = false;
    this.isClickCall = false;
    this.mOri = "PORTRAIT";
    this.isCheckingPermission = false;
  }


  state = {
    pstate: -1,
    error: -1,
    bps: 0,
    showPlayToolBar: true,
    useLenCorrent: true, // 畸变纠正

    fullScreen: false,
    isCalling: false,
    isRecording: false,
    isMute: true,
    isSleep: false,
    resolution: 0,
    isFlip: false,
    rotation: 0,

    dialogVisibility: false,

    screenshotVisiblity: false,
    screenshotPath: "",

    showErrorView: false,
    showLoadingView: false,
    showPoweroffView: false,
    errTextString: "",
    showPauseView: false,
    lastOfflineTime: "",

    isInternationServer: false,
    showRedDot: false,

    recordTimeSeconds: 0,
    currentAppState: AppState.currentState
  }


  componentDidMount() {
    Service.smarthome.reportLog(Device.model, "com.xiaomi.cardcamera begin to init; rn package version: 28 ");

    Host.ui.keepScreenNotLock(true);

    this.restoreOri();

    this.isVip = false;
    this.isPowerOn = false;
    this.isInWindow = false;
    CameraPlayer.getInstance().bindConnectionCallback(this._connectionHandler);
    CameraPlayer.getInstance().bindP2pCommandCallback(this._p2pCommandHandler);
    CameraPlayer.getInstance().bindNetworkInfoCallback(this._networkChangeHandler);
    CameraPlayer.getInstance().bindPowerOffCallback(this._powerOffHandler);

    DeviceEventEmitter.addListener(kBpsDataReceiveCallbackName, ({ data }) => {
      if (!this.isPageForeGround) {
        return;// 直播页不在前面就不展示
      }
      if (Platform.OS != "android" && !CameraPlayer.getInstance().isIOSPluginForeGround) {
        return;// ios手机切换到后台了，不刷新
      }
      this.setState({
        bps: data
      });
    });
    this.recordListener = DeviceEventEmitter.addListener(kRecordTimeCallbackName, (data) => {
      if (data == null) {
        return;
      }
      let time = Number.parseInt(data.recordTime);
      this.setState({ recordTimeSeconds: time });
      console.log(data);// 录制时长。
    });

    Service.miotcamera.bindBPSReceiveCallback(kBpsDataReceiveCallbackName);

    this.restoreOri();
    Orientation.addOrientationListener(this._orientationListener);

    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }

    // getVipStatus;
    this._fetchVipStatus.bind(this);
    this._fetchVipStatus();

    // todo:处理隐私弹框  非分享用户不用弹

    // this.showLoadingView();//showloading
    // 查询是否休眠了

    // 弹隐私弹框


    /*****
     * 
     * 
     * SOS  SOS  该页面不再维护，由于插件后台不再支持本地隐私，这里隐私的代码也要去掉了
     * 
     * 请参考新页面代码
     * 
     */

    // this.checkPrivacyDialog();// trigger first startconnect
    // this.showLoadingView();
    // this.queryPowerOffProperty();//不用管隐私弹框，直接走自己的逻辑

    this.loadLocalSetttings();

    // query sdcardstatus
    StorageKeys.LIVE_VIDEO_RESOLUTION.
      then((result) => {
        if (typeof (result) == "string") {
          StorageKeys.LIVE_VIDEO_RESOLUTION = 0;// 设置默认sp
          result = 0;
        }
        this._setIndexOfResolution(result);
        this.setState({ resolution: result });
      })
      .catch((err) => {
        console.log(err);
        this._setIndexOfResolution(0);
        this.setState({ resolution: 0 });
      });
    if (Platform.OS === "ios") {
      AppState.addEventListener('change', this._handleAppStateChange);
    }

    if (Device.isSetPinCode && Platform.OS == "android") { // 如果设置了密码，在直播页面监听输入密码结束事件 判断是否是从push跳过来的  @since 10047
      this.pinCodeEvent = DeviceEvent.pinCodeVerifyPassed.addListener(() => {

        let type = Package.entryInfo.type;
        let event = Package.entryInfo.event;
        let extra = Package.entryInfo.extra;
        let did = Package.entryInfo.did;
        PushHandler.handlePushFromStartUp(did, type, event, extra);
      });
    }

  }

  _setIndexOfResolution(resolution) {
    if (Platform.OS == "ios") {
      let index = 0;
      switch (resolution) {
        case 1:
          index = 1;
          break;
        case 3:
          index = 2;
          break;
        default:
          index = 0;
          break;
      }
      this.selectedIndexArray = [index];
    }
  }

  _setStatusBarForNativeView() {
    // this.isPageForeGround = false;
    StatusBar.setBarStyle("dark-content");
  }

  _orientationListener = (orientation) => {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    if (!this.isPageForeGround) {
      return;
    }
    console.log(TAG, `device orientation changed :${ orientation } want ${ this.mOri }`);
    if (this.mOri === orientation) {
      if (orientation === 'LANDSCAPE') {
        // do something with landscape layout
        this.setState({ fullScreen: true });
      } else {
        // do something with portrait layout
        this.setState({ fullScreen: false });
      }
    } else {
      // ios need restore for next lock
      this.restoreOri();
    }
  };

  _handleAppStateChange = (nextAppState) => {
    // stop record if app enter background
    if (this.state.currentAppState.match(/active|inactive/) && nextAppState === "background") {
      if (this.state.isRecording) {
        console.log('App has come to the background stop recording!');
        this._stopRecord();
      }
    }
    this.setState({ currentAppState: nextAppState });
  }


  _fetchFirmwareUpdateInfo() {
    if (Device.isOwner) {

      CheckFirmwareUpdate.checkFirmwareVersion()
        .then((needUpgrade) => {
          this.setState({ showRedDot: needUpgrade });
        })
        .catch((err) => {
          console.log(err);
          this.setState({ showRedDot: false });
        });
    }
  }

  _powerOffHandler = (isPowerOn) => {
    // 远端的下发的状态与本地的状态不一致 就回走到这里
    if (!isPowerOn) {
      this.setState({ isSleep: true, showPlayToolBar: false, showPoweroffView: true, showLoadingView: false, showPauseView: false });
      this.isPowerOn = false;
      this._onPause();
    } else {
      this.setState({ isSleep: false, showPlayToolBar: true, showPoweroffView: false, showLoadingView: false, showPauseView: false });
      this.isPowerOn = true;
      Service.smarthome.reportLog(Device.model, "on power off notify");
      this.queryNetworkJob();

    }

  }

  loadLocalSetttings() {
    StorageKeys.IS_DATA_USAGEE_WARNING.then((res) => { // 是否使用流量保护
      if (typeof (res) === "string") {
        StorageKeys.IS_DATA_USAGEE_WARNING = false;// 默认关闭流量保护，产品提得需求。
        this.isDataUsageWarning = false;
      } else {
        this.isDataUsageWarning = res;
      }
    }).catch((error) => {
      StorageKeys.IS_DATA_USAGEE_WARNING = false;
      console.log(error);
      this.isDataUsageWarning = false;
    });

    StorageKeys.IS_LENS_DISTORTION_CORREECTION.then((res) => { // 是否使用畸变纠正
      if (typeof (res) === "string") {
        res = true;// 默认启用畸变纠正
        StorageKeys.IS_LENS_DISTORTION_CORREECTION = true;
      }
      this.setState({ useLenCorrent: res });
    }).catch((error) => {
      console.log(error);
      this.setState({ useLenCorrent: true });
    });

    CameraPlayer.getInstance().getSdcardStatus()
      .then(({ sdcardCode }) => {
        // let result = JSON.parse(res);//res优先转化为json 转化失败则是str
        this.sdcardCode = sdcardCode;
      })
      .catch(({ sdcardCode, error }) => {
        // fix MIIO-40229
        // error in this form {"error": {"code": -2003, "domain": "MiHomeNetworkErrorRemote", "localDescription": "The operation couldn’t be completed. (MiHomeNetworkErrorRemote error -2003.)"}, "message": "callMethod failedError Domain=MiHomeNetworkErrorRemote Code=-2003 \"(null)\" UserInfo={ot_cost=1570, id=10, code=-2003, net_cost=71, exe_time=100, message=default error, otlocalts=1598861669714605, error={code = \"-2003\"}}"};
        this.sdcardCode = sdcardCode;
        console.log("request sdcard status error", error);
      });
      
    this._fetchFirmwareUpdateInfo();
  }

  checkIsInternationalServer() {
    Service.getServerName().then((server) => {
      let countryCode = server.countryCode;// countryCode是大写
      if (countryCode.toLowerCase() === "cn") {
        this.setState({ isInternationServer: false });
        StorageKeys.IS_INTERNATIONAL_SERVER = false;
        return;
      }
      this.setState({ isInternationServer: true });
      StorageKeys.IS_INTERNATIONAL_SERVER = true;

    }).catch((err) => {
      console.log(err);
      this.setState({ isInternationServer: true });
      StorageKeys.IS_INTERNATIONAL_SERVER = false;
    });
  }

  checkPrivacyDialog() {
    console.trace();
    // PrivacyUtil.showUserPrivacyNew()
    //   .then(() => {
    //     Service.smarthome.reportLog(Device.model, "camera device auth success, start to connect");
    //     // 已经授权了
    //     this.showLoadingView();
    //     this.queryPowerOffProperty();
    //   })
    //   .catch((err) => {
    //     Service.smarthome.reportLog(Device.model, `camera device auth failed, reason:${ JSON.stringify(err) }`);
    //     // 没有授权
    //     // 没有授权 88了
    //     Package.exit();
    //     console.log(err);
    //   });

  }

  _onResume() {
    CameraPlayer.getInstance().bindConnectionCallback(this._connectionHandler);
    CameraPlayer.getInstance().bindP2pCommandCallback(this._p2pCommandHandler);
    CameraPlayer.getInstance().bindNetworkInfoCallback(this._networkChangeHandler);
    CameraPlayer.getInstance().bindPowerOffCallback(this._powerOffHandler);

    StorageKeys.IS_IMAGE_FLIP.then((isFlipOn) => {
      StorageKeys.IMAGE_ROTATION.then((imgRotation) => {
        this.setState({
          isFlip: isFlipOn,
          rotation: imgRotation
        });
      });
    }).catch((err) => {
      console.log(err);
    });

    if (this.isFirstEnter) {
      return;// 刚进来的时候不请求connect  避免出现问题
    }
    if (!this.isPageForeGround) {
      return;
    }

    if (!this.isPluginForeGround) {
      return;
    }
    // 之前在render里设置的颜色，从设置里退回，如果摄像头处于关闭状态 render不会掉用。
    // StatusBar.setBarStyle('light-content');

    // first query is power off or not
    let isPowerOn = CameraPlayer.getInstance().getPowerState();
    if (this.isPowerOn != isPowerOn) { // 去往其他页面的时候power off了
      this._powerOffHandler(isPowerOn);
      return;
    }

    if (!this.isPowerOn) { // 电源g关了  没有必要往下面走.
      return;
    }

    // if (this.currentNetworkState != CameraPlayer.getInstance().getNetworkType())

    // get network type;

    this.loadLocalSetttings();

    setTimeout(() => {
      if (this.cameraGLView == null) {
        return;
      }
      Service.smarthome.reportLog(Device.model, "onresume");
      // 这里直接走连接的步骤吧  出现错误也会提示的
      this.queryNetworkJob();// 这里会处理是否连接成功之类的逻辑

    }, 500);

  }

  _onPause() {
    CameraPlayer.getInstance().bindConnectionCallback(null);
    CameraPlayer.getInstance().bindP2pCommandCallback(null);
    CameraPlayer.getInstance().bindPowerOffCallback(null);
    CameraPlayer.getInstance().bindNetworkInfoCallback(null);

    if (!this.isPowerOn) {
      return;
    }
    if (this.state.showErrorView) {
      return;
    }
    if (this.cameraGLView == null) {
      return;
    }
    this._stopRecord();
    this._toggleAudio(true);
    this._stopCall();
    CameraPlayer.getInstance().stopVideoPlay();
    this.cameraGLView.stopRender();// stopRender
    this.setState({ showPauseView: true, showLoadingView: false, showPlayToolBar: false });
  }

  queryNetworkJob() {

    CameraPlayer.getInstance().queryShouldPauseOn4G()
      .then(({ state, pauseOnCellular }) => {
        if (state === "CELLULAR" && pauseOnCellular && this.isDataUsageWarning) { // 普通网络 && 数据流量提醒
          Toast.success("nowifi_pause");
          this._onPause();
          return;
        }
        // 其他网络条件 走连接的步骤吧
        this._startConnect();// 开始连接
      })
      .catch((err) => { // 获取网络状态失败 也直接走开始连接的流程
        console.log(err);
        this._startConnect();// 开始连接
      });
  }

  restoreOri() {
    console.log(TAG, "restoreOri");
    if ("PORTRAIT" === this.mOri) {
      this.toPortrait();
    } else {
      this.toLandscape();
    }
  }


  toPortrait() {
    console.log(TAG, "toPortrait");
    this.mOri = "PORTRAIT";
    Orientation.lockToPortrait();
  }

  toLandscape() {
    console.log(TAG, "toLandscape");
    this.mOri = "LANDSCAPE";
    if (Platform.OS === "android") {
      Orientation.lockToLandscape();
    } else {
      Orientation.lockToLandscapeRight();
    }
  }

  componentWillUnmount() {
    // this._onPause();// 防止按了refresh 相关属性没有走到。
    this.toPortrait();
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
    Host.ui.keepScreenNotLock(true);
    this.didFocusListener.remove();
    this.didBlurListener.remove();
    this.didResumeListener.remove();
    this.willPauseListener.remove();
    if (this.willAppearListener) {
      this.willAppearListener.remove();
    }
    if (this.willDisappearListener) {
      this.willDisappearListener.remove();
    }
    this.pinCodeEvent && this.pinCodeEvent.remove();
    this.recordListener.remove();
    CameraPlayer.getInstance().bindConnectionCallback(null);
    CameraPlayer.getInstance().bindP2pCommandCallback(null);
    CameraPlayer.getInstance().bindPowerOffCallback(null);
    CameraPlayer.getInstance().bindNetworkInfoCallback(null);
    CameraPlayer.getInstance().destroy();
    CameraPlayer.destroyInstance();
    Orientation.removeOrientationListener(this._orientationListener);
    SdFileManager.getInstance().destroyInstance();
    if (Platform.OS === "ios") {
      AppState.removeEventListener('change', this._handleAppStateChange);
    }
  }

  _connectionHandler = (connectionState) => {
    if (this.state.pstate == connectionState.state) {
      return;// 状态一样 没有必要通知
    }
    if (connectionState.state == 0) {
      this.isConnecting = false;
      // if (this.isChangingNetwork && this.currentNetworkState >= 1) { // 切换网络后走到disconnect 这里应该尝试重连
      //   this.isChangingNetwork = false;
      //   this._toggleAudio(true);
      //   this._stopCall();

      //   this.setState((state) => {
      //     return {
      //       pstate: connectionState.state,
      //       error: connectionState.error
      //     };
      //   }, () => {
      //     if (this.state.showPauseView) { // 之前已经查过了  没必要再查一次。
      //       return;
      //     }
      //     this.queryNetworkJob();// 处理因为网络变化走到这里的重连  要区分是wifi还是4g
      //   });
      //   return;
      // }
      if (this.state.isSleep) {
        // 休眠状态下断开了连接，也不显示errorView
        return;
      }
      if (this.connRetry) {
        this.connRetry = false;
        setTimeout(() => {
          this._startConnect();
        }, 100);
        console.log("connection retry");
        return;
      }
      this.setState({ showPlayToolBar: false, isRecording: false, isCalling: false, showLoadingView: false, showPauseView: false, showPoweroffView: false, showErrorView: true, errTextString: `${ LocalizedStrings["camera_connect_error"] } ${ connectionState.error }, ${ LocalizedStrings["camera_connect_retry"] }` });
      this._stopRecord();
      // this._toggleAudio(true);
      this.cameraGLView.stopAudioPlay();
      // sync ui state
      this.setState({ isMute: true });
      this.cameraGLView.stopAudioRecord();
      // this._stopCall();
      // CameraPlayer.getInstance().stopVideoPlay();
      this.cameraGLView.stopRender();// stopRender
    }

    if (connectionState.state == 2) { // onconnected 发送video-start
      if (!this.isConnecting) {
        return;
      }
      this.isConnecting = false;
      this.connRetry = false;
      this.startVideoRetry = false;
      console.log("start send video start");
      this._realStartVideo();
      this.sendResolutionCmd(this.state.resolution);// 刷新resolution
    }
    if (connectionState.state == 1) {
      this.isConnecting = true;
    }
    if (connectionState.state >= 2) {
      this.setState({ showLoadingView: false, showPlayToolBar: true, showErrorView: false });
      Service.miotcamera.bindBPSReceiveCallback(kBpsDataReceiveCallbackName);
    }
    this.setState({
      pstate: connectionState.state,
      error: connectionState.error
    });
  }

  _p2pCommandHandler = ({ command, data }) => {
    console.log('reach serverCmd callback');
    if (command == MISSCommand.MISS_CMD_SPEAKER_START_RESP) {
      this.isClickCall = false;
      console.log(' receive start speaker');
      console.log(`data:${ data }`);
      let ba = base64js.toByteArray(data);
      if (ba.length > 0) {
        console.log('receive start speaker 0');
        console.log(ba[0]);
        if (Platform.OS === 'android') {
          if (ba[0] == 48) {
            console.log("start call in android");
            this.isAudioMuteTmp = this.state.isMute;
            this.cameraGLView.startAudioRecord();
            if (Device.model == VersionUtil.Model_Camera_V1) {
              console.log("Model_Camera_V1 toggleAudio mute true.");
              this._toggleAudio(true);
            } else {
              console.log("this._toggleAudio(false).");
              this._toggleAudio(false);
            }
            this.setState({ isCalling: true });
          }
        } else {
          if (ba[0] == 0) {
            this.isAudioMuteTmp = this.state.isMute;
            this._toggleAudio(false);
            setTimeout(() => {
              this.cameraGLView.startAudioRecord();
            }, 800);// temp solution for bug MIIO-42838
            this.setState({ isCalling: true });
          }
        }
      }
    } else {
      console.log(`receive command:${ command } data:${ JSON.stringify(data) }`);
    }
  }

  _networkChangeHandler = (networkState) => {
    // if (this.currentNetworkState != networkState) { // 网络状态不一样
    //   this.isChangingNetwork = true;
    // }
    if (this.isFirstEnter) { // 放到后台的包  刚进来的时候
      return;
    }
    console.log("处理网络变化");

    if (networkState == 0) { // 网络断开了连接 showError?
      CameraPlayer.getInstance().disconnectToDevice();// 去往其他注册了网络监听的页面，就不会走到这里了，如果走到这里，这里必须先执行断开的操作。
      return;
    }
    this.currentNetworkState = networkState;
    if (this.isPageForeGround) { // 有网络来了  发起重连吧
      setTimeout(() => {
        if (this.cameraGLView == null) {
          return;
        }
        Service.smarthome.reportLog(Device.model, "on network changed");
        this.queryNetworkJob();
      }, 500);// 过一会再查询 ，那个查询网络类型的api有问题。
    }
  }

  // android返回键处理
  onBackHandler = () => {
    if (!this.isPageForeGround) {
      return false;
    }
    if (this.state.fullScreen) {
      this.toPortrait();
      return true;
    }
    if (this.state.isRecording) {
      Toast.success("camera_recording_block");
      return true;
    }
    if (this.state.isCalling) {
      Toast.success("camera_speaking_block");
      return true;
    }
    this._onPause();
    return false;
  }


  _fetchVipStatus() {
    // todo:需要框架端提供能够指定hostName前缀的api
    API.get("/miot/camera/app/v1/vip/status", "business.smartcamera")
      .then((result) => {
        if (result.code != 0) {
          return;
        }
        let data = result.data;
        if (data == null) {
          return;
        }
        let vip = data["vip"];
        let inCloseWindow = data["closeWindow"];
        StorageKeys.IS_VIP_STATUS = vip;
        this.isVip = vip;
        this.inCloseWindow = inCloseWindow;
      })
      .catch((err) => {
        console.log(err);
        this.isVip = false;
        this.inCloseWindow = false;
      });
  }

  // query whether is power off;
  queryPowerOffProperty() {
    // todo fist check power on/off
    RPC.callMethod("get_prop", ["power"])
      .then((res) => {
        this.isFirstEnter = false;
        console.log(res);
        // this.setState({ isSleep: false, showPlayToolBar: true })
        let isPowerOn = res.result[0] === "on";
        this.isPowerOn = isPowerOn;

        CameraPlayer.getInstance().setPowerState(isPowerOn);
        if (isPowerOn) {
          Service.smarthome.reportLog(Device.model, "onPowerOn");
          this.queryNetworkJob();// 如果电源打开  要查询网络
        } else { // 休眠了
          this.setState({ isSleep: true, showPlayToolBar: false, showPoweroffView: true, showLoadingView: false, showPauseView: false });
        }
      })
      .catch((err) => { // 查询状态出错  不管  直接去查询
        console.log(err);
        this.isFirstEnter = false;
        Service.smarthome.reportLog(Device.model, "onPowerOn");
        this.queryNetworkJob();
      });
  }

  _startConnect() {
    console.trace();
    if (!this.state.showLoadingView) { // 如果没有loading
      this.setState({ showLoadingView: true, showErrorView: false, showPlayToolBar: false });
    }
    if (this.state.showPauseView) {
      this.setState({ showPauseView: false });
    }
    // 开始连接
    if (CameraPlayer.getInstance().isConnected()) {
      // 如果已经连接成功 直接发送video_start
      this._realStartVideo();
      return;
    }
    this.isConnecting = true;
    CameraPlayer.getInstance().startConnect();
    if (!Device.isOnline) {
      this._getLastOnlineTime();
    }
  }

  _realStartVideo() {
    if (this.state.showPauseView) {
      this.setState({ showPauseView: false });
    }
    Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_VIDEO_START, {})
      .then((retCode) => {
        console.log("startVideo success ", retCode, "mute", this.state.isMute);
        this.cameraGLView.startRender();// startBindVideo
        if (this.state.pstate == 3) {
          this.setState({ showLoadingView: false });// 已经渲染过  直接跳过去
        }
        if (!this.state.isMute) {
          // need renew AudioQueueRef for sound play
          this.cameraGLView.stopAudioPlay();
          Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {}).then((retCode) => {
            console.log("resume audioplay ", retCode);
          });
          this.cameraGLView.startAudioPlay();
        }
      })
      .catch((err) => {
        if (this.startVideoRetry) {
          this.startVideoRetry = false;
          setTimeout(() => {
            this._startConnect();
          }, 100);

          console.log("start video retry");
          return;
        }
        this.setState({ showLoadingView: false, showErrorView: true, errTextString: `${ LocalizedStrings["camera_connect_error"] } ${ err } ${ LocalizedStrings["camera_connect_retry"] }` });// 已经渲染过  直接跳过去
      });
  }

  showLoadingView() {
    this.setState({ showLoadingView: true });
  }


  _onVideoClick() {
    if (!CameraPlayer.getInstance().isConnected()) {
      return;
    }
    this.setState({
      showPlayToolBar: !this.state.showPlayToolBar
    });
    console.log("click video view");
  }

  canStepOut() {
    if (this.state.isRecording) {
      Toast.success("camera_recording_block");
      return false;
    }
    if (this.state.isCalling) {
      Toast.success("camera_speaking_block");
      return false;
    }
    if (this.state.isSleep) {
      Toast.success("camera_power_off");
      return false;
    }
    return true;
  }

  render() {
    return (
      <View style={styles.main}>
        <SafeAreaView style={{ backgroundColor: "black" }}></SafeAreaView>

        {this._renderVideoLayout()}
        {this._renderCallViewLayout()}
        {this._renderBottomLayout()}
        <DeviceOfflineDialog
          ref="offlineDialog"
        />
        <SafeAreaView></SafeAreaView>
      </View>
    );
  }

  _renderVideoLayout() {

    return (
      <View style={this.state.fullScreen ? styles.videoContainerFull : styles.videoContainerNormal}>
        {this._renderVideoView()}

        {this._renderPowerOffView()}
        {this._renderErrorRetryView()}
        {this._renderLoadingView()}


        {this._renderTitleView()}
        {this._renderVideoControlView()}
        {this._renderLandscapeCallView()}
        {this._renderResolutionDialog()}
        {this._renderSnapshotView()}
        {this._renderPauseView()}
        {this._renderRecordTimeView()}
      </View>
    );
  }

  _renderRecordTimeView() {
    if (!this.state.isRecording) {
      return null;
    }
    let containerHeight = 80;

    if (this.state.fullScreen) {
      containerHeight = 60;
    }
    let seconds = this.state.recordTimeSeconds;
    let second = Number.parseInt(seconds % 60);
    let minute = Number.parseInt(seconds / 60 % 60);
    let hour = Number.parseInt(seconds / 60 / 60 % 24);

    return (
      <View
        style={{ position: "absolute", top: containerHeight, display: "flex", alignItems: "center", justifyContent: "center", width: "100%" }}
      >
        <Text
          style={{ fontSize: 12, color: "red" }}
        >
          {`${ hour > 9 ? hour : `0${ hour }` }:${ minute > 9 ? minute : `0${ minute }` }:${ second > 9 ? second : `0${ second }` }`}
        </Text>

      </View>
    );
  }

  _renderPauseView() {
    if (!this.state.showPauseView) {
      return null;
    }
    return (
      <View styles={{ position: "absolute", width: "100%", height: "100%" }}
        pointerEvents={"none"}
      >
        <View style={{ width: "100%", height: "100%", display: "flex", alignItems: "center", justifyContent: "center" }}

        >
          <ImageButton
            style={{ width: 64, height: 64 }}
            source={require("../../Resources/Images/home_icon_pause_normal.png")}
            onPress={() => {
              // StorageKeys.IS_DATA_USAGEE_WARNING = false //wifi下
              this.isDataUsageWarning = false;
              Service.smarthome.reportLog(Device.model, "on pause clicked");
              this.queryNetworkJob();
            }}
          />
        </View>
      </View>
    );
  }

  _renderVideoView() {
    let videoStyle = styles.videoViewNormal;
    if (this.state.rotation == 90) {
      videoStyle = styles.videoViewRotation90;
    } else if (this.state.rotation == 270) {
      videoStyle = styles.videoViewRotation270;
    }

    return (
      <CameraRenderView
        ref={(ref) => { this.cameraGLView = ref; }}
        maximumZoomScale={3.0}
        style={[videoStyle]}
        videoCodec={CameraConfig.getCameraCoderParam(Device.model, Device.lastVersion).videoCodec}
        audioCodec={CameraConfig.getCameraCoderParam(Device.model, Device.lastVersion).audioCodec}
        audioRecordSampleRate={MISSSampleRate.FLAG_AUDIO_SAMPLE_8K}
        audioRecordChannel={MISSAudioChannel.FLAG_AUDIO_CHANNEL_MONO}
        audioRecordDataBits={MISSDataBits.FLAG_AUDIO_DATABITS_16}
        fullscreenState={this.state.fullScreen}
        videoRate={15}
        correctRadius={CameraConfig.getCameraCorrentParam(Device.model).radius}
        osdx={CameraConfig.getCameraCorrentParam(Device.model).osdx}
        osdy={CameraConfig.getCameraCorrentParam(Device.model).osdy}
        useLenCorrent={this.state.useLenCorrent}
        onVideoClick={this._onVideoClick.bind(this)}
        did={Device.deviceID}
        isFull={false}
        recordingVideoParam={{ width: 1920, height: 1080 }}
      >
      </CameraRenderView>
    );
  }

  _renderSnapshotView() {
    if (!this.state.screenshotVisiblity) {
      return null;
    }
    return (
      <ImageButton
        style={this.state.fullScreen ? styles.snapShotFull : styles.snapShot}
        source={(this.state.screenshotPath == "") ? null : ({ uri: `file://${ Host.file.storageBasePath }/${ this.state.screenshotPath }` })}
        onPress={() => {
          if (!this.canStepOut()) {
            return;
          }
          clearTimeout(this.snapshotTimeout);
          this.setState({ screenshotVisiblity: false, screenshotPath: "" });// 点击后就消失。
          this.toPortrait();
          if (this.isForVideoSnapshot) {
            console.log("点击了缩略图，跳转到视频页面");
            this.props.navigation.navigate("AlbumVideoViewPage");
          } else {
            console.log("点击了缩略图，跳转到图片页面");
            this.props.navigation.navigate("AlbumPhotoViewPage");
          }

          this.isForVideoSnapshot = false;
          // todo jump to album activity
        }}
      />
    );
  }

  _renderPowerOffView() {
    // todo render poweroffview  full
    if (!this.state.showPoweroffView) {
      return null;
    }
    return (
      <TouchableWithoutFeedback
        style={{ position: "absolute", width: "100%", height: "100%" }}
        onPress={() => {
          this._toggleSleep(false);
        }}
      >
        <View style={{ backgroundColor: "black", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
        >
          <Image
            style={{ width: 54, height: 54 }}
            source={require("../../Resources/Images/icon_camera_sleep.png")} />
          <Text
            style={{ marginTop: 10, fontSize: 14, color: "#bfbfbf" }}>
            {LocalizedStrings["camera_power_off"]}
          </Text>
        </View>
      </TouchableWithoutFeedback>
    );
  }

  _getLastOnlineTime() {
    Service.callSmartHomeAPI('/appgateway/miot/appdeviceinfo_service/AppDeviceInfoService/get_last_online', { dids: [Device.deviceID] })
      .then((res) => {
        let timeStamp = res.info[0].last_online;
        let time = dayjs.unix(timeStamp).format("YYYY-MM-DD HH:mm:ss");
        
        this.setState({ lastOfflineTime: `${ LocalizedStrings['offline_time_str'] }: ${ time }` });
        console.log('response:', (JSON.stringify(res)));
      })
      .catch((e) => {
        console.error(`error:${ JSON.stringify(e) }`);
      });
  }

  _renderErrorRetryView() {
    if (!this.state.showErrorView) {
      return null;
    }
    if (!Device.isOnline) {
      this.state.errTextString = LocalizedStrings['device_offline'];
    }
    return (
      <View
        style={{ position: "absolute", backgroundColor: "black", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <TouchableOpacity
          style={{ display: "flex", alignItems: "center" }}
          onPress={() => {
            this.setState({ showErrorView: false });
            Service.smarthome.reportLog(Device.model, "on error Retry");
            this.queryNetworkJob();
          }}// 走重新播放的逻辑,如果是断线了  会走重连的逻辑的}
        >
          <Image
            style={{ width: 54, height: 54 }}
            source={require("../../Resources/Images/icon_camera_fail.png")} />
          <Text
            style={{ marginTop: 10, fontSize: 14, color: "#bfbfbf" }}>
            {this.state.errTextString}
          </Text>
          {Device.isOnline ? null :
            <Text
              style={{ marginTop: 5, fontSize: 12, color: "#bfbfbf" }}
            >
              {this.state.lastOfflineTime}
            </Text>}
        </TouchableOpacity>
        {Device.isOnline ? null :
          <TouchableOpacity
            style={{ display: "flex", alignItems: "center" }}
            onPress={() => {
              this.refs.offlineDialog.show();
            }}>
            <Text style={{
              color: "#fff",
              fontSize: 12,
              paddingTop: 4,
              paddingBottom: 4,
              paddingLeft: 17,
              paddingRight: 17,
              backgroundColor: "#249A9F",
              borderRadius: 20,
              marginTop: 10
            }}
            >{LocalizedStrings['offline_see_help']}</Text>
          </TouchableOpacity>}
      </View>
    );
    // todo render errorRetryView not
  }

  _renderLoadingView() {
    // todo render loading view
    if (!this.state.showLoadingView) {
      return null;
    }
    return (
      <View

        style={{ position: "absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <LoadingView
          style={{ width: 54, height: 54 }}

        />
        <Text
          style={{ marginTop: 10, fontSize: 12, color: "#ffffff" }}>
          {LocalizedStrings["camera_loading"]}
        </Text>
      </View>
    );
  }



  _renderTitleView() {
    if (this.state.fullScreen) {
      return null;
    }

    // first change statusBar
    // 切换到其他页面时候强制设置light-content 会导致浅色页面statusbar看不到
    if (this.isPageForeGround && this.isPluginForeGround) { // 在前台时才会显示
      StatusBar.setBarStyle('light-content');
    }
    if (Platform.OS == 'android') {
      // StatusBar.setTranslucent(true); // 测试过的机型几乎都无效：华为荣耀V9，红米Note4X，小米Mix2
    }

    // second get statusBar height;
    let containerHeight = StatusBar.currentHeight || 0;
    containerHeight += navigationBarHeightFat;
    const containerStyle = {
      position: "absolute",
      top: 0,
      backgroundColor: "#00000000",
      height: containerHeight,
      width: "100%",
      display: "flex",
      flexDirection: "row",
      alignItems: "center", // 垂直居中
      paddingTop: StatusBar.currentHeight || 0,
      paddingLeft: 9,
      paddingRight: 9
    };

    const textContainerStyle = {
      flexGrow: 1,
      alignSelf: 'stretch', // 控制自己填充满父类的高度
      display: "flex",
      flexDirection: "column",
      justifyContent: 'center',
      alignItems: 'stretch', // 控制子类填充满本身的宽度
      marginHorizontal: 5,
      numberOfLines: 1,
      ellipsizeMode: "tail"

    };

    const titleTextStyle = {
      fontSize: 16,
      // lineHeight: 22,
      textAlignVertical: 'center',
      textAlign: 'center'
    };

    const subTitleTextStyle = {
      fontSize: 12,
      lineHeight: 17,
      fontFamily: 'MI-LANTING--GBK1-Light',
      textAlignVertical: 'center',
      textAlign: 'center'
    };
    const darkTitleColor = '#ffffff'; // 深色背景下标题颜色
    const darkSubtitleColor = '#ffffff'; // 深色背景下副标题颜色
    const titleColor = { color: darkTitleColor };
    const subTitleColor = { color: darkSubtitleColor };
    return (

      <LinearGradient
        colors={['#00000099', '#00000000']}
        style={containerStyle}>
        <View
          style={{ width: iconSize, height: iconSize, position: "relative" }}>

          <ImageButton
            style={{ width: iconSize, height: iconSize, position: "absolute" }}
            source={require("../../Resources/Images/icon_back.png")}
            highlightedSource={require("../../Resources/Images/icon_back_pres.png")}
            onPress={() => {
              this._onPause();
              Package.exit();
            }}
          />
        </View>

        <View style={textContainerStyle}>
          <Text
            numberOfLines={1}
            style={[titleTextStyle, titleColor]}
          >
            {Device.name ? (Device.name.length > 15 ? `${ Device.name.substr(0, 15) }...` : Device.name) : ""}
          </Text>

          <Text
            numberOfLines={1}
            style={[subTitleTextStyle, subTitleColor]}
          >
            {
              this.state.bps > 1024
                ? `${ Number.parseInt(this.state.bps / 1024) } kb/s`
                : `${ Number.parseInt(this.state.bps) } b/s`
            }
          </Text>

        </View>

        <View
          style={{ width: iconSize, height: iconSize, position: "relative" }}>
          {
            this.state.showRedDot ?
              <Image
                style={{ width: iconSize, height: iconSize, position: "absolute", display: "none" }}
                source={require("../../Resources/Images/icon_dot.png")}
              />
              : null
          }

          <ImageButton
            style={{ width: iconSize, height: iconSize, position: "absolute" }}
            source={require("../../Resources/Images/icon_more.png")}
            highlightedSource={require("../../Resources/Images/icon_more_pres.png")}
            onPress={() => {
              if (Device.isReadonlyShared) {
                Toast.fail("cloud_share_hint");
                return;
              }
              this.props.navigation.navigate('Setting', { 'title': '设置', hasFirmwareUpdate: this.state.showRedDot });
              this.setState({ showRedDot: false });
              // AlbumHelper.justSnapshot(this.cameraGLView);
              AlbumHelper.snapshotForSetting(this.cameraGLView);
            }}
          />
        </View>

      </LinearGradient>
    );
  }

  _renderVideoControlView() {
    const sleepIcons = [
      {
        source: require('../../Resources/Images/icon_sleep.png'),
        highlightedSource: require('../../Resources/Images/icon_sleep_pres.png'),
        onPress: () => this._toggleSleep(true)// 睡眠
      },
      {
        source: require('../../Resources/Images/icon_sleep_cancle.png'),
        highlightedSource: require('../../Resources/Images/icon_sleep_cancle_pres.png'),
        onPress: () => this._toggleSleep(false)// 唤醒
      }
    ];
    const audioIcons = [
      {
        source: require('../../Resources/Images/icon_audio_on.png'),
        highlightedSource: require('../../Resources/Images/icon_audio_on_pres.png'),
        onPress: () => {
          // if (this.state.isRecording) {
          //   Toast.success("camera_recording_block");
          //   return;
          // }
          // if (this.state.isCalling) {
          //   Toast.success("camera_speaking_block");
          //   return;
          // }
          if (this.state.isCalling) {
            this.isAudioMuteTmp = true;
          }
          this._toggleAudio(true);
        }
      },
      {
        source: require('../../Resources/Images/icon_audio_off.png'),
        highlightedSource: require("../../Resources/Images/icon_audio_off_pres.png"),
        onPress: () => {
          // if (this.state.isRecording) {
          //   Toast.success("camera_recording_block");
          //   return;
          // }
          // if (this.state.isCalling) {
          //   Toast.success("camera_speaking_block");
          //   return;
          // }
          if (this.state.isCalling) {
            this.isAudioMuteTmp = false;
          }
          this._toggleAudio(false);// 默认是这个状态
        }
      }
    ];
    const snapShotIcon = [{
      source: require('../../Resources/Images/icon_screenshot.png'),
      highlightedSource: require('../../Resources/Images/icon_screenshot_pres.png'),
      onPress: () => this._startSnapshot()
    }];

    const recordIcon = [
      {
        source: require('../../Resources/Images/icon_record.png'),
        highlightedSource: require('../../Resources/Images/icon_record_pres.png'),
        onPress: () => this._startRecord()
      }, {
        source: require('../../Resources/Images/icon_recording.png'),
        highlightedSource: require('../../Resources/Images/icon_recording_pres.png'),
        onPress: () => {
          this._stopRecord();
        }
      }
    ];

    const fullScreenIcons = [
      {
        source: require('../../Resources/Images/icon_screen_full.png'),
        highlightedSource: require('../../Resources/Images/icon_screen_full_pres.png'),
        onPress: () => { this.toLandscape(); }
      },
      {
        source: require('../../Resources/Images/icon_screen_full_exit.png'),
        highlightedSource: require('../../Resources/Images/icon_screen_full_exit_pres.png'),
        onPress: () => { this.toPortrait(); }
      }
    ];
    let sleepIndex = this.state.isSleep ? 1 : 0;
    let audioIndex = this.state.isMute ? 1 : 0;
    let recordIndex = this.state.isRecording ? 1 : 0;
    let resolutionText = "";
    switch (this.state.resolution) {
      case 1:
        resolutionText = LocalizedStrings["camera_quality_low"];
        break;
      case 3:
        resolutionText = LocalizedStrings["camera_quality_fhd"];
        break;
      case 0:
      default:
        resolutionText = LocalizedStrings["camera_quality_auto"];
        break;
    }


    if (this.state.showPlayToolBar) {
      return (
        <LinearGradient colors={this.state.fullScreen ? ['#00000099', '#00000000'] : ['#00000000', '#00000099']} style={this.state.fullScreen ? { position: "absolute", right: 0 } : { bottom: 0, position: "absolute" }}>
          <View style={this.state.fullScreen ? styles.videoControlBarFull : styles.videoControlBar}>
            {
              <View style={styles.videoControlBarItem}>
                <ImageButton
                  onPress={sleepIcons[sleepIndex].onPress}
                  style={styles.videoControlBarItemImg}
                  source={sleepIcons[sleepIndex].source}
                  highlightedSource={sleepIcons[sleepIndex].highlightedSource}
                />
              </View>
            }

            <View style={styles.videoControlBarItem}>

              <ImageButton
                onPress={audioIcons[audioIndex].onPress}
                style={styles.videoControlBarItemImg}
                source={audioIcons[audioIndex].source}
                highlightedSource={audioIcons[audioIndex].highlightedSource}
              />
            </View>

            <View style={styles.videoControlBarItem}>

              <ImageButton
                onPress={snapShotIcon[0].onPress}
                style={styles.videoControlBarItemImg}
                source={snapShotIcon[0].source}
                highlightedSource={snapShotIcon[0].highlightedSource}
              />
            </View>

            <View style={styles.videoControlBarItem}>

              <ImageButton
                onPress={recordIcon[recordIndex].onPress}
                style={styles.videoControlBarItemImg}
                source={recordIcon[recordIndex].source}
                highlightedSource={recordIcon[recordIndex].highlightedSource}
              />
            </View>

            <View style={styles.videoControlBarItem}>
              <Text
                style={{ fontSize: 10, textAlign: "center", padding: 4, color: "#ffffff", borderColor: "#FFFFFFCC", borderRadius: 3, borderWidth: 1 }}
                ellipsizeMode="tail"
                numberOfLines={2}
                onPress={() => {
                  if (this.state.isRecording) {
                    Toast.success("camera_recording_block");
                    return;
                  }
                  this.setState({ dialogVisibility: true });
                }
                }
              >
                {resolutionText}
              </Text>
            </View>

            <View style={styles.videoControlBarItem}>

              <ImageButton
                onPress={fullScreenIcons[this.state.fullScreen ? 1 : 0].onPress}
                style={styles.videoControlBarItemImg}
                source={fullScreenIcons[this.state.fullScreen ? 1 : 0].source}
                highlightedSource={fullScreenIcons[this.state.fullScreen ? 1 : 0].highlightedSource}
              />
            </View>
          </View>
        </LinearGradient>
      );

    } else {
      return (null);
    }
  }

  _startCall() {

    this.lastClickTime = new Date().getTime();

    if (this.state.isRecording) {
      Toast.success("camera_recording_block");
      return;
    }

    if (this.state.isSleep) {
      Toast.success("camera_power_off");
      return;
    }

    if (this.state.showPauseView || this.state.showErrorView || this.state.pstate < 2) {
      Toast.success("call_no_play");
      return;
    }

    if (this.isClickCall) {
      return;
    }
    this.isClickCall = true;

    if (this.state.isCalling) {
      return;
    }
    if (Platform.OS === "android") {
      this.isCheckingPermission = true;
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.RECORD_AUDIO, null)
        .then((granted) => {
          this.isCheckingPermission = false;
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_SPEAKER_START_REQ, {})
              .then((retCode) => {
                console.log("speaker on get send callback");
                console.log(retCode);
              })
              .catch((error) => Toast.fail("action_failed", error));
          } else {
            Toast.success("camera_no_audio_permission");
          }
        }).catch((error) => {
          this.isCheckingPermission = false;
          Toast.fail("action_failed", error);
        });
    } else {
      System.permission.request(Permissions.RECORD_AUDIO).then((res) => {
        Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_SPEAKER_START_REQ, {})
          .then((retCode) => {
            console.log("speaker on get send callback");
            console.log(retCode);
          })
          .catch((err) => Toast.fail("action_failed", err));
      }).catch((error) => {
        Toast.success("camera_no_audio_permission");
      });


    }

  }

  _stopCall() {
    if (!this.state.isCalling) {
      return;
    }
    Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_SPEAKER_STOP, {}).then((retCode) => {
      console.log("speaker off get send callback");
      console.log(retCode);
    }).catch((err) => console.log(err));
    this.cameraGLView.stopAudioRecord();
    this._toggleAudio(this.isAudioMuteTmp);// 恢复对讲之前的状态
    this.setState({
      isCalling: false
    });
  }

  _startSnapshot() {
    if (Platform.OS === "android") {
      this.isCheckingPermission = true;
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          this.isCheckingPermission = false;
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this._realStartSnapshot(false, true);
          } else {
            Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
          this.isCheckingPermission = false;
          Toast.success("action_failed");
        });
    } else {
      // no ios's photos const use hardcode
      System.permission.request("photos").then((res) => {
        this._realStartSnapshot(false, true);
      }).catch((error) => {
        Toast.success("camera_no_write_permission");
      });

    }
  }

  _realStartSnapshot(isFromVideo, shouldShowPop) {
    AlbumHelper.snapShot(this.cameraGLView)
      .then((path) => {
        console.log(path);
        this.isForVideoSnapshot = isFromVideo;
        if (!shouldShowPop) {
          return;// 点右上角的设置时，调用截图api，不弹框
        }
        this.setState({ screenshotVisiblity: true, screenshotPath: path });// show snapshotview
        this.snapshotTimeout = setTimeout(() => {
          this.isForVideoSnapshot = false;
          this.setState({ screenshotVisiblity: false, screenshotPath: null });
        }, 3000);
        // 文件路径。
      })
      .catch((error) => {
        console.log(JSON.stringify(error));
        Toast.success("action_failed");
      });
  }

  _stopRecord() {
    if (!this.state.isRecording) {
      return;
    }

    if (this.state.resolution != 3) { // 不是高清
      Service.miotcamera.sendP2PCommandToDevice(
        MISSCommand.MISS_CMD_STREAM_CTRL_REQ, { "videoquality": this.state.resolution })
        .then(() => { // 不修改这些信息。

        })
        .catch((err) => {
          console.log(err);
        });
    }

    if (this.state.isMute) { // 原本静音
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_STOP, {}).then(() => {

      });
      this.cameraGLView.stopAudioPlay();
    }

    this.cameraGLView.stopRecord().then(() => {
      this.setState({ isRecording: false, recordTimeSeconds: 0 });
      if (this.videoRecordPath == null || this.videoRecordPath == "") {
        return;
      }
      setTimeout(() => {
        // 录制成功后 要把视频转存储到相册。
        AlbumHelper.saveToAlbum(this.videoRecordPath, true)
          .then((result) => {
            console.log(result);
          })
          .catch((err) => {
            console.log(err);
          });
        // 录屏完成后 截图一张。
        AlbumHelper.justSnapshot(this.cameraGLView)
          .then(() => {
            this.isForVideoSnapshot = true;
            this.setState({ screenshotVisiblity: true, screenshotPath: AlbumHelper.getSnapshotName() });// show snapshotview

            this.snapshotTimeout = setTimeout(() => {
              this.setState({ screenshotVisiblity: false, screenshotPath: null });
            }, 3000);
          })
          .catch((error) => {
            console.log(JSON.stringify(error));
            Toast.success("action_failed");
          });
      }, 500);
    })
      .catch((error) => {
        this.setState({ isRecording: false, recordTimeSeconds: 0 });
        if (error == -2) {
          Toast.fail("record_video_failed_time_mini");
        } else {
          Toast.fail("record_video_failed");
        }
      });

  }

  _startRecord() {
    if (Platform.OS === "ios") {
      // no ios's photos const use hardcode
      System.permission.request("photos").then((res) => {
        this.realStartRecord();
      }).catch((error) => {
        Toast.success("camera_no_write_permission");
      });
    } else {
      this.isCheckingPermission = true;
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          this.isCheckingPermission = false;
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this.realStartRecord();
          } else {
            Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
          this.isCheckingPermission = false;
          Toast.fail("action_failed", error);
        });
    }
  }

  realStartRecord() {
    let that = this;
    // 切换清晰度为高清
    if (this.state.resolution != 3) { // 不是高清
      Service.miotcamera.sendP2PCommandToDevice(
        MISSCommand.MISS_CMD_STREAM_CTRL_REQ, { "videoquality": 3 })
        .then(() => { // 不修改这些信息。
          // this.setState({ resolution: index })
          // StorageKeys.LIVE_VIDEO_RESOLUTION = index;
          that._realStartRecord(1);
        })
        .catch((err) => {
          console.log(err);
          that._realStartRecord(2);
        });
    } else {
      this._realStartRecord(3);
    }
  }

  _realStartRecord(fromi) {
    console.log(`_realStartRecord called from: ${ fromi }`);
    // 打开声音
    if (this.state.isMute) { // 不是有声音 开启声音通道 不播放
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {}).then((retCode) => {
        console.log("audio start get send callback");
        console.log(retCode);
      });
    }
    let path = AlbumHelper.getFileName(true);
    this.videoRecordPath = path;
    this.cameraGLView.startRecord(`${ Host.file.storageBasePath }/${ path }`, kRecordTimeCallbackName)
      .then((retCode) => {
        console.log(`start record, retCode: ${ retCode }`);
        this.setState({ isRecording: true });
      })
      .catch((err) => {
        console.log(err);
        Toast.success("action_failed");
      });
  }

  _changeResolution(position) {

    // if (this.state.pstate < 2) {//没有连接上
    //   //
    //   return;
    // }
    // if (this.state.isRecording) {
    //   //
    //   return;
    // }
    let index = 0;
    switch (position) {
      case 1:
        index = 1;
        break;
      case 2:
        index = 3;
        break;
      default:
        index = 0;
        break;
    }

    // show dialog for user to choose
    // send p2p cmds
    this.sendResolutionCmd(index);
  }

  sendResolutionCmd(index) {
    Service.miotcamera.sendP2PCommandToDevice(
      MISSCommand.MISS_CMD_STREAM_CTRL_REQ, { "videoquality": index })
      .then(() => {
        this.setState({ resolution: index });
        StorageKeys.LIVE_VIDEO_RESOLUTION = index;
      })
      .catch((err) => {
        console.log(err);
      });
  }

  _toggleSleep(isSleep) {
    if (this.state.isRecording) {
      Toast.success("camera_recording_block");
      return;
    }
    if (this.state.isCalling) {
      Toast.success("camera_speaking_block");
      return;
    }

    if (this.isClickSleep) {
      return;
    }
    this.isClickSleep = true;

    this.lastClickSleepTime = new Date().getTime();

    if (isSleep) {
      RPC.callMethod("set_" + "power", ["off"])
        .then((result) => {
          this.isClickSleep = false;
          this.setState({ isSleep: true, showPlayToolBar: false, showPoweroffView: true, showLoadingView: false, showPauseView: false });
          this.isPowerOn = false;
          CameraPlayer.getInstance().setPowerState(false);
          console.log(result);

          // this.cameraGLView.stopAudioPlay();//释放资源
          // Service.miotcamera.disconnectToDevice();//断开连接
          this._onPause();
        })
        .catch((err) => {
          this.isClickSleep = false;
          if (VersionUtil.judgeIsV1(Device.model)) { // 不管。。。v1原生插件这么干的

          } else {
            Toast.fail("action_failed", err);
          }
        });
    } else {
      RPC.callMethod("set_" + "power", ["on"])
        .then(() => {
          this.isClickSleep = false;
          this.setState({ isSleep: false, showPlayToolBar: true, showPoweroffView: false });
          Service.smarthome.reportLog(Device.model, "on wake up");
          this.queryNetworkJob();
          this.isPowerOn = true;
          CameraPlayer.getInstance().setPowerState(true);
        })
        .catch((err) => {
          this.isClickSleep = false;
          if (VersionUtil.judgeIsV1(Device.model)) { // 不管。。。v1原生插件这么干的

          } else {
            Toast.fail("action_failed", err);
          }
        });
    }
  }

  _toggleAudio(isMute) {
    if (this.state.isMute == isMute) { // 状态一致，没有必要走到下面。
      return;
    }
    if (isMute) {
      if (this.state.isMute) { // 已经静音
        return;
      }
      if (!this.state.isRecording) {
        Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_STOP, {}).then((retCode) => {
          console.log("audio stop get send callback");
          console.log(retCode);
        });
      }

      this.cameraGLView.stopAudioPlay();
      this.setState({ isMute: true });
      return;
    }
    if (!this.state.isRecording) {
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {}).then((retCode) => {
        console.log("audio start get send callback");
        console.log(retCode);
      });
    }
    this.cameraGLView.startAudioPlay();
    this.setState({ isMute: false });

  }

  _renderResolutionDialog() {
    if (Platform.OS == "ios") {
      return this._renderResolutionDialog_ios();
    } else {
      let index = 0;
      switch (this.state.resolution) {
        case 1:
          index = 1;
          break;
        case 3:
          index = 2;
          break;
        default:
          index = 0;
          break;
      }
      return (<SingleChoseDialog title={LocalizedStrings["camera_quality_choose"]}
        dataSource={[LocalizedStrings["camera_quality_auto"], LocalizedStrings["camera_quality_low"], LocalizedStrings["camera_quality_fhd"]]}
        cancel={LocalizedStrings["action_cancle"]}
        confirm={LocalizedStrings["action_confirm"]}
        cancelable={true}
        timeout={0}
        check={index}
        onCancel={(e) => {
          console.log('onCancel', e);
        }}
        onConfirm={(e) => {
          console.log('onConfirm', e);
          let position = e.position;
          this._changeResolution(position);
        }}
        onCheck={(e) => {
          console.log('onCheck', e);
        }}
        onDismiss={() => {
          console.log('onDismiss');
          this.setState({ dialogVisibility: false });
        }}
        visible={this.state.dialogVisibility} />);
    }
  }
  _renderResolutionDialog_ios() {
    return (<ChoiceDialog
      style={{ width: 100 }}
      useNewType={true}
      visible={this.state.dialogVisibility}
      title={LocalizedStrings["camera_quality_choose"]}
      options={[
        {
          title: LocalizedStrings["camera_quality_auto"]
        },
        {
          title: LocalizedStrings["camera_quality_low"]
        },
        {
          title: LocalizedStrings["camera_quality_fhd"]
        }
      ]}
      selectedIndexArray={this.selectedIndexArray}
      onDismiss={(_) => this.setState({
        dialogVisibility: false
      })}
      onSelect={(result) => this.selectedIndexArray = result}
      buttons={[
        {
          text: LocalizedStrings["action_cancle"]
        },
        {
          text: LocalizedStrings["action_confirm"],
          callback: (result) => {
            console.log(`选中的选项`, result);
            this.selectedIndexArray = result;
            this._changeResolution(result[0]);
            this.setState({
              dialogVisibility: false
            });
          }
        }
      ]}
    />);
  }

  _renderLandscapeCallView() {
    if (!this.state.fullScreen) {
      return null;
    }
    if (!this.state.showPlayToolBar) {
      return null;
    }

    const landCallIcons = [
      {
        source: require('../../Resources/Images/icon_call_land.png'),
        highlightedSource: null,
        onPress: () => this._startCall()
      },
      {
        source: require('../../Resources/Images/icon_call_end_land.png'),
        highlightedSource: null,
        onPress: () => this._stopCall()
      }
    ];
    let currentIndex = this.state.isCalling ? 1 : 0;
    return (
      // 控制在底部
      <LinearGradient colors={['#00000000', '#00000099']} style={styles.landscapeCallViewLayout}>
        <View style={styles.landscapeCallViewLayoutImg}>
          <ImageButton
            style={{ width: 80, height: 40 }}
            source={landCallIcons[currentIndex].source}
            highlightedSource={landCallIcons[currentIndex].highlightedSource}
            onPress={landCallIcons[currentIndex].onPress}
          />
        </View>

      </LinearGradient>
    );
  }

  _renderCallViewLayout() {
    if (this.state.fullScreen) {
      return null;
    }
    const callIcons = [
      {
        source: require('../../Resources/Images/icon_call_port.png'),
        highlightedSource: null,
        onPress: () => this._startCall()
      },
      {
        source: require('../../Resources/Images/icon_call_end_port.png'),
        highlightedSource: null,
        onPress: () => this._stopCall()
      }
    ];
    let currentIndex = this.state.isCalling ? 1 : 0;
    return (
      <View style={styles.callViewLayout}>
        <ImageButton
          style={{ width: 95, height: 95 }}
          source={callIcons[currentIndex].source}
          highlightedSource={callIcons[currentIndex].highlightedSource}
          onPress={callIcons[currentIndex].onPress}
        />
      </View>
    );
  }

  _renderBottomLayout() {
    if (this.state.fullScreen) {
      return null;
    }
    const alarmIcon =
    {
      source: require('../../Resources/Images/icon_alarm.png'),
      highlightedSource: require('../../Resources/Images/icon_alarm_pres.png'),
      onPress: () => {
        //
        if (this.state.isRecording) {
          Toast.success("camera_recording_block");
          return;
        }
        if (this.state.isCalling) {
          Toast.success("camera_speaking_block");
          return;
        }
        this._setStatusBarForNativeView();
        Service.miotcamera.showAlarmVideos(AlarmEventType.EventType_All);// todo 完善native端的代码
      },
      text: LocalizedStrings["camera_housekeeping"]
    };

    const playbackIcon =
    {
      source: require('../../Resources/Images/icon_playback.png'),
      highlightedSource: require('../../Resources/Images/icon_playback_pres.png'),
      onPress: () => {
        // todo 获取sd卡状态
        if (!this.canStepOut()) {
          return;
        }
        if (this.state.pstate < 2) {
          // 未连接
          Toast.success("no_playback_for_connect");
          return;
        }
        if (this.sdcardCode == -1) {
          // sdcard状态获取失败
          Toast.success("no_sdcard_error");
          return;
        }
        if (this.sdcardCode == 4) {
          Toast.success("formating_error");
          return;
        }
        if (this.sdcardCode == 3) { // sdcard error
          this.props.navigation.navigate("SDCardSetting");
          return;
        }
        if (this.sdcardCode == 1 || this.sdcardCode == 5) { // nosdcard
          // todo navigate to no memory card page
          this.props.navigation.navigate("SDCardSetting");// 这个页面 如果没有sd卡 也会自己显示空页面。
          return;
        }

        this.props.navigation.navigate("SdcardTimelinePlayerPage", { title: "sdcardPage" });// todo 待实现回看的代码
      },
      text: LocalizedStrings["camera_playback"]
    };

    const cloudIcon =
    {
      source: require('../../Resources/Images/icon_cloud.png'),
      highlightedSource: require('../../Resources/Images/icon_cloud_pres.png'),
      onPress: () => {
        if (this.state.isRecording) {
          Toast.success("camera_recording_block");
          return;
        }
        if (this.state.isCalling) {
          Toast.success("camera_speaking_block");
          return;
        }
        if (!Device.isOwner && !this.isVip) {
          Toast.success("share_user_permission_hint");
          return;
        }
        this._setStatusBarForNativeView();
        Service.miotcamera.showCloudStorage(true, false);// todo 不同model 要区分情况

      },
      text: LocalizedStrings["camera_cloud"]
    };

    const faceIcon =
    {
      source: require('../../Resources/Images/icon_face.png'),
      highlightedSource: require('../../Resources/Images/icon_face_pres.png'),
      onPress: () => {
        if (this.state.isRecording) {
          Toast.success("camera_recording_block");
          return;
        }
        if (this.state.isCalling) {
          Toast.success("camera_speaking_block");
          return;
        }
        if (Device.isFamily) {
          console.log(Device.isFamily);
        }
        if (!Device.isOwner) {
          Toast.success("face_deny_tips");
          return;
        }
        this._setStatusBarForNativeView();
        Service.miotcamera.showFaceRecognize(this.isVip);// todo 不同model 要区分情况
      },
      text: LocalizedStrings["camera_face"]
    };


    return (
      <View style={styles.bottomLayout}>
        <TouchableOpacity
          style={styles.bottomLayoutItem}
          onPress={alarmIcon.onPress}
        >
          <Image
            style={{ width: 30, height: 30 }}
            source={alarmIcon.source}
          />
          <Text
            style={{ margin: 5, fontSize: 10, color: "#333333" }}
            ellipsizeMode="tail"
            numberOfLines={2}
          >
            {alarmIcon.text}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.bottomLayoutItem}
          onPress={playbackIcon.onPress}
        >
          <Image
            style={{ width: 30, height: 30 }}
            source={playbackIcon.source}
          />
          <Text
            style={{ margin: 5, fontSize: 10, color: "#333333" }}
            ellipsizeMode="tail"
            numberOfLines={2}
          >
            {playbackIcon.text}
          </Text>
        </TouchableOpacity>
        {this.state.isInternationServer ?
          null :
          <TouchableOpacity
            style={styles.bottomLayoutItem}
            onPress={cloudIcon.onPress}
          >
            <Image
              style={{ width: 30, height: 30 }}
              source={cloudIcon.source}
            />
            <Text
              style={{ margin: 5, fontSize: 10, color: "#333333" }}
              ellipsizeMode="tail"
              numberOfLines={2}
            >
              {cloudIcon.text}
            </Text>
          </TouchableOpacity>
        }

        {(this.state.isInternationServer || VersionUtil.judgeIsV1(Device.model)) ?
          null :
          <TouchableOpacity
            style={styles.bottomLayoutItem}
            onPress={faceIcon.onPress}
          >
            <Image
              style={{ width: 30, height: 30 }}
              source={faceIcon.source}
            />
            <Text
              style={{ margin: 5, fontSize: 10, color: "#333333" }}
              ellipsizeMode="tail"
              numberOfLines={2}
            >
              {faceIcon.text}
            </Text>
          </TouchableOpacity>
        }



      </View>
    );

  }
}

const styles = StyleSheet.create({
  main: {
    display: 'flex',
    flexDirection: 'column',
    flexWrap: 'nowrap',
    backgroundColor: 'white',
    height: "100%"
  },

  videoContainerNormal: {
    backgroundColor: 'black',
    width: "100%",
    height: "65%",
    position: "relative"// absolute以第一个非static的父类变量作为定位标准
  },

  videoContainerFull: {
    backgroundColor: 'black',
    width: "100%",
    height: "100%",
    position: "relative"// absolute以第一个非static的父类变量作为定位标准
  },

  videoViewNormal: {
    position: "absolute",
    width: "100%",
    height: "100%",
    transform: [{ rotate: '0deg' }]
  },
  videoViewRotation90: {
    position: "absolute",
    width: "100%",
    height: "100%",
    transform: [{ rotate: '270deg' }]
  },
  videoViewRotation270: {
    position: "absolute",
    width: "100%",
    height: "100%",
    transform: [{ rotate: '90deg' }]
  },

  videoControl: {
    position: "absolute",
    bottom: 0,
    width: "100%",
    height: 80
  },

  videoControlBar: {// 横向
    backgroundColor: '#FFF1',
    display: "flex",
    flexWrap: "nowrap",
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-around",
    paddingTop: 25
  },
  videoControlBarFull: {
    backgroundColor: '#FFF1',
    display: "flex",
    width: "100%",
    flexDirection: "row",
    justifyContent: "flex-end"
  },
  videoControlBarItem: {// 内容居中排列
    width: 50,
    height: 50,
    display: "flex",
    justifyContent: "center",
    alignItems: "center"
  },

  videoControlBarItemImg: {
    width: "100%",
    height: "100%"
  },

  landscapeCallViewLayout: {
    width: "100%",
    paddingBottom: 10,
    paddingTop: 20,
    position: "absolute",
    bottom: 0
  },
  landscapeCallViewLayoutImg: {
    display: "flex",
    margin: "auto",
    width: "100%",
    flexDirection: "row",
    justifyContent: "center"
    // textAlign:"center"
  },

  callViewLayout: {
    flexGrow: 1,
    width: "100%",
    flexDirection: "column",
    display: "flex",
    alignItems: "center",
    justifyContent: "center"
  },

  bottomLayout: {
    display: "flex",
    width: "100%",
    height: 60,
    flexDirection: "row",
    flexWrap: 'nowrap'
  },

  bottomLayoutItem: {
    flexGrow: 1,
    height: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center"
  },
  snapShot: {
    position: "absolute",
    bottom: 40,
    left: 0,
    margin: 6,
    width: 100,
    height: 66,
    borderRadius: 5
  },
  snapShotFull: {
    position: "absolute",
    bottom: 10,
    left: 15,
    margin: 6,
    width: 100,
    height: 66,
    borderRadius: 5
  }
});
