import React from 'react';
import { Text, View } from 'react-native';
import PropTypes from 'prop-types';
import { ConfigContext } from "mhui-rn/dist/components/configProvider";
import { styles, Constants, Font, Opacity } from '../../common/styles/Styles';
import { Slider } from '@miblanchard/react-native-slider';


const DEFAULT_SLIDER_HEIGHT = 80;
const DEFAULT_SLIDER_MARGIN_TOP = 20;
const DEFAULT_SLIDER_PADDING_TOP = 20;
const DEFAULT_SLIDER_TEXT_MARGIN_LEFT_RIGHT = 40;
const DEFAULT_THUMB_WIDTH = 154;
const DEFAULT_THUMB_HEIGHT = 80;
const DEFAULT_THUMB_MARGIN = 4;

/**
 * @export public
 * @module NumberSlider
 * @description NumberSlider for CarIoT
 * @property {object} style - style
 * @property {string} title - 标题文本
 * @property {string} startText - 起点文本
 * @property {string} endText - 终点文本
 * @property {string} unit - 单位文本
 * @property {number} value - 滑动条选择数值
 * @property {number} thumbValue - 滑动条显示数值
 * @property {number} minValue - 滑动条最小数值
 * @property {number} maxValue - 滑动条最大数值
 * @property {int} step - 步长
 * @property {bool} trackClickable - 是否可点击
 * @property {function} onValueChange - 滑动时回调方法
 * @property {function} onSlidingComplete - 滑动结束回调方法
 * @property {bool} disabled - 是否禁用，默认值 false
 */
class NumberSlider extends React.PureComponent {
    static contextType = ConfigContext;
    static propTypes = {
      style: PropTypes.object,
      title: PropTypes.string,
      startText: PropTypes.string,
      endText: PropTypes.string,
      unit: PropTypes.string,
      value: PropTypes.number,
      thumbValue: PropTypes.number,
      minValue: PropTypes.number,
      maxValue: PropTypes.number,
      step: PropTypes.number,
      trackClickable: PropTypes.bool,
      onValueChange: PropTypes.func,
      onSlidingComplete: PropTypes.func,
      disabled: PropTypes.bool
    };

    constructor(props) {
      super(props);
      
      this.defaultMinValue = 0;
      this.defaultMaxValue = 100;
      this.defaultStep = 1;
      this.defaultValue = 0;

    }

    componentDidMount() {
    
    }

    selectTemperatureThumb() {
      return (
        <View style={[styles.sliderThumbStyle, { marginLeft: DEFAULT_THUMB_MARGIN, marginRight: DEFAULT_THUMB_MARGIN }, this.props.disabled ? styles.sliderDisableThumbStyle : null]}>
          <Text style={[styles.sliderThumbTextStyle, styles.buttonHightLightTextStyle]}>{this.props.thumbValue}{this.props.unit}</Text>
        </View>
      );
    }

    render() {
      const opacity = this.props.disabled ? Opacity.Disabled : Opacity.Normal;
      const minValue = this.props.minValue ? this.props.minValue : this.defaultMinValue;
      const maxValue = this.props.maxValue ? this.props.maxValue : this.defaultMaxValue;
      const step = this.props.step ? this.props.step : this.defaultStep;
      return (
        <View style={[{
          marginTop: Constants.DEFAULT_MARGIN_TOP_BOTTOM,
          marginBottom: Constants.DEFAULT_MARGIN_TOP_BOTTOM
        }, this.props.style]}
        pointerEvents={this.props.disabled ? "none" : "auto"}
        >
          {this.props.title ? <Text style={[styles.titleTextStyle, { marginBottom: Constants.DEFAULT_TEXT_MARGIN_BOTTOM, opacity }]}>{this.props.title}</Text> : null}
          <View style={[styles.borderStyle, { height: DEFAULT_SLIDER_HEIGHT, borderRadius: DEFAULT_SLIDER_HEIGHT / 2, paddingTop: DEFAULT_SLIDER_PADDING_TOP }]}>
            <View
              style={{
                marginLeft: DEFAULT_SLIDER_TEXT_MARGIN_LEFT_RIGHT,
                marginRight: DEFAULT_SLIDER_TEXT_MARGIN_LEFT_RIGHT,
                marginTop: DEFAULT_SLIDER_MARGIN_TOP,
                flexDirection: "row",
                position: 'absolute',
                opacity
              }}
            >
              <Text style={[styles.subTitleTextStyle, { flex: 1, fontSize: Font.Size._28 }]}>{this.props.startText}{this.props.unit}</Text>
              <Text style={[styles.subTitleTextStyle, { fontSize: Font.Size._28 }]}>{this.props.endText}{this.props.unit}</Text>
            </View>
            <Slider
              maximumTrackTintColor="transparent"
              maximumValue={maxValue}
              minimumTrackTintColor="transparent"
              minimumValue={minValue}
              step={step}
              onValueChange={(value) => {
                if (this.props.onValueChange) {
                  this.props.onValueChange(value);
                }
              }}
              onSlidingComplete={(value) => {
                if (this.props.onSlidingComplete) {
                  this.props.onSlidingComplete(value);
                }
              }}
              value={this.props.value}
              trackClickable={this.props.trackClickable}
              renderThumbComponent={() => this.selectTemperatureThumb()}
              thumbTouchSize={{ width: DEFAULT_THUMB_WIDTH, height: DEFAULT_THUMB_HEIGHT }}
            />
          </View>
        </View>
      );
    }
}

export default NumberSlider;
export { NumberSlider };