'use strict';

import React from 'react';
import {
  View,
  ScrollView, Dimensions, Platform
} from 'react-native';

import ConstDefine from '../Tool/ConstDefine';
import { Device } from "miot";
import { CommonSetting, SETTING_KEYS } from "miot/ui/CommonSetting";

const { width, height } = Dimensions.get('window');
const isIphoneX = (Platform.OS === 'ios' && (Number((`${ height / width }`).substr(0, 4)) * 100) === 216);


const NavigatorBarHeight = ConstDefine.NavigatorBarHeight;


const { first_options } = SETTING_KEYS;
let firstOptions = [], secondOptions = [], extraOptions = {};
export default class MHSetting extends React.Component {
  constructor(props) {
    super(props);

    this._createMenuData();

    this.state = {
      deviceName: Device.name,
      indicatorSwitch: true,
      visibleIconChooseDialog: false,
      findDeviceDialogVisible: false,
      isNewUpdated: [first_options.FIRMWARE_UPGRADE]
    };
  }

  componentWillMount() {

  }

  componentWillUnmount() {

  }

  _createMenuData() { // 准备设置项作为显示列表数据源
    // 显示部分一级菜单项
    firstOptions = [
      first_options.FIRMWARE_UPGRADE
      // first_options.SHARE,
      // first_options.IFTTT,
      // first_options.MEMBER_SET
    ];

    // 显示部分二级菜单项
    secondOptions = [
      secondOptions.SECURITY,
      secondOptions.TIMEZONE,
      secondOptions.FEEDBACK

    ];
    // 显示固件升级二级菜单
    extraOptions = {
      showUpgrade: true,
      excludeRequiredOptions: [
        SETTING_KEYS.first_options.LOCATION,
        SETTING_KEYS.first_options.NAME
      ]
      /* licenseUrl: LocalizedStrings.agreement_url,
            policyUrl: LocalizedStrings.policy_url,
            excludeRequiredOptions: [secondAllOptions.SECURITY] */
    };
  }

  render() {

    return (
      <View style={{
        backgroundColor: '#fff',
        marginTop: 0,
        height: NavigatorBarHeight,
        flex: 1
      }}>

        <ScrollView
          showsVerticalScrollIndicator={false}>
         
          <CommonSetting
            navigation={this.props.navigation}
            firstOptions={firstOptions}
            showDot={this.state.isNewUpdated}
            secondOptions={secondOptions}
            extraOptions={extraOptions}
          />
          <View style={{ height: 20 }}/>
        </ScrollView>


      </View>
    );
  }


  componentDidMount() {

  }
}


