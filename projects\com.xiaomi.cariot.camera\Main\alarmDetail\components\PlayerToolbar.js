import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  Image,
  TouchableOpacity
} from 'react-native';
import Slider from "react-native-slider";
import Util from "../../util2/Util";
import ImageButton from "miot/ui/ImageButton";
import { DescriptionConstants } from '../../Constants';
import TrackUtil from '../../util/TrackUtil';
import {dynamicColor} from 'miot/ui/Style';
import Toast from '../../components/Toast';

const TAG = "AlarmPlayerToolBar";
const SliderMin = 1;
const SliderMax = 100;
export default class PlayerToolbar extends React.Component {
  constructor(props) {
    super(props);

    let dur = this.props.duration;
    let cur = this.props.startTime;
    let isMuteValue = this.props.isMute == null ? true : this.props.isMute;
    let percent = cur / Math.max(1, dur);
    this.state = {
      isPlaying: this.props.isPlaying,
      isMute: isMuteValue,
      isFullscreen: this.props.isFullscreen,
      curTime: cur,
      progress: percent * (SliderMax - SliderMin) + SliderMin,
      duration: dur
    };
    this.mSeek = -1;
    this.mDisableFullscreen = false;
  }

  updateOrientation(isFullscreen) {
    this.setState({isFullscreen: isFullscreen});
  }
  handlePlayPressed() {
    this.setState({
      isPlaying: !this.state.isPlaying
    });
    if (this.props.playPressed) {
      this.props.playPressed(!this.state.isPlaying);
    }
  }

  handleMutePressed() {
    if (!this.state.isMute) {
      TrackUtil.reportClickEvent("Playback_OpenVolume_ClickNum");
    }
    if (this.props.isFastPlay) {
      return;
    }
    
    this.setState({
      isMute: !this.state.isMute
    });
    if (this.props.mutePressed) {
      this.props.mutePressed(!this.state.isMute);
    }
  }

  handleFastPlayProcessed() {
    if (this.props.fastPlayPressed) {
      this.props.fastPlayPressed(!this.props.isFastPlay);
    }

  }
  handleFullscreenPressed() {
    if (this.mDisableFullscreen) {
      Toast.loading('c_setting');
      return;
    }

    this.setState({
      isFullscreen: !this.state.isFullscreen
    });
    if (this.props.fullscreenPressed) {
      this.props.fullscreenPressed(!this.state.isFullscreen);
    }
  }

  disableFullscreenButton(disable) {
    this.mDisableFullscreen = disable;
  }

  updateDuration(duration) {
    this.setState({ duration: duration });
  }

  updatePlayState(aPlaying) {
    this.setState({ isPlaying: aPlaying });
  }

  updateCurTime(curTime, newDuration = -1) {
    if (newDuration != -1 && newDuration != this.state.duration) {
      this.setState({ duration: newDuration });
    }
    if (curTime < 0) {
      this.setState({
        isPlaying: false
      });
    }
    if (this.mSeek < 0) {
      if (curTime < 0) {
        this.setState({
          progress: 0,
          curTime: 0
        });
      } else {
        if (this.state.duration > 0) {
          let percent = Math.min(1.0, curTime / Math.max(1, this.state.duration));
          // console.log(TAG, "updateCurTime", percent, ":",curTime, "~",this.state.duration);
          this.setState({
            progress: percent * (SliderMax - SliderMin) + SliderMin,
            curTime: curTime
          });
        } else {
          console.log(TAG, "ignore time update when loading not finish");
        }
      }
    }
  }

  textFromDuration(duration) {
    let mStr = "00";
    let sStr = "00";
    if (duration) {
      mStr = Util.zeroPad(Math.floor(duration / 60), 10);
      sStr = Util.zeroPad(Math.floor(duration % 60), 10);
    }
    return `${ mStr }:${ sStr }`;
  }

  handleSeekComplete() {
    if (this.mSeek !== -1) {
      this.props.onSeeked((this.mSeek - SliderMin) / (SliderMax - SliderMin));
      this.mSeek = -1;
    }
  }

  handleSeek(aPos) {
    this.mSeek = aPos;
    let ct = this.state.duration * (this.mSeek - SliderMin) / (SliderMax - SliderMin);
    this.setState({ curTime: ct });

  }

  setMute(mute = true) {
    this.setState({ isMute: mute });
  }

  setPlay(playing) {
    this.setState({
      isPlaying: playing
    });
  }

  render() {
    // console.log(TAG, "render this.state.isPlaying", this.state.isPlaying, "this.props.startTime", this.props.startTime);
    let playSource = this.state.isPlaying ? require('../../../resources2/images/icon_camera_pause.png') : require('../../../resources2/images/icon_camera_play.png');
    let muteSource = this.props.isFastPlay ? require("../../../Resources/Images/icon_camera_mute_playback_dis.png") : (this.state.isMute ? require('../../../resources2/images/icon_volume_mute_w.png') : require('../../../Resources/Images/icon_camera_unmute_playback.png'));

    let fastPlaySource = this.props.isFastPlay ? require('../../../Resources/Images/X2_nor.png') : require('../../../Resources/Images/X1_nor.png');
    let cur = this.state.curTime;
    let progress = this.state.progress;
    if (this.state.duration == 0) {
      cur = 0;
      progress = 0;
    }
    let curTime = this.textFromDuration(cur);
    let duration = this.textFromDuration(this.state.duration);
    return (
      <View style={styles.container}>
        <TouchableOpacity
          accessibilityLabel={this.state.isPlaying ? DescriptionConstants.yc_5 : DescriptionConstants.yc_16}
          style={styles.playButton} onPress={() => this.handlePlayPressed()}
          accessible={true}
          accessibilityState={{
            selected:this.state.isPlaying
          }}
          testID={this.state.isPlaying ?'1':'0'}
          >
          <Image
            source={playSource}
            style={styles.img}
          >
          </Image>
        </TouchableOpacity>
        <Text
          style={styles.timeLabel}
          accessibilityLabel={DescriptionConstants.yc_6 + "00:" + curTime}
        >
          {curTime}
        </Text>
        <Slider
          disabled={!(this.state.duration > 0)}
          trackStyle={{ height: 1 }}
          style={{ flexGrow: 1, height: 20, marginLeft: 10, marginRight: 10 }}
          maximumValue={SliderMax}
          minimumValue={SliderMin}
          step={1}
          onSlidingComplete={() => this.handleSeekComplete()}
          onValueChange={(aPos) => this.handleSeek(aPos)}
          minimumTrackTintColor={"#32BAC0"}
          maximumTrackTintColor={"rgba(255,255,255,0.2)"}
          value={progress > SliderMax ? SliderMax : progress}
          thumbTintColor={this.state.duration > 0 ? dynamicColor('#fff','#fff') : "gray"}
          accessible={true}
          accessibilityLabel={!this.state.isFullscreen ? DescriptionConstants.yc_7 : DescriptionConstants.yc_18}
         
        />
        <Text
          style={styles.timeLabel}
          accessibilityLabel={ DescriptionConstants.yc_8 + "00:" + duration}

        >
          {duration}
        </Text>
        <TouchableOpacity
          style={styles.playButton}
          onPress={() => this.handleMutePressed()}
          accessible={true}
          accessibilityLabel={this.state.isMute ? DescriptionConstants.yc_9 : DescriptionConstants.yc_20}
          accessibilityState={{
            selected:this.state.isMute
          }}
          testID={
            this.state.isMute ? '1' :'0'
          }
        >
          <Image
            source={muteSource}
            style={styles.img}
          >

          </Image>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.playButton}
          onPress={() => this.handleFastPlayProcessed()}
          accessibilityLabel={DescriptionConstants.yc_10?.replace('1',this.props.isFastPlay?'2':'1')}

        >
          <Image
            source={fastPlaySource}
            style={styles.img}
          >
          </Image>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.playButton}
          onPress={() => this.handleFullscreenPressed()}
          accessibilityLabel={!this.state.isFullscreen ? DescriptionConstants.yc_11 : DescriptionConstants.yc_22}

        >
          <Image
            source={this.state.isFullscreen ? require('../../../Resources/Images/icon_camera_fullscreen_exit_playback.png') :require('../../../Resources/Images/icon_camera_fullscreen_playback.png')}
            style={styles.img}
          >
          </Image>
        </TouchableOpacity>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  playButton: {
    width: 40,
    height: 40,
    alignItems: 'center'
  },
  timeLabel: {
    color: 'white',
    width:50
  },
  muteButton: {
    width: 40,
    height: 40
  },
  fullScreenButton: {
    width: 40,
    height: 40
  },
  img: {
    width: 40,
    height: 40
  }
});
