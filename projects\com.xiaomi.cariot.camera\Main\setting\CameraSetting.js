'use strict';

import { <PERSON><PERSON><PERSON>, Device, Host } from "miot";
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import Separator from 'miot/ui/Separator';
import React from 'react';

import { ScrollView, View, Text, Platform ,Alert, NativeModules } from 'react-native';
import { ChoiceDialog } from 'miot/ui/Dialog';


import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import StorageKeys from '../StorageKeys';
import { DirectionViewConstant } from '../ui/DirectionView';
import CameraPlayer from '../util/CameraPlayer';
import CameraConfig from '../util/CameraConfig';

import Toast from '../components/Toast';
import { strings, Styles } from "miot/resources";
import { RkButton } from 'react-native-ui-kitten';
import { AbstractDialog, DragGear, MessageDialog, NormalGear, NumberSpinner, StringSpinner } from "mhui-rn";
import SlideGear from "../ui/SlideGear";

import Service from "miot/Service";
import Package from "miot/Package";
import VersionUtil from "../util/VersionUtil";
import API from "../API";
import NavigationBar from "miot/ui/NavigationBar";
import RPC from "../util/RPC";
import TrackUtil from '../util/TrackUtil';
import { DescriptionConstants } from "../Constants";
import CommonMsgDialog from "../ui/CommonMsgDialog";
import AlarmUtil, {
  ALARM_INTERVAL_PIID,
  MOTION_DETECTION_SIID,
  SPEC_PIID_KEY_VOLUME,
  SPEC_SIID_KEY_SPEAKER
} from "../util/AlarmUtil";
import AlarmUtilV2, {
  CAMERA_FACE_SHOT_SIID,
  CAMERA_FACE_SHOT_SWITCH_PIID,
  CAMERA_NIGHT_MODE_SIID,
  CAMERA_NIGHT_MODE_SWITCH_PIID, PIID_ALARM_INTERVAL,
  PIID_CALL_VOLUME,
  PIID_CAMERA_CORRECTION, PIID_CAMERA_HDR,
  PIID_CAMERA_WATERMARK, PIID_COCKPIT_STORAGE_SWITCH, PIID_THEME, PIID_TIME_SHOW,
  SCREEN_SETTING_BRIGHTNESS_PIID, SCREEN_SETTING_DISPLAY_STATE_PIID,
  SCREEN_SETTING_SIID,
  SCREEN_SETTING_SWITCH_PIID,
  SIID_CALL_VOLUME,
  SIID_CAMERA_CONTROL, SIID_COCKPIT_SERVICE, SIID_MOTION_DETECTION, SIID_THEME_SETTINGS,
  STATUS_LIGHT_SIID,
  STATUS_LIGTH_PIID
} from "../util/AlarmUtilV2";
import I18n from "miot/resources/Strings";
import WDRSetting from "./WDRSetting";
import BaseSettingPage from "../BaseSettingPage";
import SleepSetting from "./SleepSetting";
import Util from "../util2/Util";

const HUMAN_TRACK = {
  Key: { did: Device.deviceID, siid: 2, piid: 8 },
  ON: [{ did: Device.deviceID, siid: 2, piid: 8, value: true }],
  OFF: [{ did: Device.deviceID, siid: 2, piid: 8, value: false }]
};

export default class CameraSetting extends BaseSettingPage {

  constructor(props, context) {
    super(props, context);
    this.state = {
      isStatusLightOn: false,
      isDataUsageWarning: false,
      isPtzRotationEnabled: true,
      showRestartConfirm: false,
      isHumanTrack: false,
      isAIFrame: false,
      volumeDialog: false,
      volumeType: 0,
      cameraVolume: -1,
      callCameraVolume: -1,
      showPermissionDialog: false,
      showptzConfirmDialog: false,
      showAutomaticSwitch: false,
      screenLightDialog: false,
      screenLightValue: 25,
      screenLightMode: 1,
      screenCloseDisplay: false, // 息屏显示
      nightMode: false,
      faceShotSwitch: false, // 人脸特写
      wdrMode: false, // 宽动态
      screenTheme: 1,
      interval: 10,
      intervalIndex: 2,
      hdrValue: "关闭",
      HDRIndex: 0
    };
    this.isSupportPtzCheck = CameraConfig.isSupportPtzCheck(Device.model);
    this.isSupportHumanTrack = CameraConfig.isSupportHumanTack(Device.model);// 022 021 039 029
    this.intervalData = [
      {title: `3${LocalizedStrings['tip_time_minute']}`, value: 3},
      {title: `5${LocalizedStrings['tip_time_minute']}`, value: 5},
      {title: `10${LocalizedStrings['tip_time_minute']}`, value: 10}
    ];
    this.HDRData = [
      {title: `关闭`, value: 0},
      {title: `打开`, value: 1},
      {title: `自动`, value: 2}
    ];
    this.motionObj = {};
    this.motionObj.startTime = '';
    this.motionObj.endTime = '';
    this.motionObj.isAlarmOn = false;
    this.motionObj.interval = 5;
    if (CameraConfig.getInternationalServerStatus()) {
      this.isSupportMotionTrack = false;// 海外服务器移动追踪不打开
      if (CameraConfig.Model_xiaomi_c01a01 == Device.model) {// 产品们如此定义要求的
        this.isSupportMotionTrack = true;
      }
    } else {
      this.isSupportMotionTrack = CameraConfig.isSupportMotionTrack(Device.model);
    }


    StorageKeys.AUTOMATIC_MODE.then((result) => {
      if (result === "") {
        StorageKeys.AUTOMATIC_MODE = false;
      } else {
        this.isAutomaticModeOpen = result;
      }
      console.log("res-----------------✈✈✈✈-----------------✈", result);
    });
    this.isSupportPhysicalCover = CameraConfig.isSupportPhysicalCover(Device.model);
  }

  getTitle() {
    return LocalizedStrings['s_camera_setting'];
  }

  renderSettingContent() {
    return (
      <View style={styles.container}>

        <View showsVerticalScrollIndicator={false}>

          <View style={styles.featureSetting}
            key={8}
          >
            <View style={styles.titleContainer}>
              <Text style={styles.title} accessibilityLabel={DescriptionConstants.sz_3} >{LocalizedStrings['cs_image_setting']}</Text>
            </View>
            {/* 时间水印 */}
            <ListItemWithSwitch
              accessibilityLabel={DescriptionConstants.sz_4_53}
              title={LocalizedStrings['is_watermark']}
              showSeparator={false}
              value={this.state.isWatermarkEnable}
              onValueChange={(value) => this._onWatermarkValueChange(value)}
              titleStyle={{ fontWeight: 'bold' }}
              onPress={() => {
              }}
              accessibilitySwitch={{
                accessibilityLabel: DescriptionConstants.sz_4_53
              }}
            />

            {/* 镜头畸变纠正 */}
            <ListItem
              accessibilityLabel={DescriptionConstants.sz_4_55}
              title={LocalizedStrings['is_lens_distortion_correction']}
              showSeparator={false}

              value={this.state.isLDCEnable ? LocalizedStrings['on'] : LocalizedStrings['off']}
              onValueChange={(value) => this._onLDCValueChange(value)}
              titleStyle={{ fontWeight: 'bold' }}
              unlimitedHeightEnable={true}
              titleNumberOfLines={2}
              onPress={() => {
                this.props.navigation.navigate('LDCSetting', {
                  selectLDCCallBack: ((value) => {
                    this.setState({
                      isLDCEnable: value
                    });
                  }),
                  isLDCEnable: this.state.isLDCEnable
                });
              }}
              accessibilitySwitch={{
                accessibilityLabel: DescriptionConstants.sz_4_55
              }}
            />

            {/* 宽动态范围模式(WDR) */}
            {CameraConfig.isSupportWDR(Device.model) ?
              <ListItem
                title={CameraConfig.isSupportHDR(Device.model) ? LocalizedStrings['setting_hdr'] : LocalizedStrings['setting_wdr']}
                value={this.state.hdrValue}
                showSeparator={false}
                onPress={() => {
                  TrackUtil.reportClickEvent("WDRNumber");
                  this.setState({isHDRVisible: true});
                }}
                titleStyle={{ fontWeight: 'bold' }}
                valueNumberOfLines={3}
                titleNumberOfLines={2}
              /> : null
            }

            <ListItem
              accessibilityLabel={DescriptionConstants.sz_4_59}
              title={LocalizedStrings['is_rotate_image']}
              showSeparator={false}
              onPress={() => {
                TrackUtil.reportClickEvent("RoteScreenNumber");
                this.props.navigation.navigate('ImageRotateSetting');
              }}
              titleStyle={{ fontWeight: 'bold' }}

            />

            {/*视频存储*/}
            <ListItemWithSwitch
              accessibilityLabel={DescriptionConstants.sz_4_53}
              title={LocalizedStrings['show_nas_info']}
              showSeparator={false}
              value={this.state.storageSwitch}
              onValueChange={(value) => this._onStorageValueChange(value)}
              titleStyle={{ fontWeight: 'bold' }}
              onPress={() => {
              }}
              accessibilitySwitch={{
                accessibilityLabel: DescriptionConstants.sz_4_53
              }}
            />

            <ListItem
              title={LocalizedStrings['ss_alarm_interval']}
              showSeparator={false}
              subtitle={LocalizedStrings['ss_alarm_interval_description']}
              value={this.state.interval + LocalizedStrings["tip_time_minute"]}
              onPress={() => {
                this.setState({ isPickerVisiable: true });

              }

              }
              unlimitedHeightEnable={true}

              titleStyle={{ fontWeight: 'bold' }}
              titleNumberOfLines={2}
              subtitleNumberOfLines={10}


            />

            <ListItem
              title={LocalizedStrings['camera_volume_setting']}
              titleNumberOfLines={2}
              showSeparator={false}
              onPress={() => {
                this.volumeNewValue = this.state.cameraVolume;
                this.setState({ volumeDialog: true, volumeType: 1, tempCallVolumeValue: this.state.cameraVolume });
              }}
              value={this.state.cameraVolume >= 0 ? `${ this.state.cameraVolume }%` : ""}
              titleStyle={{ fontWeight: 'bold' }}
            />


            <ListItem
              title={LocalizedStrings['ptz_check']}
              showSeparator={false}
              onPress={() => {
                this.setState({ showptzConfirmDialog: true });
                // 暂时屏蔽对话框，因为没有通过多语言文案审查
                // this.handle_ptz_check();
              }
              }
              titleStyle={{ fontWeight: 'bold' }}
              accessibilityLabel={DescriptionConstants.sz_4_14}
            />

          </View>





          <MessageDialog
            visible={this.state.showRestartConfirm}
            title={LocalizedStrings['cs_restart']}
            message={LocalizedStrings['cs_restart_detail']}
            onDismiss={() => this.setState({ showRestartConfirm: false })}
            buttons={[
              { text: LocalizedStrings["action_cancle"], callback: () => this.setState({ showRestartConfirm: false }) },
              { text: LocalizedStrings["action_confirm"], callback: () => this._restart() }
            ]}
            accessible={true}
            accessibilityLabel={DescriptionConstants.sz_4_19}
          />
        </View>


        {this._renderPermissionDialog()}
        {this._renderPtz_checkConfirmDialog()}
        {this._renderVolumeDialog()}
        {this._renderAutomaticSwitch()}
        {this._renderIntervalDialog()}
        {this._renderHDRDialog()}
      </View>
    );
  }

  _renderIntervalDialog() {
    return (
      <ChoiceDialog
        visible={this.state.isPickerVisiable}
        title={LocalizedStrings['ss_alarm_interval']}
        dialogStyle={{
          titleStyle: {
            fontSize: 18,
            fontWeight: '400'
          }}}
        useNewType={true}
        options={this.intervalData}
        selectedIndexArray={[this.state.intervalIndex]}
        itemStyleType={2}
        onDismiss={(_) => this.setState({ isPickerVisiable: false })}
        buttons={[{
          text: I18n.cancel,
          callback: () => {
            this.setState({ isPickerVisiable: false });
          }
        }, {
          text: I18n.ok,
          callback: (res) => {
            console.log("result1", res);
            let value = this.intervalData[res[0]].value;
            this._onIntervalChanged(value,res[0]);
          }
        }]}
      />
    );
  }

  _renderHDRDialog() {
    return (
      <ChoiceDialog
        visible={this.state.isHDRVisible}
        title={LocalizedStrings['setting_hdr']}
        dialogStyle={{
          titleStyle: {
            fontSize: 18,
            fontWeight: '400'
          }}}
        useNewType={true}
        options={this.HDRData}
        selectedIndexArray={[this.state.HDRIndex]}
        itemStyleType={2}
        onDismiss={(_) => this.setState({ isHDRVisible: false })}
        buttons={[{
          text: I18n.cancel,
          callback: () => {
            this.setState({ isHDRVisible: false });
          }
        }, {
          text: I18n.ok,
          callback: (res) => {
            console.log("result1", res);
            let value = this.HDRData[res[0]].value;
            this._onHDRChanged(value,res[0]);
          }
        }]}
      />
    );
  }

  _onHDRChanged(value,index = 0) {
    console.log("value", value);
    try {
      let intValue = Number.parseInt(value);
      if (isNaN(intValue) || intValue == Infinity) {
        Toast.fail("c_set_fail", "value is Nan or infinity");
        return;
      }
    } catch (err) {
      Toast.fail("c_set_fail", err);
      return;
    }
    this.setState({ isHDRVisible: false });

    Toast.loading('c_setting');

    const params = [{ sname: SIID_CAMERA_CONTROL, pname: PIID_CAMERA_HDR, value: value }];

    AlarmUtilV2.setSpecPValue(params).then((res) => {
      if (res[0].code == 0) {
        this.setState({ hdrValue: this.HDRData[index].title, HDRIndex: index });
        Toast.success('c_set_success');
      } else {
        Toast.fail('c_set_fail');
      }
    }).catch((err) => {
      this.setState({ detectionSwitch: !value });
      Toast.fail('c_set_fail', err);
    });
  }

  _onIntervalChanged(value,index = 0) {
    console.log("value", value);
    switch (Number.parseInt(value)) {
      case 3:
        TrackUtil.reportResultEvent("MonitoringSetting_Interval_Status", "type", 1);
        break;
      case 5:
        TrackUtil.reportResultEvent("MonitoringSetting_Interval_Status", "type", 2);
        break;
      case 10:
        TrackUtil.reportResultEvent("MonitoringSetting_Interval_Status", "type", 3);
        break;
      case 30:
        TrackUtil.reportResultEvent("MonitoringSetting_Interval_Status", "type", 4);
        break;
    }

    try {
      let intValue = Number.parseInt(value);
      if (isNaN(intValue) || intValue == Infinity) {
        Toast.fail("c_set_fail", "value is Nan or infinity");
        return;
      }
    } catch (err) {
      Toast.fail("c_set_fail", err);
      return;
    }
    this.setState({ isPickerVisiable: false });

    Toast.loading('c_setting');

    const params = [{ sname: SIID_MOTION_DETECTION, pname: PIID_ALARM_INTERVAL, value: value }];

    AlarmUtilV2.setSpecPValue(params).then((res) => {
      if (res[0].code == 0) {
        this.setState({ interval: parseInt(value), intervalIndex: index });
        Toast.success('c_set_success');
      } else {
        Toast.fail('c_set_fail');
      }
    }).catch((err) => {
      this.setState({ detectionSwitch: !value });
      Toast.fail('c_set_fail', err);
    });
  }


  renderSettingBottomContent() {
    return (
      <View style={{ flexDirection: 'row', bottom: 27 }}
            key={11}
      >
        <RkButton
          style={{ margin: Styles.common.padding, flexGrow: 1, height: 46, borderRadius: 23, backgroundColor: '#f5f5f5', display: 'flex', bottom: -20 }}
          onPress={() => {
            TrackUtil.reportClickEvent("RebootNumber");
            this.setState({ showRestartConfirm: true });

          }
          }
          activeOpacity={0.8}
        >
          <Text style={{ color: '#F43F31', fontSize: 16, fontWeight: 'bold', textAlign: 'center' }}>
            {LocalizedStrings['cs_restart']}
          </Text>
        </RkButton>
      </View>
    )
  }
  _renderVolumeDialogV2() {
    return (
      <AbstractDialog
        visible={this.state.volumeDialog}
        title={LocalizedStrings.camera_volume_setting_dialog}
        showSubtitle={false}
        onDismiss={() => {
          this.setState({ volumeDialog: false });
        }}
        useNewTheme
        canDismiss={true}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ volumeDialog: false });
              console.log('取消');
            }
          },
          {
            text: LocalizedStrings.action_confirm,
            callback: () => {
              this.setState({ volumeDialog: false });
              let params = [{sname: SPEC_SIID_KEY_SPEAKER, pname: SPEC_PIID_KEY_VOLUME, value: this.volumeNewValue}];
              if (this.state.volumeType === 0){
                //对讲音量设置
                params = [{sname: SIID_CALL_VOLUME, pname: PIID_CALL_VOLUME, value: this.volumeNewValue}];
              }
              AlarmUtilV2.setSpecPValue(params).then(()=>{
                if (this.state.volumeType === 0){
                  this.setState({ callCameraVolume: this.volumeNewValue });
                }else {
                  this.setState({ cameraVolume: this.volumeNewValue });
                }
              }).catch((err)=>{
                console.log("setSpeakerVolume err=", JSON.stringify(err));
              });
            }
          }
        ]}>
        <StringSpinner
          style={{ width: 300, height: 200, alignSelf: "center", marginBottom: 15 }}
          dataSource={["0", "10", "20", "30", "40", "50", "60", "70", "80", "90", "100"]}
          defaultValue={`${ this.state.cameraVolume }`}
          valueFormat={"%.0f"}
          pickerInnerStyle={{ 
            lineColor: "#00000000",
            // textColor: "#ff0000",
            // selectTextColor: "#0000FF",
            fontSize: 17,
            selectFontSize: 22,
            rowHeight: 35,
            // selectBgColor: "#f5f5f5"
          }}
          unit={"%"}
          onValueChanged={(data) => {
            console.log(`newValue:${ data.newValue },oldValue:${ data.oldValue }`);
            this.volumeNewValue = Number.parseInt(data.newValue);
          }}
          onValueFastChanged={(data) => {
            // console.log(`mhpicker:${JSON.stringify(data)}`);
            console.log("ValueFastChanged", data.nativeEvent);
            this.volumeNewValue = Number.parseInt(data.nativeEvent.newValue);
          }}
        />
      </AbstractDialog>
    );
  }

  _renderVolumeDialog() {
    // this.options = this.state.volumeType === 0 ? Array.from({ length: 10 }, (v, i) =>  (i + 1) * 10) : Array.from({ length: 11 }, (v, i) =>  i * 10);
    this.options = Array.from({ length: 10 }, (v, i) =>  (i + 1) * 10);
    let subtitle = this.state.tempCallVolumeValue == -1 ? '100%' : `${this.state.tempCallVolumeValue}%`;
    let defaultValueIndex = this.state.tempCallVolumeValue == -1 ? 9
      : this.options.indexOf(this.state.tempCallVolumeValue);
    return (
      <AbstractDialog
        visible={this.state.volumeDialog}
        title={LocalizedStrings['camera_volume_setting']}
        subtitle={subtitle}
        showSubtitle={true}
        useNewTheme={true}
        onDismiss={(_) => this.setState({ volumeDialog: false })}
        buttons={[{
          text: I18n.cancel,
          callback: () => {
            this.setState({ volumeDialog: false });
          }
        }, {
          text: I18n.ok,
          callback: (value) => {
            console.log("result1", this.state.lightValue,this.state.tempLightValue);
            this.setState({ volumeDialog: false });
            let params = [{sname: SPEC_SIID_KEY_SPEAKER, pname: SPEC_PIID_KEY_VOLUME, value: this.volumeNewValue}];
            if (this.state.volumeType === 0){
              //对讲音量设置
              params = [{sname: SIID_CALL_VOLUME, pname: PIID_CALL_VOLUME, value: this.volumeNewValue}];
            }
            AlarmUtilV2.setSpecPValue(params).then((res)=>{
              if (res[0].code === 0) {
                Toast.fail("c_set_success");
                if (this.state.volumeType === 0){
                  this.setState({ callCameraVolume: this.volumeNewValue });
                }else {
                  this.setState({ cameraVolume: this.volumeNewValue });
                }
              } else {
                Toast.fail("c_set_fail");
              }

            }).catch((err)=>{
              Toast.fail("c_set_fail");
              console.log("setSpeakerVolume err=", JSON.stringify(err));
            });
          }
        }]}
      >

        <View>
          <SlideGear
            options={this.options}
            value={defaultValueIndex}
            maximumTrackTintColor={ DarkMode.getColorScheme() == 'dark' ? 'xm#2D2D2D' : "#E2E2E2" }
            minimumTrackTintColor={ '#32BAC0' }
            blockStyle={{backgroundColor: '#32BAC0', width: 20}}
            type={SlideGear.TYPE.RECTANGLE}
            indicatorTextStyle={ { color: DarkMode.getColorScheme() == 'dark' ? '#0000000F' : 'black' } }
            stopAtIndicatorText={ true }
            containerStyle={{ marginHorizontal: 28, height: 30 }}
            onValueChange={index => {
              console.log("===onValueChange===",index);
              this.setState({ tempCallVolumeValue: this.options[index] });
            }}
            onSlidingComplete={index => {
              console.log("===onSlidingComplete===",index,this.state.callVolumeValue,this.options[index])
              this.volumeNewValue = this.options[index];
              this.setState({ tempCallVolumeValue: this.options[index] });
            }}
            showEndText={false}
          />
          <View style={{ flex: 1, flexDirection: 'row',justifyContent: 'space-between', marginBottom: 26, marginTop: 5, marginHorizontal: 28 }}>
            <Text>{this.state.volumeType === 0 ? "10" : "0"}</Text>
            <Text>{"100%"}</Text>
          </View>
        </View>
      </AbstractDialog>
    );
  }

  handle_ptz_check() {
    // 改为spec方式
    if (!CameraPlayer.getInstance().getPowerState()) {
      Toast.success("camera_close_off");
      return;
    }
    // CameraPlayer.getInstance().sendDirectionCmd(DirectionViewConstant.CMD_CHECK, true);
    // 由p2p2改为spec方式进行云存校准
    let params = {
      did: Device.deviceID,
      siid: 21,
      aiid: 3,
      in: []
    };
    Service.spec.doAction(params).then((res) => {
      console.log("======= doAction delete success", res);
    }).catch((err) => {
      console.log("======== doAction delete fail:",err);
    });
  }

  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    let message = LocalizedStrings["cs_float_window_permission"];
    return (
      <MessageDialog
        title={LocalizedStrings["tips"]}
        message={message}
        messageStyle={{
          fontSize: 14,
          textAlign: 'center'
      }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ showPermissionDialog: false });
            }
          },
          {
            text: LocalizedStrings["setting"],
            callback: () => {
              Host.ui.openTerminalDeviceSettingPage(1);
              this.setState({ showPermissionDialog: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog} />
    );
  }

  _renderPtz_checkConfirmDialog() {
    if (!this.state.showptzConfirmDialog) {
      return null;
    }
    let message = LocalizedStrings["ptz_check_tip"];
    return (
      <MessageDialog
        title={LocalizedStrings["ptz_check_title"]}
        message={message}
        messageStyle={{
          fontSize: 14,
      }}
      accessible
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ showptzConfirmDialog: false });
            }
          },
          {
            text: LocalizedStrings["btn_confirm"],
            callback: () => {
              this.handle_ptz_check();
              this.setState({ showptzConfirmDialog: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showptzConfirmDialog: false });
        }}
        visible={this.state.showptzConfirmDialog} />
    );
  }
  _renderAutomaticSwitch() {
    if (!this.state.showAutomaticSwitch) {
      return null;
    }
    // let message = <ListItemWithSwitch
    //   showSeparator={false}
    //   title={"自动化"}
    //   value={StorageKeys.AUTOMATIC_MODE}
    //   onValueChange={() => { Toast.success("c_set_success"); }}
    //   titleStyle={{ fontWeight: 'bold' }}
    //   onPress={() => {
    //   }}
    //   accessibilityLabel={DescriptionConstants.sz_6_5}
    //   accessibilitySwitch={{
    //     accessibilityLabel: DescriptionConstants.sz_6_5
    //   }}
    // />;
    return (
      <ChoiceDialog
        visible={this.state.showAutomaticSwitch}
        title={"自动化模式"}
        options={
          [{ title: "开启" }, { title: "关闭" }]
        }
        // selectedIndexArray={[0]}
        selectedIndexArray={this.isAutomaticModeOpen ? [0] : [1]}
        onDismiss={() => this.setState({ showAutomaticSwitch: false })}
        onSelect={(index) => this._onModeValueWillChange(index == 1 ? 0 : 1)}
      />
    )
  }
  componentDidMount() {
    super.componentDidMount();
    // 拉数据
    if (VersionUtil.isUsingSpec(Device.model)) {

      // AlarmUtil.getSpeakerVolume().then((res) => {
      //   console.log("getSpeakerVolume res=", JSON.stringify(res));
      //   this.setState({ cameraVolume: res[0].value });
      //   this.volumeNewValue = res[0].value;
      // }).catch((err) => {
      //   console.log("getSpeakerVolume err=", JSON.stringify(err));
      // });

      let params = [
        { sname: SIID_CAMERA_CONTROL, pname: PIID_CAMERA_WATERMARK },
        { sname: SIID_COCKPIT_SERVICE, pname: PIID_COCKPIT_STORAGE_SWITCH },
        { sname: SIID_MOTION_DETECTION, pname: PIID_ALARM_INTERVAL },
        { sname: SIID_CAMERA_CONTROL, pname: PIID_CAMERA_HDR },
        { sname: SPEC_SIID_KEY_SPEAKER, pname: SPEC_PIID_KEY_VOLUME }
      ];
      AlarmUtilV2.getSpecPValue(params,2)
        .then((result) => {
          let isFail = false;
          let stateProps = {};

          // 时间水印
          if (result[0].code === 0) {
            stateProps.isWatermarkEnable = result[0].value;
          } else {
            isFail = true;
          }

          // 视频存储开关
          if (result[1].code === 0) {
            stateProps.storageSwitch = result[1].value;
          } else {
            isFail = true;
          }

          if (result[2].code === 0) {
            let alarmInterval = result[2].value;
            let index = this.intervalData.findIndex((aItm) => {
              return aItm.value == alarmInterval;
            });
            stateProps.interval = alarmInterval;
            stateProps.intervalIndex = index;
          } else {
            isFail = true;
          }

          if (result[3].code === 0) {
            let hdrValue = result[3].value;
            let index = this.HDRData.findIndex((aItm) => {
              return aItm.value == hdrValue;
            });
            stateProps.hdrValue = this.HDRData[index].title;
            stateProps.HDRIndex = index;
          } else {
            isFail = true;
          }

          if (result[4].code === 0) {
            stateProps.cameraVolume = result[4].value;
          } else {
            isFail = true;
          }
          this.setState(stateProps);
          if (isFail) {
            Toast.fail('c_get_fail');
          }
        })
        .catch((err) => {
          Toast.fail('c_get_fail', err);
        });
      AlarmUtil.getAiFrameSwitch022().then((res) => {
        this.setState({
          isAIFrame: res[0].value
        });
      }).catch((err) => {
        console.log("getAiFrameSwitch022 err=", JSON.stringify(err));
      })
    } else {
      RPC.callMethod("get_prop", [
        'light'
      ]).then((res) => {
        this.setState({
          isStatusLightOn: res.result[0] == 'on'
        });
      }).catch((err) => {
        Toast.fail('c_get_fail', err);
      });
    }
    StorageKeys.IS_AI_FRAME_OPEN.
      then((result) => {
        this.setState({
          isAIFrame: result
        });
      })
      .catch((err) => {
        console.log("======AIFrame",err);
        Toast.fail('c_get_fail', err);
      });

    StorageKeys.IS_DATA_USAGEE_WARNING.then((res) => {
      this.setState({
        isDataUsageWarning: res
      });
    }).catch((err) => {
      console.log("======USAGEE",err);
      Toast.fail('c_get_fail', err);
    });

    StorageKeys.IS_PTZ_ROTATION_ENABLE.then((res) => {
      this.setState({
        isPtzRotationEnabled: res
      });
    }).catch((err) => {
      console.log("======spec",err);
      Toast.fail('c_get_fail', err);
    });

    if (CameraConfig.isSupportWDR(Device.model) || CameraConfig.isSupportHDR(Device.model)) {
      WDRSetting.get_wdr().then((res) => {
        console.log(`image get_wdr：${ res }`);
        this.setSelectWDR(res);
      }).catch((err) => {
        console.log("======",err)
        Toast.fail('c_get_fail', err);
      });

    }

    // 人形追踪
    if (this.isSupportHumanTrack) {
      this._getHumanTrack();
    }



    this.isSupportPtzCheck = CameraConfig.isSupportPtzCheck(Device.model);
    let vv = Host.systemInfo;
    let isSupportPlat = (Host.apiLevel>=10059) && (Platform.OS == 'ios');//10059及以上版本
    let isLowIphone8Device = ((vv.mobileModel.indexOf('iPhone5') == 0) || (vv.mobileModel.indexOf('iPhone6') == 0) || (vv.mobileModel.indexOf('iPhone7') == 0)|| (vv.mobileModel.indexOf('iPhone8') == 0)|| (vv.mobileModel.indexOf('iPhone9') == 0));
    if (isSupportPlat && !isLowIphone8Device && parseFloat(vv.sysVersion) >= 13 && CameraConfig.isSupportAIFrame(Device.model)) {
      this.setState({ isSupportAIFrame: true })
    }
    if (Platform.OS === "android" && (CameraConfig.isSupportAIFrame(Device.model))) {
      if (NativeModules.MHCameraSDK.isAIFaceAvailable) {
        NativeModules.MHCameraSDK.isAIFaceAvailable(Device.deviceID, (ret, value) => {
          if (ret) {
            this.setState({ isSupportAIFrame: true });
          }
        });
      }
    }
    if (VersionUtil.Model_Chuangmi_022 == Device.model) {
      this.setState({ isSupportAIFrame: true });
    }

    if (CameraConfig.isDeviceCorrect(Device.model)) {
      const params = [{ sname: SIID_CAMERA_CONTROL, pname: PIID_CAMERA_CORRECTION }];
      AlarmUtilV2.getSpecPValue(params,2)
        .then((res) => {
          if (res[0].code == 0) {
            this.setState({
              isLDCEnable: res[0].value
            });
          } else {
            Toast.fail('c_get_fail');
          }
        })
        .catch((err) => {
          console.log("=========correct",err);
          Toast.fail('c_get_fail', err);
        });
    }else {
      StorageKeys.IS_LENS_DISTORTION_CORREECTION.then((res) => {
        this.setState({
          isLDCEnable: res
        });
      }).catch(() => {
        this.setState({
          isLDCEnable: false
        });
      });
    }

  }

  _onModeValueWillChange(value) {
    StorageKeys.AUTOMATIC_MODE = this.isAutomaticModeOpen = (value == 1);
    // 做一些处理
    Toast.success('c_set_success');
  }
    
  _onStatusLightValueChange(value) {
    TrackUtil.reportClickEvent("StatusLight_ClickNum");

    if (VersionUtil.isUsingSpec(Device.model)) {

      let param = [{ sname: STATUS_LIGHT_SIID, pname: STATUS_LIGTH_PIID, value: value }];

      AlarmUtilV2.getSpecPValue(param)
        .then((result) => {
          if (result[0].code == 0) {
            this.setState({
              isStatusLightOn: value
            });
            Toast.success('c_set_success');
          } else {
            this.setState({
              isStatusLightOn: !value
            });
            Toast.fail('c_set_fail');
          }
        })
        .catch((err) => {
          this.setState({
            isStatusLightOn: !value
          });
          Toast.fail('c_set_fail', err);
        });
    } else {
      RPC.callMethod("set_light", [
        value ? 'on' : 'off'
      ]).then((res) => {
        this.setState({
          isStatusLightOn: res.result[0] == 'OK' ? value : !value
        });
        Toast.success('c_set_success');
      }).catch((err) => {
        this.setState({
          isStatusLightOn: !value
        });
        Toast.fail('c_set_fail', err);
      });
    }

  }

  _onLDCValueChange(value) {
    TrackUtil.reportClickEvent("LensDistortionCorrectionOnOff_ClickNum");
    if (CameraConfig.isDeviceCorrect(Device.model)) {
      const params = [{ sname: SIID_CAMERA_CONTROL, pname: PIID_CAMERA_CORRECTION, value: value }];
      AlarmUtilV2.setSpecPValue(params)
        .then((res) => {
          if (res[0].code == 0) {
            this.setState({
              isLDCEnable: value
            });
          } else {
            this.setState({
              isLDCEnable: !value
            });
            Toast.fail('c_set_fail');
          }
        })
        .catch((err) => {
          this.setState({
            isLDCEnable: !value
          });
          Toast.fail('c_set_fail', err);
        });
    } else {
      StorageKeys.IS_LENS_DISTORTION_CORREECTION = value;
      this.setState({
        isLDCEnable: value
      });
      Toast.success('c_set_success');
    }

  }

  _onStorageValueChange(value) {
    Toast.loading('c_setting');
    const params = [{sname: SIID_COCKPIT_SERVICE, pname: PIID_COCKPIT_STORAGE_SWITCH, value: value}];
    AlarmUtilV2.setSpecPValue(params).then((res) => {
      if (res[0].code == 0) {
        this.setState({
          storageSwitch: value
        });
        Toast.success('c_set_success');
      } else {
        this.setState({
          storageSwitch: !value
        });
        Toast.fail('c_set_fail');
      }
    }).catch((err) => {
      this.setState({
        storageSwitch: !value
      });
      Toast.fail('c_set_fail', err);
    });

  }


  _onWatermarkValueChange(value) {
    TrackUtil.reportClickEvent("WatermarkOnOff_ClickNum");
    Toast.loading('c_setting');
    if (VersionUtil.isUsingSpec(Device.model)) {
      const params = [{sname: SIID_CAMERA_CONTROL, pname: PIID_CAMERA_WATERMARK, value: value}];
      AlarmUtilV2.setSpecPValue(params).then((res) => {
        if (res[0].code == 0) {
          this.setState({
            isWatermarkEnable: value
          });
          StorageKeys.IS_WATERMARK_OPEN = value;
          Toast.success('c_set_success');
        } else {
          this.setState({
            isWatermarkEnable: !value
          });
          StorageKeys.IS_WATERMARK_OPEN = !value;
          Toast.fail('c_set_fail');
        }
      }).catch((err) => {
        this.setState({
          isWatermarkEnable: !value
        });
        StorageKeys.IS_WATERMARK_OPEN = !value;
        Toast.fail('c_set_fail', err);
      });
    } else {
      RPC.callMethod("set_watermark", [
        value ? 'on' : 'off'
      ]).then((res) => {
        this.setState({
          isWatermarkEnable: res.result[0] == 'OK' ? value : !value
        });
        StorageKeys.IS_WATERMARK_OPEN = (res.result[0] == 'OK' ? value : !value);
        Toast.success('c_set_success');
      }).catch((err) => {
        this.setState({
          isWatermarkEnable: !value
        });
        StorageKeys.IS_WATERMARK_OPEN = !value;
        Toast.fail('c_set_fail', err);
      });
    }

  }

  _onDataUsageWarningValueChange(value) {
    TrackUtil.reportClickEvent("FlowProtection_ClickNum");
    StorageKeys.IS_DATA_USAGEE_WARNING = value;
    this.setState({
      isDataUsageWarning: value
    });
    Toast.success('c_set_success');
  }

  _onScreenDisplayValueChange(value) {
    TrackUtil.reportClickEvent("FlowProtection_ClickNum");
    AlarmUtilV2.setSpecPValue([{ sname: SCREEN_SETTING_SIID, pname: SCREEN_SETTING_SWITCH_PIID, value: value }])
      .then((res) => {
        if (res[0].code == 0) {
          Toast.success('c_set_success');
          this.setState( { screenCloseDisplay: value });
        } else {
          Toast.fail('c_set_fail');
          this.setState( { screenCloseDisplay: !value });
        }
      }).catch((error) => {
        Toast.fail('c_set_fail');
        this.setState( { screenCloseDisplay: !value });
      });
  }

  _onFaceShotValueChange(value) {
    TrackUtil.reportClickEvent("FlowProtection_ClickNum");
    AlarmUtilV2.setSpecPValue([{ sname: CAMERA_FACE_SHOT_SIID, pname: CAMERA_FACE_SHOT_SWITCH_PIID, value: value }])
      .then((res) => {
        if (res[0].code == 0) {
          Toast.success('c_set_success');
          this.setState( { faceShotSwitch: value });
        } else {
          Toast.fail('c_set_fail');
          this.setState( { faceShotSwitch: !value });
        }
      }).catch((error) => {
      Toast.fail('c_set_fail');
      this.setState( { faceShotSwitch: !value });
    });
  }

  _onPtzRotationValueChange(value) {
    StorageKeys.IS_PTZ_ROTATION_ENABLE = value;
    this.setState({
      isPtzRotationEnabled: value
    });
    Toast.success('c_set_success');
  }

  _onAIFrameChanged(newValue) {
    if (VersionUtil.isAiCameraModel(Device.model)) {
      AlarmUtil.putAiFrameSwitch022(newValue).then(() => {
        this.onAiFrameChangedSuccess(newValue);
      }).catch(() => {
        this.setState({
          isAIFrame: !newValue
        });
        Toast.fail('c_set_fail');
      });
    } else {
      StorageKeys.IS_AI_FRAME_OPEN = newValue;
      this.onAiFrameChangedSuccess(newValue);
    }
    
  }

  setSelectWDR(value) {
    this.setState({
      wdrMode: value
    });
  }

  onAiFrameChangedSuccess(newValue) {
    this.setState({
      isAIFrame: newValue
    });
    Toast.success('c_set_success');
    newValue ?
      TrackUtil.reportResultEvent("MonitoringSetting_smartFrameSwitchStatus", "type", 1)
      :
      TrackUtil.reportResultEvent("MonitoringSetting_smartFrameSwitchStatus", "type", 2);
  }

  _getHumanTrack() {
    if (VersionUtil.isUsingSpec(Device.model)) {

      const params = [HUMAN_TRACK.Key];
      console.log("why! _getHumanTrack");
      Service.spec.getPropertiesValue(params, 2).then((result) => {
        console.log(`why!, _getHProps result=${JSON.stringify(result)}`);
        console.log(`why!, _getHProps typeof(result)=${typeof (result)}`);

        if (result instanceof Array && result.length >= 1) {
          let htValue = result[0].value;
          console.log(`why!, htValue=${htValue}`);
          this.setState({ isHumanTrack: htValue });
        }
      }).catch((err) => {
        console.log(`_getHumanTrack err=${JSON.stringify(err)}`);
      });

    } else {
      API.get("/miot/camera/app/v2/get/alarmSwitch", "business.smartcamera", {})
        .then((result) => {
          console.log("request alarmSwitch", JSON.stringify(result));
          if (result.code != 0) {
            console.log("getAlarmSwitch:", JSON.stringify(-1));
            Toast.fail('c_get_fail');
          } else {
            this.motionObj = result.data.motionDetectionSwitch;
            this.setState({ isHumanTrack: this.motionObj.trackSwitch });
          }
        })
        .catch((error) => {
          console.log("getAlarmSwitch:", JSON.stringify(-1));
          Toast.fail('c_get_fail', error);
        });
    }
  }

  _onHumanTrackChanged(newValue) {
    console.log(`why! _onUpdateHumanTrack: ${newValue}`);
    newValue ?
      TrackUtil.reportResultEvent("MonitoringSetting_Tracking_Status", "type", 1)
      :
      TrackUtil.reportResultEvent("MonitoringSetting_Tracking_Status", "type", 2);

    let setValue = newValue ? HUMAN_TRACK.ON : HUMAN_TRACK.OFF;
    console.log(`why! setValue: ${setValue}`);

    if (VersionUtil.isUsingSpec(Device.model)) {
      Service.spec.setPropertiesValue(setValue).then((res) => {
        console.log('why!, setPropertiesValue success', res);
        // this._getHumanTrack();
        Toast.success('c_set_success');
        this.setState({ isHumanTrack: newValue });
      }).catch((res) => {
        console.log('why!, setPropertiesValue error', res);
        Toast.fail('c_set_fail', res);
      });
    } else {
      let startTime = this.motionObj.startTime;
      let endTime = this.motionObj.endTime;
      let interval = this.motionObj.interval;
      let isAlarmOn = this.motionObj.detectionSwitch;
      let track = newValue;

      let jsonObject = {};
      jsonObject.interval = interval;
      jsonObject.open = isAlarmOn;
      jsonObject.startTime = startTime;
      jsonObject.endTime = endTime;
      jsonObject.trackSwitch = track;

      API.post("/miot/camera/app/v2/put/motionDetectionSwitch", "business.smartcamera", jsonObject)
        .then((result) => {
          console.log("put alarm:", JSON.stringify(result));
          if (result.code == 0) {
            Toast.success('c_set_success');
            this.setState({ isHumanTrack: newValue });
          } else {
            Toast.fail('c_set_fail');
          }
        })
        .catch((error) => {
          console.log("request alarmSwitch Error:", error);
          console.log("getAlarmSwitch:", JSON.stringify(-1));
          this.setState({ isHumanTrack: !newValue });

          Toast.fail('c_set_fail');

        });
    }

  }

  openFloatWindow() {
    Service.miotcamera.openFloatWindow().then(() => {
      Package.exit();
    })
      .catch((error) => {
        if (error.code == -2) {
          this.setState({ showPermissionDialog: true })
        } else {
          Toast.fail("c_set_fail", error);
        }
      });
    TrackUtil.reportClickEvent('Camera_Pop-upWindow_ClickNum');
  }

  _restart() {
    this.setState({
      showRestartConfirm: false
    });
    RPC.callMethod("restart_device", []).then(() => {
      Toast.success('c_set_success');
    }).catch((err) => {
      Toast.fail('c_set_fail', err);
    });
  }
}
