'use strict';

import { Device } from "miot";
import { Service } from "miot";
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import Separator from 'miot/ui/Separator';
import React from 'react';
import { ScrollView, View, Text } from 'react-native';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import Toast from '../components/Toast';
import VersionUtil from "../util/VersionUtil";
import CameraConfig from "../util/CameraConfig";
import { CAMERA_CONTROL_SEPC_PARAMS } from "../Constants";
import NavigationBar from "miot/ui/NavigationBar";
import RPC from "../util/RPC";
import TrackUtil from '../util/TrackUtil';
import CameraPlayer from '../util/CameraPlayer';
import { DescriptionConstants } from "../Constants";
import { handlerOnceTap } from "../util/HandlerOnceTap";
export default class SleepSetting extends React.Component {

  constructor(props, context) {
    super(props, context);

    this.state = {
      isPower: true,
      openBandNearby: false,
      fetchingData: true
    };
  }

  render() {
    let isV1 = VersionUtil.judgeIsV1(Device.model);
    return (
      <View style={styles.container}>
        <ScrollView showsVerticalScrollIndicator={false}>

          {/* <View style={[styles.blank, { borderTopWidth: 0 }]} /> */}
          <View style={styles.featureSetting}>
            <ListItemWithSwitch
              accessibilityLabel={CameraConfig.isSupportPhysicalCover(Device.model) ? DescriptionConstants.sz_4_11_1 : DescriptionConstants.sz_4_22}
              title={CameraConfig.isSupportPhysicalCover(Device.model) ? LocalizedStrings["physical_cover_title"] : LocalizedStrings['sls_sleep']}
              titleNumberOfLines={3}
              showSeparator={false}
              value={!this.state.isPower}
              onValueChange={(value) => this._onPowerValueChange(!value)}
              titleStyle={{ fontWeight: 'bold' }}
              disabled={this.state.fetchingData}
              onPress={() => {
              }}
              accessibilitySwitch={{
                accessibilityLabel: CameraConfig.isSupportPhysicalCover(Device.model) ? DescriptionConstants.sz_4_11_1 : DescriptionConstants.sz_4_22
              }}
            />
          </View>

          {/* <View style={styles.blank} /> */}
          <View style={styles.featureSetting}>
            <ListItem
              title={CameraConfig.isSupportPhysicalCover(Device.model) ?
                LocalizedStrings["physical_cover_auto"] :
                LocalizedStrings['sls_set_automatic_sleep_time']}
              showSeparator={false}
              unlimitedHeightEnable={true}
              titleNumberOfLines={2}
              onPress={() => handlerOnceTap(() => {
                // if (!CameraConfig.isSupportPhysicalCover(Device.model)) {
                //   // 没有物理的时候
                // }
                TrackUtil.reportClickEvent("AutoSleepSetting_ClickNum");
                this._openTimerSettingPageWithOptions();
              })
              } 
              titleStyle={{ fontWeight: 'bold' }}

            />
          </View>
          {
            isV1 ?
              <View style={styles.featureSetting}>
                <ListItem
                  title={LocalizedStrings['settings_auto_scence_bletooth']}
                  showSeparator={false}
                  subTitle={LocalizedStrings["settings_auto_scence_bletooth_sub"]}
                  value={this.state.openBandNearby ? LocalizedStrings["on"] : LocalizedStrings["off"]}
                  onPress={() => {
                    this.props.navigation.navigate("BandNearbyClosePage");
                  }} 
                  titleStyle={{ fontWeight: 'bold' }}
                  
                />
              </View>
              : null
          }
        </ScrollView>
      </View>
    );
  }

  componentDidMount() {
    this.props.navigation.setParams({
      title: CameraConfig.isSupportPhysicalCover(Device.model) ? LocalizedStrings['physical_cover_setting'] : LocalizedStrings['sls_title'],
      titleNumberOfLines: 2,
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.isPageForeGround = true;
        this._onResume();
      }
    );
  }

  _onResume() {
    if (VersionUtil.isUsingSpec(Device.model)) {
      Service.spec.getPropertiesValue([{ ...CAMERA_CONTROL_SEPC_PARAMS[6] }])
        .then((res) => {
          let isOk = res[0].code == 0;
          if (isOk) {
            let value = res[0].value;// on  not sleep  off sleep
            this.setState({ isPower: value, fetchingData: false });
          } else {
            Toast.fail('c_get_fail');

          }
        })
        .catch((error) => {
          Toast.fail('c_get_fail', error);
          this.setState({
            fetchingData: false
          });
        });
    } else {
      RPC.callMethod("get_prop", [
        'power', "band_nearby"
      ]).then((res) => {
        console.log("res:", res);
        this.setState({
          isPower: res.result[0] == 'on',
          openBandNearby: res.result[1] == 'on',
          fetchingData: false
        });
      }).catch((err) => {
        Toast.fail('c_get_fail', err);
        this.setState({
          fetchingData: false
        });
      });
    }

  }

  _onPowerValueChange(value) {
    TrackUtil.reportClickEvent("sleepOnOff_ClickNum");
    TrackUtil.reportClickEvent('Camera_Sleep_ClickNum');

    if (VersionUtil.isUsingSpec(Device.model)) {
      Service.spec.setPropertiesValue([{ ...CAMERA_CONTROL_SEPC_PARAMS[6], value: value }])
        .then((result) => {
          if (result[0].code == 0) {
            this.setState({
              isPower: value
            });
            CameraPlayer.getInstance().setPowerState(value);
            Toast.success('c_set_success');
          } else {
            this.setState({
              isPower: !value
            });
            Toast.success('c_set_fail');
          }
        })
        .catch((err) => {
          Toast.fail('c_set_fail', err);
        });
    } else {
      RPC.callMethod("set_power", [
        value ? 'on' : 'off'
      ]).then((res) => {
        this.setState({
          isPower: res.result[0] == 'OK' ? value : !value
        });
        if (res.result[0] == 'OK') {
          Toast.success('c_set_success');
        } else {
          Toast.fail('c_set_fail');
        }
      }).catch((err) => {
        Toast.fail('c_set_fail', err);
        this.setState({ isPower: !value });
      });
      
    }
    TrackUtil.reportClickEvent("sleepOnOff_ClickNum");
    TrackUtil.reportClickEvent('Camera_Sleep_ClickNum');
    
  }
  // 定时自动休眠没有
  _openTimerSettingPageWithOptions() {
    let params = {
      onMethod: VersionUtil.isUsingSpec(Device.model) ? "set_properties" : "set_power",
      onParam: VersionUtil.isUsingSpec(Device.model) ? [{ ...CAMERA_CONTROL_SEPC_PARAMS[6], value: false }] : "off",
      offMethod: VersionUtil.isUsingSpec(Device.model) ? "set_properties" : "set_power",
      offParam: VersionUtil.isUsingSpec(Device.model) ? [{ ...CAMERA_CONTROL_SEPC_PARAMS[6], value: true }] : "on",
      // displayName: "自定义场景名称",
      // identify: Device.deviceID,
      // onTimerTips: '',
      // offTimerTips: '定时列表页面、设置时间页面 关闭时间副标题（默认：关闭时间）',
      // listTimerTips: '定时列表页面 定时时间段副标题（默认：开启时段）',
      bothTimerMustBeSet: false,
      onModeTips: LocalizedStrings["timed_on_sleep"],
      offModeTips: LocalizedStrings["timed_off_sleep"],
      identify: Device.deviceID,
      onTimerTips: LocalizedStrings['sleep_auto_on_time'],
      offTimerTips: LocalizedStrings['sleep_auto_off_time'],
      showOnTimerType: true,
      showOffTimerType: true,
      showPeriodTimerType: true
    };
    if (CameraConfig.isSupportPhysicalCover(Device.model)) {
      params = {
        onMethod: VersionUtil.isUsingSpec(Device.model) ? "set_properties" : "set_power",
        onParam: VersionUtil.isUsingSpec(Device.model) ? [{ ...CAMERA_CONTROL_SEPC_PARAMS[6], value: false }] : "off",
        offMethod: VersionUtil.isUsingSpec(Device.model) ? "set_properties" : "set_power",
        offParam: VersionUtil.isUsingSpec(Device.model) ? [{ ...CAMERA_CONTROL_SEPC_PARAMS[6], value: true }] : "on",
        displayName: LocalizedStrings["physical_cover_auto"],
        identify: Device.deviceID,
        onTimerTips: LocalizedStrings["physical_cover_start_time"],
        offTimerTips: LocalizedStrings["physical_cover_end_time"],
        onModeTips: LocalizedStrings["on_mode_tips"],
        offModeTips: LocalizedStrings["off_mode_tips"],
        listTimerTips: LocalizedStrings["physical_cover_fixtime"],
        bothTimerMustBeSet: false,
        showOnTimerType: true,
        showOffTimerType: true,
        showPeriodTimerType: true
      };
    }

    Service.scene.openTimerSettingPageWithOptions(params);
  }

  componentWillUnmount() {
    this.didFocusListener.remove();
  }
}
