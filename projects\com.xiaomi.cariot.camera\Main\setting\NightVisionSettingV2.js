'use strict';

import {NavigationBar, ListItemWithSwitch} from "mhui-rn";
import React from 'react';
import {ScrollView, Image, Text, View, TouchableOpacity, Dimensions, StyleSheet} from 'react-native';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import Toast from '../components/Toast';
import TrackUtil from '../util/TrackUtil';
import { DescriptionConstants } from "../Constants";
import ChoiceItem from "../widget/ChoiceItem";
import BaseSettingPage from "../BaseSettingPage";
import AlarmUtilV2, { PIID_GLIMMER_FULL_COLOR, PIID_NIGHT_VISION, SIID_CAMERA_CONTROL } from "../util/AlarmUtilV2";
const { width: screenWidth } = Dimensions.get("screen");
export default class NightVisionSettingV2 extends BaseSettingPage {

  constructor(props, context) {
    super(props, context);
    this.state = {
      glimmerFullColorSwitch: true,
      nightVisionValue: -1
    };
  }

  getTitle(): string {
    return LocalizedStrings['night_vision_setting'];
  }

  renderSettingContent() {
    let nightVisionArray = [
      {title: LocalizedStrings.nvs_auto, subtitle: LocalizedStrings.nvs_auto_subtitle, value: 2},
      {title: LocalizedStrings.nvs_open, subtitle: LocalizedStrings.nvs_open_subtitle, value: 0},
      {title: LocalizedStrings.nvs_close, subtitle: LocalizedStrings.nvs_close_subtitle, value: 1}];
    return (
      <View style={styles.container}>

          <View style={{ flexDirection: "row", flexWrap: "wrap" }} key={0}>
            <View style={{ marginTop: 12,marginBottom:20, flexDirection: "row", flexWrap: "wrap", justifyContent: "space-between", width: "100%", position: "relative" }}>
              <View style={{ width: "100%" }} key={"img_1"}>
                <Image key={"img_1"} source={require("../../resources2/images/img_night_vision_top.png")}
                       style={{ marginLeft: 24, width: screenWidth - 48, height: (179 / 320) * (screenWidth - 48), borderRadius: 9 }}/>
              </View>
            </View>
          </View>

          <View style={styles.featureSetting} key={5}>

            {nightVisionArray.map((item,index)=>{
              return(
                  <ChoiceItem title={item.title}
                              key={index.toString()}
                              subtitle={item.subtitle}
                              containerStyle={{minHeight:70,padding:15,marginHorizontal: 22,marginVertical:6}}
                              checked={item.value == this.state.nightVisionValue}
                              onlyChecked={true}
                              selectedIconLeft={require("../../Resources/Images/icon_selected_tick.png")}
                              onValueChange={(value) => {
                                value && this._onSelectedItem(item.value);
                              }}/>
               )
            })}
          </View>

          <View style={style.whiteblank}/>


          <Text style={style.functionTitle}
                  accessibilityLabel={DescriptionConstants.sz_3}>{LocalizedStrings['glimmer_setting']}</Text>
          <View style={{ width: "100%" }} key={"img_1"}>
            <Image key={"img_1"} source={require("../../resources2/images/img_glimmer_colorful.png")}
                   style={{ marginLeft: 24, width: screenWidth - 48, height: (179 / 320) * (screenWidth - 48), borderRadius: 9 }}/>
          </View>

          <ListItemWithSwitch
              title={LocalizedStrings['glimmer_colorful_pic']}
              titleStyle={this.state.nightVisionValue == 0 ? {color: 'rgba(0,0,0,0.3)'} : null}
              subtitle={LocalizedStrings.glimmer_colorful_pic_subtitle}
              subtitleStyle={this.state.nightVisionValue == 0 ? {color: 'rgba(0,0,0,0.3)'} : null}
              value={this.state.glimmerFullColorSwitch}
              disabled={this.state.nightVisionValue == 0}
              titleNumberOfLines={3}
              subtitleNumberOfLines={4}
              containerStyle={{minHeight:72,marginTop:20}}
              onValueChange={(value) => {
                let params  = [{ "sname": SIID_CAMERA_CONTROL, "pname": PIID_GLIMMER_FULL_COLOR, value: value}];
                AlarmUtilV2.setSpecPValue(params).then((res) => {
                  if (res[0].code == 0) {
                    this.setState({glimmerFullColorSwitch: value});
                    Toast.success("c_set_success");
                  } else {
                    Toast.fail('c_set_fail');
                    this.setState({glimmerFullColorSwitch: !value});
                  }

                }).catch(() => {
                  this.setState({glimmerFullColorSwitch: !value});
                  Toast.fail('c_set_fail');
                })
              }}
              showSeparator={false}/>

        </View>
    );
  }


  componentDidMount() {
    super.componentDidMount();
    let params = [
      {"sname": SIID_CAMERA_CONTROL, "pname": PIID_GLIMMER_FULL_COLOR},
      {"sname": SIID_CAMERA_CONTROL, "pname": PIID_NIGHT_VISION}
    ];
    AlarmUtilV2.getSpecPValue(params).then((res) => {
      this.setState({
        glimmerFullColorSwitch:res[0].value,
        nightVisionValue: res[1].value
      });
    }).catch((err) => {
      Toast.fail('c_get_fail', err);
    });

  }

  _onSelectedItem(value) {
    TrackUtil.reportResultEvent("NightVisionNumber", "type", value);
    let valueCopy = this.state.nightVisionValue;
    this.setState({
      nightVisionValue: value
    });
    let params  = [{ "sname": SIID_CAMERA_CONTROL, "pname": PIID_NIGHT_VISION, value: value}];

    AlarmUtilV2.setSpecPValue(params)
        .then((res) => {
          if (res[0].code == 0) {
            Toast.success('c_set_success');
          } else {
            Toast.fail('c_set_fail');
            this.setState({
              nightVisionValue: valueCopy
            });
          }

        }).catch((err) => {
      this.setState({
        nightVisionValue: valueCopy
      });
      Toast.fail('c_set_fail', err);
    });
  }



}



const style = StyleSheet.create({

  functionTitle:{
    marginLeft:27,
    marginTop:8,
    marginBottom:30,
    fontWeight:'bold',
    fontSize:12,
    color:"#666"
  },

  desc_title: {
    color: "#000000",
    fontSize: 18,
    fontWeight: "bold",
    paddingHorizontal: 28,
    marginTop:20,
    fontFamily: "MI Lan Pro"
  },
  desc_subtitle: {
    color: "rgba(0, 0, 0, 0.6)",
    fontSize: 14,
    marginTop: 10,
    fontFamily: "MI Lan Pro",
    lineHeight: 21,
    marginHorizontal: 28,
    marginBottom:18
  },

  whiteblank: {
    height: StyleSheet.hairlineWidth,
    marginHorizontal: 24,
    backgroundColor: "#e5e5e5",
    marginVertical: 20
  },

});
