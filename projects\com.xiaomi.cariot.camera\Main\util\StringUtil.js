import { Service, Device } from "miot";
import Util from '../util2/Util';
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import dayjs from "dayjs";
const WEEKENDS = [LocalizedStrings["sunday1"], LocalizedStrings["monday1"], LocalizedStrings["tuesday1"], LocalizedStrings["wednesday1"], LocalizedStrings["thursday1"], LocalizedStrings["friday1"], LocalizedStrings["saturday1"]];
const TODAY = LocalizedStrings["today"];
const YESTERDAY = LocalizedStrings["yestoday"];
export default class StringUtil {
  static dayLineString(date = new Date()) {
    let str = '';
    let week = date.getDay();
    str = dayjs.unix(date.getTime()/1000).format(LocalizedStrings["mmdd"]);
    if (Util.isToday(date)) {
      str = `${str} ${TODAY}`;
    } else if (Util.isYestoday(date)) {
      str = `${str} ${YESTERDAY}`;
    } else {
      str = `${str} ${WEEKENDS[week]}`;
    }

    return str;
  }
}