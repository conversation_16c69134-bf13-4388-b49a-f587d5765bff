'use strict';
import React from 'react';
import {
  ScrollView,
  View,
  Text,
  Image,
  Dimensions,
  TouchableOpacity,
  StyleSheet,
  ImageBackground,
  ART, Platform, BackHandler
} from 'react-native';
let { Surface, Shape, Path } = ART;

import { Device, Service } from 'miot';
import Host from "miot/Host";
import NavigationBar from "miot/ui/NavigationBar";
import { LoadingDialog } from 'miot/ui/Dialog';
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import Separator from 'miot/ui/Separator';
import Toast from '../components/Toast';
import VersionUtil from '../util/VersionUtil';
import Util from "../util2/Util";
import { localStrings as LocalizedStrings } from '../MHLocalizableString';

const ScreenOptions = Dimensions.get('window');
const viewWidth = ScreenOptions.width - 24 * 2;
const viewHeight = viewWidth * 9 / 16;
// 将直播流播放器分成8*4个矩形块
const itemWidth = viewWidth / 8;
const itemHeight = viewHeight / 4;
const REACT_MARGIN = 3; // 矩形线框到画面边缘的外边距

export default class SmartMonitorSetting extends React.Component {

  static sceneSelectedIndexCopy = 0;// Spec中保存的场景模式，用来比对是否改动
  static sceneDurationDataArray = [];// 三个看护场景的数据
  constructor(props, context) {
    super(props, context);
    this.state = {
      smartMonitorSwitch: false, // 智能看护总开关
      monitorAreaData: null, // 看护区域坐标数据 如 "[{\"area\":\"[0,0],[94.100]\"}]"
      sceneSelectedIndex: 0, // 当前选中看护模式的索引
      progressing: false
    };

    SmartMonitorSetting.sceneSelectedIndexCopy = 0;// Spec中保存的场景模式，用来比对是否改动
    SmartMonitorSetting.sceneDurationDataArray = [];// 三个看护场景的数据

    // 矩形线框的左上角和右下键的x、y坐标轴
    this.rectDatas = [REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN];
    // 矩形背景的左上角和右下键的x、y坐标
    this.rectBackGround = [0, 0, viewWidth, viewHeight];

    this.didFocusListener = this.props.navigation.addListener('didFocus', () => { this.isPageActive = true; });
    this.willBlurListener = this.props.navigation.addListener('willBlur', () => { this.isPageActive = false; });
    this.timeStamp = Date.now();
    this.existsSettingsImg = false;
  }

  componentDidMount() {
    this.setNavigationBar();
    this._refreshEffectiveMonitorArea([...this.rectDatas]);
    this._getAllSpecData();

    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this._onBackHandler);
    }
    setTimeout(() => {
      Host.file.isFileExists(VersionUtil.settingsImgPath).then((success) => {
        console.log("isFileExists===", success);
        this.existsSettingsImg = success;
        this.forceUpdate();
      }).catch((err) => {
        console.log("err=", JSON.stringify(err));
      });
    }, 100);
  }

  // 设置导航栏
  setNavigationBar() {
    this.props.navigation.setParams({
      title: LocalizedStrings['setting_smart_monitoring'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => {
            // if (this._isCanSave()) {
            //   this._updateSceneDurationData();
            // } else {
            //   this.props.navigation.goBack();
            // }
            this.props.navigation.goBack();

          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
  }

  // 获取智能看护看关，划定区域的数据、设置的看护场景的时间段的Spec数据
  _getAllSpecData() {
    this.setState({ progressing: true });
    Service.spec.getPropertiesValue([
      { did: Device.deviceID, siid: 11, piid: 1 }, // 开关
      { did: Device.deviceID, siid: 11, piid: 2 }, // 划定的坐标数据
      { did: Device.deviceID, siid: 11, piid: 3 } // 设置的时间段
    ], 2).then((vo) => {
      console.log("Spec data--------->", JSON.stringify(vo));
      let newStates = { progressing: false };
      let isFailed = false;
      if (vo[0]?.code == 0) {
        newStates.smartMonitorSwitch = vo[0].value;
      } else {
        isFailed = true;
      }

      if (vo[1]?.code == 0) {
        newStates.monitorAreaData = vo[1].value;
      } else {
        isFailed = true;
      }

      if (vo[2]?.code == 0) {
        newStates.sceneSelectedIndex = JSON.parse(vo[2].value).mode;
        SmartMonitorSetting.sceneSelectedIndexCopy = newStates.sceneSelectedIndex;
        SmartMonitorSetting.sceneDurationDataArray = JSON.parse(vo[2].value).param;
      } else {
        isFailed = true;
      }

      this.setState(newStates, () => {
        if (isFailed) {
          Toast.fail('c_get_fail');
        }
        if (this.state.monitorAreaData) {
          this._parseAreaDataSpecValue(this.state.monitorAreaData);
        }
      });
    }).catch((_) => {
      Toast.fail('c_get_fail');
      this.setState({ progressing: false });
    });

    /** *************如果Spec获取不到，使用下面的模拟数据************** */
    /* let sceneResult = "{\"mode\":0,\"param\":[{\"idx\":0,\"clock\":[{\"clock_idx\":\"0\",\"start\":\"00:00\",\"end\":\"23:59\",\"repeat\":127,\"enable\":1}]},{\"idx\":1,\"clock\":[{\"clock_idx\":\"0\",\"start\":\"00:00\",\"end\":\"01:59\",\"repeat\":120,\"enable\":1}]},{\"idx\":2,\"clock\":[{\"clock_idx\":\"0\",\"start\":\"00:00\",\"end\":\"23:59\",\"repeat\":127,\"enable\":1},{\"clock_idx\":\"1\",\"start\":\"00:00\",\"end\":\"01:59\",\"repeat\":120,\"enable\":1}]}]}";
        sceneResult = JSON.parse(sceneResult);
        this.setState({
            sceneSelectedIndex: sceneResult.mode,

            progressing:false,
            smartMonitorSwitch:true
        });
        this.sceneDurationDataArray = [...sceneResult.param]; //三个看护场景的数据 */
    /** *************如果Spec获取不到，使用上面的模拟数据************** */

  }

  // 解析Spec协议得出用户框定的线框的左上右下坐标值，并存入this.rectDatas刷新UI
  _parseAreaDataSpecValue(specValueString) { // "[{\"area\":\"[0,0],[94,100]\"}]",
    let coordsArrayString = JSON.parse(specValueString)[0].area;
    let coordsStringArray = coordsArrayString.replace("],", "]_").split('_');// ["[0,0]","[94,100]"]
    let coordsArray = [...JSON.parse(coordsStringArray[0]), ...JSON.parse(coordsStringArray[1])];
    coordsArray = [coordsArray[0] / 100.0 * viewWidth, coordsArray[1] / 100.0 * viewHeight,
      coordsArray[2] / 100.0 * viewWidth, coordsArray[3] / 100.0 * viewHeight];

    // 尝试修正设置spec时转为百分比带来的误差
    if(coordsArray[0] < REACT_MARGIN) {
      coordsArray[0] = REACT_MARGIN;
    }
    coordsArray[1] < REACT_MARGIN ? coordsArray[1] = REACT_MARGIN : coordsArray[1] = coordsArray[1] - 1.5;
    coordsArray[2] > viewWidth - REACT_MARGIN ? coordsArray[2] = viewWidth - REACT_MARGIN : coordsArray[2] = coordsArray[2] - 2;
    coordsArray[3] > viewHeight - REACT_MARGIN ? coordsArray[3] = viewHeight - REACT_MARGIN : coordsArray[3] = coordsArray[3] - 1.5;
    this._refreshEffectiveMonitorArea(coordsArray);
  }


  render() {
    return (
      <View style={{ flex: 1, backgroundColor: Util.isDark() ? "#000000" : "#fff" }}>

        <ScrollView showVerticalScrollIndicator={false}>
          {this.state.smartMonitorSwitch ? this._renderMonitorArea() : this._renderPlaceHolderImage()}

          <Text numberOfLines={3} style={{
            fontSize: 13, color: "#000000CC", marginHorizontal: 27 }}>
            {LocalizedStrings.setting_monitor_feature}</Text>

          <ListItemWithSwitch
            title={LocalizedStrings.setting_smart_monitoring}
            value={this.state.smartMonitorSwitch}
            showSeparator={false}
            onValueChange={(value) => this._onSmartMonitorSwitchChange(value)}
          />

          {this._renderFunctionSettings()}

        </ScrollView>

        <LoadingDialog
          visible={this.state.progressing}
          message={LocalizedStrings['c_setting']}
          onModalHide={() => this.setState({ progressing: false })}/>
      </View>
    );
  }

  /* 未开启看护开关，显示占位图 */
  _renderPlaceHolderImage() {
    return (<Image style={{ width: viewWidth, height: viewHeight, marginHorizontal: 24, marginTop: 13, marginBottom: 20, borderRadius: 9 }} 
      source={require("../../Resources/Images/ai2_monitor_img.webp")} imageStyle={{ borderRadius: 10 }}/>);
  }

  /* 绘制用户划定之后的有效看护区域 */
  _renderMonitorArea() {
    // 代表有效看护区域的半透明矩形背景的绘制路径
    let background_path = Path()
      .moveTo(this.rectBackGround[0], this.rectBackGround[1])
      .lineTo(this.rectBackGround[2], this.rectBackGround[1])
      .lineTo(this.rectBackGround[2], this.rectBackGround[3])
      .lineTo(this.rectBackGround[0], this.rectBackGround[3])
      .close();
    let imageSource = `${ Host.file.storageBasePath }/${ VersionUtil.settingsImgPath }`;
    if (!this.existsSettingsImg) {
      imageSource = require("../../Resources/Images/ai2_monitor_img.webp");
    } else {
      if (Platform.OS !== "ios") {
        imageSource = `${ Host.file.storageBasePath }/${ VersionUtil.settingsImgPath }?timestamp=${ this.timeStamp }`;
      }
      imageSource = { uri: imageSource };
    }
    return (
      <ImageBackground style={{
        width: viewWidth,
        height: viewHeight,
        marginHorizontal: 24,
        marginTop: 13,
        marginBottom: 20
      }} imageStyle={{ borderRadius: 0 }}>
        {/* 直播流截图可能不存在，故不能让其做ImageBackground。需要让一个固定图片来兜底 */}
        <Image style={{
          width: viewWidth,
          height: viewHeight
        }} source={imageSource} key={ this.timeStamp }/>

        <View style={{ position: 'absolute' }}>
          <Surface width={viewWidth} height={viewHeight}>
            <Shape d={background_path} fill="#32BAC0" opacity="0.3"/>
          </Surface>
        </View>

      </ImageBackground>);
  }

  /* 智能看护的功能设置(区域、场景时间段)View */
  _renderFunctionSettings() {
    if (!this.state.smartMonitorSwitch) return null;
    return (
      <View>
        <ListItem
          title={LocalizedStrings.setting_monitor_area}
          containerStyle={{ marginBottom: 20 }}
          showSeparator={false}
          value={!this.state.monitorAreaData ? LocalizedStrings.targetPushTitle_subtitle1 : null}
          onPress={() => {
            this.props.navigation.navigate("MonitorAreaModifyPage",
              {
                areaData: [...this.rectDatas],
                callback: (areaDataNew) => this._refreshEffectiveMonitorArea([...areaDataNew])
              });
          }}/>

        <Separator style={{ marginHorizontal: 24, backgroundColor: "#E5E5E5" }}/>

        <Text style={{
          fontSize: 12,
          color: "#666666",
          marginLeft: 27,
          marginTop: 28
        }}>{LocalizedStrings.setting_monitor_scene}</Text>

        <SingleChoiceItem
          isChecked={this.state.sceneSelectedIndex == 0}
          title={LocalizedStrings.setting_monitor_key_area_title}
          subtitle={LocalizedStrings.setting_monitor_key_area_subtitle}
          containerStyle={{ marginTop: 15 }}
          onCheckedListener={() => {
            this._updateSceneDurationData(0).then(() => {
              this.setState({ sceneSelectedIndex: 0 });
            }).catch(() => {
            });
          }}
          subItemTitle={LocalizedStrings.setting_monitor_duration}
          subItemOnPress={() => this._goToDurationListPage(0)}
        />

        <SingleChoiceItem
          isChecked={this.state.sceneSelectedIndex == 1}
          title={LocalizedStrings.setting_monitor_baby_sleeping_title}
          subtitle={LocalizedStrings.setting_monitor_baby_sleeping_subtitle}
          containerStyle={{ marginTop: 12 }}
          onCheckedListener={() => {
            this._updateSceneDurationData(1).then(() => {
              this.setState({ sceneSelectedIndex: 1 });
            }).catch(() => {
            });
          }}
          subItemTitle={LocalizedStrings.setting_monitor_duration}
          subItemOnPress={() => this._goToDurationListPage(1)}
        />

        <SingleChoiceItem
          isChecked={this.state.sceneSelectedIndex == 2}
          title={LocalizedStrings.setting_monitor_nobody_move_title}
          subtitle={LocalizedStrings.setting_monitor_nobody_move_subtitle}
          containerStyle={{ marginTop: 12, marginBottom: 60 }}
          onCheckedListener={() => {
            this._updateSceneDurationData(2).then(() => {
              this.setState({ sceneSelectedIndex: 2 });
            }).catch(() => {
            });
          }}
          subItemTitle={LocalizedStrings.setting_monitor_duration}
          subItemOnPress={() => this._goToDurationListPage(2)}
        />
      </View>
    );

  }


  // 操作智能看护开关
  _onSmartMonitorSwitchChange(value) {
    Toast.loading('c_setting');
    let params = [{ did: Device.deviceID, siid: 11, piid: 1, value: value }];
    Service.spec.setPropertiesValue(params).then((vo) => {
      if (vo[0].code == 0) {
        this.setState({ smartMonitorSwitch: value });
      } else {
        this.setState({ smartMonitorSwitch: !value });
      }
      Toast.success('c_set_success');
    }).catch((err) => {
      this.setState({ smartMonitorSwitch: !value });
      Toast.fail("action_failed", err);
    });
  }

  // 通过用户划定的矩形线框的左上和右下角图标，计算出有效区域的矩形框左上和右下角坐标并刷新显示
  _refreshEffectiveMonitorArea(rectangleCoords) {
    this.rectDatas = rectangleCoords;
    // this.rectBackGround = [Math.floor(this.rectDatas[0] / itemWidth) * itemWidth, Math.floor(this.rectDatas[1] / itemHeight) * itemHeight,
    //   Math.ceil(this.rectDatas[2] / itemWidth) * itemWidth, Math.ceil(this.rectDatas[3] / itemHeight) * itemHeight];
    this.rectBackGround = this.rectDatas;
    this.forceUpdate(); // 刷新页面
  }

  // 跳转到看护时间段管理页面
  _goToDurationListPage(sceneIndex) {
    console.log("Jump to MonitorDurationListPage---->", JSON.stringify(SmartMonitorSetting.sceneDurationDataArray));
    let sceneDataArrayCopy = JSON.parse(JSON.stringify(SmartMonitorSetting.sceneDurationDataArray));
    this.props.navigation.navigate('MonitorDurationListPage',
      {
        sceneDataArray: sceneDataArrayCopy,
        sceneSelectedIndex: sceneIndex,
        // callback: (data) => {
        //   console.log("-=-=-=-=-=-=-=-=-=", data);
        //   this.sceneDurationDataArray[sceneIndex] = data;
        //   this.sceneSelectedIndexCopy = sceneIndex; // 更改mode值，防止从列表页面退出后直接退出再更新一次Spec
        // }
      });
  }


  componentWillUnmount() {
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this._onBackHandler);
    }
    this.didFocusListener && this.didFocusListener.remove();
    this.willBlurListener && this.willBlurListener.remove();
  }

    _onBackHandler = () => {
      // if (this._isCanSave() && this.isPageActive) { // 无isPageActive，此逻辑会在下个页面左滑返回时调用
      //   this._updateSceneDurationData();
      //   return true;
      // }
      return false;
    };

    // 判断用户是否切换了看护场景模式(mode)
    // _isCanSave() {
    //   if (SmartMonitorSetting.sceneDurationDataArray.length == 0) { // 看护场景数据获取异常
    //     return false;
    //   }
    //   return SmartMonitorSetting.sceneSelectedIndexCopy != this.state.sceneSelectedIndex;
    // }

    // 更新智能场景的mode设置数据
    _updateSceneDurationData(index) {
      console.log("all smart monitor duration data---->", JSON.stringify(SmartMonitorSetting.sceneDurationDataArray));
      this.setState({ progressing: true });
      let specValue = { mode: index };
      specValue.param = SmartMonitorSetting.sceneDurationDataArray;
      let params = [{ did: Device.deviceID, siid: 11, piid: 3, value: JSON.stringify(specValue) }];
      console.log("send Spec command parameters", JSON.stringify(params));
      return new Promise((resolve, reject) => {
        Service.spec.setPropertiesValue(params).then((vo) => {
          this.setState({ progressing: false });
          if (vo[0].code == 0) {
            resolve(vo);
            // this.props.navigation.goBack();
          } else {
            Toast.fail("action_failed");
            reject("code is not 0");
          }
        }).catch((err) => {
          this.setState({ progressing: false });
          Toast.fail("action_failed");
          reject(err);
        });
      });
    }

}

/* 临时封装的场景选择小组件 */
const SingleChoiceItem = ({ title, subtitle, isChecked, onCheckedListener, subItemTitle, subItemOnPress, containerStyle = {} }) => {
  let darkMode = Util.isDark();
  return (
    <TouchableOpacity
      onPress={() => isChecked ? null : onCheckedListener()}
      disabled={isChecked}
    >
      <View
        style={[styles.singleChoiceItemContainer, { backgroundColor: isChecked ? darkMode ? "#25A9AF4D" : "#32BAC01A" : darkMode ? "#FFFFFFB2" : "#0000000F" }, containerStyle]}>
        <View style={{ margin: 15, flexDirection: 'row', alignItems: "center" }}>
          <Image style={{ width: 13, height: 11 }}
            source={isChecked ? require("../../Resources/Images/icon_smart_monitor_tick.png") : null}/>
          <View style={{ marginLeft: 16 }}>
            <Text numberOfssLines={3}
              style={{ lineHeight: 22, fontSize: 16, fontWeight: "bold", color: isChecked ? darkMode ? "#25A9AF" : "#32BAC0" : darkMode ? "#FFFFFFE5" : "#000000" }}>{title}</Text>
            <Text numberOfssLines={6}
              style={{ lineHeight: 18, fontSize: 13, color: isChecked ? darkMode ? "#25A9AF" : "#32BAC0" : darkMode ? "#FFFFFF80" : "#00000099" }}>{subtitle}</Text>
          </View>
        </View>

        {isChecked ?
          <ListItem
            title={subItemTitle}
            containerStyle={{
              backgroundColor: "transparent",
              paddingLeft: 45,
              paddingRight: 15,
              width: "100%",
              borderRadius: 10
            }}
            showSeparator={false}
            onPress={subItemOnPress}/>
          : null}
      </View>
    </TouchableOpacity>
  );
};



const styles = StyleSheet.create({
  singleChoiceItemContainer: {
    borderRadius: 10,
    marginHorizontal: 22
  }
});