'use strict';
import { Device, DarkMode } from "miot";
import React from 'react';
import { Image, ActivityIndicator, StyleSheet, ScrollView, View, Text, TextInput, Button } from 'react-native';
import RPC from "../util/RPC";
import Toast from '../components/Toast';
import { ListItemWithSwitch, ListItem } from 'miot/ui/ListItem';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import NavigationBar from "miot/ui/NavigationBar";
import LogUtil from "../util/LogUtil";
import { MessageDialog } from "mhui-rn";
export default class SelectNASLocation extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
      selectOldVideo: 0,
      isPickerOldVideo: false,
      storageLocation: '1',
      showLoading: true,
      showFailDialog: false,
      darkMode: false
      //这里应该有默认的位置
    };

  }
  render() {
    return (
      <View >
        {this._renderLoadingView()}
        {this._renderSelectView()}
        {this._renderNoLocationView()}
        {this._renderSetFailDialog()}
      </View>
    );
  }

  _renderSetFailDialog() {
    return (
      <MessageDialog
        visible={this.state.showFailDialog}
        title={LocalizedStrings['c_set_fail']}
        message={LocalizedStrings["nas_set_fail_tips"]}
        canDismiss={true}
        buttons={[
          {
            text: LocalizedStrings["offline_divice_ok"],
            // style: { color: 'lightblue' },
            callback: (_) => {
              this.setState({ showFailDialog: false });
            }
          }
        ]}
      />
    );
  }

  componentDidMount() {
    let { locationList } = this.props.navigation.state.params
    console.log(' locationList', locationList)
    if (locationList == null || locationList.length == 0) {
      this.setState({
        noLocationView: true,
        showLoading: false,
        selectView: false
      })
    }
    else {
      this.setState({
        showLoading: false,
        locationList: locationList,
        selectView: true
      });
    }

    this.props.navigation.setParams({
      // show:true
      title: LocalizedStrings["sel_nas_location"],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
    //刷新的时候 
    //这里可以不写在这里
    this.willFocusSubscription = this.props.navigation.addListener(
      'didFocus', () => {
        this._getStorage();
      }
    );


  }
  _getStorage() {
    let { deviceMessage } = this.props.navigation.state.params
    this.setState({
      deviceMessage: deviceMessage
    })

  }
  componentWillUnmount() {
    if (this.getInfoIntervalID > 0) {
      clearInterval(this.getInfoIntervalID);
      this.getInfoIntervalID = 0;
    }

    this.willFocusSubscription.remove();
  }
  _renderNoLocationView() {
    if (this.state.noLocationView) {
      return (
        <View
          style={{ backgroundColor: '#fff', width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
        >
          <View style={{
            justifyContent: 'center',
            alignItems: 'center',
          }}>
            <Image
              style={{ width: 119, height: 112 }}
              source={require('../../resources2/images/camera_smb_empty.png')}
            />
            {/* 这个后期都用改 */}
            <Text style={{ color: 'grey', top: 10 }}>{LocalizedStrings['nas_list_empty']}</Text>
            {/* mj_color_black_30_transparent" 这是啥颜色？？ */}
          </View>
        </View>

      )
    }
  }
  _renderLoadingView() {
    if (!this.state.showLoading) {
      return;
    }
    let isDark = DarkMode.getColorScheme() == "dark";
    return (
      <View
        // style={styles.container}
        style={{ backgroundColor: 'white', width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <ActivityIndicator
          style={{ width: 54, height: 54 }}
          color={isDark ? "xm#ffffff" : "xm#000000"}
          size={"large"}

        />
        <Text
          style={{ marginTop: 10, fontSize: 12, color: "#000000" }}>
          {LocalizedStrings["camera_loading"]}
        </Text>
      </View>
    );

  }
  _renderSelectView() {
    if (this.state.selectView) {
      return (
        <ScrollView style={styles.container}>
          {
            this.state.locationList.map((item, i) =>
            (
              <View style={{ display: 'flex', flexDirection: 'row', justifyContent: "flex-start", alignItems: "center" }}
                key={i}
              >
                <View>
                  <Image style={{ width: 30, height: 30 }} source={require('../../resources2/images/camera_store_icon_file_nor.png')}></Image>
                </View>
                <View>
                  <ListItem
                    key={i}
                    showSeparator={false}
                    title={item}
                    titleStyle={{ fontSize: 13 }}
                    hideArrow={true}
                    onPress={() => {
                      this._onSelectedItem(item)
                    }
                    }
                  />
                </View>

              </View>

            )
            )
          }
        </ScrollView>
      )
    }


  }
  _onSelectedItem(value) {
    let { deviceMessage } = this.props.navigation.state.params
    Toast.loading('c_setting');
    let params = {
      sync_interval: 300,
      video_retention_time: 7776000,
      last_sync_time: 520000,
      state: 1,
      share: {
        type: deviceMessage.share.type,
        group: deviceMessage.share.group,
        addr: deviceMessage.share.addr,
        name: deviceMessage.share.name,
        dir: value,
        user: deviceMessage.share.user,
        pass: deviceMessage.share.pass,
        // dirName: this.state.locationListName,
      }

    }
    RPC.callMethod("nas_set_config", params).then((res) => {
      if (res.result[0] == 'OK') {
        this.props.navigation.navigate('NASNetworkLocation', { deviceMessage: params, NASLocation: value })

        // setTimeout(() => {
        // this._getNASStatus();
        // }, 300);
        // clearInterval(this.getInfoIntervalID);

        // this.getInfoIntervalID = setInterval(() => {
        //     this._getNASStatus();
        // }, 2000);


      }
      // this.setState({
      //     storageLocation: res.result[0] == 'OK' ? value : this.state.storageLocation
      // });
      Toast.success('c_set_success');
    }).catch((err) => {
      LogUtil.logOnAll("SelectNASLocation", "nas_set_config failed" + JSON.stringify(err));
      this.setState({ showFailDialog: true });
      // Toast.fail('c_set_fail', err);
    });


  }
  _getNASStatus() {
    RPC.callMethod("nas_get_config", {}).then((res) => {
      console.log("nas_get_config", res)
      LogUtil.logOnAll("nas_get_config3-=-=-=-=", JSON.stringify(res));
      if (res.result.dir == this.state.storageLocationName) {
        this.props.navigation.navigate('NASNetworkLocation', { deviceMessage: res.result, NASLocation: this.state.storageLocationName })
      }

    }).catch((err) => {
      LogUtil.logOnAll("SelectNASLocation", "nas_get_config failed" + JSON.stringify(err));
      console.log('err', err)
      Toast.fail('c_get_fail', err);

    })
  }



}
export const styles = StyleSheet.create({
  container: {
    backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : '#fff',
    flexDirection: 'column',
    height: '100%',
    width: '100%',
    paddingLeft: 24,
    paddingRight: 24

  }
});
