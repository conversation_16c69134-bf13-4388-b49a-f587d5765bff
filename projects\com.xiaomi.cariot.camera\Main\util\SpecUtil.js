import { Service } from "miot";
import { Device } from "miot";
import VersionUtil from "./VersionUtil";
import { CAMERA_CONTROL_SEPC_PARAMS } from "../Constants";
import RPC from "./RPC";
import StorageKeys from "../StorageKeys";
import AlarmUtilV2, {
  PIID_CAMERA_WATERMARK,
  SCREEN_SETTING_BRIGHTNESS_PIID,
  SCREEN_SETTING_DISPLAY_STATE_PIID, SIID_CAMERA_CONTROL
} from "./AlarmUtilV2";

export default class SpecUtil {
  static toggleSleep(isSleep) {
    return new Promise((resolve, reject) => {
      if (isSleep) {
        if (VersionUtil.isUsingSpec(Device.model)) {
          Service.spec.setPropertiesValue([{ ...CAMERA_CONTROL_SEPC_PARAMS[6], value: false }])
            .then((result) => {
              let isOk = result[0].code == 0;
              if (isOk) {
                resolve(isOk);
              } else {
                reject("spec not ok");
              }
            })
            .catch((error) => {
              reject(error);
            });
        } else {
          RPC.callMethod("set_" + "power", ["off"])
            .then((result) => {
              resolve(result);
            })
            .catch((err) => {
              reject(err);
            });
        }
      } else {
        if (VersionUtil.isUsingSpec(Device.model)) {
          Service.spec.setPropertiesValue([{ ...CAMERA_CONTROL_SEPC_PARAMS[6], value: true }])
            .then((result) => {
              let isOk = result[0].code == 0;
              if (isOk) {
                resolve(isOk);
              } else {
                reject("spec failed");
              }
            })
            .catch((err) => {
              reject(err);
            });
        } else {
          RPC.callMethod("set_" + "power", ["on"])
            .then((result) => {
              resolve(result);
            })
            .catch((err) => {
              reject(err);
            });
        }

      }
    });
    
  }

  static queryWatermark() {
    return new Promise((resolve, reject) => {
      if (VersionUtil.isUsingSpec(Device.model)) {
        let params = [{ sname: SIID_CAMERA_CONTROL, pname: PIID_CAMERA_WATERMARK }];
        AlarmUtilV2.getSpecPValue(params)
        // Service.spec.getPropertiesValue([CAMERA_CONTROL_SEPC_PARAMS[5]])
          .then((result) => {
            StorageKeys.IS_WATERMARK_OPEN = (result[0].value || false);
            resolve(result[0].value);
          })
          .catch((error) => {
            reject(error);
          });
      } else {
        RPC.callMethod("get_prop", ["watermark"])
          .then((res) => {
            if (res.hasOwnProperty('result')) {
              StorageKeys.IS_WATERMARK_OPEN = (res.result[0] == 'on');
              resolve(res.result[0] == 'on');
            } else {
              reject("result error");
            }
          })
          .catch((err) => { // 查询状态出错  不管  直接去查询
            reject(err);
          });
      }
    });
  }


}
