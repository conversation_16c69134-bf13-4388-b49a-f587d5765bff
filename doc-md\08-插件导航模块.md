<a name="module_miot/Package"></a>

## miot/Package
扩展程序包参数, 主要来自于[packageInfo.json](packageInfo.json) 的配置与系统本身的特性

**Export**: public  
**Doc_name**: 插件导航模块  
**Doc_index**: 8  
**Doc_directory**: sdk  
**Example**  
```js
import {Package} from 'miot'
 import Package from 'miot/Package'

     Package.entrance
     Package.entryInfo
     Package.exitInfo={...}

    Package.pluginID
    Package.packageID
    Package.packageName
    Package.version
    Package.minApiLevel
    Package.buildType
    Package.isDebug
    Package.models

    Package.entry(App, ()=>{...});
    Package.exit({...});
```

* [miot/Package](#module_miot/Package)
    * [module.exports](#exp_module_miot/Package--module.exports) ⏏
        * _static_
            * [.exitInfo](#module_miot/Package--module.exports.exitInfo) : <code>json</code>
            * [.rnVersion](#module_miot/Package--module.exports.rnVersion)
            * [.BLEAutoCheckUpgradeOptions](#module_miot/Package--module.exports.BLEAutoCheckUpgradeOptions)
            * [.entrance](#module_miot/Package--module.exports.entrance) : <code>Entrance</code>
            * [.pageParams](#module_miot/Package--module.exports.pageParams) : <code>object</code>
            * [.entryInfo](#module_miot/Package--module.exports.entryInfo) : <code>json</code>
            * [.packageID](#module_miot/Package--module.exports.packageID) : <code>int</code>
            * [.version](#module_miot/Package--module.exports.version) : <code>string</code>
            * [.packageName](#module_miot/Package--module.exports.packageName) : <code>string</code>
            * [.minApiLevel](#module_miot/Package--module.exports.minApiLevel) : <code>int</code>
            * [.buildType](#module_miot/Package--module.exports.buildType) : <code>string</code>
            * [.isDebug](#module_miot/Package--module.exports.isDebug) : <code>boolean</code>
            * [.models](#module_miot/Package--module.exports.models) : <code>string</code>
            * [.entry(RootComponent, afterPackageEntry)](#module_miot/Package--module.exports.entry)
            * [.exit(info)](#module_miot/Package--module.exports.exit)
            * [.installModule(moduleId)](#module_miot/Package--module.exports.installModule) ⇒ <code>json</code>
        * _inner_
            * [~_packageActiveDate](#module_miot/Package--module.exports.._packageActiveDate)
            * [~Entrance](#module_miot/Package--module.exports..Entrance) : <code>object</code>
                * [.Main](#module_miot/Package--module.exports..Entrance.Main)
                * [.Scene](#module_miot/Package--module.exports..Entrance.Scene)
            * [~PackageEvent](#module_miot/Package--module.exports..PackageEvent) : <code>object</code>
                * ["packageWillLoad"](#module_miot/Package--module.exports..PackageEvent.event_packageWillLoad)
                * ["packageDidLoaded"](#module_miot/Package--module.exports..PackageEvent.event_packageDidLoaded)
                * ["packageWillPause"](#module_miot/Package--module.exports..PackageEvent.event_packageWillPause)
                * ["packageDidResume"](#module_miot/Package--module.exports..PackageEvent.event_packageDidResume)
                * ["packageAuthorizationAgreed"](#module_miot/Package--module.exports..PackageEvent.event_packageAuthorizationAgreed)
                * ["packageAuthorizationCancel" (autoExit)](#module_miot/Package--module.exports..PackageEvent.event_packageAuthorizationCancel)
                * ["packageReceivedInformation"](#module_miot/Package--module.exports..PackageEvent.event_packageReceivedInformation)
                * ["packageWillExit"](#module_miot/Package--module.exports..PackageEvent.event_packageWillExit)
                * ["packageViewWillAppear"](#module_miot/Package--module.exports..PackageEvent.event_packageViewWillAppear)
                * ["packageReceivedOutAppInformation"](#module_miot/Package--module.exports..PackageEvent.event_packageReceivedOutAppInformation)
                * ["packageViewWillDisappearIOS"](#module_miot/Package--module.exports..PackageEvent.event_packageViewWillDisappearIOS)
                * ["packageWillStopAndroid"](#module_miot/Package--module.exports..PackageEvent.event_packageWillStopAndroid)
            * [~EVENT_TYPE](#module_miot/Package--module.exports..EVENT_TYPE) : <code>enum</code>
            * [~extra](#module_miot/Package--module.exports..extra) : <code>Object</code>
            * [~onPluginEvent(type, data)](#module_miot/Package--module.exports..onPluginEvent)


* * *

<a name="exp_module_miot/Package--module.exports"></a>

### module.exports ⏏
**Kind**: Exported member  
**Export**:   

* * *

<a name="module_miot/Package--module.exports.exitInfo"></a>

#### module.exports.exitInfo : <code>json</code>
退出后返回给调用者的信息, 例如自定义场景

**Kind**: static property of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  
**Example**  
```js
//自定义trigger场景保存退出 finishCustomSceneSetupWithTrigger
var trigger = Package.entryInfo;
trigger.payload = { 'xxx': 'xxx' };//trigger payload 数据
Package.exitInfo = trigger;

//自定义action场景保存退出 finishCustomSceneSetupWithAction
var action = Package.entryInfo;
action.payload = { 'xxx': 'xxx' };//action payload 数据
Package.exitInfo = action;
...
Package.exit();
```

* * *

<a name="module_miot/Package--module.exports.rnVersion"></a>

#### module.exports.rnVersion
获取React Native版本

**Kind**: static property of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  

* * *

<a name="module_miot/Package--module.exports.BLEAutoCheckUpgradeOptions"></a>

#### module.exports.BLEAutoCheckUpgradeOptions
自动BLE/Mesh设备升级检查，即使设置了alertDialog为true，也仅仅会在直连完成后才弹窗，红点进插件就可以显示

**Kind**: static property of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  
**Since**: 10039  

| Param | Description |
| --- | --- |
| redPoit | 红点 |
| alertDialog | 弹窗 |
| authType | 蓝牙连接类型(0: 普通小米蓝牙协议设备(新接入设备已废弃该类型)，1: 安全芯片小米蓝牙设备（比如锁类产品） 4: Standard Auth 标准蓝牙认证协议(通常2019.10.1之后上线的新蓝牙设备) 5: mesh 设备) |

**Example**  
```js
Package.BLEAutoCheckUpgradeOptions = {
  enable: true,
  redPoint: true,
  alertDialog: true,
  authType: 5
}
```

* * *

<a name="module_miot/Package--module.exports.entrance"></a>

#### module.exports.entrance : <code>Entrance</code>
入口类型,Main or Scene or 用户自定义（Host.ui.openPluginPage(did, pageName, pageParams) 中 pageName的值）

**Kind**: static constant of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  
**Read only**: true  

* * *

<a name="module_miot/Package--module.exports.pageParams"></a>

#### module.exports.pageParams : <code>object</code>
入口类型参数, Host.ui.openPluginPage(did, pageName, pageParams) 中 pageParams的值

**Kind**: static constant of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  
**Read only**: true  

* * *

<a name="module_miot/Package--module.exports.entryInfo"></a>

#### module.exports.entryInfo : <code>json</code>
打开rn插件时，从native传递到RN的初始化数据信息

**Kind**: static constant of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  
**Read only**: true  

* * *

<a name="module_miot/Package--module.exports.packageID"></a>

#### module.exports.packageID : <code>int</code>
小米开放平台生成的插件包 ID

**Kind**: static constant of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  
**Read only**: true  

* * *

<a name="module_miot/Package--module.exports.version"></a>

#### module.exports.version : <code>string</code>
程序包的版本号, 来自于[project.json](project.json) 的 [version](version)

**Kind**: static constant of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  
**Read only**: true  

* * *

<a name="module_miot/Package--module.exports.packageName"></a>

#### module.exports.packageName : <code>string</code>
程序包名, 来自于[project.json](project.json) 的 [package_name](package_name)

**Kind**: static constant of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  
**Read only**: true  

* * *

<a name="module_miot/Package--module.exports.minApiLevel"></a>

#### module.exports.minApiLevel : <code>int</code>
扩展程序适用的最低 API level, 来自于[project.json](project.json) 的 [min_api_level](min_api_level)

**Kind**: static constant of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  
**Read only**: true  

* * *

<a name="module_miot/Package--module.exports.buildType"></a>

#### module.exports.buildType : <code>string</code>
发布类型, debug | release

**Kind**: static constant of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  
**Read only**: true  

* * *

<a name="module_miot/Package--module.exports.isDebug"></a>

#### module.exports.isDebug : <code>boolean</code>
判断是否是调试版本

**Kind**: static constant of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  
**Read only**: true  

* * *

<a name="module_miot/Package--module.exports.models"></a>

#### module.exports.models : <code>string</code>
适配的固件 model, 来自于@link packageInfo.json 的

**Kind**: static constant of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  
**Read only**: true  

* * *

<a name="module_miot/Package--module.exports.entry"></a>

#### module.exports.entry(RootComponent, afterPackageEntry)
系统入口

**Kind**: static method of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  

| Param | Type | Description |
| --- | --- | --- |
| RootComponent | <code>React.Component</code> | 入口的React Component模块 |
| afterPackageEntry | <code>function</code> | 进入后, RootComponent 加载之前执行, 缺省为空 |

**Example**  
```js
import SceneMain from '...';
import App from '...';

import {Package, Entrance} from 'miot';

switch(Package.entrance){
  case Entrance.Scene:
     Package.entry(SceneMain, ()=>{...});
     break;
  default:
     Package.entry(App, ()=>{...});
     break;
}
```

* * *

<a name="module_miot/Package--module.exports.exit"></a>

#### module.exports.exit(info)
强制退出插件

**Kind**: static method of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  

| Param | Type | Description |
| --- | --- | --- |
| info | <code>JSON</code> | 如果不为空, 则等同于设置 Package.exitInfo SDK_10052  新增 animated字段 eg: Package.exit({'animated': false}) 表示退出时不使用动画 目前只在ios生效，默认为true |

**Example**  
```js
Package.exit({...});
```
**Example**  
```js
Package.exitInfo = {...}
 Package.exit();
```

* * *

<a name="module_miot/Package--module.exports.installModule"></a>

#### module.exports.installModule(moduleId) ⇒ <code>json</code>
since 10052
android only
部分插件用到的功能不会跟着米家APP一起安装，需要先安装再使用(请求安装的接口是installModule)，
已经安装的模块多次调用installModule不会重复安装。
需要调用前需要安装的功能有：
 react-native-opencv 从10052开始引入，对应的moduleId为RnOpencv，Android平台需要先安装再使用，iOS则没这个要求

**Kind**: static method of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  
**Returns**: <code>json</code> - 返回值：安装成功或已安装返回{code:0,data:{installed:true}}，安装失败返回{code:0,data:{installed:false}}  

| Param | Type | Description |
| --- | --- | --- |
| moduleId | <code>string</code> | 可选值：RnOpencv(对应为react-native-opencv) |

**Example**  
```js
const moduleId = 'RnOpencv';
Package.installModule(moduleId).then(res=>{
     if(res && res.data && res.data.installed){
       console.log(`module:${moduleId} is installed`).
     }
   }).catch(err=>{
      console.log('installeModule error:',JSON.stringify(err));
   })
```

* * *

<a name="module_miot/Package--module.exports.._packageActiveDate"></a>

#### module.exports~\_packageActiveDate
RN活跃时间统计

**Kind**: inner property of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  

* * *

<a name="module_miot/Package--module.exports..Entrance"></a>

#### module.exports~Entrance : <code>object</code>
扩展程序调用的入口类型

**Kind**: inner namespace of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  

* [~Entrance](#module_miot/Package--module.exports..Entrance) : <code>object</code>
    * [.Main](#module_miot/Package--module.exports..Entrance.Main)
    * [.Scene](#module_miot/Package--module.exports..Entrance.Scene)


* * *

<a name="module_miot/Package--module.exports..Entrance.Main"></a>

##### Entrance.Main
主入口

**Kind**: static constant of [<code>Entrance</code>](#module_miot/Package--module.exports..Entrance)  

* * *

<a name="module_miot/Package--module.exports..Entrance.Scene"></a>

##### Entrance.Scene
场景入口

**Kind**: static constant of [<code>Entrance</code>](#module_miot/Package--module.exports..Entrance)  

* * *

<a name="module_miot/Package--module.exports..PackageEvent"></a>

#### module.exports~PackageEvent : <code>object</code>
Package事件名集合

**Kind**: inner namespace of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  
**Example**  
```js
import {PackageEvent} from 'miot'
   const subscription = PackageEvent.packageWillPause.addListener(()=>{
         ...
    })
   ...
   subscription.remove()
   ...
```

* [~PackageEvent](#module_miot/Package--module.exports..PackageEvent) : <code>object</code>
    * ["packageWillLoad"](#module_miot/Package--module.exports..PackageEvent.event_packageWillLoad)
    * ["packageDidLoaded"](#module_miot/Package--module.exports..PackageEvent.event_packageDidLoaded)
    * ["packageWillPause"](#module_miot/Package--module.exports..PackageEvent.event_packageWillPause)
    * ["packageDidResume"](#module_miot/Package--module.exports..PackageEvent.event_packageDidResume)
    * ["packageAuthorizationAgreed"](#module_miot/Package--module.exports..PackageEvent.event_packageAuthorizationAgreed)
    * ["packageAuthorizationCancel" (autoExit)](#module_miot/Package--module.exports..PackageEvent.event_packageAuthorizationCancel)
    * ["packageReceivedInformation"](#module_miot/Package--module.exports..PackageEvent.event_packageReceivedInformation)
    * ["packageWillExit"](#module_miot/Package--module.exports..PackageEvent.event_packageWillExit)
    * ["packageViewWillAppear"](#module_miot/Package--module.exports..PackageEvent.event_packageViewWillAppear)
    * ["packageReceivedOutAppInformation"](#module_miot/Package--module.exports..PackageEvent.event_packageReceivedOutAppInformation)
    * ["packageViewWillDisappearIOS"](#module_miot/Package--module.exports..PackageEvent.event_packageViewWillDisappearIOS)
    * ["packageWillStopAndroid"](#module_miot/Package--module.exports..PackageEvent.event_packageWillStopAndroid)


* * *

<a name="module_miot/Package--module.exports..PackageEvent.event_packageWillLoad"></a>

##### "packageWillLoad"
插件将要加载

**Kind**: event emitted by [<code>PackageEvent</code>](#module_miot/Package--module.exports..PackageEvent)  

* * *

<a name="module_miot/Package--module.exports..PackageEvent.event_packageDidLoaded"></a>

##### "packageDidLoaded"
插件加载完成事件

**Kind**: event emitted by [<code>PackageEvent</code>](#module_miot/Package--module.exports..PackageEvent)  

* * *

<a name="module_miot/Package--module.exports..PackageEvent.event_packageWillPause"></a>

##### "packageWillPause"
插件将暂时退出前台事件

**Kind**: event emitted by [<code>PackageEvent</code>](#module_miot/Package--module.exports..PackageEvent)  

* * *

<a name="module_miot/Package--module.exports..PackageEvent.event_packageDidResume"></a>

##### "packageDidResume"
插件将重回前台事件

**Kind**: event emitted by [<code>PackageEvent</code>](#module_miot/Package--module.exports..PackageEvent)  

* * *

<a name="module_miot/Package--module.exports..PackageEvent.event_packageAuthorizationAgreed"></a>

##### "packageAuthorizationAgreed"
SDK弹出的隐私同意时的回调

**Kind**: event emitted by [<code>PackageEvent</code>](#module_miot/Package--module.exports..PackageEvent)  
**Since**: 10037  

* * *

<a name="module_miot/Package--module.exports..PackageEvent.event_packageAuthorizationCancel"></a>

##### "packageAuthorizationCancel" (autoExit)
用户撤销隐私授权时的回调

**Kind**: event emitted by [<code>PackageEvent</code>](#module_miot/Package--module.exports..PackageEvent)  

| Param |
| --- |
| autoExit | 


* * *

<a name="module_miot/Package--module.exports..PackageEvent.event_packageReceivedInformation"></a>

##### "packageReceivedInformation"
插件接收到场景等通知消息

**Kind**: event emitted by [<code>PackageEvent</code>](#module_miot/Package--module.exports..PackageEvent)  

* * *

<a name="module_miot/Package--module.exports..PackageEvent.event_packageWillExit"></a>

##### "packageWillExit"
插件将退出事件

**Kind**: event emitted by [<code>PackageEvent</code>](#module_miot/Package--module.exports..PackageEvent)  

* * *

<a name="module_miot/Package--module.exports..PackageEvent.event_packageViewWillAppear"></a>

##### "packageViewWillAppear"
从 Native 界面返回到插件,可以通过监听此事件更新已加载过的视图，或进行相应的事件处理。

**Kind**: event emitted by [<code>PackageEvent</code>](#module_miot/Package--module.exports..PackageEvent)  

* * *

<a name="module_miot/Package--module.exports..PackageEvent.event_packageReceivedOutAppInformation"></a>

##### "packageReceivedOutAppInformation"
插件收到外部APP跳转带过来的信息

**Kind**: event emitted by [<code>PackageEvent</code>](#module_miot/Package--module.exports..PackageEvent)  
**Since**: 10053  

* * *

<a name="module_miot/Package--module.exports..PackageEvent.event_packageViewWillDisappearIOS"></a>

##### "packageViewWillDisappearIOS"
从插件页面离开到 Native 界面, iOS Only

**Kind**: event emitted by [<code>PackageEvent</code>](#module_miot/Package--module.exports..PackageEvent)  
**Since**: 10038  

* * *

<a name="module_miot/Package--module.exports..PackageEvent.event_packageWillStopAndroid"></a>

##### "packageWillStopAndroid"
插件进入后台(Android only)
在插件内，用户按下home键，米家进入后台会发送该通知

**Kind**: event emitted by [<code>PackageEvent</code>](#module_miot/Package--module.exports..PackageEvent)  
**Since**: 10048  

* * *

<a name="module_miot/Package--module.exports..EVENT_TYPE"></a>

#### module.exports~EVENT\_TYPE : <code>enum</code>
JS端通知Native端的事件类型

**Kind**: inner enum of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  
**Properties**

| Name | Type | Default | Description |
| --- | --- | --- | --- |
| NAVIGATION_STATE_CHANGE | <code>number</code> | <code>1</code> | 插件路由发生变化 |


* * *

<a name="module_miot/Package--module.exports..extra"></a>

#### module.exports~extra : <code>Object</code>
entryInfo={entrance:scene|main,info:{json}}

**Kind**: inner constant of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  

* * *

<a name="module_miot/Package--module.exports..onPluginEvent"></a>

#### module.exports~onPluginEvent(type, data)
在插件端发生某些事件，通知native端

**Kind**: inner method of [<code>module.exports</code>](#exp_module_miot/Package--module.exports)  

| Param | Type | Description |
| --- | --- | --- |
| type | <code>number</code> | 事件类型 |
| data | <code>object</code> | 传入native的数据 |


* * *

