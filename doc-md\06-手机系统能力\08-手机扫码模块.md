<a name="module_miot/system"></a>

## miot/system
通过米家APP扫描二维码

**Export**: public  
**Doc_name**: 手机扫码模块  
**Doc_index**: 8  
**Doc_directory**: system  
**Example**  
```js
import {System} from "miot"
...
System.scancode.getScanCode().then(res => {//return result})
...
```

* [miot/system](#module_miot/system)
    * [~IScanCode](#module_miot/system..IScanCode)
        * [.scanCode()](#module_miot/system..IScanCode+scanCode) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>


* * *

<a name="module_miot/system..IScanCode"></a>

### miot/system~IScanCode
**Kind**: inner interface of [<code>miot/system</code>](#module_miot/system)  

* * *

<a name="module_miot/system..IScanCode+scanCode"></a>

#### iScanCode.scanCode() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
使用米家APP进行扫码操作

**Kind**: instance method of [<code>IScanCode</code>](#module_miot/system..IScanCode)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code> - res
成功时：{"code":0, "data":xxx},data.result:string,扫码结果
失败时：{"code":-1, "message":"xxx" }；  
**Since**: 10043  
**Example**  
```js
System.scancode.scanCode().then((res) => {
    if (res && res.data) {
      alert(`getScanCode success,result:${ res.data.result }`);
    } else {
      alert(`getScanCode fail,${ JSON.stringify(res) }`);
    }
  }).catch((error) => {
    alert(`getScanCode fail,${ JSON.stringify(error) }`);
  });
```

* * *

