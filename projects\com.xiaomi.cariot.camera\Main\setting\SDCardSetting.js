'use strict';

import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import Separator from 'miot/ui/Separator';
import { strings } from 'miot/resources';
import React from 'react';
import { StyleSheet, ScrollView, Image, Text, View, Dimensions, StatusBar, TouchableOpacity, ActivityIndicator } from 'react-native';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';

import { styles as settingStyles } from './SettingStyles';

import { Device, Host, DarkMode } from "miot";
import { ChoiceDialog, MessageDialog } from 'miot/ui/Dialog';
import Toast from '../components/Toast';
import StorageKeys from '../StorageKeys';
import Service from 'miot/Service';
import SdFileManager from '../sdcard/util/SdFileManager';
import id from 'miot/resources/strings/id';
import CameraPlayer from '../util/CameraPlayer';
import NoSdcardPage from './NoSdcardPage';
import Util from '../util2/Util';
import VersionUtil from '../util/VersionUtil';
import {
  CAMERA_CONTROL_SEPC_PARAMS
} from '../Constants';
import CancelableProgressDialog from '../ui/CancelableProgressDialog';
import NavigationBar from "miot/ui/NavigationBar";
import RPC from '../util/RPC';
import TrackUtil from '../util/TrackUtil';
import LogUtil from '../util/LogUtil';
import { DescriptionConstants } from '../Constants';
import CameraConfig from '../util/CameraConfig';
import AlarmUtil from '../util/AlarmUtil';
import AlarmUtilV2, {
  CAMERA_SDCARD_SIID,
  CAMERA_SDCARD_EJECT_ACTION,
  CAMERA_SDCARD_FORMAT_ACTION,
  SIID_CAMERA_CONTROL,
  CAMERA_CONTROL_RECORD_MODE_PIID,
  PIID_COCKPIT_STORAGE_SWITCH, SIID_COCKPIT_SERVICE
} from "../util/AlarmUtilV2";
export default class SDCardSetting extends React.Component {
  // static navigationOptions = (res) => {
  //   if (!res.navigation.state || !res.navigation.state.params) {
  //     return;
  //   }
  //   let params = res.navigation.state.params;
  //   return {
  //     gesturesEnabled: params && params.enableGestures
  //   };
  // };
  constructor(props, context) {
    super(props, context);

    // if (recordModeStr === "stop") {
    //   this.recordMode = 2;
    // } else if (recordModeStr === "off") {
    //   this.recordMode = 0;
    // } else {
    //   this.recordMode = 1;
    // }
    this.state = {
      sdcardStatus: -1, // 参考 'sds_status'
      motionRecord: 2, // stop(关闭录制) on(开启移动侦测录制) off(关闭移动侦测，始终录制)   数字 0 all 1 on  2 stop

      sdRecordSwitch: false,
      showSelectMode: false,
      showFormatConfirm: false,
      showExitConfirm: false,

      totalSize: 0,
      videoSize: 0,
      idleSize: 0,

      isVip: false,
      hideSdcardStatus: true,
      sdcardFormatDialog: false,
      isLoading: true
    };
    this._getSetting = this._getSetting.bind(this);   
    this.sdcardNoExitCodeTimes = 0;
    this._formatSdCardTimes = 0;
  }

  _renderSDcardStatusViewV2() {
    let usedSize = Util._formatSize(this.state.videoSize);
    let totalSize = Util._formatSize(this.state.totalSize);
    return (
      <View style={styles.stateContainer}
        key={1}
      >
        <View style={styles.stageBack}>
          <Image style={styles.stateImage}
            source={
              (this.state.sdcardStatus != 0 && this.state.sdcardStatus != 4)
                ? require('../../Resources/Images/mjv3_sdCard_abnormal.png')
                : (
                  this.state.motionRecord == 2
                    ? require('../../Resources/Images/mjv3_sdCard_pause.png')
                    : require('../../Resources/Images/mjv3_sdCard_normal.png')
                )
            }
          />
          <View style={styles.stateCover}>
            {/* 上面的存储暂停 */}
            <View style={{ height: 124, width: 180, justifyContent: "center", alignItems: "center", backgroundColor: "#00000000", paddingBottom: 30 }}>
              {
                this.state.motionRecord == 2 ?
                  <Text
                    style={styles.stateCoverTitle}>
                    {LocalizedStrings["camera_storage_pause"]}
                  </Text>
                  :
                  <Text
                    style={[styles.stateCoverTitle, { fontSize: Util.isLanguageCN() ? 22 : 18 }]}
                    numberOfLines={3}
                  >
                    {(LocalizedStrings['sds_status'])}
                  </Text>
              }
            </View>

            <View style={styles.stateCoverSeprate}></View>

            {
              this.state.motionRecord != 2 ?
                <Text style={styles.stateCoverDescV2}>
                  {LocalizedStrings[`sds_status_${ this.state.sdcardStatus }`]}
                </Text>
                :
                null
            }
          </View>
        </View>
        <Text
          style={styles.totalText}>
          {
            this.state.sdcardStatus == 4 || this.state.sdcardStatus == 0 && this.state.totalSize != null && this.state.totalSize != 0 
              ? this.state.sdcardStatus == 4 ? '' 
                : this.state.motionRecord == 0 && this.sd_card_record_duration && this.sd_card_record_duration > 0 ? `${ LocalizedStrings["sdcard_status_capcity"].replace("%1$d", usedSize).replace("%2$d", totalSize) }\n${ LocalizedStrings["sdcard_record_duration"].replace("%1$s", this.sd_card_record_duration) }`
                  : LocalizedStrings["sdcard_status_capcity"].replace("%1$d", usedSize).replace("%2$d", totalSize)
              : this.state.sdcardStatus == 3 || this.state.sdcardStatus == 2 || this.state.sdcardStatus == CameraPlayer.SD_CARD_NEED_FORMAT_CODE 
                || this.state.sdcardStatus == CameraPlayer.SD_CARD_FILE_ERROR_CODE
                ? LocalizedStrings["sds_try_format"]
                : LocalizedStrings[`sds_try_action_${ this.state.sdcardStatus == CameraPlayer.SD_CARD_INCOMPATIBLE_CODE 
                  ? CameraPlayer.SD_CARD_TOO_SMALL_CODE : this.state.sdcardStatus }`]
          }
        </Text>
      </View>
    );
  }

  _renderSDcardStatusView() {
    return (
      <View style={styles.stateContainer}
        key={1}
      >
        <View style={styles.stageBack}>
          <Image style={styles.stateImage}
            source={
              (this.state.sdcardStatus == 3 || this.state.sdcardStatus == CameraPlayer.SD_CARD_NEED_FORMAT_CODE)
                ? require('../../Resources/Images/mjv3_sdCard_abnormal.png')
                : (
                  this.state.motionRecord == 2
                    ? require('../../Resources/Images/mjv3_sdCard_pause.png')
                    : require('../../Resources/Images/mjv3_sdCard_normal.png')
                )
            }
          />
          <View style={styles.stateCover}>
            {/* 上面的存储暂停 */}
            <View style={{ height: 124, width: 180, justifyContent: "center", alignItems: "center", backgroundColor: "#00000000", paddingBottom: 30 }}>
              {
                this.state.motionRecord == 2 ?
                  <Text
                    style={styles.stateCoverTitle}>
                    {LocalizedStrings["camera_storage_pause"]}
                  </Text>
                  :
                  <Text
                    style={[styles.stateCoverTitle, { maxWidth: 160, fontSize: Util.isLanguageCN() ? 22 : 18 }]}
                    numberOfLines={3}
                  >
                    {(this.state.hideSdcardStatus ? "" : LocalizedStrings['sds_status']) + LocalizedStrings[`sds_status_${this.state.sdcardStatus}`]}
                  </Text>
              }
            </View>



            <View style={styles.stateCoverSeprate}></View>

            {
              this.state.sdcardStatus == 3 ?
                <Text style={styles.stateCoverDesc}>
                  {LocalizedStrings['sds_try_format']}
                </Text>
                :
                null
            }
            {
              this.state.sdcardStatus != 3 ?
                <Text
                  style={[styles.stateCoverDetail, {textAlign: "center", textAlignVertical: "center"}]}>{LocalizedStrings['sds_left'] + "\n" + Util._formatSize(this.state.idleSize)}</Text>
                :
                null
            }
            
            {/* <Text style={styles.stateCoverDetail}>{LocalizedStrings['sds_left']}</Text>
            <Text style={{ position: "absolute", bottom: 39, color: "#ffffff", fontSize: Util.isLanguageCN() ? 15 }}>{this._formatSize(this.state.idleSize)}
            </Text> */}
          </View>
        </View>
        <Text
          style={styles.totalText}>
          {
            this.state.sdcardStatus != 3 && this.state.totalSize != null && this.state.totalSize != 0 ? LocalizedStrings['sds_total'] + Util._formatSize(this.state.totalSize) : ""
          }
        </Text>
      </View>
    );
  }
  render() {
    let sdCardView = (
      <ScrollView showsVerticalScrollIndicator={false} key={111111}>
        {CameraConfig.supportSDCardV2(Device.model) ? this._renderSDcardStatusViewV2() : this._renderSDcardStatusView()}
        {/* <View style={styles.whiteblank}
          key={2}
        /> */}
        {
          CameraConfig.debugSpecialSupport() ?
            <View style={ {
              height: 0.5,
              marginHorizontal: 27,
              backgroundColor: "#e5e5e5",
              marginBottom: 20,
              marginTop: 20 }}></View>
            : null
        }

        {
          CameraConfig.debugSpecialSupport() ? <View
            style={styles.featureSetting}
            key={8}>
            <ListItemWithSwitch
              title={LocalizedStrings['sds_switch']}
              // value={this.state.motionRecord != 2}
              value={this.state.sdRecordSwitch}
              onValueChange={(value) => {
                if (this.state.sdcardStatus == 4) {
                  Toast.fail('formating_error');
                  return;
                } else {
                  this._onEnableValueChange(value);

                }
              }}
              showSeparator={false}
              unlimitedHeightEnable={true}
              titleNumberOfLines={2}
              onPress={() => {}}
              accessibilitySwitch={{
                accessibilityLabel: DescriptionConstants.sz_7_11
              }}
            />

          </View> : null
        }

        { CameraConfig.debugSpecialSupport() && this.state.motionRecord != 2 && <View style={ {
          height: 0.5,
          marginHorizontal: 27,
          backgroundColor: "#e5e5e5",
          marginBottom: 20,
          marginTop: 20 }}></View>}
        {
          CameraConfig.debugSpecialSupport() ?
            <View
              style={styles.featureSetting}
              key={4}
            >
              <ListItem
                title={LocalizedStrings['sds_format']}
                showSeparator={false}
                onPress={() => {
                  TrackUtil.reportClickEvent("Setting_FormatSDCard_ClickNum");
                  if (this.state.sdcardStatus == 4) {
                    Toast.fail('formating_error');
                    return;
                  }

                  this.setState({ showFormatConfirm: true });
                }

                }
                valueNumberOfLines={3}
                unlimitedHeightEnable={true}
                titleNumberOfLines={2}
              />

            </View> : null
        }


      </ScrollView>
    );
    let width = Dimensions.get("window").width - 34;
    let noSDCardView = (
      <NoSdcardPage
        key={'NoSdcardPage'}
        onButtonClick={
          () => {
            // 只会支持10069  直接跳购买页啊
            let mSupportCloudCountry = CameraConfig.isSupportCloud();
            let isInternationalServer = CameraConfig.getInternationalServerStatus();
            Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: "sdmgt_button" });// 直接跳过去了

            // if (isInternationalServer && mSupportCloudCountry) {// 海外云存 跳购买页
            //   Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: "sdmgt_button" });// 直接跳过去了
            // } else {
            //   this.props.navigation && this.props.navigation.navigate("CloudIntroPage");
            // }
          }
        }
        // showBuyButton={this.props.navigation&&this.props.navigation.state.routeName !== "topPage2" ? true : false}
        showBuyButton={this.props.navigation&&(this.props.navigation.state.routeName !== "topPage2" && this.props.navigation.state.routeName !== "topPage1") ? true : false}
      />
    );

    return (

      <View style={styles.container}>
        {
          this.state.sdcardStatus == -1
            ? (<View></View>)
            : (this.state.sdcardStatus != 1 && this.state.sdcardStatus != 5
              ? [sdCardView]
              : [noSDCardView]
            )
        }
        <ChoiceDialog
          visible={this.state.showSelectMode}
          dialogStyle={{ itemSubtitleNumberOfLines: 2, itemSubtitleStyle: { paddingRight: 10 }, itemTitleNumberOfLines: 2 }}
          title={LocalizedStrings['sds_record_mod']}
          options={
            ['1', '2'].map((item) => {
              return {
                title: LocalizedStrings[`sds_record_mod${item}`],
                subtitle: LocalizedStrings[`sds_record_mod${item}_detail`]
              };
            })
          }
          selectedIndexArray={this.state.motionRecord == 0 ? [1] : [0]}
          onDismiss={() => this.setState({ showSelectMode: false })}
          onSelect={(index) => this._onModeValueWillChange(index == 1 ? 0 : 1)}
        />
        <MessageDialog
          visible={CameraConfig.debugSpecialSupport() && this.state.showFormatConfirm}
          title={LocalizedStrings['sds_format']}
          message={LocalizedStrings['sds_format_alert']}
          onDismiss={() => this.setState({ showFormatConfirm: false })}
          buttons={[
            { text: LocalizedStrings["action_cancle"], callback: () => this.setState({ showFormatConfirm: false }) },
            { text: LocalizedStrings["action_confirm"], callback: () => this._formatSdCard() }
          ]}
        />
        <MessageDialog
          visible={CameraConfig.debugSpecialSupport() && this.state.showExitConfirm}
          title={LocalizedStrings['sds_exit']}
          message={LocalizedStrings['sds_exit_alert']}
          messageStyle={{ textAlign: 'center' }}
          onDismiss={() => this.setState({ showExitConfirm: false })}
          buttons={[
            { text: LocalizedStrings["action_cancle"], callback: () => this.setState({ showExitConfirm: false }) },
            { text: LocalizedStrings["action_confirm"], callback: () => this._exitSdCard() }
          ]}
        />
        <CancelableProgressDialog
          disableOutsideCancel={false}
          cancelable={true}
          ref={(ref) => {
            this.cancelableProgressDialog = ref;
          }}
          loadingText={LocalizedStrings["formating_error"]}

        >
        </CancelableProgressDialog>
        {this.state.isLoading ?  
          <View
            style={{position: "absolute", height: "100%", width: "100%", justifyContent: "center", alignItems: "center"}}
          >
            <ActivityIndicator
              style={{ width: 54, height: 54 }}
              color={Util.isDark() ? "xm#ffffff" : "xm#000000"}
              size={"large"}
              animating={this.state.isLoading}
              hidesWhenStopped={true}
            />
          </View>
        : null}
        {this._renderSDCardFormatDialog()}
        
      </View>
    );
  }

  componentDidMount() {
    this.props.navigation && this.props.navigation.setParams({
      title: LocalizedStrings['sds_title'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation && this.props.navigation.goBack(); }
        }
      ],
      titleStyle: {
        fontSize: Util.isLanguageCN() ? 18 : 16,
        color: '#333333',
        fontWeight: 500
      }
    });


    StorageKeys.IS_VIP_STATUS.then((res) => {
      this.setState({ isVip: res });
    }).catch(() => {
      this.setState({ isVip: false });
    });

    this.willFocusSubscription = this.props.navigation && this.props.navigation.addListener(
      'didFocus', () => {
        console.log("sdcardsetting did focus begin refresh getsetting");
        DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content'); // 白底黑字 测试过的机型都有效：华为荣耀V9，红米Note4X，小米Mix2

      }
    );
    this.setState({isLoading: true});
    this._getSetting();

    let language = Host.locale.language || "en";
    let isNoneChinaLand = language != "zh" && language != "tw" && language != "hk";
    this.setState({ hideSdcardStatus: isNoneChinaLand });
    this.getInfoIntervalID = 0;
    console.log(this.state.sdcardStatus, this.state.sdcardStatus != 1 && this.state.sdcardStatus != 5, 'this.state.sdcardStatus != 1 && this.state.sdcardStatus != 5');

    let toFormateSDCard = this.props.navigation && this.props.navigation.getParam("toFormateSDCard");
    let needShowFormatDialog = this.props.navigation && this.props.navigation.getParam("needShowFormatDialog");
    if (toFormateSDCard == true) {
      this._formatSdCard();
    } else if (needShowFormatDialog) {
      this.setState({ sdcardFormatDialog: true });
    }
    this.getCardRecordDuration();
  }

  getCardRecordDuration() {
    AlarmUtilV2.getSD_SD_CARD_RECORD_DURATION().then((res) => {
      LogUtil.logOnAll("getSD_SD_CARD_RECORD_DURATION res==", JSON.stringify(res));
      let days = res[0].value / 60.0 / 24.0;
      LogUtil.logOnAll("getSD_SD_CARD_RECORD_DURATION days==", days);
      this.sd_card_record_duration = days;
      this.sd_card_record_duration = this.sd_card_record_duration.toFixed(1);
      LogUtil.logOnAll("this.sd_card_record_duration=", this.sd_card_record_duration);
      this.forceUpdate();
    }).catch((err) => {
      LogUtil.logOnAll("getSD_SD_CARD_RECORD_DURATION err==", JSON.stringify(err));
    });
  }
  _renderSDCardFormatDialog() {
    if (!CameraConfig.debugSpecialSupport()) {
      return null;
    }
    let myButtons = [
      {
        text: LocalizedStrings["action_cancle"],
        // style: { color: 'lightpink' },
        callback: (_) => {
          this.setState({ sdcardFormatDialog: false });
        }
      },
      {
        text: LocalizedStrings["action_confirm"],
        // style: { color: 'lightblue' },
        callback: (_) => {
          this.setState({ sdcardFormatDialog: false });
          this._formatSdCard();
        }
      }
    ];
    if (Device.isReadonlyShared) {
      myButtons = [
        {
          text: LocalizedStrings["offline_divice_ok"],
          // style: { color: 'lightpink' },
          callback: (_) => {
            this.setState({ sdcardFormatDialog: false });
          }
        }
      ];
    }
    return (
      <MessageDialog
        visible={this.state.sdcardFormatDialog && [3, 8, 10, 11].includes(this.state.sdcardStatus)}
        title={this.state.sdcardStatus == 3 ? LocalizedStrings["sdcard_status_error"] : LocalizedStrings[`sdcard_format_title_${ this.state.sdcardStatus }`]}
        message={Device.isReadonlyShared ? LocalizedStrings[`sdcard_format_message_readonly_${ this.state.sdcardStatus }`] 
          : LocalizedStrings[`sdcard_format_message_${ this.state.sdcardStatus }`]}
        // canDismiss={false}
        buttons={myButtons}
        onDismiss={() => {
          this.setState({ sdcardFormatDialog: false });
        }}
      />
    );
  }

  componentWillUnmount() {
    if (this.getInfoIntervalID > 0) {
      clearInterval(this.getInfoIntervalID);
      this.getInfoIntervalID = 0;
    }

    this.willFocusSubscription && this.willFocusSubscription.remove();
    this.setState = () => false;
  }

  _getSetting() {
    AlarmUtilV2.getSpecPValue([{sname: SIID_COCKPIT_SERVICE, pname: PIID_COCKPIT_STORAGE_SWITCH}]).then((res) => {
      console.log("++++++++++++++++++",res);
      if (res[0].code === 0) {
        this.setState({ sdRecordSwitch: res[0].value });
      }
    }).catch((err) => {
      console.log("++++++++++++++++++err",err);
    });
    CameraPlayer.getInstance(true).getSdcardStatus(true)
      .then(({ sdcardCode, recordMode, totalSize, videoSize, idleSize }) => {
        if (sdcardCode == '1') {
          TrackUtil.reportResultEvent("Setting_SDCard_Status", "type", 2);
        } else {
          TrackUtil.reportResultEvent("Setting_SDCard_Status", "type", 1);
        }
        TrackUtil.reportResultEvent("Setting_SDCard_Info", "info", idleSize);
        this.setState({
          sdcardStatus: sdcardCode,
          motionRecord: recordMode,
          totalSize: totalSize,
          videoSize: videoSize,
          idleSize: idleSize,
          isLoading: false
        });
      })
      .catch((error) => {
        console.log("==========",error)
        if (error.recordMode || error.sdcardCode) {
          this.setState({ isLoading: false,  motionRecord: error.recordMode, sdcardStatus: error.sdcardCode });
        } else {
          Toast.fail('c_get_fail', error.error);
          this.setState({ isLoading: false });
        }

      });
  }

  async _onEnableValueChange(value) {
    value
      ? TrackUtil.reportResultEvent("Setting_RecordOnOff_Status", "type", 1)
      : TrackUtil.reportResultEvent("Setting_RecordOnOff_Status", "type", 2);
    let currentRecordMode = await StorageKeys.CURRENT_RECORD_MODE;
    Toast.loading('c_setting');

    if (VersionUtil.isUsingSpec(Device.model)) {
      // let newValue;
      // if (currentRecordMode == null || currentRecordMode == "") {
      //   newValue = value ? 0 : 2;
      // } else {
      //   newValue = value ? currentRecordMode : 2;
      // }
      let param = [{ sname: SIID_COCKPIT_SERVICE, pname: PIID_COCKPIT_STORAGE_SWITCH, value: value }];
      // let param = { ...CAMERA_CONTROL_SEPC_PARAMS[2], value: newValue };
      AlarmUtilV2.setSpecPValue(param)
      // Service.spec.setPropertiesValue([param])
        .then((result) => {
          let isOk = result[0].code == 0;
          if (isOk) {
            this.setState({ sdRecordSwitch: value });
            Toast.success('c_set_success');
          } else {
            this.setState({ sdRecordSwitch: this.state.sdRecordSwitch });
            Toast.success('c_set_fail');
          }
        })
        .catch((err) => {
          this.setState({ motionRecord: this.state.motionRecord });
          Toast.success('c_set_fail');
        });
    } else {
      let newValue = null; // 产品提的要求：如果是重新打开开关，之前没有修改过录制模式，就默认全量录制；如果用户修改录制模式，重新打开就用之前设置过的状态。
      if (currentRecordMode == null || currentRecordMode == "") {
        // 之前没有修改过 
        newValue = value ? 0 : 2;
      } else {
        newValue = value ? currentRecordMode : 2;// 读取之前存储的值
      }

      let serverValue = newValue == 0 ? "off" : (newValue == 1 ? "on" : "stop");

      RPC.callMethod("set_motion_record", [serverValue])
        .then((res) => {
          this.setState({
            motionRecord: res.result == 1 ? newValue : this.state.motionRecord
          });
          Toast.success('c_set_success');
        })
        .catch((err) => {
          this.setState({
            motionRecord: this.state.motionRecord
          });
        });
    }

  }


  _onModeValueWillChange(value) {
    console.log(value, 'value');
    value == 1 ?
      TrackUtil.reportResultEvent("Setting_RecordMode_Status", "type", 2)
      :
      TrackUtil.reportResultEvent("Setting_RecordMode_Status", "type", 1);

    this.setState({
      showSelectMode: false
    });

    let serverValue = value == 1 ? "on" : "off";

    Toast.loading('c_setting');
    if (VersionUtil.isUsingSpec(Device.model)) {
      // let param = { ...CAMERA_CONTROL_SEPC_PARAMS[2], value: value };
      // Service.spec.setPropertiesValue([param])
      let param = [{ sname: SIID_CAMERA_CONTROL, pname: CAMERA_CONTROL_RECORD_MODE_PIID, value: value }];
      AlarmUtilV2.setSpecPValue(param)
        .then((result) => {
          let success = result[0].code == 0;
          if (success) {
            this.setState({
              motionRecord: value
            });
            Toast.success('c_set_success');
            StorageKeys.CURRENT_RECORD_MODE = value;// “自定义”录制模式了，要记住
          } else {
            this.setState({
              motionRecord: this.state.motionRecord
            });
            Toast.fail('c_set_fail');
          }
        })
        .catch((err) => {
          this.setState({
            motionRecord: this.state.motionRecord
          });
          Toast.fail('c_set_fail');
        });
    } else {
      let valueStr = "off";
      if (value == 0) {
        valueStr = "off";
      } else if (value == 1) {
        valueStr = "on";
      }
      RPC.callMethod('set_motion_record', [
        valueStr
      ]).then((res) => {
        this.setState({
          motionRecord: res.result == 1 ? value : this.state.motionRecord
        });
        Toast.success('c_set_success');
        StorageKeys.CURRENT_RECORD_MODE = value;// “自定义”录制模式了，要记住

      }).catch((err) => {
        this.setState({
          motionRecord: this.state.motionRecord
        });
        Toast.fail('c_set_fail', err);
      });
    }

  }

  _formatSdCard() {
    console.log("why!, _formatSdCard");

    this.setState({
      showFormatConfirm: false
    });
    this.sdcardNoExitCodeTimes = 0;
    this.formatingSdcard = true;
    this.cancelableProgressDialog && this.cancelableProgressDialog.show();
    this.props.navigation && this.props.navigation.setParams({ enableGestures: false });
    this._formatSdCardTimes = 0;
    if (VersionUtil.isUsingSpec(Device.model)) {
      AlarmUtilV2.doSpecAction({ sname: CAMERA_SDCARD_SIID, aname: CAMERA_SDCARD_FORMAT_ACTION })
        .then((result) => {
          let isSuccess = result.code == 0;
          if (isSuccess) {
            setTimeout(() => {
              this._getFormatStatus();
            }, 300);
            clearInterval(this.getInfoIntervalID);

            this.getInfoIntervalID = setInterval(() => {
              this._formatSdCardTimes++;
              this._getFormatStatus();
            }, 5000);
          } else {
            this._formatSdCardTimes = 0;
            LogUtil.logOnAll("sds_format_fail reason: 2222222 result=", JSON.stringify(result));
            Toast.fail('sds_format_fail');
            this.cancelableProgressDialog && this.cancelableProgressDialog.hide();
            this.formatingSdcard = false;
            this.props.navigation && this.props.navigation.setParams({ enableGestures: true });
          }
        })
        .catch((err) => {
          LogUtil.logOnAll("sds_format_fail reason: 3333333 err=", JSON.stringify(err));
          Toast.fail('sds_format_fail', err);
          this.cancelableProgressDialog && this.cancelableProgressDialog.hide();
          this.formatingSdcard = false;
          this.props.navigation && this.props.navigation.setParams({ enableGestures: true });
        });

    } else {
      RPC.callMethod('sd_format', []).then((res) => {

        setTimeout(() => {
          this._getFormatStatus();
        }, 300);
        clearInterval(this.getInfoIntervalID);

        this.getInfoIntervalID = setInterval(() => {
          this._getFormatStatus();
        }, 2000);

      }).catch((err) => {
        LogUtil.logOnAll("sds_format_fail reason: 4444444 err=", JSON.stringify(err));
        Toast.fail('sds_format_fail', err);
        this.cancelableProgressDialog && this.cancelableProgressDialog.hide();
        this.formatingSdcard = false;
        this.props.navigation && this.props.navigation.setParams({ enableGestures: true });
      });
    }
  }
  _getFormatStatus() {

    CameraPlayer.getInstance().getSdcardStatus(true)
      .then(({ recordMode, sdcardCode, totalSize, videoSize, idleSize }) => {
        LogUtil.logOnAll("SdcardSetting", "recordMode:", recordMode, "sdcardCode", sdcardCode, "totalSize", Util._formatSize(totalSize), "videoSize", Util._formatSize(videoSize), "idleSize", Util._formatSize(idleSize));
        if (sdcardCode == 0) {
          this.setState({
            sdcardStatus: 0,
            motionRecord: recordMode,
            totalSize: totalSize == 0 ? this.state.totalSize : totalSize,
            videoSize: videoSize == 0 ? this.state.videoSize : videoSize,
            idleSize: idleSize == 0 ? this.state.idleSize : idleSize
          });
          clearInterval(this.getInfoIntervalID);
          this.getInfoIntervalID = 0;
          Toast.success('sds_format_success');
          this.cancelableProgressDialog && this.cancelableProgressDialog.hide();
          this.sdcardNoExitCodeTimes = 0;
          this.formatingSdcard = false;
          this.props.navigation && this.props.navigation.setParams({ enableGestures: true });
          SdFileManager.getInstance().clearSdcardFileList();
          // 获取循环录制天数
          this.getCardRecordDuration();
        } else if (sdcardCode == 9) {
          // 格式化后卡
          this.setState({
            sdcardStatus: 9,
            motionRecord: recordMode
          });
          clearInterval(this.getInfoIntervalID);
          this.getInfoIntervalID = 0;
          Toast.success('sds_format_success');
          this.cancelableProgressDialog && this.cancelableProgressDialog.hide();
          this.sdcardNoExitCodeTimes = 0;
          this.formatingSdcard = false;
          this.props.navigation && this.props.navigation.setParams({ enableGestures: true });
          SdFileManager.getInstance().clearSdcardFileList();
          this.getCardRecordDuration();
        } else if (sdcardCode == 4) {
          this.setState({
            sdcardStatus: 4,
            motionRecord: recordMode
          });
          this.sdcardNoExitCodeTimes = 0;
        } else if (sdcardCode == 1 && this.sdcardNoExitCodeTimes <= 10) {//部分平台 格式化后，会有一会的状态1 需要做一下兼容,连续10次都是1才认为格式化失败了。
          this.sdcardNoExitCodeTimes++;
        } else {
          if (!CameraConfig.supportSDCardV2(Device.model) || this._formatSdCardTimes > 36) {
            this.sdcardNoExitCodeTimes = 0;
            LogUtil.logOnAll("sds_format_fail reason: 5555555");
            Toast.fail('sds_format_fail');
            this.cancelableProgressDialog && this.cancelableProgressDialog.hide();
            clearInterval(this.getInfoIntervalID);
            this.getInfoIntervalID = 0;
            this.setState({
              sdcardStatus: sdcardCode,
            });
            this.formatingSdcard = false;
            this.props.navigation && this.props.navigation.setParams({ enableGestures: true });
          }
        }
      })
      .catch((err) => {
        if (!CameraConfig.supportSDCardV2(Device.model) || this._formatSdCardTimes > 36) {
          LogUtil.logOnAll("sds_format_fail reason: 1111111 err=", JSON.stringify(err));
          Toast.fail('sds_format_fail');
          this.cancelableProgressDialog && this.cancelableProgressDialog.hide();
          clearInterval(this.getInfoIntervalID);
          this.getInfoIntervalID = 0;
          this.formatingSdcard = false;
          this.props.navigation && this.props.navigation.setParams({ enableGestures: true });
        }
      });

  }

  _getFormatRecordMode(recordModeStr) {
    if (recordModeStr === "stop") {
      return 2;
    } else if (recordModeStr === "on") {
      return 1;
    } else {
      return 0;
    }
  }

  _exitSdCard() {
    this.setState({ showExitConfirm: false });
    if (VersionUtil.isUsingSpec(Device.model)) {
      AlarmUtilV2.doSpecAction({ sname: CAMERA_SDCARD_SIID, aname: CAMERA_SDCARD_EJECT_ACTION })
        .then((result) => {
          if (result.code == 0) {
            Toast.success('sds_exit_success');
          } else {
            Toast.fail('sds_exit_fail');
          }
        })
        .catch((err) => {
          Toast.fail('sds_exit_fail', err);
        });
    } else {
      RPC.callMethod('sd_umount', []).then(() => {
        Toast.success('sds_exit_success');
      }).catch((err) => {
        Toast.fail('sds_exit_fail', err);
      });
    }
    this.setState({ sdcardStatus: 1 });
  }
}

const storageStyles = StyleSheet.create({
  stateContainer: {
    width: '100%',
    backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : '#fff',
    alignItems: 'center'
  },
  stageBack: {
    width: 217,
    height: 217,
    display: "flex",
    alignItems: "center",
    marginTop: 30
  },
  stateImage: {
    width: '100%',
    height: '100%'
  },
  stateCover: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    display: "flex",
    alignItems: 'center', 
    justifyContent: 'space-evenly',
    bottom: 13
  },
  stateCoverTitle: {
    fontSize: Util.isLanguageCN() ? 22 : 20,
    color: 'white',
    textAlign: "center",
    textAlignVertical: "center"
  },
  stateCoverDesc: {
    position: "absolute",
    bottom: 60,
    fontSize: Util.isLanguageCN() ? 12 : 10,
    color: 'white',
    textAlign: "center"
  },
  stateCoverDescV2: {
    position: "absolute",
    bottom: 45,
    fontSize: Util.isLanguageCN() ? 20 : 18,
    color: 'white',
    width: '90%',
    textAlign: "center"
  },
  stateCoverSeprate: {
    position: "absolute",
    top: 124,
    backgroundColor: '#e5e5e5',
    height: 1,
    width: '80%'
  },
  stateCoverDetail: {
    position: "absolute",
    bottom: 33,
    fontSize: Util.isLanguageCN() ? 15 : 13,
    color: 'white'
  },
  totalText: {
    textAlign:'center',
    marginTop: 20,
    marginBottom: 30,
    fontSize: Util.isLanguageCN() ? 16 : 14,
    color: 'rgba(0,0,0,0.6)'
  },
  tipsBack: {
    paddingTop: 3,
    paddingLeft: 24,
    paddingRight: 24
  },
  tips: {
    marginTop: 5,
    fontSize: Util.isLanguageCN() ? 14 : 12,
    color: 'rgba(0,0,0,0.5)',
    marginLeft: 9,
    lineHeight: 27

  },
  noTopBack: {
    backgroundColor: 'white',
    width: '100%',
    alignItems: 'center'
  },
  noTopImage: {
    resizeMode: 'center',
    marginTop: 25,
    width: 216
  },
  noDetailBack: {
    paddingTop: 3,
    paddingLeft: 24,
    paddingRight: 24
  },
  noGridContainer: {
    borderWidth: 1.5,
    borderColor: 'rgba(0,0,0,0.1)'
  },
  noGrid: {
    width: '33.33333333%',
    height: 40,
    borderWidth: 0.3,
    borderColor: 'rgba(0,0,0,0.1)',
    alignItems: 'center',
    justifyContent: 'center'
  },
  noText: {
    marginTop: 5,
    fontSize: Util.isLanguageCN() ? 14 : 12,
    color: 'rgba(0,0,0,0.5)',
    textAlign: "center"
  },
  noTextLeft: {
    marginTop: 5,
    fontSize: Util.isLanguageCN() ? 14 : 12,
    color: 'rgba(0,0,0,0.5)'
  }
});

const styles = { ...settingStyles, ...storageStyles };
