import React from 'react';
import { Text, View, Image, TouchableOpacity, Animated } from 'react-native';
import PropTypes from 'prop-types';
import { ConfigContext } from "mhui-rn/dist/components/configProvider";
import { preventRepeatClick } from '../../common/utils/click-util'; 
import { styles, Font } from '../../common/styles/Styles';

const MEDIUM_LARGE_PADDINGTOP = 28;
const DEFAULT_IC_CLOSE_SIZE = 48;
const DEFAULT_SWITCH_BUTTON_SIZE = 80;
const SMALL_HEADER_HEIGHT = 168;
const MEDIUM_LARGE_HEADER_HEIGHT = 148;
const TITLE_MAX_WIDTH = 390;
const SUBTITLE_MAX_WIDTH = 319;

/**
 * @export public
 * @module HeaderComponent
 * @description HeaderComponent for CarIoT
 * @property {string} title - 标题（设备名称）
 * @property {string} subTitle - 副标题
 * @property {bool} hideSwitch - 是否隐藏开关
 * @property {bool} switchValue - 开关状态
 * @property {bool} disableSwitch - 开关使能
 * @property {function} onSwitchChange - 切换开关的回调函数
 * @property {string} type - 类型{"small", "medium", "large"}
 * @property {function} onClosePress - 点击关闭回调
 * @property {object} style - style
 */
class HeaderComponent extends React.PureComponent {
    static contextType = ConfigContext;
    static propTypes = {
      title: PropTypes.string,
      subTitle: PropTypes.string,
      hideSwitch: PropTypes.bool,
      switchValue: PropTypes.bool,
      disableSwitch: PropTypes.bool,
      onSwitchChange: PropTypes.func.isRequired,
      type: PropTypes.string,
      onClosePress: PropTypes.func.isRequired,
      style: PropTypes.object
    };

    constructor(props) {
      super(props);
      
      this.state = {
        switchValue: this.props.switchValue,
        scale: new Animated.Value(0)
      };

    }

    scale = () => {
      this.scaleAnimation && this.scaleAnimation.stop();
      this.state.scale.setValue(0);
      this.scaleAnimation = Animated.timing(this.state.scale, {
        toValue: 1,
        duration: 200
      });
      this.scaleAnimation.start();
    }

    componentDidMount() {
    
    }

    UNSAFE_componentWillReceiveProps(newProps) {
      if (newProps.switchValue !== this.state.switchValue) {
        this.setState({
          switchValue: newProps.switchValue
        });
      }
    }

    render() {
      const {
        colorScheme
      } = this.context;
      let scale = this.state.scale.interpolate({
        inputRange: [0, 0.5, 1],
        outputRange: [1, 0.5, 1]
      });
      const height = (this.props.type && this.props.type == "small") ? SMALL_HEADER_HEIGHT : MEDIUM_LARGE_HEADER_HEIGHT;
      const paddingTop = (this.props.type && this.props.type == "small") ? 0 : MEDIUM_LARGE_PADDINGTOP;
      return (
        <View style={[{
          flexDirection: 'row',
          alignItems: 'center',
          height,
          paddingTop: paddingTop
        }, this.props.style]}>
          <TouchableOpacity
            onPress={this.props.onClosePress}
          >
            <Image
              style={{ width: DEFAULT_IC_CLOSE_SIZE, height: DEFAULT_IC_CLOSE_SIZE }}
              source={colorScheme == 'dark' ? require("../../resources/images/ic_nav_close.png") : require("../../resources/images/ic_nav_close_light.png")} />
          </TouchableOpacity>
          <View 
            style={{ 
              flexDirection: 'row', 
              alignItems: 'center',
              flex: 1,
              justifyContent: 'center'
            }}
          >
            <Text numberOfLines={1} style={[styles.titleTextStyle, { fontSize: Font.Size._36, maxWidth: TITLE_MAX_WIDTH }]} >{this.props.title}</Text>
            {this.props.subTitle ? <Text numberOfLines={1} style={[styles.subTitleTextStyle, { fontSize: Font.Size._36, maxWidth: SUBTITLE_MAX_WIDTH }]} >{` | ${ this.props.subTitle }`}</Text> : null }
          </View>
          {this.props.hideSwitch ? null : <View pointerEvents={this.props.disableSwitch ? "none" : "auto"}>
            <TouchableOpacity 
              style={{ alignItems: 'center', justifyContent: 'center' }}
              onPress={() => { 
                preventRepeatClick('switchChange', () => {
                  this._onSwitchChange();
                });
              }}
              onPressIn={() => {
                this.scale();
              }}>
              <Animated.Image
                style={{ width: DEFAULT_SWITCH_BUTTON_SIZE, height: DEFAULT_SWITCH_BUTTON_SIZE, transform: [{ scale: scale }] }}
                resizeMode={'contain'}
                source={this.props.disableSwitch ? (this.state.switchValue ? (colorScheme == 'dark' ? require('../../resources/images/switch_on_disabled.png') : require('../../resources/images/switch_on_disabled_light.png')) : (colorScheme == 'dark' ? require('../../resources/images/switch_off_disabled.png') : require('../../resources/images/switch_off_disabled_light.png'))) :
                  (this.state.switchValue ? (colorScheme == 'dark' ? require('../../resources/images/switch_on.png') : require('../../resources/images/switch_on_light.png')) : (colorScheme == 'dark' ? require('../../resources/images/switch_off.png') : require('../../resources/images/switch_off_light.png')))} />
            </TouchableOpacity>
          </View>}
        </View>
      );
    }

    _onSwitchChange() {
      this.setState({
        switchValue: !this.state.switchValue
      });
      
      if (typeof this.props.onSwitchChange === 'function') {
        this.props.onSwitchChange(!this.state.switchValue);
      }
    }
}

export default HeaderComponent;
export { HeaderComponent };