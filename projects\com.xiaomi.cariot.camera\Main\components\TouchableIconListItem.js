import React from "react";
import { View, Text, TouchableOpacity, Image } from "react-native";
import { Styles } from "miot/resources";

export default class TouchableIconItemListItem extends React.Component {
  constructor(props) {
    super(props);

    this.state = {};
  }

  render() {
    return (
      <View>
        <View style={{ backgroundColor: "#ffffff", flexDirection: "row", justifyContent: "space-between", paddingLeft: Styles.common.padding }}>
          <View
            style={{
              height: 50,
              justifyContent: "center"
            }}>
            <Text
              style={{
                textAlign: "left",
                textAlignVertical: "center",
                color: "#000000",
                fontSize: 15
              }}
            >
              {this.props.title}
            </Text>
            <Text
              style={{
                textAlign: "left",
                textAlignVertical: "center",
                color: "rgba(0,0,0,0.6)",
                fontSize: 12,
                marginTop: 2
              }}
            >
              {this.props.subtitle}
            </Text>
          </View>
          <View style={{ flexDirection: "row", alignItems: "center", paddingRight: Styles.common.padding }}>
            <Text
              style={{
                textAlign: "right",
                textAlignVertical: "center",
                color: this.props.detailColor,
                fontSize: 12,
                marginTop: 2
              }}
            >
              {this.props.detail}
            </Text>
            <TouchableOpacity
              onPress={this.props.onIconPress}
              style={{
                alignSelf: "center",
                width: 30,
                height: 50,
                paddingLeft: 6,
                paddingVertical: 15

              }}
            >
              <Image
                source={this.props.iconSource}
                style={{ width: 20, height: 20, alignSelf: "center" }}
              />
            </TouchableOpacity>
          </View>
        </View>
        {
        // <Separator style={{marginLeft: Styles.common.padding}}/>
        }
      </View>
    );
  }
}
