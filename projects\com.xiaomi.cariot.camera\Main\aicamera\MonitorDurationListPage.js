import React from 'react';
import { View, BackHandler, FlatList, Platform, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';

import { Host, Device, Service } from "miot";
import { NavigationBar } from 'mhui-rn';
import { LoadingDialog, MessageDialog } from 'miot/ui/Dialog';
import Switch from 'miot/ui/Switch';
import Checkbox from 'miot/ui/Checkbox';
import Util from "../util2/Util";
import { IMG_DARKMODE_TINT } from "../util/CameraConfig";
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlarmUtil from "../util/AlarmUtil";
import Toast from "../components/Toast";
import SmartMonitorSetting from './SmartMonitorSetting';
import SpecUtil from '../util/SpecUtil';

const LONG_TIME_KEY_PREFIX = "prop.s_chuangmi_clocks"; // 存放长时间无人出现自定义push内容的key值前缀
const DURATION_MAX_LIMIT = 9; // 设置时间段个数上限
export default class MonitorDurationListPage extends React.Component {
    static navigationOptions = (navigation) => {
      return {
        headerTransparent: true,
        header: null
      };
    };

    constructor(props, context) {
      super(props, context);
      this.state = {
        isLoading: false,
        listDataSource: [], // 展示列表
        showDelDialog: false,
        editMode: false,
        progressing: false
      };
      this.keys = []; // 每个时间段的索引数组，添加、删除时要更新
      this.selectCount = 0; // 编辑模式下勾选的个数
      this.needUpdatePushInfo = false; // TODO 可以细化到只当修改或删除时间段时才去更新长时间无人推送的push内容

      this.sceneDataArray = this.props.navigation.getParam('sceneDataArray'); // 三个看护场景的数据(设置Spec时用到)
      this.currentSceneIndex = this.props.navigation.getParam('sceneSelectedIndex'); // 当前显示和操作的场景索引(设置Spec时用到)
      this.sceneData = this.sceneDataArray[this.currentSceneIndex];
      this.sceneDataCopy = JSON.parse(JSON.stringify(this.sceneData));
      this.monitorSettingPageCallback = this.props.navigation.getParam('callback');

    }

    componentDidMount() {
      if (Platform.OS === "android") {
        BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
      }
      this.loadData();
    }

    loadData() {
      this.setState({
        isLoading: true
      });

      let i = 0;
      this.sceneData.clock.forEach((item) => { // 添加select标记在编辑模式中是否被选中
        item.select = false;
        // 老数据如果没有clock_idx,则加入
        if (item.clock_idx == undefined) {
          item.clock_idx = i;
          this.keys.push(i);
        } else {
          this.keys.push(item.clock_idx);
        }
        i++;
      });
      this.keys.sort((a1, a2) => {
        return a1 - a2;
      });
      console.log("current scene's all duration data----->", this.sceneData);
      console.log("current scene duration's clock_index array---->", this.keys);
      this.setState({ listDataSource: this.sceneData.clock, isLoading: false });

      // ////////////////////测试完毕后可以删除////////////////////////////////
      let propsArray = [];
      for (let i = 0; i <= DURATION_MAX_LIMIT; i++) {
        propsArray.push(`${ LONG_TIME_KEY_PREFIX }${ i }`);
      }
      AlarmUtil.batchGetDatas([{ did: Device.deviceID, props: propsArray }]).then((res) => {
        console.log(`===========================${ JSON.stringify(res) }`); // res[Device.deviceID]
      }).catch((err) => {
        console.log(err);
      });
      // ////////////////////测试完毕后可以删除////////////////////////////////
    }


    render() {
      // TODO listDataSource为空，需要给个空View的提示或icon吧。或者不给删完，全选并删除的时候给提醒拦截？
      let isDark = Util.isDark();
      return (<View style={{
        display: "flex",
        height: "100%",
        width: "100%",
        flex: 1,
        flexDirection: "column",
        backgroundColor: isDark ? "#000000" : "#FFFFFF",
        alignItems: "center"
      }}>
        {this._renderTitleBar()}

        <FlatList
          style={{ width: "100%", marginBottom: this.state.editMode ? 0 : 80 }}
          data={this.state.listDataSource}
          renderItem={(data) => this._renderItemView(data.item, data.index)}
          contentContainerStyle={[{ flexGrow: 1 }]}
          keyExtractor={(item, index) => index.toString()}
          refreshing={this.state.isLoading}
          onRefresh={() => {
            this._toLoadList();
          }}>
        </FlatList>

        {this._renderBottomDeleteBtn()}
        {this._renderBottomAddBtn()}

        {this._renderDeleteDialog()}

        <LoadingDialog
          visible={this.state.progressing}
          message={LocalizedStrings['c_setting']}
          onModalHide={() => this.setState({ progressing: false })}/>
      </View>);
    }

    _toLoadList() {
      // Spec data---------> [
      // {"did":"1070202728","siid":11,"piid":1,"value":true,"code":0,"updateTime":1649764548,"exe_time":0,"start":1649764547394,"duration":218},
      // {"did":"1070202728","siid":11,"piid":2,"value":"[{\"area\":\"[25, 25],[69, 87]\"}]","code":0,"updateTime":1649764548,"exe_time":0,"start":1649764547394,"duration":218},
      // {"did":"1070202728","siid":11,"piid":3,"value":"{\"mode\":2,\"param\":[{\"idx\":0,\"clock\":[{\"start\":\"08:00\",\"end\":\"20:00\",\"repeat\":127,\"enable\":true,\"clock_idx\":0}]},{\"idx\":1,\"clock\":[{\"start\":\"08:00\",\"end\":\"20:00\",\"repeat\":127,\"enable\":true,\"clock_idx\":0}]},{\"idx\":2,\"clock\":[{\"repeat\":0,\"start\":\"16:28\",\"end\":\"16:30\",\"enable\":true,\"clock_idx\":0}]}]}","code":0,"updateTime":1649764548,"exe_time":0,"start":1649764547394,"duration":218}
      // ]
      // 去刷新列表 param
      AlarmUtil.getSmartCareConfig().then((res) => {
        console.log("getSmartCareConfig=", JSON.stringify(res));
        SmartMonitorSetting.sceneDurationDataArray = JSON.parse(res[0].value).param;
        this.sceneDataArray = JSON.parse(JSON.stringify(SmartMonitorSetting.sceneDurationDataArray));
        this.sceneData = this.sceneDataArray[this.currentSceneIndex];
        this.setState({ listDataSource: this.sceneData.clock, isLoading: false });
      }).catch((err) => {
        console.log("getSmartCareConfig=", JSON.stringify(err));
      });
    }

    _renderTitleBar() {
      let titleBarContent = {
        title: LocalizedStrings.setting_monitor_duration,
        type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
        left: [
          {
            key: NavigationBar.ICON.BACK,
            onPress: () => {
              if (this.state.editMode) {
                this.state.listDataSource.forEach((item) => item.select = false);
                this.setState({ editMode: false });
              } else {
                // clock的子元素中多了一个select键，需要去除
                // this.sceneData.clock.forEach((item) => {
                //   delete item.select;
                // });

                // if (JSON.stringify(this.sceneDataCopy) != JSON.stringify(this.sceneData)) { // 数据改变，更新！
                //   this._updateSceneDurationData();
                // } else {
                //   this.props.navigation.goBack();
                // }
                this.props.navigation.goBack();

              }
            }
          }
        ],
        right: [
          {
            key: this.state.editMode ? NavigationBar.ICON.SELECT_ALL : null,
            onPress: () => { // TODO 全选和全不选是否需要切换图标？
              if (this.selectCount >= this.state.listDataSource.length) {
                this.state.listDataSource.forEach((item) => item.select = false);
                this.selectCount = 0;
              } else {
                this.state.listDataSource.forEach((item) => item.select = true);
                this.selectCount = this.state.listDataSource.length;
              }
              this.setState({ editMode: true });
            }
          }
        ],
        titleStyle: {
          fontSize: 18,
          color: '#333333',
          fontWeight: 500
        }
      };
      return (
        <NavigationBar {...titleBarContent} />
      );
    }

    _renderItemView(item, index) {
      // {"enable": 0, "end": "23:59", "repeat": 127, "select": false, "start": "00:00"}
      return (
        <View style={{ display: "flex", flexDirection: "column" }}>
          <View style={{ display: "flex", flexDirection: "row", alignItems: "center", paddingRight: 20 }}>
            <TouchableOpacity
              style={{ display: "flex", flexDirection: "column", flexGrow: 2, padding: 20, width: "80%" }}
              onLongPress={() => this.onItemLongPress(item)}
              onPress={() => {
                if (this.state.editMode) {
                  item.select = !item.select;
                  item.select ? this.selectCount++ : this.selectCount--;
                  this.forceUpdate();
                } else {
                  // 跳转到编辑页面
                  this.props.navigation.navigate('MonitorDurationSetting',
                    {
                      durationData: Object.assign({}, item),
                      durationDataArray: [...this.state.listDataSource],
                      callback: (data) => {
                        this.sceneData.clock[index] = data;
                        this.setState({ listDataSource: this.sceneData.clock });
                        this.needUpdatePushInfo = this.currentSceneIndex == 2;
                        this._updateSceneDurationData();
                      }
                    });
                }
                // this.forceUpdate();
              }}>
              <Text>{this.getDurationText(item.start, item.end)}</Text>
              <Text>{Util.getRepeatString(item.repeat)}</Text>
            </TouchableOpacity>
            {this.state.editMode ? <Checkbox
              style={{ width: 20, height: 20, borderRadius: 20 }}
              checked={item.select}
              onValueChange={(checked) => {
                item.select = checked;
                item.select ? this.selectCount++ : this.selectCount--;
              }}
            /> : <Switch
              value={item.enable}
              disabled={false}
              onValueChange={(checked) => {
                item.enable = checked;
                this.onItemCheckChanged();
              }}
            />}
          </View>
        </View>
      );
    }

    // 选择按钮变化
    onItemCheckChanged() {
      this.sceneData.clock = this.state.listDataSource; // 打开或关闭时间段按钮需要更新场景数据
      this._updateSceneDurationData();
    }

    // 长按item呼出编辑模式
    onItemLongPress(item) {
      item.select = true;
      this.selectCount = 1;
      this.setState({ editMode: true });
    }

    // 显示时间段
    getDurationText(start, end) {
      let text = `${ start } - ${ end }`;
      let startValue = parseInt(start.split(":")[0]) * 60 + parseInt(start.split(":")[1]);
      let endValue = parseInt(end.split(":")[0]) * 60 + parseInt(end.split(":")[1]);
      if (startValue > endValue) {
        text = `${ start } - ${ LocalizedStrings.setting_monitor_next_day }${ end }`;
      }
      return text;
    }

    // 底部删除按钮
    _renderBottomDeleteBtn() {
      if (!this.state.editMode) return null;
      return (<TouchableOpacity
        style={{ display: "flex", flexDirection: "column", alignItems: "center", width: "100%" }}
        onPress={() => {
          if (this.selectCount <= 0) {
            Toast._showToast(LocalizedStrings.select_one_delete_item);
            return;
          }
          this.setState({ showDelDialog: true });
        }}>
        <View style={{ backgroundColor: "#FFFFFF", width: "100%" }}>
          <View style={{
            height: 0,
            borderTopWidth: StyleSheet.hairlineWidth,
            borderColor: '#bdbdbd',
            opacity: 0.7,
            margin: StyleSheet.hairlineWidth
          }}/>
        </View>
        <Image
          style={{ marginTop: 10, width: 35, height: 35, tintColor: Util.isDark() ? IMG_DARKMODE_TINT : null }}
          source={require("../../Resources/Images/icon_delete_normal.png")}
        />
        <Text style={{
          color: "#000000",
          fontSize: 11,
          marginBottom: Host.isIphoneXSeries ? 25 : 10
        }}>{LocalizedStrings["delete_files"]}</Text>
      </TouchableOpacity>
      );
    }

    // 底部右下角添加按钮
    _renderBottomAddBtn() {
      if (this.state.editMode) return null;
      return (<TouchableOpacity
        style={{ position: 'absolute', right: 30, bottom: 30 }}
        onPress={() => this.onAddItem()}>
        <Image style={{ width: 50, height: 50 }} source={require("../../Resources/Images/mj_widget_add_nor.png")}/>
      </TouchableOpacity>);
    }

    // 跳转去添加时间段
    onAddItem() {
      if (this.state.listDataSource.length >= DURATION_MAX_LIMIT) { // TODO 达到设置上限9时的提示文案？
        Toast.show(LocalizedStrings.max_add_item_tips);
        return;
      }
      this.props.navigation.navigate('MonitorDurationSetting', {
        durationDataArray: [...this.state.listDataSource],
        callback: (data) => {
          // 先整出新数据的index
          let key = 0;
          this.sceneData.clock.forEach((item, index) => {
            if (key == this.keys[index]) {
              key++;
            }
          });
          data.clock_idx = key;
          this.sceneData.clock.push(data);
          this.setState({ listDataSource: this.sceneData.clock });

          // 添加新的index到keys并整理好
          this.keys.push(key);
          this.keys.sort((a1, a2) => {
            return a1 - a2;
          });
          this.needUpdatePushInfo = this.currentSceneIndex == 2;
          this._updateSceneDurationData();
        }
      });
    }

    // 删除时间段确认对话框  //TODO 文案需要更换
    _renderDeleteDialog() {
      return (
        <MessageDialog
          visible={this.state.showDelDialog}
          title={LocalizedStrings['plug_timer_del']}
          message={LocalizedStrings['plug_timer_del_hint']}
          canDismiss={true}
          buttons={[
            {
              text: LocalizedStrings["btn_cancel"],
              callback: (_) => {
                this.setState({ showDelDialog: false });
              }
            },
            {
              text: LocalizedStrings["delete_files"],
              callback: () => {
                this.onClickDeleteButton();
                this.setState({ showDelDialog: false });
              }
            }
          ]}
          onDismiss={() => {
          }}
        />
      );
    }

    // 点击底部删除按钮
    onClickDeleteButton() {
      // TODO 全选时是否需要给个提示? 防止删除生效时段删除完了，选此场景无法生效
      if (this.selectCount == 0) {
        // TODO 未勾选点删除给与什么提示呢？或者提供禁用态icon
        return;
      }

      if (this.state.listDataSource.length <= 1 || this.state.listDataSource.length == this.selectCount) {
        this.state.listDataSource.forEach((item) => item.select = false);
        this.setState({ editMode: false });
        Toast._showToast(LocalizedStrings["one_at_last"], false);
        return;
      }

      this.setState({
        // 删除勾选的时间段 和 勾选时间段的clock_idx在keys中对应的元素
        listDataSource: this.state.listDataSource.filter((vo, index) => {
          if (vo.select && vo.clock_idx != undefined) {
            this.keys.splice(index, 1);
          }
          return !vo.select;
        })
      }, () => {
        this.sceneData.clock = this.state.listDataSource;
        // if (this.state.listDataSource == 0) { // 删光时，直接退出编辑模式
        //   this.setState({ editMode: false });
        // }
        this.needUpdatePushInfo = this.currentSceneIndex == 2;
        this._updateSceneDurationData();
      });
    }


    componentWillUnmount() {
      if (Platform.OS === "android") {
        BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
      }
    }

    onBackHandler = () => {
      if (this.state.editMode) {
        this.state.listDataSource.forEach((item) => item.select = false);
        this.setState({ editMode: false });
        this.selectCount = 0;
        return true;
      } else {
        // clock的子元素中多了一个select键，需要去除
        // this.sceneData.clock.forEach((item) => {
        //   delete item.select;
        // });
        // if (JSON.stringify(this.sceneDataCopy) != JSON.stringify(this.sceneData)) { // 数据改变，更新！
        //   this._updateSceneDurationData();
        //   return true;
        // } else {
        //   return false;
        // }
        return false;

      }
    }

    // 更新智能场景的时间设置数据
    _updateSceneDurationData() {
      this.sceneData.clock.forEach((item) => {
        delete item.select;
      });
      this.setState({ progressing: true });
      let specValue = { mode: this.currentSceneIndex };
      specValue.param = this.sceneDataArray;
      let params = [{ did: Device.deviceID, siid: 11, piid: 3, value: JSON.stringify(specValue) }];
      console.log("send Spec command parameters", JSON.stringify(params));
      Service.spec.setPropertiesValue(params).then((vo) => {
        this.setState({ progressing: false, editMode: false });
        if (vo[0].code == 0) {
          if (this.needUpdatePushInfo) { // 长时间无人出现场景模式需要更新推送的内容
            this._updateDiyPushContent();
          }
          // this.monitorSettingPageCallback(this.sceneData);
          SmartMonitorSetting.sceneDurationDataArray[this.currentSceneIndex] = this.sceneData;
          SmartMonitorSetting.sceneSelectedIndexCopy = this.currentSceneIndex; // 更改mode值，防止从列表页面退出后直接退出再更新一次Spec
          // this.props.navigation.goBack();
        } else {
          Toast.fail("action_failed");
        }
      }).catch((err) => {
        this.setState({ progressing: false });
        Toast.fail("action_failed");
      });
    }

    // 长时间无人出现模式下，需要更新自定义的push内容到云端---->【开始时间-结束时间】长时间无人出现
    _updateDiyPushContent() {
      let cloudPushInfo = {};
      this.sceneData.clock.forEach((item) => {
        if (item.clock_idx != undefined) {
          cloudPushInfo[`${ LONG_TIME_KEY_PREFIX }${ item.clock_idx }`] = `【${ this.getDurationText(item.start, item.end) }】${ LocalizedStrings.setting_monitor_nobody_move_title }`;
        }
      });

      if (JSON.stringify(cloudPushInfo) == "{}") {
        return;
      }
      console.log("update all push content to cloud---->", JSON.stringify(cloudPushInfo));
      let data = { did: Device.deviceID, props: cloudPushInfo };
      AlarmUtil.setProps(data).then((res) => {
        console.log("update all push content SUCCESS", JSON.stringify(res));
      }).catch((err) => {
        console.log("update all push content FAILED", JSON.stringify(err));
      });
    }

}

