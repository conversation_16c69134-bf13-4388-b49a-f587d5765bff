import React from 'react';
import {
  View,
  ScrollView,
  Text,
  TouchableOpacity,
  Image, TouchableWithoutFeedback, NativeModules
} from 'react-native';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import {
  styles,
  Dividers,
  Constants,
  Radius,
  Font,
  SwitchComponent,
  SegmentControl,
  Slider
} from 'micariot-ui-sdk';
import { CarPluginWindow, carStyles } from './common/Styles';
import { DarkMode, Device, PackageEvent, Service } from "miot";
import ClickableCardButton from "../widget/ClickableCardButton";
import CameraConfig from "../util/CameraConfig";
import CameraPlayer, { MISSCommand_ECO } from "../util/CameraPlayer";
import Util from "../util2/Util";
import Toast from "../components/ToastCar";
import { strings as I18n } from "miot/resources";
import CarCommonDialog from "./widget/CarCommonDialog";
import { DirectionViewConstant } from '../ui/DirectionView';
import LogUtil from "../util/LogUtil";
import SdFileManager from "../sdcard/util/SdFileManager";
import StorageKeys from "../StorageKeys";
import ProjectInfo from "../../project.json";
import {
  CAMERA_CONTROL_CAR_SIID,
  CAMERA_CONTROL_CORRECTION_PIID,
  CAMERA_CONTROL_HDR_PIID,
  CAMERA_CONTROL_TIME_WATERMARK_PIID,
  CAMERA_SPEAKER_SIID,
  CAMERA_SPEAKER_VOLUME_PIID, COCKPIT_SERVICE_CROSS_PLAT_PIID,
  COCKPIT_SERVICE_LIVE_SWITCH_PIID, COCKPIT_SERVICE_RECORD_VOICE_PIID,
  COCKPIT_SERVICE_SIID,
  COCKPIT_SERVICE_STORAGE_SWITCH_PIID, MEMORY_CARD_MANAGEMENT_FORMAT_AIID, MEMORY_CARD_MANAGEMENT_POP_AIID,
  MEMORY_CARD_MANAGEMENT_SIID,
  MOTION_DETECTION_ALARM_INTERVAL_PIID, MOTION_DETECTION_MOTION,
  MOTION_DETECTION_SIID
} from "../util/CarSpecConstant";
import { MISSCommand } from "miot/service/miotcamera";
import native from "miot/native";
import CarComplexDialog from "./widget/CarComplexDialog";

const SWITCH_TYPE = {
  VIDEO_STORAGE: 'VIDEO_STORAGE',
  CLOUD_VIDEO_STORAGE: 'CLOUD_VIDEO_STORAGE',
  REMOTE: 'REMOTE',
  DISTORTION_CORRECTION: 'DISTORTION_CORRECTION',
  HDR: 'HDR',
  WATERMARK: 'WATERMARK',
  RECORD: 'RECORD',
  CROSS_PLAT: 'CROSS_PLAT'
};
const INTERVAL = [3, 5, 10];
const TAG = "CarSetting";
export default class CarSetting extends React.Component {
  static navigationOptions = (navigation) => {
    return {
      headerTransparent: true,
      header: null
    };
  };

  constructor(props, context) {
    super(props, context);
    this.isStartPtzCheck = false;
    this.state = {
      value: 200,
      isVideoSwitchOperateOnce: true,
      videoSaveSwitch: CameraConfig.SETTING_PROP.videoSaveSwitch ? CameraConfig.SETTING_PROP.videoSaveSwitch : false,
      videoCloudSaveSwitch: CameraConfig.SETTING_PROP.videoCloudSaveSwitch ? CameraConfig.SETTING_PROP.videoCloudSaveSwitch : false,
      remoteSwitch: CameraConfig.SETTING_PROP.remoteSwitch ? CameraConfig.SETTING_PROP.remoteSwitch : false,
      distortionCorrectionSwitch: CameraConfig.SETTING_PROP.distortionCorrectionSwitch ? CameraConfig.SETTING_PROP.distortionCorrectionSwitch : false,
      hdrIndex: CameraConfig.SETTING_PROP.hdrIndex ? CameraConfig.SETTING_PROP.hdrIndex : 0,
      waterMarkSwitch: CameraConfig.SETTING_PROP.waterMarkSwitch ? CameraConfig.SETTING_PROP.waterMarkSwitch : false,
      volumeValue: CameraConfig.SETTING_PROP.volumeValue ? CameraConfig.SETTING_PROP.volumeValue : 0,
      recordVoiceSwitch: CameraConfig.SETTING_PROP.recordVoiceSwitch ? CameraConfig.SETTING_PROP.recordVoiceSwitch : false,
      // crossPlatformSwitch: CameraConfig.SETTING_PROP.crossPlatformSwitch ? CameraConfig.SETTING_PROP.crossPlatformSwitch : false,
      showFormatDialog: false,
      showExitDialog: false,
      showLocalDialog: false,
      showCloudDialog: false,
      showCloudInfoDialog: false,
      showRemoteDialog: false,
      showRecordDialog: false,
      showPtzDialog: false,
      showCrossPlatDialog: false,
      isFormatting: CameraPlayer.getInstance().sdcardCode == 4,
      interval: CameraConfig.SETTING_PROP ? CameraConfig.SETTING_PROP.interval : 10,
      intervalIndex: CameraConfig.SETTING_PROP ? INTERVAL.indexOf(CameraConfig.SETTING_PROP.interval) : 2,
      sdcardCode: CameraPlayer.getInstance().sdcardCode,
      isPtzCorrecting: false
    };

    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this._onResume();
      }
    );
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'didBlur',
      () => {
        this.isPageForeGround = false;
        this._onPause();
      }
    );

    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      console.log("CarSetting", "did resume");
      this._onResume();
    });

    this.willPauseListener = PackageEvent.packageWillPause.addListener(() => {
      this._onPause();
    });

  }

  componentDidMount() {
    CameraPlayer.getInstance().bindP2pCommandCallback(this._p2pCommandHandler);// p2p连接
    CameraPlayer.getInstance().bindSDCallback(this._sdCallback);// p2p连接
    CameraPlayer.getInstance().bindHdrStateCallback(this._hdrStateCallback);


    this.getSettings();
    this.checkPtz();
    // 获取本地存储开关是否打开过
    // StorageKeys.CAR_STORAGE = false;
    StorageKeys.CAR_STORAGE.then((res) => {
      console.log("CAR_STORAGE:", res);
      if (typeof (res) === "string" || res == null || res === '') {
        this.setState({ isVideoSwitchOperateOnce: false });
      } else {
        this.setState({ isVideoSwitchOperateOnce: res });
      }
    }).catch((error) => {
      console.log("CAR_STORAGE error:", error);
    });
  }

  _p2pCommandHandler = ({ command, data }) => {
    // 扩展程序注册命令回复回调，command为返回的命令号值，data 为P2P命令的返回数据。
    if (command == MISSCommand_ECO.MISS_CMD_RESET_STATUS) {
      console.log("MISS_CMD_RESET_STATUS", data);
      if (typeof (data) == 'string') {
        data = JSON.parse(data);
      }
      if (data.value == 1) {
        // 校准中
        console.log("is ptz correct", this.ptzCorrectTimeout);
        if (!this.ptzCorrectTimeout) {
          this.ptzCorrectTimeout = setTimeout(() => {
            this.setState({ isPtzCorrecting: false });
          }, 42000);
        }
        this.setState({ isPtzCorrecting: true });
      } else {
        // 空闲
        this.ptzCorrectTimeout && clearTimeout(this.ptzCorrectTimeout);
        this.setState({ isPtzCorrecting: false });
      }
    } else if (command == MISSCommand.MISS_CMD_MOTOR_RESP) {
      // console.log(data); // {"ret":0,"angle":12,"elevation":1}
    } else {
      // console.log(`receive other command:${ command } data:${ JSON.stringify(data) }`);
    }
  };

  _sdCallback = (value) => {
    if (value == 0) {
      // 正常
      if (this.state.sdcardCode != 0) {
        if (this.state.isFormatting && this.state.sdcardCode == 4 && this.getInfoIntervalID) {
          console.log("=========isFormatting=================");
          // 正在格式化
          return;
        }
        // 获取SD卡信息相关
        this.delayToGetSdInfo && clearTimeout(this.delayToGetSdInfo);
        this.delayToGetSdInfo = setTimeout(() => {
          this.getSdcardInfo();
        }, 3000);

        // CameraPlayer.getInstance().sdcardCode = 0;
        // this.setState({ sdcardCode: 0 });

      }
    } else {
      CameraPlayer.getInstance().sdcardCode = value;
      this.setState({ sdcardCode: value });
    }
  }

  _hdrStateCallback = (value) => {
    CameraConfig.SETTING_PROP.hdrIndex = value;
    this.setState({ hdrIndex: value });
  }


  getSettings() {
    let params = [
      { did: Device.deviceID, siid: COCKPIT_SERVICE_SIID, piid: COCKPIT_SERVICE_STORAGE_SWITCH_PIID },
      { did: Device.deviceID, siid: COCKPIT_SERVICE_SIID, piid: COCKPIT_SERVICE_LIVE_SWITCH_PIID },
      { did: Device.deviceID, siid: CAMERA_SPEAKER_SIID, piid: CAMERA_SPEAKER_VOLUME_PIID },
      { did: Device.deviceID, siid: CAMERA_CONTROL_CAR_SIID, piid: CAMERA_CONTROL_CORRECTION_PIID },
      { did: Device.deviceID, siid: CAMERA_CONTROL_CAR_SIID, piid: CAMERA_CONTROL_HDR_PIID },
      { did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: MOTION_DETECTION_ALARM_INTERVAL_PIID },
      { did: Device.deviceID, siid: CAMERA_CONTROL_CAR_SIID, piid: CAMERA_CONTROL_TIME_WATERMARK_PIID }
    ];
    if (CameraConfig.supportVoiceMute()) {
      params.push({ did: Device.deviceID, siid: COCKPIT_SERVICE_SIID, piid: COCKPIT_SERVICE_RECORD_VOICE_PIID });
    }

    if (CameraConfig.supportDivideStorage()) {
      params.push({ did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: MOTION_DETECTION_MOTION });
    }

    // if (CameraConfig.supportCrossPlatView()) {
    //   params.push({ did: Device.deviceID, siid: COCKPIT_SERVICE_SIID, piid: COCKPIT_SERVICE_CROSS_PLAT_PIID });
    // }
    // AlarmUtilV2.getSpecPValue(params).then((res) => {
    Service.spec.getPropertiesValue(params, 2).then((res) => {
      LogUtil.logOnAll(TAG, "getPropertiesValue",res);
      let stateProps = {};
      if (res[0].code === 0) {
        stateProps.videoSaveSwitch = res[0].value;
      }
      if (res[1].code === 0) {
        stateProps.remoteSwitch = res[1].value;
      }
      if (res[2].code === 0) {
        stateProps.volumeValue = res[2].value;
      }
      if (res[3].code === 0) {
        stateProps.distortionCorrectionSwitch = res[3].value;
      }
      if (res[4].code === 0) {
        stateProps.hdrIndex = res[4].value;
      }
      if (res[5].code === 0) {
        stateProps.interval = res[5].value;
        stateProps.intervalIndex = INTERVAL.indexOf(res[5].value);
      }
      if (res[6].code === 0) {
        stateProps.waterMarkSwitch = res[6].value;
      }
      if (CameraConfig.supportCrossPlatView()) {
        if (res[7].code === 0) {
          stateProps.recordVoiceSwitch = res[7].value;
        }

        if (res[8].code === 0) {
          stateProps.videoCloudSaveSwitch = res[8].value;
          if (res[8].value) {
            this.onSwitchValueChange(SWITCH_TYPE.CLOUD_VIDEO_STORAGE, false, true);
          }
        }

        // if (res[9].code === 0) {
        //   stateProps.crossPlatformSwitch = res[9].value;
        // }

      } else if (CameraConfig.supportDivideStorage()) {
        if (res[7].code === 0) {
          stateProps.recordVoiceSwitch = res[7].value;
        }

        if (res[8].code === 0) {
          stateProps.videoCloudSaveSwitch = res[8].value;
          if (res[8].value) {
            this.onSwitchValueChange(SWITCH_TYPE.CLOUD_VIDEO_STORAGE, false, true);
          }
        }

      } else if (CameraConfig.supportVoiceMute() && res[7].code === 0) {
        stateProps.recordVoiceSwitch = res[7].value;
      }
      this.setState(stateProps);
    }).catch((err) => {
      console.log("++++++++++++++++++err", err);
    });

    this.getSdcardInfo();
  }

  getSdcardInfo() {
    CameraPlayer.getInstance().getSdcardStatusDirect(true)
      .then(({ sdcardCode, recordMode, totalSize, videoSize, idleSize }) => {
        this.setState({
          sdcardCode: sdcardCode,
          motionRecord: recordMode,
          totalSize: totalSize,
          videoSize: videoSize,
          idleSize: idleSize,
          isLoading: false,
          isFormatting: sdcardCode == 4
        });

        if (sdcardCode === 4) {
          // 格式化中 需要去轮询SD卡状态，待SD卡正常后
          this._formatSdCardTimes = 0;
          this.getInfoIntervalID && clearInterval(this.getInfoIntervalID);
          this.getInfoIntervalID = setInterval(() => {
            this._formatSdCardTimes++;
            this._getFormatStatus();
          }, 5000);
        }
      })
      .catch((error) => {
        console.log("==========", error);
        if (error.recordMode || error.sdcardCode) {
          this.setState({ isLoading: false, motionRecord: error.recordMode, sdcardCode: error.sdcardCode });
        } else {
          Toast.fail('c_get_fail', error.error);
          this.setState({ isLoading: false });
        }

      });
  }

  _onResume() {
    // DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');
  }

  _onPause() {
  }

  onSwitchValueChange(switchType, value, ignoreAfterSet = false) {
    let params = [];
    switch (switchType) {
      case SWITCH_TYPE.VIDEO_STORAGE:
        params = [{
          did: Device.deviceID,
          siid: COCKPIT_SERVICE_SIID,
          piid: COCKPIT_SERVICE_STORAGE_SWITCH_PIID,
          value: value
        }];
        // if (!CameraConfig.supportDivideStorage()) {
        //   params.push({ did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: MOTION_DETECTION_MOTION, value: value });
        // }
        break;
      case SWITCH_TYPE.CLOUD_VIDEO_STORAGE:
        params = [{ did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: MOTION_DETECTION_MOTION, value: value }];
        break;
      case SWITCH_TYPE.REMOTE:
        params = [{
          did: Device.deviceID,
          siid: COCKPIT_SERVICE_SIID,
          piid: COCKPIT_SERVICE_LIVE_SWITCH_PIID,
          value: value
        }];
        break;
      case SWITCH_TYPE.DISTORTION_CORRECTION:
        params = [{
          did: Device.deviceID,
          siid: CAMERA_CONTROL_CAR_SIID,
          piid: CAMERA_CONTROL_CORRECTION_PIID,
          value: value
        }];
        break;
      case SWITCH_TYPE.HDR:
        params = [{ did: Device.deviceID, siid: CAMERA_CONTROL_CAR_SIID, piid: CAMERA_CONTROL_HDR_PIID, value: value }];
        break;
      case SWITCH_TYPE.WATERMARK:
        params = [{
          did: Device.deviceID,
          siid: CAMERA_CONTROL_CAR_SIID,
          piid: CAMERA_CONTROL_TIME_WATERMARK_PIID,
          value: value
        }];
        break;
      case SWITCH_TYPE.RECORD:
        params = [{
          did: Device.deviceID,
          siid: COCKPIT_SERVICE_SIID,
          piid: COCKPIT_SERVICE_RECORD_VOICE_PIID,
          value: value
        }];
        break;
      case SWITCH_TYPE.CROSS_PLAT:
        params = [{
          did: Device.deviceID,
          siid: COCKPIT_SERVICE_SIID,
          piid: COCKPIT_SERVICE_CROSS_PLAT_PIID,
          value: value
        }];
        break;
    }
    Service.spec.setPropertiesValue(params).then((res) => {
      console.log("SET================", res,value);
      if (ignoreAfterSet) {
        return;
      }
      if (res[0].code === 0) {
        // Toast.success('c_set_success');
        switch (switchType) {
          case SWITCH_TYPE.VIDEO_STORAGE:
            this.setState({ videoSaveSwitch: value });
            CameraConfig.SETTING_PROP.videoSaveSwitch = value;
            break;
          case SWITCH_TYPE.CLOUD_VIDEO_STORAGE:
            this.setState({ videoCloudSaveSwitch: value });
            CameraConfig.SETTING_PROP.videoCloudSaveSwitch = value;
            break;
          case SWITCH_TYPE.REMOTE:
            this.setState({ remoteSwitch: value });
            CameraConfig.SETTING_PROP.remoteSwitch = value;
            break;
          case SWITCH_TYPE.DISTORTION_CORRECTION:
            this.setState({ distortionCorrectionSwitch: value });
            CameraConfig.SETTING_PROP.distortionCorrectionSwitch = value;
            break;
          case SWITCH_TYPE.HDR:
            this.setState({ hdrIndex: value });
            CameraConfig.SETTING_PROP.hdrIndex = value;
            break;
          case SWITCH_TYPE.WATERMARK:
            this.setState({ waterMarkSwitch: value });
            CameraConfig.SETTING_PROP.waterMarkSwitch = value;
            break;
          case SWITCH_TYPE.RECORD:
            this.setState({ recordVoiceSwitch: value });
            CameraConfig.SETTING_PROP.recordVoiceSwitch = value;
            break;
          case SWITCH_TYPE.CROSS_PLAT:
            this.setState({ crossPlatformSwitch: value });
            CameraConfig.SETTING_PROP.crossPlatformSwitch = value;
            break;
        }
      } else {
        if (ignoreAfterSet) {
          return;
        }
        this.setErrorCallback(switchType, value);
      }
    }).catch((err) => {
      LogUtil.logOnAll(TAG, "set value error:", err);
      if (ignoreAfterSet) {
        return;
      }
      this.setErrorCallback(switchType, value);
    });
  }

  setErrorCallback(switchType, value) {
    Toast.fail('c_set_fail');
    switch (switchType) {
      case SWITCH_TYPE.VIDEO_STORAGE:
        this.setState({ videoSaveSwitch: !value });
        break;
      case SWITCH_TYPE.CLOUD_VIDEO_STORAGE:
        this.setState({ videoCloudSaveSwitch: !value });
        break;
      case SWITCH_TYPE.REMOTE:
        this.setState({ remoteSwitch: !value });
        break;
      case SWITCH_TYPE.DISTORTION_CORRECTION:
        this.setState({ distortionCorrectionSwitch: !value });
        break;
      case SWITCH_TYPE.HDR:
        // this.setState({ hdrIndex: !value });
        break;
      case SWITCH_TYPE.WATERMARK:
        this.setState({ waterMarkSwitch: !value });
        break;
    }
  }

  // 报警时间间隔
  _onIntervalChanged(value, index = 0) {
    console.log("value", value);
    Service.spec.setPropertiesValue([{
      did: Device.deviceID,
      siid: MOTION_DETECTION_SIID,
      piid: MOTION_DETECTION_ALARM_INTERVAL_PIID,
      value: value
    }])
      .then((res) => {
        console.log("_onIntervalChanged", res);
        // if (res[0].code == 0) {
        //   this.setState({ interval: parseInt(value), intervalIndex: index });
        //   CameraConfig.SETTING_PROP.interval = parseInt(value);
        // } else {
        //   CameraConfig.SETTING_PROP.interval = this.state.interval;
        //   Toast.fail('c_set_fail');
        //   this.setState({ interval: this.state.interval, intervalIndex: this.state.intervalIndex });
        // }
        if (res[0].code != 0) {
          CameraConfig.SETTING_PROP.interval = this.state.interval;
          Toast.fail('c_set_fail');
          this.setState({ interval: this.state.interval, intervalIndex: this.state.intervalIndex });
        }
      }).catch((err) => {
      CameraConfig.SETTING_PROP.interval = this.state.interval;
      Toast.fail('c_set_fail', err);
      this.setState({ interval: this.state.interval, intervalIndex: this.state.intervalIndex });
    });
  }

  checkPtz() {
    // MISS_CMD_RESET_STATUS
    Service.miotcamera.sendP2PCommandToDevice(MISSCommand_ECO.MISS_CMD_RESET_STATUS, {}).then((res) => {

    }).catch((res) => {

    });
  }

  handle_ptz_check() {
    if (!CameraPlayer.getInstance().getPowerState()) {
      Toast.success("camera_close_off");
      return;
    }
    this.isStartPtzCheck = true;
    let obj = {};
    obj["operation"] = DirectionViewConstant.CMD_CHECK;
    Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_MOTOR_REQ, obj)
      .then((result) => {
        console.log("+++++++++++success",result);
        this.setState({ isPtzCorrecting: true });
        // 兜底计时
        this.ptzCorrectTimeout && clearTimeout(this.ptzCorrectTimeout);
        this.ptzCorrectTimeout = setTimeout(() => {
          this.setState({ isPtzCorrecting: false });
        }, 42000);
      })
      .catch((error) => {
        console.log("+++++++++++error",error);
        Toast.fail("action_failed");
      });
    // CameraPlayer.getInstance().sendDirectionCmd(DirectionViewConstant.CMD_CHECK, true);
    // 由p2p2改为spec方式进行云存校准
    // let params = {
    //   did: Device.deviceID,
    //   siid: 21,
    //   aiid: 3,
    //   in: []
    // };
    // Service.spec.doAction(params).then((res) => {
    //   console.log("======= doAction delete success", res);
    // }).catch((err) => {
    //   console.log("======== doAction delete fail:",err);
    // });
  }

  _formatSdCard() {
    console.log("why!, _formatSdCard");

    this.setState({ showFormatDialog: false, isFormatting: true });
    this.sdcardNoExitCodeTimes = 0;
    this.formatingSdcard = true;
    this._formatSdCardTimes = 0;
    // AlarmUtilV2.doSpecAction({ siid: CAMERA_SDCARD_SIID, aname: CAMERA_SDCARD_FORMAT_ACTION })

    Service.spec.doAction({
      did: Device.deviceID,
      siid: MEMORY_CARD_MANAGEMENT_SIID,
      aiid: MEMORY_CARD_MANAGEMENT_FORMAT_AIID,
      in: []
    })
      .then((result) => {
        // 车机端返回的格式化result 为一个空对象
        // let isSuccess = result.code == 0;
        let isSuccess = true;
        if (isSuccess) {
          this.firstFormatSuccessIsDo = false;
          setTimeout(() => {
            this._getFormatStatus();
          }, 300);
          clearInterval(this.getInfoIntervalID);

          this.getInfoIntervalID = setInterval(() => {
            this._formatSdCardTimes++;
            this._getFormatStatus();
          }, 5000);
        } else {
          this._formatSdCardTimes = 0;
          LogUtil.logOnAll("sds_format_fail reason: 2222222 result=", JSON.stringify(result));
          Toast.fail('sds_format_fail');
          this.formatingSdcard = false;
          this.setState({ isFormatting: false });

        }
      })
      .catch((err) => {
        LogUtil.logOnAll("sds_format_fail reason: 3333333 err=", JSON.stringify(err));
        Toast.fail('sds_format_fail', err);
        this.formatingSdcard = false;
        this.setState({ isFormatting: false });
      });
  }

  _getFormatStatus() {

    CameraPlayer.getInstance().getSdcardStatusDirect(true)
      .then(({ recordMode, sdcardCode, totalSize, videoSize, idleSize }) => {
        LogUtil.logOnAll("SdcardSetting", "recordMode:", recordMode, "sdcardCode", sdcardCode, "totalSize", Util._formatSize(totalSize), "videoSize", Util._formatSize(videoSize), "idleSize", Util._formatSize(idleSize));
        if (sdcardCode == 0) {
          // 为了能正常显示容量，再轮询一次状态，再提示格式化成功，及展示相关容量
          if (!this.firstFormatSuccessIsDo) {
            this.firstFormatSuccessIsDo = true;
            LogUtil.logOnAll("CarSetting", "the first format is success");
            return;
          }
          this.setState({
            sdcardCode: 0,
            motionRecord: recordMode,
            totalSize: totalSize == 0 ? this.state.totalSize : totalSize,
            videoSize: videoSize == 0 ? this.state.videoSize : videoSize,
            idleSize: idleSize == 0 ? this.state.idleSize : idleSize,
            isFormatting: false
          });

          Toast.success('sds_format_success');
          this.sdcardNoExitCodeTimes = 0;
          this.formatingSdcard = false;
          SdFileManager.getInstance().clearSdcardFileList();
          clearInterval(this.getInfoIntervalID);
          this.getInfoIntervalID = 0;

        } else if (sdcardCode == 9) {
          // 格式化后卡
          this.setState({
            sdcardCode: 9,
            motionRecord: recordMode,
            isFormatting: false
          });
          clearInterval(this.getInfoIntervalID);
          this.getInfoIntervalID = 0;
          Toast.success('sds_format_success');
          this.sdcardNoExitCodeTimes = 0;
          this.formatingSdcard = false;
          SdFileManager.getInstance().clearSdcardFileList();
          // this.getCardRecordDuration();
        } else if (sdcardCode == 4) {
          this.setState({
            sdcardCode: 4,
            motionRecord: recordMode,
            isFormatting: true
          });
          this.sdcardNoExitCodeTimes = 0;
        } else if (sdcardCode == 1 && this.sdcardNoExitCodeTimes <= 10) {//部分平台 格式化后，会有一会的状态1 需要做一下兼容,连续10次都是1才认为格式化失败了。
          this.sdcardNoExitCodeTimes++;
        } else {
          if (this._formatSdCardTimes > 36) {
            this.sdcardNoExitCodeTimes = 0;
            LogUtil.logOnAll("sds_format_fail reason: 5555555");
            Toast.fail('sds_format_fail');
            clearInterval(this.getInfoIntervalID);
            this.getInfoIntervalID = 0;
            this.setState({
              sdcardCode: sdcardCode,
              isFormatting: false
            });
            this.formatingSdcard = false;
          }
        }
      })
      .catch((err) => {
        if (!CameraConfig.supportSDCardV2(Device.model) || this._formatSdCardTimes > 36) {
          LogUtil.logOnAll("sds_format_fail reason: 1111111 err=", JSON.stringify(err));
          Toast.fail('sds_format_fail');
          clearInterval(this.getInfoIntervalID);
          this.getInfoIntervalID = 0;
          this.formatingSdcard = false;
          this.setState({ isFormatting: false });
        }
      });

  }


  _exitSdCard() {
    this.setState({ showExitDialog: false });
    // AlarmUtilV2.doSpecAction({ siid: CAMERA_SDCARD_SIID, aname: CAMERA_SDCARD_EJECT_ACTION })
    Service.spec.doAction({
      did: Device.deviceID,
      siid: MEMORY_CARD_MANAGEMENT_SIID,
      aiid: MEMORY_CARD_MANAGEMENT_POP_AIID,
      in: []
    })
      .then((result) => {
        Toast.success('sds_exit_success');
        CameraPlayer.getInstance().sdcardCode = 5;
        this.setState({ sdcardCode: 5 });

      })
      .catch((err) => {
        Toast.fail('sds_exit_fail', err);
      });

    // this.setState({ sdcardCode: 1 });
  }

  toLogin() {
    try {
      native.MIOTHost.transCustomInfo('open_login_page', null, (code, result) => {
        if (code) {
          console.log(`transCustomInfo: ${ JSON.stringify(result) }`);
        } else {
          console.log(`transCustomInfo error ${ JSON.stringify(result) }`);
        }
      });
    } catch (e) {
      Toast._showToast("暂不支持登录调用");
    }

  }

  render() {

    return (
      <View style={ [carStyles.mediumLargeContainerStyle, { paddingLeft: 0, paddingRight: 0 }] }>
        { this.renderTitle() }
        <ScrollView
          contentContainerStyle={ {
            paddingBottom: Constants.DEFAULT_MARGIN_TOP_BOTTOM,
            paddingLeft: CarPluginWindow.MediumLarge.MarginLeft_Right,
            paddingRight: CarPluginWindow.MediumLarge.MarginLeft_Right
          } }
          showsVerticalScrollIndicator={ true }
          scrollEventThrottle={ 16 }>

          <View style={ { height: 88, justifyContent: 'center' } }>
            <Text style={ {
              fontSize: 24,
              color: carStyles.groupStyle.color
            } }>{ LocalizedStrings['remote_view_title'] }</Text>
          </View>
          <SwitchComponent
            // style={{ width: 500 }}
            value={ this.state.remoteSwitch }
            onValueChange={ (value) => {
              console.log(`SwitchComponent value is ${ value }`);
              if (value) {
                this.setState({ showRemoteDialog: true });
              } else {
                this.onSwitchValueChange(SWITCH_TYPE.REMOTE, value);
              }
            } }
            disabled={ false }
            style={ { height: 152 } }
            title={ LocalizedStrings['setting_remote_permission'] }
            subTitle={ LocalizedStrings['setting_remote_permission_subtitle'] }
          />

          <Slider
            title={ LocalizedStrings['setting_volume'] }
            value={ this.state.volumeValue }
            minValue={ 0 }
            maxValue={ 100 }
            imageSource={ DarkMode.getColorScheme() === 'dark' ? require('../../Resources/Images/car/ic_media_volume.png') : require('../../Resources/Images/car/ic_media_volume_light.png') }
            onValueChange={ (value) => {
              console.log(`Slider onVallueChange is ${ value }`);
            } }
            onSlidingComplete={ (value) => {
              console.log(`Slider onSlidingComplete is ${ value }`);
              let params = [{
                did: Device.deviceID,
                siid: CAMERA_SPEAKER_SIID,
                piid: CAMERA_SPEAKER_VOLUME_PIID,
                value: value
              }];
              Service.spec.setPropertiesValue(params).then((res) => {
                if (res[0].code === 0) {
                  this.setState({ volumeValue: value });
                  CameraConfig.SETTING_PROP.volumeValue = value;

                } else {
                  Toast.fail('c_set_fail');
                }
              }).catch((res) => {
                Toast.fail('c_set_fail');
              });
            } }
            disabled={ false }
            // style={{ width: 500 }}
          />
          <Dividers/>


          <View style={ { height: 88, justifyContent: 'center' } }>
            <Text style={ {
              fontSize: 24,
              color: carStyles.groupStyle.color
            } }>{ LocalizedStrings['show_nas_info'] }</Text>
          </View>

          <SwitchComponent
            value={ this.state.videoSaveSwitch }
            onValueChange={ (value) => {
              console.log(`SwitchComponent value is ${ value }`);
              if (CameraConfig.carLoginState != 0 && CameraConfig.carLoginState != 1) {
                // 判断为未登录状态, 跳转到登录页面
                this.toLogin();
                this.setState({ videoSaveSwitch: this.state.videoSaveSwitch });
              } else {
                // 改为每次都需要二次确认框
                if (value) {
                  this.setState({ showLocalDialog: true });
                } else {
                  this.onSwitchValueChange(SWITCH_TYPE.VIDEO_STORAGE, false);
                }
              }

            } }
            disabled={ false }
            style={ { height: 152 } }
            title={ LocalizedStrings['local_storage_title'] }
            subTitle={ LocalizedStrings['local_storage_subtitle'] }
          />

          { this.renderSDInfo() }

          {/*{*/}
          {/*  !CameraConfig.supportDivideStorage() ? null :*/}
          {/*    <SwitchCarComponent*/}
          {/*      value={ this.state.videoCloudSaveSwitch }*/}
          {/*      onValueChange={ (value) => {*/}
          {/*        console.log(`SwitchComponent value is ${ value }`);*/}
          {/*        if (CameraConfig.carLoginState != 0 && CameraConfig.carLoginState != 1) {*/}
          {/*          // 判断为未登录状态, 跳转到登录页面*/}
          {/*          this.toLogin();*/}
          {/*          this.setState({ videoCloudSaveSwitch: this.state.videoCloudSaveSwitch });*/}
          {/*        } else {*/}
          {/*          // 改为每次都需要二次确认框*/}
          {/*          if (value) {*/}
          {/*            this.setState({ showCloudDialog: true });*/}
          {/*          } else {*/}
          {/*            this.onSwitchValueChange(SWITCH_TYPE.CLOUD_VIDEO_STORAGE, false);*/}
          {/*          }*/}
          {/*        }*/}

          {/*      } }*/}
          {/*      onInfoPress={() => {*/}
          {/*        this.setState({ showCloudInfoDialog: true });*/}
          {/*      }}*/}
          {/*      disabled={ false }*/}
          {/*      style={ { height: 152 } }*/}
          {/*      title={ LocalizedStrings['s_cloud_setting'] }*/}
          {/*      subTitle={ this.state.videoCloudSaveSwitch ? CameraConfig.isVip ? LocalizedStrings['cloud_storage_open_vip_subtitle'] : LocalizedStrings['cloud_storage_open_subtitle'] : LocalizedStrings['cloud_storage_subtitle'] }*/}
          {/*    />*/}
          {/*}*/}

          {/*{*/}
          {/*  CameraConfig.isVip || (CameraConfig.supportDivideStorage() && !this.state.videoCloudSaveSwitch) || (!CameraConfig.supportDivideStorage() && !this.state.videoSaveSwitch) ? null :*/}
          {/*    <SegmentControl*/}
          {/*      title={ LocalizedStrings['setting_cloud_interval'] }*/}
          {/*      values={ [`3${ LocalizedStrings['tip_time_minute'] }`, `5${ LocalizedStrings['tip_time_minute'] }`, `10${ LocalizedStrings['tip_time_minute'] }`] }*/}
          {/*      // selectedIndex={ this.state.intervalIndex }*/}
          {/*      selectedIndex={ this.intervalServerIndex }*/}
          {/*      style={ { marginBottom: Constants.DEFAULT_MARGIN_TOP_BOTTOM } }*/}
          {/*      tipsArray={[LocalizedStrings['cloud_interval_tips'],LocalizedStrings['cloud_interval_tips'],LocalizedStrings['cloud_interval_tips']]}*/}
          {/*      onChange={ (index) => {*/}
          {/*        console.log(`SegmentControl selected: ${ index }`);*/}
          {/*        this.setState({ interval: parseInt(INTERVAL[index]), intervalIndex: index });*/}
          {/*        CameraConfig.SETTING_PROP.interval = parseInt(INTERVAL[index]);*/}
          {/*        this._onIntervalChanged(INTERVAL[index], index);*/}
          {/*      } }*/}
          {/*      disabled={ false }*/}
          {/*      // tipsArray={['Segment1Segment1', '', 'Segment3Segment3', 'Segment4Segment4']}*/}
          {/*    />*/}
          {/*}*/}

          {
            !CameraConfig.supportVoiceMute() ? null :
              <SwitchComponent
                value={ this.state.recordVoiceSwitch }
                onValueChange={ (value) => {
                  console.log(`SwitchComponent value is ${ value }`);
                  if (CameraConfig.carLoginState != 0 && CameraConfig.carLoginState != 1) {
                    // 判断为未登录状态, 跳转到登录页面
                    this.toLogin();
                    this.setState({ showRecordDialog: this.state.showRecordDialog });
                  } else {
                    if (value) {
                      this.setState({ showRecordDialog: true });
                    } else {
                      this.onSwitchValueChange(SWITCH_TYPE.RECORD, value);
                    }
                  }

                } }
                disabled={ false }
                style={ { height: 152 } }
                title={ LocalizedStrings['setting_voice_record'] }
                subTitle={ LocalizedStrings['setting_voice_record_subtitle'] }
              />
          }
          {/*{*/}
          {/*  CameraConfig.supportCrossPlatView() ? <SwitchComponent*/}
          {/*    value={ this.state.crossPlatformSwitch }*/}
          {/*    onValueChange={ (value) => {*/}
          {/*      console.log(`SwitchComponent value is ${ value }   ${Device.isOwner}`);*/}
          {/*      if (value) {*/}
          {/*        this.setState({ showCrossPlatDialog: true });*/}
          {/*      } else {*/}
          {/*        this.onSwitchValueChange(SWITCH_TYPE.CROSS_PLAT, value);*/}
          {/*      }*/}
          {/*    } }*/}
          {/*    style={ { height: 152 } }*/}
          {/*    title={ LocalizedStrings['setting_cross_platform_view'] }*/}
          {/*    subTitle={ LocalizedStrings['setting_cross_platform_view_subtitle'] }*/}
          {/*  /> : null*/}
          {/*}*/}


          <Dividers/>


          <View style={ { height: 88, justifyContent: 'center' } }>
            <Text style={ {
              fontSize: 24,
              color: carStyles.groupStyle.color
            } }>{ LocalizedStrings['cs_image_setting'] }</Text>
          </View>
          <SwitchComponent
            value={ this.state.waterMarkSwitch }
            onValueChange={ (value) => {
              console.log(`SwitchComponent value is ${ value }`);
              this.onSwitchValueChange(SWITCH_TYPE.WATERMARK, value);
            } }
            disabled={ false }
            style={ { height: 152 } }
            title={ LocalizedStrings['setting_watermark'] }
            subTitle={ LocalizedStrings['setting_watermark_subtitle'] }
          />

          {/*暂时隐藏掉畸变，后期可能会上*/ }
          {/*<SwitchComponent*/ }
          {/*  // style={{ width: 500 }}*/ }
          {/*  value={ this.state.distortionCorrectionSwitch }*/ }
          {/*  onValueChange={ (value) => {*/ }
          {/*    console.log(`SwitchComponent value is ${ value }`);*/ }
          {/*    this.onSwitchValueChange(SWITCH_TYPE.DISTORTION_CORRECTION, value);*/ }
          {/*  } }*/ }
          {/*  disabled={ false }*/ }
          {/*  title={ LocalizedStrings['is_lens_distortion_correction'] }*/ }
          {/*/>*/ }

          <SegmentControl
            title={ LocalizedStrings['setting_HDR'] }
            values={ [LocalizedStrings['setting_close'], LocalizedStrings['setting_open'], LocalizedStrings['setting_auto']] }
            selectedIndex={ this.state.hdrIndex }
            style={ { marginBottom: Constants.DEFAULT_MARGIN_TOP_BOTTOM } }

            onChange={ (index) => {
              console.log(`SegmentControl selected: ${ index }`);
              // 加个
              this.delayToSetHDR && clearTimeout(this.delayToSetHDR);
              this.delayToSetHDR = setTimeout(() => {
                console.log(`real to set selected: ${ index }`);
                if (index === this.state.hdrIndex) {
                  // 相同 就不去设置了
                  return;
                }
                this.onSwitchValueChange(SWITCH_TYPE.HDR, index);
              }, 500);
            } }
            disabled={ false }
            tipsArray={ [LocalizedStrings['setting_HDR_subtitle'], LocalizedStrings['setting_HDR_subtitle'], LocalizedStrings['setting_HDR_subtitle']] }
          />


          <Dividers/>

          <ClickableCardButton
            title={ LocalizedStrings['ptz_check'] }
            style={ { width: 564, justifyContent: 'center' } }
            showImage={ false }
            // disabled={this.state.isPtzCorrecting}
            showLoadingImage={ this.state.isPtzCorrecting }
            onPress={ () => {
              console.log("云台校准 onPress");
              if (this.state.isPtzCorrecting) {
                // 已经处于校准中
                return;
              }
              this.setState({ showPtzDialog: true });
            } }/>
        </ScrollView>
        { this.renderCloudDialog() }
        { this.renderCloudInfoDialog() }
        { this.renderLocalDialog() }
        { this.renderCrossPlatDialog() }
        { this.renderRemoteDialog() }
        { this.renderSDFormatDialog() }
        { this.renderSDExitDialog() }
        { this.renderVoiceOpenDialog() }
        { this.renderPtzDialog() }
      </View>
    );
  }


  renderSDInfo() {
    // if (!this.state.videoSaveSwitch) {
    //   return null;
    // }
    let sdcardCode = this.state.sdcardCode;
    if (sdcardCode == 7) {
      // 7弹出中，添加SD卡监听会存在此状态，之后又收不到新的SD卡状态
      // 改为已弹出
      sdcardCode = 5;
    }
    let usedSize = Util._formatSize(this.state.videoSize, true);
    let totalSize = Util._formatSize(this.state.totalSize, true);
    let statusDesc = `${ LocalizedStrings['sds_status'] }：${ LocalizedStrings[`sds_status_${ sdcardCode }`] }`;
    let spaceDesc = sdcardCode == 4 || sdcardCode == 0 && this.state.totalSize != null && this.state.totalSize != 0
      ? this.state.duration && this.state.duration > 0 ? `${ LocalizedStrings["sdcard_status_capcity"].replace("%1$d", usedSize).replace("%2$d", totalSize) }，${ LocalizedStrings["sdcard_record_duration"].replace("%1$s", this.mHeaderInfo.duration) }` : LocalizedStrings["sdcard_status_capcity"].replace("%1$d", usedSize).replace("%2$d", totalSize)
      : sdcardCode == 3 || sdcardCode == 2 || sdcardCode == CameraPlayer.SD_CARD_NEED_FORMAT_CODE || sdcardCode == CameraPlayer.SD_CARD_FILE_ERROR_CODE
        ? LocalizedStrings["sds_try_format"]
        : LocalizedStrings[`sds_try_action_${ sdcardCode == CameraPlayer.SD_CARD_INCOMPATIBLE_CODE
          ? CameraPlayer.SD_CARD_TOO_SMALL_CODE : sdcardCode }`];
    let leftSpacePercent = 0;
    let holdSpacePercent = 0;
    if (this.state.totalSize && (this.state.sdcardCode == 2 || this.state.sdcardCode == 0)) {
      holdSpacePercent = Number.parseInt(this.state.videoSize * 100 / this.state.totalSize);
      if (holdSpacePercent < 1) {
        holdSpacePercent = 1;
      }
      leftSpacePercent = 100 - holdSpacePercent;
    }
    return (
      <>
        <View style={ [styles.itemSecondaryStyle, {
          maxHeight: 187,
          padding: Constants.DEFAULT_MARGIN_TOP_BOTTOM,
          marginTop: Constants.DEFAULT_MARGIN_TOP_BOTTOM,
          marginBottom: 12,
          borderRadius: Radius.WidgetLevel
        }] }>
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
            <Text style={ [styles.titleTextStyle] }>{ statusDesc }</Text>
            { sdcardCode == 4 ? null :
              <Text style={ [styles.subTitleTextStyle] }>{ spaceDesc }</Text>
            }
          </View>

          <View style={ [{
            width: "100%",
            position: "relative",
            flexDirection: "row",
            height: 10,
            borderRadius: 3,
            marginTop: 28,
            backgroundColor: carStyles.progressBgStyle.backgroundColor
          }] }>
            <View style={ [{
              flexGrow: holdSpacePercent,
              borderRadius: 3,
              height: 10,
              backgroundColor: carStyles.progressStyle.backgroundColor
            }] }>
            </View>
            <View style={ { flexGrow: leftSpacePercent, height: 10 } }>
            </View>
          </View>
        </View>
        {
          sdcardCode == 1 || sdcardCode == 5 ? null :
            <View style={ { flexDirection: 'row' } }>

              <ClickableCardButton
                title={ LocalizedStrings['setting_sds_format'] }
                style={ { width: 564, justifyContent: 'center', marginRight: Constants.DEFAULT_TEXT_MARGIN_BOTTOM } }
                showImage={ false }
                disabled={ this.state.isFormatting || (CameraConfig.carLoginState != 0 && CameraConfig.carLoginState != 1) }
                showLoadingImage={ this.state.isFormatting }
                onPress={ () => {
                  this.setState({ showFormatDialog: true });
                } }/>

              <ClickableCardButton
                title={ LocalizedStrings['setting_sds_exit'] }
                style={ { width: 564, justifyContent: 'center' } }
                disabled={ CameraConfig.carLoginState != 0 && CameraConfig.carLoginState != 1 }
                showImage={ false }
                onPress={ () => {
                  if (this.state.sdcardCode == 4) {
                    // SD卡正在格式化中，给个toast提示
                    Toast.success("formating_error");
                    return;
                  }
                  this.setState({ showExitDialog: true });
                } }/>

            </View>
        }

      </>
    );
  }

  renderTitle() {
    const {
      colorScheme
    } = this.context;

    return (
      <View style={ [{
        flexDirection: 'row',
        alignItems: 'center',
        height: 148,
        paddingLeft: CarPluginWindow.MediumLarge.MarginLeft_Right,
        paddingRight: CarPluginWindow.MediumLarge.MarginLeft_Right
      }, this.props.style] }>
        <TouchableOpacity
          onPress={ () => {
            this.props.navigation.goBack();
          } }
        >
          <Image
            style={ { width: 48, height: 48 } }
            source={ DarkMode.getColorScheme() == 'dark' ? require("../../Resources/Images/car/ic_nav_arrow.png") : require("../../Resources/Images/car/ic_nav_arrow_light.png") }/>
        </TouchableOpacity>
        <View
          style={ {
            flexDirection: 'row',
            alignItems: 'center',
            flex: 1,
            justifyContent: 'center',
            width: '100%',
            marginLeft: CarPluginWindow.MediumLarge.MarginLeft_Right,
            // height: '100%',
            position: 'absolute'
          } }
        >
          <TouchableWithoutFeedback
            onLongPress={ () => {
              let version_info = 'plugin version_code:' + ProjectInfo.version_code;

              Toast._showToast(version_info);
            } }
          >
            <Text numberOfLines={ 1 } style={ [styles.titleTextStyle, {
              fontSize: Font.Size._36
            }] }>{ LocalizedStrings['setting'] }</Text>
          </TouchableWithoutFeedback>
        </View>
      </View>
    );
  }

  renderLocalDialog() {
    return (
      <CarCommonDialog
        visible={ this.state.showLocalDialog }
        title={ LocalizedStrings['open_storage_function'] }
        message={ LocalizedStrings['open_local_function_desc'] }
        onDismiss={ () => this.setState({ showLocalDialog: false }) }
        buttons={ [
          {
            text: LocalizedStrings['open'],
            backgroundColor: DarkMode.getColorScheme() == "dark" ? "xm#B52C2E" : "xm#CC4239",
            callback: () => {
              console.log("-------确定------");
              StorageKeys.CAR_STORAGE = true;
              this.setState({ showLocalDialog: false, isVideoSwitchOperateOnce: true });
              this.onSwitchValueChange(SWITCH_TYPE.VIDEO_STORAGE, true);

            }
          }, {
            text: I18n.cancel,
            callback: (res) => {
              console.log("-------取消------");
              this.setState({ showLocalDialog: false });
            }
          }
        ] }
      />
    );
  }

  renderCrossPlatDialog() {
    return (
      <CarCommonDialog
        visible={ this.state.showCrossPlatDialog }
        title={ LocalizedStrings['setting_open_cross_platform_view'] }
        message={ LocalizedStrings['setting_open_cross_platform_msg'] }
        onDismiss={ () => this.setState({ showCrossPlatDialog: false }) }
        buttons={ [
          {
            text: LocalizedStrings['open'],
            backgroundColor: DarkMode.getColorScheme() == "dark" ? "xm#B52C2E" : "xm#CC4239",
            callback: () => {
              console.log("-------确定------");
              this.setState({ showCrossPlatDialog: false });
              this.onSwitchValueChange(SWITCH_TYPE.CROSS_PLAT, true);

            }
          }, {
            text: I18n.cancel,
            callback: (res) => {
              console.log("-------取消------");
              this.setState({ showCrossPlatDialog: false });
            }
          }
        ] }
      />
    );
  }

  renderCloudDialog() {
    let message = [
      { title: LocalizedStrings['cloud_message1'] },
      { title: CameraConfig.isVip ? LocalizedStrings['cloud_message_vip_open'] : LocalizedStrings['cloud_message_vip_not_open'] },
      { title: LocalizedStrings['cloud_message2'], color: styles.errorTextStyle.color },
      CameraConfig.isVip ? { title: LocalizedStrings['cloud_message2_vip'] } : {
        title: LocalizedStrings['cloud_message2_non_vip'],
        subContent: LocalizedStrings['cloud_message2_non_vip_subMessage']
      }
    ];
    return (
      <CarComplexDialog
        visible={ this.state.showCloudDialog }
        title={ LocalizedStrings['open_cloud_function'] }
        // message={ LocalizedStrings['open_cloud_function_desc'] }
        message={ message }
        onDismiss={ () => this.setState({ showCloudDialog: false }) }
        buttons={ [
          {
            text: LocalizedStrings['open'],
            backgroundColor: DarkMode.getColorScheme() == "dark" ? "xm#B52C2E" : "xm#CC4239",
            callback: () => {
              console.log("-------确定------");
              StorageKeys.CAR_STORAGE = true;
              this.setState({ showCloudDialog: false, isVideoSwitchOperateOnce: true });
              this.onSwitchValueChange(SWITCH_TYPE.CLOUD_VIDEO_STORAGE, true);

            }
          }, {
            text: I18n.cancel,
            callback: (res) => {
              console.log("-------取消------");
              this.setState({ showCloudDialog: false });
            }
          }
        ] }
      />
    );
  }

  renderCloudInfoDialog() {
    let message = [
      { title: LocalizedStrings['cloud_message1'] },
      { title: LocalizedStrings['cloud_message_vip_not_open'], subContent: LocalizedStrings['cloud_message3'] },
      { title: LocalizedStrings['cloud_message2'], color: styles.errorTextStyle.color },
      { title: LocalizedStrings['cloud_message2_non_vip'], subContent: LocalizedStrings['cloud_message2_non_vip_subMessage'] }
    ];
    return (
      <CarComplexDialog
        visible={ this.state.showCloudInfoDialog }
        title={ LocalizedStrings['s_cloud_setting'] }
        message={ message }
        onDismiss={ () => this.setState({ showCloudInfoDialog: false }) }
        buttons={ [
          {
            text: LocalizedStrings['offline_divice_ok'],
            callback: () => {
              this.setState({ showCloudInfoDialog: false });
            }
          }
        ] }
      />
    );
  }

  renderRemoteDialog() {
    return (
      <CarCommonDialog
        visible={ this.state.showRemoteDialog }
        title={ LocalizedStrings['open_remote_function'] }
        message={ LocalizedStrings['open_remote_function_desc'] }
        onDismiss={ () => this.setState({ showRemoteDialog: false }) }
        buttons={ [
          {
            text: I18n.ok,
            callback: () => {
              this.setState({ showRemoteDialog: false });
              this.onSwitchValueChange(SWITCH_TYPE.REMOTE, true);

            }
          }, {
            text: I18n.cancel,
            callback: (res) => {
              this.setState({ showRemoteDialog: false });
            }
          }
        ] }
      />
    );
  }

  renderSDFormatDialog() {
    return (
      <CarCommonDialog
        visible={ this.state.showFormatDialog }
        title={ LocalizedStrings['sds_format'] }
        message={ LocalizedStrings['format_sd_desc'] }
        onDismiss={ () => this.setState({ showFormatDialog: false }) }
        buttons={ [
          {
            text: LocalizedStrings['format'],
            backgroundColor: DarkMode.getColorScheme() == "dark" ? "xm#B52C2E" : "xm#CC4239",
            callback: () => {
              this._formatSdCard();
            }
          }, {
            text: I18n.cancel,
            callback: (res) => {
              this.setState({ showFormatDialog: false });
            }
          }
        ] }
      />
    );
  }

  renderSDExitDialog() {
    return (
      <CarCommonDialog
        visible={ this.state.showExitDialog }
        title={ LocalizedStrings['sds_exit'] }
        message={ LocalizedStrings['format_exit_desc'] }
        onDismiss={ () => this.setState({ showExitDialog: false }) }
        buttons={ [
          {
            text: LocalizedStrings['exit_sd'],
            callback: () => {
              this._exitSdCard();
            }
          }, {
            text: I18n.cancel,
            callback: (res) => {
              console.log("-------取消------");
              this.setState({ showExitDialog: false });
            }
          }
        ] }
      />
    );
  }

  renderVoiceOpenDialog() {
    return (
      <CarCommonDialog
        visible={ this.state.showRecordDialog }
        title={ LocalizedStrings['open_record_voice_title'] }
        message={ LocalizedStrings['open_record_voice_msg'] }
        onDismiss={ () => this.setState({ showRecordDialog: false }) }
        buttons={ [
          {
            backgroundColor: DarkMode.getColorScheme() == "dark" ? "xm#B52C2E" : "xm#CC4239",
            text: LocalizedStrings['open'],
            callback: () => {
              this.setState({ showRecordDialog: false });
              this.onSwitchValueChange(SWITCH_TYPE.RECORD, true);
            }
          }, {
            text: I18n.cancel,
            callback: (res) => {
              this.setState({ showRecordDialog: false });
            }
          }
        ] }
      />
    );
  }

  renderPtzDialog() {
    return (
      <CarCommonDialog
        visible={ this.state.showPtzDialog }
        title={ LocalizedStrings['ptz_check'] }
        message={ LocalizedStrings['ptz_check_tip'] }
        onDismiss={ () => this.setState({ showPtzDialog: false }) }
        buttons={ [
          {
            text: I18n.ok,
            callback: () => {
              this.setState({ showPtzDialog: false });
              this.handle_ptz_check();
            }
          }, {
            text: I18n.cancel,
            callback: (res) => {
              this.setState({ showPtzDialog: false });
            }
          }
        ] }
      />
    );
  }

  componentWillUnmount() {
    this.didFocusListener.remove();
    this.didBlurListener.remove();
    this.didResumeListener.remove();
    this.willPauseListener.remove();
    this.getInfoIntervalID && clearInterval(this.getInfoIntervalID);
    this.delayToSetHDR && clearTimeout(this.delayToSetHDR);
    this.ptzCorrectTimeout && clearTimeout(this.ptzCorrectTimeout);
    this.delayToGetSdInfo && clearTimeout(this.delayToGetSdInfo);

    if (this.willAppearListener) {
      this.willAppearListener.remove();
    }
    this._deviceNameChangedListener && this._deviceNameChangedListener.remove();
    if (this.willDisappearListener) {
      this.willDisappearListener.remove();
    }
  }
}
