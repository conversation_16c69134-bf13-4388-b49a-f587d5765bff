import { DarkMode } from 'miot';
export const Event = {
  // Bell: "Bell",
  // Stay: "Stay",
  // Pass: "Pass",
  // Demolition: "Demolition",
  // BellVideo: "BellVideo",
  ChildDetected: "ChildDetected",
  BabyCry: "BabyCry",
  PeopleMotion: "PeopleMotion",
  ObjectMotion: "ObjectMotion",
  IgnoreEvent: "IgnoreEvent",
  KnownFace: "Face", // 服务端并不区分Face and KnownFace, 坑
  Pet: "Pet",
  <PERSON>: "Dog",
  Cat: "Cat",
  // Face: "Face",
  LouderSound: "LouderSound",
  AI: "AI",
  Default: "Default",
  CameraCalling: "CameraCalling"
};

function findCfgForKey(aCfg, aKey) {
  for (let cfg of aCfg.items) {
    if (aKey == cfg.key) {
      return cfg;
    }
    if (cfg.link != null && cfg.link.cfg != null) {
      // console.log(TAG, "process sub", itm.title);
      let ret = findCfgForKey(cfg.link.cfg, aKey);
      if (ret != null) {
        return ret;
      }
    } 
  }
  return null;
}

export const FastSetting = {
  RealTimeMode: "RealTimeMode",
  find: (aCfg, aKey) => {
    return findCfgForKey(aCfg, aKey);
  }
};

export const UICfg = {
  DlgCanDissmiss: true,
  IconTint: { tintColor: "dark" == DarkMode.getColorScheme() ? "xm#FFFFFF" : null }
};
