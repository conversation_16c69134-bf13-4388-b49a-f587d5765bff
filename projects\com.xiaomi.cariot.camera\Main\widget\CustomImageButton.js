/**
 * @export
 * @module miot/ui/ImageButton
 * @description 按钮
 *
 */

import React from 'react';
import { Image, TouchableWithoutFeedback } from 'react-native';
import PropTypes from 'prop-types';

import { AccessibilityPropTypes, getAccessibilityConfig, AccessibilityRoles } from 'mhui-rn/dist/utils/accessibility-helper';

export default class CustomImageButton extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      buttonPressed: false
    };
  }

  static initialState = {
    buttonPressed: false
  };

  static propTypes = {
    source: PropTypes.any,
    highlightedSource: PropTypes.any,
    onPress: PropTypes.func,
    disabled: PropTypes.bool,
    style: PropTypes.any,
    accessible: AccessibilityPropTypes.accessible,
    accessibilityLabel: AccessibilityPropTypes.accessibilityLabel,
    accessibilityHint: AccessibilityPropTypes.accessibilityHint,
    accessibilityRole: AccessibilityPropTypes.accessibilityRole
  };

  static defaultProps = {
    source: null,
    highlightedSource: null,
    onPress: null
  };

  _buttonPressIn() {
    this.setState({ buttonPressed: true });
  }

  _buttonPressOut() {
    this.setState({ buttonPressed: false });
  }

  _isButtonPressed() {
    return this.state.buttonPressed;
  }

  render() {
    let { source, highlightedSource, disabled, onPress, accessible, accessibilityLabel, accessibilityHint, accessibilityRole, accessibilityState, ...rest } = this.props;
    if (this._isButtonPressed() && highlightedSource) {
      source = highlightedSource;
    }
    return (
      <TouchableWithoutFeedback
        disabled={ disabled }
        onPress={ onPress }
        onPressIn={ this._buttonPressIn.bind(this) }
        onPressOut={ this._buttonPressOut.bind(this) }
        { ...getAccessibilityConfig({
          accessible: accessible,
          accessibilityRole: accessibilityRole || AccessibilityRoles.button,
          accessibilityLabel: accessibilityLabel,
          accessibilityHint: accessibilityHint,
          accessibilityState: {
            // disabled: !!disabled
            checked: accessibilityState?.checked,
            selected: accessibilityState?.selected
          }
        }) }
      >
        <Image
          source={ source }
          { ...rest }/>
      </TouchableWithoutFeedback>
    );
  }
}
