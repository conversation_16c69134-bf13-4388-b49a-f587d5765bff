import RNToast from 'react-native-root-toast';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import LogUtil from '../util/LogUtil';
import Util from '../util2/Util';
export class _Toast {
  constructor() {
  }

  lasToast = null

  _hideLastToast() {
    if (this.lasToast != null) {
      RNToast.hide(this.lasToast);
      this.lasToast = null;
    }
  }

  _showToast(message, isLong = false) {
    if (this.lasToast != null) {
      RNToast.hide(this.lasToast);
      this.lasToast = null;
    }

    this.lasToast = RNToast.show(message, {
      duration: isLong ? RNToast.durations.LONG : RNToast.durations.SHORT,
      position: RNToast.positions.BOTTOM,
      containerStyle: { backgroundColor: '#FFFFFFFF', borderWidth: 0.5, borderColor: '#0000001A', paddingHorizontal: 15, maxHeight: 68, borderRadius: 12, marginBottom: 106 },
      textColor: '#4C4C4CFF',
      textStyle: { fontWeight: '400', fontSize: Util.isLanguageCN() ? 14 : 12 },
      shadow: true,
      shadowColor: '#e5e5e5',
      opacity: 1,
      onShown : () => {
        if (this.lasToast != null) {
          RNToast.hide(this.lasToast);
        }
      }
    });
    // 这里设置隐藏
    // if (this.lasToast) {
    //   clearTimeout(this.hideToast)
    //   this.hideToast = setTimeout(() => {
    //     RNToast.hide(this.lasToast);
    //     this.lasToast = null
    //   }, 1000)
    // }


  }

  /**
   * @param {string} localizeKey 多语言的key
   */
  loading(localizeKey = '') {
    if (localizeKey == "c_get_fail") {
      LogUtil.logOnAll("c_get_fail stack:" + new Error().stack);
    }
    this._showToast(LocalizedStrings[localizeKey], true);
  }

  /**
   * @param {string} localizeKey 多语言的key
   */
  loadingCustom(localizeKey1, localizeKey2) {
    if (localizeKey1 == "c_get_fail") {
      LogUtil.logOnAll("c_get_fail stack:" + new Error().stack);
    }
    this._showToast(LocalizedStrings[localizeKey1] + localizeKey2, true);
  }

  /**
   * @param {string} localizeKey 多语言的key
   */
  success(localizeKey) {
    if (localizeKey == "c_get_fail") {
      LogUtil.logOnAll("c_get_fail stack:" + new Error().stack);
    }
    this._showToast(LocalizedStrings[localizeKey]);
  }

  /**
   * @param {string} localizeKey 多语言的key
   * @param {Object} error 错误信息, 用于log
   */
  fail(localizeKey, error = null, isLong = false) {
    if (localizeKey == "c_get_fail") {
      LogUtil.logOnAll("c_get_fail stack:" + new Error().stack);
    }
    this._showToast(LocalizedStrings[localizeKey], isLong);
    if (error) {
      console.log(JSON.stringify(error));
    }
  }

  show(aStr) {
    if (LocalizedStrings[aStr]) {
      this.success(aStr);
    } else {
      this._showToast(aStr);
    }
  }
}

const Toast = new _Toast();
export default Toast;
