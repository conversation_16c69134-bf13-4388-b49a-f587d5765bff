import React from 'react';
import PropTypes from 'prop-types';
import { View, Animated, ViewPropTypes, Easing, TouchableOpacity, Text, Image } from 'react-native';
import { styles, Radius, Constants, Opacity, Font } from 'micariot-ui-sdk/common/styles/Styles';
import { DarkMode } from "miot";
import { CarColor } from "../../common/Styles";
import MarqueeText from "../MarqueeText";
import { localStrings as LocalizedStrings } from '../../../MHLocalizableString';

const DEFAULT_SEGMENT_HEIGHT = 136;
const DEFAULT_MARGIN = 24;

/**
 * @export public
 * @module ImageSegmentControl
 * @description ImageSegmentControl for CarIoT
 * @property {object} style - style
 * @property {string} title - 标题，如果未空则不显示标题区域
 * @property {array} values - 分段文本
 * @property {array} tipsArray - 分段提示数组
 * @property {number} selectedIndex - 选择index
 * @property {function} onChange - 选择回调方法
 * @property {bool} disabled - 是否禁用，默认false
 */
class ImageSegmentControl extends React.PureComponent {
  static propTypes = {
    style: PropTypes.object,
    title: PropTypes.string,
    values: PropTypes.array,
    tipsArray: PropTypes.array,
    selectedIndex: PropTypes.number,
    onChange: PropTypes.func,
    disabled: PropTypes.bool
  };

  constructor(props) {
    super(props);

    this.offsetHeight = 0;

    this.state = {
      selectedIndex: this.props.selectedIndex,
      segmentDimension: { width: 0, height: 0 },
      activeSegmentPosition: { x: this.offsetHeight, y: this.offsetHeight },
      positionAnimationValue: new Animated.Value(0)
    };
  }

  componentDidUpdate(pervProps) {
    if (this.props.selectedIndex !== pervProps.selectedIndex) {
      this.onSegmentSelection(this.props.selectedIndex);
    }
  }

  /**
   * On segment change event.
   *
   * @param {Number} index
   */
  onSegmentSelection = (index) => {
    const animate = () => {
      Animated.timing(this.state.positionAnimationValue, {
        toValue: this.state.activeSegmentPosition.x,
        duration: 150,
        easing: Easing.ease
      }).start(() => { });
    };
    this.props.onChange && this.props.onChange(index);

    this.setState(
      (prevState) => ({
        selectedIndex: index,
        activeSegmentPosition: { x: prevState.segmentDimension.width * index + this.offsetHeight, y: prevState.activeSegmentPosition.y }
      }),
      animate
    );
  }

  /**
   * @param {Object} event
   */
  segmentOnLayout = (event) => {
    const { width, height } = event.nativeEvent.layout;
    const segmentWidth = (width - this.offsetHeight * 2) / this.props.values.length;

    const animate = () => {
      Animated.timing(this.state.positionAnimationValue, {
        toValue: segmentWidth * this.state.selectedIndex + this.offsetHeight,
        duration: 0
      }).start();
    };

    this.setState(() => ({
      segmentDimension: { width: segmentWidth, height }
    }), animate);
  }

  render() {
    const { width, height } = this.state.segmentDimension;
    const segmentHeight = height - this.offsetHeight * 2;
    const opacity = this.props.disabled ? Opacity.Disabled : Opacity.Normal;

    return (
      <View style={[{    
        marginTop: Constants.DEFAULT_MARGIN_TOP_BOTTOM, 
        opacity
      }, this.props.style]}
      pointerEvents={this.props.disabled ? "none" : "auto"}
      >
        {this.props.title ? <Text style={[styles.titleTextStyle, { marginBottom: DEFAULT_MARGIN }]}>{this.props.title}</Text> : null}
        <View
          style={[styles.borderStyle, { flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center', height: DEFAULT_SEGMENT_HEIGHT }]}
          onLayout={this.segmentOnLayout}
        >
          {this.props.values && 
            this.props.values.map((segment, index) => {
              return (
                // eslint-disable-next-line react/jsx-key
                <Segment
                  style={{ flex: 1, height: segmentHeight }}
                  icon={segment.icon}
                  title={segment.title}
                  textStyle={index !== this.state.selectedIndex ? styles.buttonTextStyle : styles.buttonHightLightTextStyle}
                  onPress={() => this.onSegmentSelection(index)}
                  isSelected={this.state.selectedIndex === index}
                />
              );  
            })}
          <Animated.View
            style={[
              {
                flex: 1,
                zIndex: 5,
                position: 'absolute',
                width,
                height: segmentHeight,
                left: this.state.positionAnimationValue,
                top: this.state.activeSegmentPosition.y,
                borderRadius: Radius.WidgetLevel
              },
              styles.itemHighlightStyle
            ]}
          />
        </View>
        {this.props.tipsArray ? <Text numberOfLines={1} style={[{ marginTop: DEFAULT_MARGIN }, styles.subTitleTextStyle]}>{this.props.tipsArray[this.state.selectedIndex]}</Text> : null }
      </View>
    );
  }
}

const Segment = ({ icon, title, style, textStyle, isSelected, onPress }) => {
  return (
    <TouchableOpacity style={[styles.buttonBaseStyle, { zIndex: 10, marginHorizontal: 24 }, style]} onPress={onPress}>
      <Image style={ [textStyle, { width: 48, height: 48,
        tintColor: DarkMode.getColorScheme() === 'dark' || isSelected ? null : CarColor.Light.handColorHint
      }] } source={ icon }/>
      <MarqueeText
        speed={70}
        numberOfLines={1}
        text={ title || LocalizedStrings['car_grid_no_function'] }
        isSelected={isSelected}
        style={{
          marginTop: 8,
          color: DarkMode.getColorScheme() === 'dark' || isSelected ? CarColor.Dark.handTitleColor : CarColor.Light.handTitleColor,
          fontSize: Font.Size._24
        }}
      />
    </TouchableOpacity>
  );
};

Segment.propTypes = {
  icon: PropTypes.string.isRequired,
  title: PropTypes.string.isRequired,
  textStyle: ViewPropTypes.style.isRequired,
  isSelected: PropTypes.bool.isRequired,
  onPress: PropTypes.func.isRequired,
  style: ViewPropTypes.style
};

export default ImageSegmentControl;
export { ImageSegmentControl };