import PushHandler, { Go2LiveLimit, DBDlg } from "./PushHandler";
import Util from "../util2/Util";

/*
Android push
{"did": "329595064", "event": "device_doorbell_event",

"extra": "[{\"createTime\":1613614291000,\"fileId\":\"54028760605266176\",
\"fileType\":\"VIDEO\",\"offset\":0,\"pushPattern\":\"default\"}]",

"extra_click_device_time": "1613614298012", "isNotified": "true",
"model": "madv.cateye.dlowlse2", "openTime": "1613614298012", "open_plugin_cached": "true", "open_plugin_click_start_time": "1613614298004",
"package_msgType": "2", "time": "1613614292", "type": "ScenePush"}

Ios Push
{"event": "device_doorbell_event", "eventName": "kMHPluginReceivingForegroundPushEvent", "time": 1613631953,
"value": [{"createTime": 1613631954000, "fileId": "54038019831763200", "fileType": "VIDEO", "offset": 0, "pushPattern": "default"}]}
*/
/*
demolition_event	   强拆推送
bellvideo_event	   视频留言推送
someone_stay	   有人停留推送
someone_appear	   有人出现推送
device_upgrade_fail	升级失败
device_upgrade_success	升级成功
device_appear_event	有人出现事件
device_somestay_event	有人在门前停留
device_someappear_event	有人在门前出现
device_newpower_event	门铃更换了电池
visit_linkage	按门铃智能联动和联动小爱触屏音箱事件
device_upgrade_event	固件升级事件
device_stay_event	有人停留事件
device_pass_event	有人经过事件
device_nopower_event	电量耗尽事件
device_lowpower_event	低电量事件
device_doorbell_event	按门铃事件
device_demolition_event	拆除报警事件
device_bellvideo_event	视频留言事件
*/

const TAG = "ProfPushHandler";

class _ProfPushHandler extends PushHandler {

  async handlePush(aPush, aIsInLive, aRootPush) {
    let showDoorBell = DBDlg.None;
    let goLivePlay = false;
    let goVideoPlay = false;
    let videoFileId = null;
    let createTime = null;
    let extra = null;
    let type = aPush.type;
    // if jump from notify will not show dlg
    // ios lack of isNotified field
    let isFromNotify = Util.isAndroid() ? aPush.isNotified.toString().toLowerCase() === 'true' : false;
    let event = isFromNotify ? null : aPush.event;
    if (!Util.isAndroid()) {
      type = "ScenePush";
      aPush.did = "holder";
    }
    try {
      extra = Util.isAndroid() ? JSON.parse(aPush.extra) : aPush.value;
    } catch (err) {
      console.log(TAG, "parse extra error", err);
    }

    let data = null;
    // 有的消息体是object  有的是array
    if (extra instanceof Array) {
      data = extra[0];
    } else {
      data = extra;
    }

    if (aIsInLive || null == data
      || !(type === "ScenePush" && (event === "device_doorbell_event" || event === "device_warning_event" || data.fileId != null) && aPush.did)) {
      return Promise.resolve({ showDoorBell, goLivePlay, goVideoPlay, videoFileId, createTime });
    } else {

      let pushPattern = "default";
      if (data.pushPattern) {
        pushPattern = data.pushPattern.toLowerCase();
      }

      createTime = data.createTime ? data.createTime : aPush.time * 1000;
      videoFileId = data.fileId;
      if (Math.abs(Date.now() - createTime) < Go2LiveLimit) { // push发出到实际点击过了2min，就先不管了吧
        goLivePlay = true;
      } else {
        goVideoPlay = true;
      }

      if (event === "device_doorbell_event") {
        if (pushPattern === "strong" && aRootPush) {
          showDoorBell = DBDlg.Big;
        } else {
          showDoorBell = DBDlg.Small;
        }
      } else {
        showDoorBell = DBDlg.None;
      }


      if (!Util.isAndroid() && aRootPush) {
        showDoorBell = DBDlg.None;
      }
      return Promise.resolve({ showDoorBell, goLivePlay, goVideoPlay, videoFileId, createTime });
    }
  }
}

const ProfPushHandler = new _ProfPushHandler();
export default ProfPushHandler;
