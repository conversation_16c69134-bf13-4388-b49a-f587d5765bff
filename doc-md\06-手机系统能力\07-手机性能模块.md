<a name="module_miot/system"></a>

## miot/system
手机的性能：内存不足警告

**Export**: public  
**Doc_name**: 手机性能模块  
**Doc_index**: 7  
**Doc_directory**: system  
**Example**  
```js
import {MemoryWarningEvent} from "miot"
...
MemoryWarningEvent.onMemoryWarning.addListener(//listener)
...
```

* * *

<a name="module_miot/system.MemoryWarningEvent"></a>

### miot/system.MemoryWarningEvent ⇒ <code>number</code>
应用内存不足时的通知

**Kind**: static constant of [<code>miot/system</code>](#module_miot/system)  
**Returns**: <code>number</code> - level 内存告警等级，只有 Android 才有，对应系统宏定义，包括:
5：TRIM_MEMORY_RUNNING_MODERATE；
10：TRIM_MEMORY_RUNNING_LOW；
15：TRIM_MEMORY_RUNNING_CRITICAL。
详见：https://developer.android.com/reference/android/content/ComponentCallbacks2  
**Since**: 10043  
**Example**  
```js
MemoryWarningEvent.onMemoryWarning.addListener(this.myListener);
```

* * *

