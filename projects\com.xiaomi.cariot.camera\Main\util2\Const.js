import {
  Dimensions
} from 'react-native';

export const SCREEN_WIDTH = Math.min(Dimensions.get('window').height, Dimensions.get('window').width);
export const SCREEN_HEIGHT = Math.max(Dimensions.get('window').height, Dimensions.get('window').width);

export const CALL_CAR_STATE = {
  NOT_INITIAL: "notInitiate",           // 未发起远程查看
  INITIAL: "initiate",                  // 发起远程查看
  CALL_END: "callEnd",                  // 通话结束，
  REFUSE: "refuse",                     // 车机端拒绝
  CAN_NOT_CONNECT: "canNotConnect",     // 发起后20s，变更为此状态
  NO_RESPONSE: "noResponse",            // 发起后60s，变更为此状态
  HANGUP: "hangup",                     // 车机端挂断、通话中挂断，异常来电打断、车机主动挂断
  BUSY: "busy",                         // 忙线中
  IN_CALLING: "inCalling"               // 正在通话中 @20250404新增
}

export const CALL_COMMAND = {
  CALLING: 'calling',
  CANCEL: 'cancel',
  HANGUP: 'hangup',
  REQUEST_STATUS: 'request_status'

}