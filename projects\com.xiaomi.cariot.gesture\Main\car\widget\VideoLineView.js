import React from 'react';
import { Image, SectionList, Text, TouchableWithoutFeedback, View } from "react-native";
import { CarFont, carStyles } from "../common/Styles";
import PropTypes from "prop-types";
import { Constants, Radius } from "micariot-ui-sdk";
import { DarkMode, Host } from "miot";
import Util from "../../util2/Util";
import DateFormater from "../../util2/DateFormater";
import LogUtil from "../../util/LogUtil";
import SdFileManager from "../../sdcard/util/SdFileManager";
import dayjs from "dayjs";
import eventSource from "../../../Resources/Images/car/event_pet.png";

const HeaderH = 380;
const SectionHeaderH = 78 + 24;
const SectionFooterH = 78 + 24;
const ItemOneImageW = 85;
const ItemEventImageHalfW = 16;
const ItemImageDividerW = 2;
const ItemDividerW = 24;
const TAG = "VideoLineView";
const LINE_TYPE = {
  SD: 0,
  CLOUD: 1
};

export class VideoLineView extends React.Component {
  constructor(props, context) {
    super(props, context);
    this.itemInfo = new Map();
    this.itemExpandInfo = new Map();
    this.scrollEndItem = null;
    this.clickCount = 0;

    this.isScrolling = false;
    this.isScrollingCount = 0;
    this.lastScrollDate = new Date().getTime();
    this.ignoreScroll = false;
    this.layoutList = [];
    this.isClick = false;
    this.clickNum = 0;
  }

  state = {
    expand: false
  };
  static propTypes = {
    data: PropTypes.any
  };

  static defaultProps = {
    data: []
  };

  componentDidUpdate(aPrevProps) {
    if (this.props.data !== aPrevProps.data) {
      console.log(TAG, "componentDidUpdate");
      // 随着数据的更新，会被调用
      this.getItemPosition();
    }

    if (this.props.type !== aPrevProps.type) {
      // 需要清空下
      this.itemInfo = new Map();
      this.itemExpandInfo = new Map();
    }
  }

  componentDidMount() {
    console.log(TAG, "componentDidMount");
  }

  componentWillUnmount() {
    this.scollingTimeout && clearTimeout(this.scollingTimeout);
    this.ignoreScrollDelay && clearTimeout(this.ignoreScrollDelay);
  }

  render() {
    return (
      <SectionList
        ref={ (ref) => {
          this.mSectionList = ref;
        } }
        horizontal={ true }
        inverted={ true }
        overScrollMode={"never"}
        showsHorizontalScrollIndicator={ false }
        sections={ this.props.data }
        keyExtractor={ (item, index) => `${ item.startTime }_${ index }` }
        renderItem={ this.renderItem }
        renderSectionFooter={ this.renderSectionFooter }
        ListFooterComponent={ this.renderFoot }
        ListHeaderComponent={ this.renderHeader }
        onScrollBeginDrag={() => {
          console.log("++++++我开始滚动了+++++++++");
          this.isScrolling = true;
        }}
        onScrollEndDrag={() => {
          console.log("++++++我滚动结束了+++++++++");
          this.isScrolling = false;
        }}
        onScroll={ this.onScroll }
        onMomentumScrollEnd={ this.onScrollEnd }
        getItemLayout={ this.mLayoutGetterV2 }
      />
    );
  }

  /**
   *
   * @param aSections
   * @param aIdx item项所在的index，ListFooterComponent ListHeaderComponent占用index
   *              renderSectionFooter renderSectionHeader 都占用index
   * @return {{offset, length: number, index: *}}
   */
  mLayoutGetter = (aSections, aIdx) => {
    console.log("============================", aSections.length, aIdx, aSections[0]?.data?.length, aSections[1]?.data?.length, aSections[2]?.data?.length);
    let pos = -1;
    let length = aIdx < 0 ? 0 : HeaderH;
    let offset = 0;
    let i = 0;
    let sectionIndex = 0;
    let itemIndex = 0;
    let isHeaderFooter = false;
    for (i = 0; i < aSections.length && pos < aIdx; ++i) {
      let data = aSections[i].data;
      sectionIndex = i;
      // account for section header and footer
      let nPos = i === 0 ? pos + data.length + 2 : pos + data.length + 1;
      if (nPos < aIdx) {
        // pos = nPos;
        // offset = offset + data.length * this.mCardH + HeaderH + FooterH;
        pos = nPos;
        offset = i === 0 ? offset + HeaderH + SectionFooterH : offset + SectionFooterH;
        for (let j = 0; j < data.length; j++) {
          offset = offset + this.getItemWidthByDuration(data[j].duration) + ItemEventImageHalfW + ItemDividerW;
        }
      } else if (nPos === aIdx) {
        pos = nPos;
        length = SectionFooterH;
        isHeaderFooter = true;
        // offset = offset + HeaderH + data.length * this.mCardH;
        offset = i === 0 ? offset + HeaderH : offset;
        for (let j = 0; j < data.length; j++) {
          offset = offset + this.getItemWidthByDuration(data[j].duration) + ItemEventImageHalfW + ItemDividerW;
        }
      } else {
        ++pos;// add section header
        if (pos == aIdx && aIdx === 0) { // hit section header
          // list的header
          // length = aIdx === 0 ? HeaderH : SectionFooterH;
          // list的头部和尾部
          length = HeaderH;
          isHeaderFooter = true;
        } else {
          offset = i === 0 ? offset + HeaderH : offset;
          if (aIdx <= pos + data.length - 1) { // in middle
            // 需要算data组中aIdx之前，所有的item的高度总和
            for (let j = 0; j < aIdx - pos - 1; j++) {
              // for (let j = 0; j < aIdx - pos; j++) {
              let childItem = data[j];
              offset = offset + this.getItemWidthByDuration(childItem.duration) + ItemEventImageHalfW + ItemDividerW;
            }
            // offset = offset + (aIdx - pos - 1) * this.mCardH;
            length = this.getItemWidthByDuration(data[aIdx - pos].duration) + ItemEventImageHalfW + ItemDividerW;
            itemIndex = aIdx - pos;
          } else { // last footer
            // offset = offset + (aIdx - pos) * this.mCardH;
            for (let j = 0; j < aIdx - pos; j++) {
              let childItem = data[j];
              offset = offset + this.getItemWidthByDuration(childItem.duration) + ItemEventImageHalfW + ItemDividerW;
            }
            isHeaderFooter = true;
            length = SectionFooterH;
          }
          pos = aIdx;
        }
      }


    }

    if (this.state.expand) {
      this.itemExpandInfo.set(aIdx, { index: aIdx, length, offset, itemIndex, sectionIndex, isHeaderFooter });
    } else {
      this.itemInfo.set(aIdx, { index: aIdx, length, offset, itemIndex, sectionIndex, isHeaderFooter });
    }
    let ret = { length, offset, index: aIdx };

    console.log("getItemLayout", aIdx, ret, pos, HeaderH, SectionFooterH, isHeaderFooter, sectionIndex, itemIndex);
    // console.log("itemInfo", this.itemInfo,this.itemExpandInfo);

    // let ret = { length: 85, offset: 85 * aIdx + 200, index: aIdx };

    return ret;
  };

  mLayoutGetterV22 = (aSections, aIdx) => {
    let layout = this.layoutList.filter((n) => n.index == aIdx)[0];
    if (!layout) {
      return { length: 85, offset: 85 * aIdx, index: aIdx };

    }
    return { length: layout.length, offset: layout.offset, index: aIdx };
  };

  mLayoutGetterV2 = (aSections, aIdx) => {
    // console.log("getItemLayout", aIdx);

    let lastIdx = 2;
    let sectionLength = aSections.length;
    for (let i = 0; i < sectionLength; i++) {
      lastIdx = lastIdx + aSections[i].data.length + 1;
    }
    lastIdx = lastIdx - 1;
    // console.log("lastIdx:", lastIdx);
    // 尾部footer
    // if (aIdx === lastIdx) {
    //   let offset = HeaderH * 2 + SectionFooterH * sectionLength;
    //   for (let i = 0; i < sectionLength; i++) {
    //     let section = aSections[i].data;
    //     for (let j = 0; j < section.length; j++) {
    //       let childItem = section[j];
    //       offset = offset + this.getItemWidthByDuration(childItem.duration) + ItemEventImageHalfW + ItemDividerW;
    //     }
    //   }
    //   return { length: HeaderH, offset: offset, index: aIdx };
    // }

    // 其他item,包含每组的section header
    let pos = -1;
    let length = aIdx < 0 ? 0 : HeaderH;
    let offset = 0;
    let i = 0;
    let sectionIndex = 0;
    let itemIndex = 0;
    let isHeaderFooter = false;
    for (let i = 0; i < aSections.length && pos < aIdx; i++) {
      let data = aSections[i].data;
      sectionIndex = i;
      let nPos = i === 0 ? pos + data.length + 2 : pos + data.length + 1;
      if (nPos < aIdx) {
        pos = nPos;
        offset = i === 0 ? offset + HeaderH + SectionFooterH : offset + SectionFooterH;
        for (let j = 0; j < data.length; j++) {
          offset = offset + this.getItemWidthByDuration(data[j].duration) + ItemEventImageHalfW + ItemDividerW;
        }
        if (nPos + 1 === aIdx && aIdx === lastIdx) {
          // 最后的list footer
          // console.log("this last is do");
          pos = aIdx;
          length = HeaderH;
          isHeaderFooter = true;
        } else if (nPos + 2 === aIdx && lastIdx < aIdx) {
          // console.log("this is do异常", offset);
          pos = aIdx;
          length = HeaderH;
          isHeaderFooter = true;
        }
      } else if (nPos === aIdx) {
        // section的footer
        pos = nPos;
        length = SectionFooterH;
        isHeaderFooter = true;
        offset = i === 0 ? offset + HeaderH : offset;
        for (let j = 0; j < data.length; j++) {
          offset = offset + this.getItemWidthByDuration(data[j].duration) + ItemEventImageHalfW + ItemDividerW;
        }
      } else {

        // list header
        if (aIdx === 0) {
          length = HeaderH;
          isHeaderFooter = true;
        } else {
          // in middle
          if (pos === -1) {
            ++pos;
          }
          offset = i === 0 ? offset + HeaderH : offset;
          if (aIdx <= pos + data.length) { // in middle
            for (let j = 0; j < aIdx - pos - 1; j++) {
              // for (let j = 0; j < aIdx - pos; j++) {
              let childItem = data[j];
              offset = offset + this.getItemWidthByDuration(childItem.duration) + ItemEventImageHalfW + ItemDividerW;
            }
            length = this.getItemWidthByDuration(data[aIdx - pos - 1].duration) + ItemEventImageHalfW + ItemDividerW;
            itemIndex = aIdx - pos - 1;

          } else {
            console.log("why：", aIdx, pos);
            // last footer
            // offset = offset + (aIdx - pos) * this.mCardH;
            // for (let j = 0; j < aIdx - pos; j++) {
            //   let childItem = data[j];
            //   offset = offset + this.getItemWidthByDuration(childItem.duration) + ItemEventImageHalfW + ItemDividerW;
            // }
            // isHeaderFooter = true;
            // length = SectionFooterH;
          }
          pos = aIdx;
        }
      }
    }
    if (this.state.expand) {
      this.itemExpandInfo.set(aIdx, { index: aIdx, length, offset, itemIndex, sectionIndex, isHeaderFooter });
    } else {
      this.itemInfo.set(aIdx, { index: aIdx, length, offset, itemIndex, sectionIndex, isHeaderFooter });
    }
    let ret = { length, offset, index: aIdx };

    // console.log("getItemLayout", aIdx, ret, pos, HeaderH, SectionFooterH, isHeaderFooter, sectionIndex, itemIndex);

    return ret;

  };

  // 滚动中找出处于中间位置的item
  getMiddleItemByScrollX(scrollX) {

  }

  getItemPosition() {
    let list = this.props.data;
    let layoutList = [];
    let layoutIndex = 0;
    let layoutOffset = 0;
    let sIndex = 0;
    let cIndex = 0;
    // 添加一个list header
    layoutList.push({
      sectionIndex: -1,
      index: layoutIndex,
      length: HeaderH,
      offset: layoutOffset,
      itemIndex: 0,
      isHeaderFooter: true
    });
    layoutIndex += 1;
    layoutOffset = HeaderH;
    list.forEach((section, sectionIndex) => {
      sIndex = sectionIndex;
      console.log("++++++++sectionIndex", sectionIndex, section.data.length);
      // 52257
      section.data.forEach((item, itemIndex) => {
        cIndex = itemIndex;

        let itemHeight = this.getItemWidthByDuration(item.duration) + ItemEventImageHalfW + ItemDividerW;
        layoutList.push({
          index: layoutIndex,
          length: itemHeight,
          offset: layoutOffset,
          sectionIndex: sIndex,
          itemIndex: itemIndex,
          isHeaderFooter: false
        });
        // console.log("++++++++item",sectionIndex,layoutIndex,item.duration,itemHeight,itemIndex,layoutOffset);

        layoutIndex += 1;
        layoutOffset += itemHeight;
      });
      // 添加一个section footer
      layoutList.push({
        index: layoutIndex,
        length: SectionFooterH,
        offset: layoutOffset,
        sectionIndex: sIndex,
        itemIndex: cIndex + 1,
        isHeaderFooter: true
      });
      // console.log("++++++++footer",sectionIndex,layoutIndex,SectionFooterH,layoutOffset);

      layoutIndex += 1;
      layoutOffset += SectionFooterH;
    });
    layoutList.push({
      index: layoutIndex,
      length: HeaderH,
      offset: layoutOffset,
      sectionIndex: sIndex,
      itemIndex: cIndex + 1,
      isHeaderFooter: true
    });
    // bug 会多一个出来
    layoutList.push({
      index: layoutIndex + 1,
      length: HeaderH,
      offset: layoutOffset,
      sectionIndex: sIndex,
      itemIndex: cIndex + 1,
      isHeaderFooter: true
    });
    console.log("==============getItemPosition:", layoutIndex, layoutOffset);
    this.layoutList = layoutList;
  }

  getItemPositionInfo(sectionIndex, itemIndex) {
    return this.layoutList.find((item) => itemIndex === item.itemIndex && sectionIndex === item.sectionIndex);
  }

  getItemWidthByDuration(duration) {
    if (this.props.type === LINE_TYPE.SD) {
      duration = duration / 1000;
    }
    if (duration < 300) {
      // 小于5分钟 默认展示1张
      return this.state.expand ? ItemImageDividerW + ItemOneImageW * 2 : ItemOneImageW;
    } else if (duration < 3600) {
      // 小于1小时 默认展示3张
      return this.state.expand ? ItemImageDividerW * 5 + ItemOneImageW * 6 : ItemImageDividerW * 2 + ItemOneImageW * 3;
    } else {
      // 大于等于1小时 默认展示6张图片
      return this.state.expand ? ItemImageDividerW * 11 + ItemOneImageW * 12 : ItemImageDividerW * 5 + ItemOneImageW * 6;
    }
  }

  onScroll = (e) => {
    this.scrollY = e.nativeEvent.contentOffset.y;
    this.scrollX = e.nativeEvent.contentOffset.x;
    console.log(`onScroll scrollY:${ this.scrollY } scrollX: ${ this.scrollX }`);
    if (this.ignoreScroll) {
      console.log("+++++++++++scroll is ignore");
      return;
    }
    // this.isScrollingCount++;
    // this.isScrolling = true;
    // this.scollingTimeout && clearTimeout(this.scollingTimeout);
    // this.scollingTimeout = setTimeout(() => {
    //   // 短时间内、多次滚动调用后视为滚动
    //   if (this.isScrollingCount < 4) {
    //     this.isScrolling = false;
    //     this.isScrollingCount = 0;
    //   }
    // }, 300);

  };

  onScrollEnd = (e) => {
    console.log("onScrollEnd", e.nativeEvent.contentOffset.x);
    let scrollX = e.nativeEvent.contentOffset.x;
    this.isScrolling = false;
    let itemsInfo;
    if (this.state.expand) {
      itemsInfo = this.itemExpandInfo;
    } else {
      itemsInfo = this.itemInfo;
    }
    if (itemsInfo == null || itemsInfo.size <= 0) {
      return;
    }
    let infoKeys = [...itemsInfo.keys()];
    if (infoKeys == null || infoKeys.length == 0) {
      return;
    }

    infoKeys.sort((a1, a2) => {
      return a1 - a2;
    });
    console.log("++++++++++", infoKeys, this.layoutList);
    // infoKeys 长度至少有5个
    if (infoKeys.length < 3) {
    // if (infoKeys.length < 4) {
      LogUtil.logOnAll("list length is abnormal");
      return;
    }
    let middleItemInfo = null;
    let middleScroll = false;
    let playStart = false;
    // 查找滚动后，处于中间位置的item
    for (let i = 0; i < infoKeys.length; i++) {
      let itemInfo = itemsInfo.get(infoKeys[i]);
      let leftOffset = itemInfo.offset + itemInfo.length - HeaderH;
      let rightOffset = itemInfo.offset - HeaderH;
      // console.log("====:",leftOffset,rightOffset,scrollX);
      if (scrollX >= rightOffset && scrollX <= leftOffset) {
        middleScroll = true;
        // 处于此item中
        middleItemInfo = itemInfo;

        if (itemInfo.isHeaderFooter) {
          playStart = true;
          if (itemInfo.offset == 0 && i + 1 < infoKeys.length) {
            // 最右侧view
            middleItemInfo = itemsInfo.get(infoKeys[i + 1]);
          } else {
            // 辅助item，找前一个作为中间位置
            if (i - 1 >= 0) {
              middleItemInfo = itemsInfo.get(infoKeys[i - 1]);
              if (middleItemInfo.isHeaderFooter && i - 2 >= 0) {
                // 还是说明滚动到最后去了
                middleItemInfo = itemsInfo.get(infoKeys[i - 2]);
              }
            }
          }


        }

        break;
      }
    }
    if (middleItemInfo == null) {
      console.log("+++++++++++++++middleItemInfo is null");
      return;
    }
    if (!this.props.data
      || this.props.data.length <= middleItemInfo.sectionIndex
      || !this.props.data[middleItemInfo.sectionIndex].data
      || this.props.data[middleItemInfo.sectionIndex].data.length <= middleItemInfo.itemIndex) {
      return;
    }
    this.scrollEndItem = this.props.data[middleItemInfo.sectionIndex].data[middleItemInfo.itemIndex];

    let offset = 0;
    // 计算视频播放偏移量
    if (!playStart) {
      //
      let usefulWidth = middleItemInfo.length - ItemEventImageHalfW - ItemDividerW;
      if (scrollX >= middleItemInfo.offset + usefulWidth - HeaderH) {
        offset = 0;
      } else {
        let unitWidth = this.scrollEndItem.duration / usefulWidth;
        // let leftWidth = middleItemInfo.length - (scrollX - (middleItemInfo.offset - HeaderH));
        // 380+299=679
        let leftWidth = usefulWidth - (scrollX - (middleItemInfo.offset - HeaderH));
        offset = Number.parseInt(leftWidth * unitWidth);
        console.log("}}}}}}}}}}}=>", usefulWidth, unitWidth, leftWidth);
      }
    }
    // console.log("=================",this.scrollEndItem, middleItemInfo,offset);

    // 需要通知出去，播放或者标记位置信息
    this.props.onScrollEnd && this.props.onScrollEnd(this.scrollEndItem, middleItemInfo, offset);

  };

  _onViewableItemsChanged = ({ viewableItems }) => {
    // console.log("_onViewableItemsChanged:", viewableItems);
    if (viewableItems.length > 0) {
      const middleIndex = Math.floor(viewableItems.length / 2);
      // console.log("==================",viewableItems[middleIndex].item, middleIndex);
    }
  };

  renderItem = ({ item, index, section }) => {
    let images = item.images;
    if (images) {
      if (!this.state.expand) {
        images = images.filter((_, index) => index % 2 === 0);
      } else {
        if (images.length == 1) {
          images.push(images[0]);
        }
      }
    } else {
      images = [];
      if (item.duration < 300) {
        images = !this.state.expand ? [0] : [0, 1];
      } else if (item.duration < 3600) {
        images = !this.state.expand ? [0, 1, 2] : [0, 1, 2, 3, 4, 5];
      } else if (item.duration >= 3600) {
        images = !this.state.expand ? [0, 1, 2, 3, 4, 5] : [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];
      }
    }
    let eventSource = require('../../../Resources/Images/car/event_pet.png');
    if (this.props.type === LINE_TYPE.CLOUD) {
      eventSource = item.eventType == "Pet" ? require('../../../Resources/Images/car/event_pet.png') : require('../../../Resources/Images/car/event_child.png');
    } else {
      eventSource = item.eventType == "2"
        ? require('../../../Resources/Images/car/event_pet.png') : item.eventType == "42" ? require('../../../Resources/Images/car/event_child.png') : null;
    }
    return (
      <TouchableWithoutFeedback
        key={`${ item.startTime }_${ index }`}
        onPress={ () => this.handlingTap(item, index, section) }>
        <View
          style={ { flexDirection: 'row', alignItems: 'center', marginLeft: Constants.DEFAULT_TEXT_MARGIN_BOTTOM } }>

          <View style={ {
            borderRadius: Radius.WidgetLevel,
            overflow: 'hidden',
            marginLeft: 16,
            height: 48,
            flexDirection: 'row',
            backgroundColor: DarkMode.getColorScheme() === 'dark' ? "xm#C1CCE21F" : "#EEEEEE"
          } }>
            {
              images.map((imagesItem, cIndex) => {
                let imgSource;
                if (this.props.type === LINE_TYPE.SD) {
                  let path = SdFileManager.getInstance().getImageFilePath(imagesItem.startTime);
                  imgSource = (path == null) ? null : ({ uri: `file://${ Host.file.storageBasePath }${ path }` });

                } else {
                  const imgStoreUrl = item.imgStoreUrl;
                  imgSource = imgStoreUrl;
                  if (imgStoreUrl) {
                    if (Util.isLocalThumb(imgStoreUrl, item.mediaType)) {
                      imgSource = { uri: imgStoreUrl };
                    } else {
                      imgSource = { uri: `file://${ imgStoreUrl }` };
                    }
                  }
                }
                // console.log("0000000000000=>", imgSource);
                return (
                  <View
                    key={ `image_${ index }_${ cIndex }` }
                    style={ { flexDirection: 'row' } }>
                    {/* { */}
                    {/*  cIndex != 0 ? <View style={ { */}
                    {/*    width: 2, */}
                    {/*    backgroundColor: 'xm#000000' */}
                    {/*  } }></View> : null */}
                    {/* } */}
                    <View style={{
                      borderLeftWidth: cIndex != 0 ? 2 : 0,
                      borderLeftColor: "xm#000000"
                    }}>
                      <Image
                        style={ [{
                          width: 85,
                          height: 48
                        }, { backgroundColor: DarkMode.getColorScheme() === 'dark' ? "xm#4C4D51" : "#EEEEEE" }] }
                        resizeMode="stretch"
                        source={ imgSource }/>
                    </View>

                  </View>

                );
              })
            }

          </View>
          <Image
            style={ { height: 32, width: 32, position: 'absolute', left: 0 } }
            resizeMode="contain"
            source={ eventSource }/>
          {/* <Text style={ { height: 34, width: 32, position: 'absolute', left: 0 } }>{index}</Text> */ }
        </View>
      </TouchableWithoutFeedback>
    );
  };

  handlingTap(item, index, section) {
    let sectionIndex = this.props.data.findIndex((item) => section.title === item.title);
    console.log("{{{{{{{{{{{{", index, sectionIndex);
    // this.clickCount === 1
    //   ? this.doubleClick(item,index) // This catches double click
    //   : this.setState(state => ({ clickCount: state.clickCount + 1 }), () => {
    //     setTimeout(() => {
    //       if (this.state.clickCount !== 2) {
    //         this.oneClick(item,index) // this catches one click
    //       }
    //       // this.setState({ clickCount: 0 })
    //       this.clickCount = 0;
    //     }, 300);
    //   });

    // this.clickCount === 1
    //   ? this.doubleClick(item, index, sectionIndex) // This catches double click
    //   : this.handleClickAction(item, index, sectionIndex);

    this.clickNum++;
    // 毫秒内点击过后阻止执行定时器
    if (this.isClick) {
      return;
    }
    // 毫秒内第一次点击
    this.isClick = true;
    setTimeout(() => {
      // 超过1次都属于双击
      if (this.clickNum > 1) {
        console.log("=========doubleClick");
        this.doubleClick(item, index, sectionIndex);
      } else {
        console.log("=========oneClick");
        this.oneClick(item, index, sectionIndex);
      }
      this.clickNum = 0;
      this.isClick = false;
    }, 300);

  }

  handleClickAction(item, index, sectionIndex) {
    this.clickCount = this.clickCount + 1;
    setTimeout(() => {
      if (this.clickCount !== 2) {
        this.oneClick(item, index, sectionIndex); // this catches one click
        this.isScrolling = false;
      }
      this.clickCount = 0;
    }, 300);
  }

  doubleClick(item, index, sectionIndex) {
    console.log("++++++++double click");
    this.setState({ expand: !this.state.expand }, () => {
      this.getItemPosition(this.props.data);
      this.props.scaleChange && this.props.scaleChange();
    });
  }

  oneClick(item, index, sectionIndex) {
    console.log("++++++++", item, index);
    // let sectionIndex = this.props.data.findIndex(item => item.title === section.title);
    console.log("========sectionIndex:", sectionIndex);
    let itemsInfo = this.itemInfo;
    if (this.state.expand) {
      itemsInfo = this.itemExpandInfo;
    }
    // 找到数据item位置信息，后面播放中滚动会用到
    if (itemsInfo == null || itemsInfo.size <= 0) {
      return;
    }
    let infoValues = [...itemsInfo.values()];
    if (infoValues == null || infoValues.length == 0) {
      return;
    }
    // console.log("infoValues:",infoValues);
    let positionInfo = infoValues.find((item) => item.itemIndex === index && item.sectionIndex === sectionIndex && !item.isHeaderFooter);
    if (positionInfo == null) {
      return;
    }

    this.props.onItemPress && this.props.onItemPress(item, positionInfo);

    let viewOffset = HeaderH - (positionInfo.length - ItemDividerW - ItemEventImageHalfW);
    // let viewOffset = - (positionInfo.length - ItemDividerW - ItemEventImageHalfW);

    // let scrollParam = { animated: false, sectionIndex, itemIndex: positionInfo.index, viewPosition: 0, viewOffset: viewOffset };
    // 为什么会是前一个item滚动到顶部？？？？ 云存和SD卡回看还不一致？？？？
    // let itemIndex = this.props.type === LINE_TYPE.SD ? positionInfo.itemIndex + 1 : positionInfo.itemIndex;
    let itemIndex = sectionIndex === 0 ? positionInfo.itemIndex + 1 : positionInfo.itemIndex;
    let scrollParam = {
      animated: false,
      sectionIndex,
      itemIndex: positionInfo.itemIndex,
      viewPosition: 0,
      viewOffset: 0
    };
    console.log("++++++++++++oneClick", positionInfo, viewOffset, sectionIndex, scrollParam);
    let offset;
    if (HeaderH > positionInfo.length) {
      offset = positionInfo.offset - (HeaderH - positionInfo.length) - ItemDividerW - ItemEventImageHalfW;
    } else {
      offset = positionInfo.offset + (positionInfo.length - HeaderH) - ItemDividerW - ItemEventImageHalfW;
    }
    // this.mSectionList && this.mSectionList._wrapperListRef._listRef.scrollToOffset({ animated: false, offset: offset });
    // this.scrollToLocation(scrollParam);
    this.scrollToOffset({ animated: false, offset: offset });
  }

  // 取时间轴上最新的那条数据
  getLastVideoInfo() {
    let itemInfo = this.state.expand ? this.itemExpandInfo : this.itemInfo;
    console.log(TAG, "getLastVideoInfo", this.props.data.length);
    if (this.props.data.length === 0 || this.props.data[0].data.length === 0 || itemInfo.size <= 0) {
      return { item: null, itemInfo: null };
    }

    let item = this.props.data[0].data[0];
    let positionInfo = itemInfo.get(1);
    return { item, itemInfo: positionInfo };
  }

  /**
   * @Author: byh
   * @Date: 2024/12/18
   * @explanation:
   * find the timestamp in video
   ******************************************************** */
  findItemInfoByTimestamp(timestamp) {
    // 所在分组
    let sectionIndex = this.props.data.findIndex((item) => {
      let dStr1 = dayjs.unix(item.title / 1000).format('MM/DD/YYYY');
      let dStr2 = dayjs.unix(timestamp / 1000).format('MM/DD/YYYY');
      return dStr1 === dStr2;
    });

    if (sectionIndex == -1) {
      return { item: null, itemInfo: null };
    }

    let sections = this.props.data[sectionIndex].data;
    if (sections.length <= 0) {
      return { item: null, itemInfo: null };
    }

    let itemIndex = -1;
    let item = null;
    
    // 降序
    for (let i = 0; i < sections.length; i++) {
      if (sections[i].startTime <= timestamp && timestamp <= sections[i].endTime) {
        itemIndex = i;
        item = sections[i];
        break;
      }
    }

    LogUtil.logOnAll(TAG, "findItemInfoByTimestamp", sectionIndex, itemIndex);
    if (itemIndex == -1) {
      return { item: null, itemInfo: null };
    }
    let positionInfo = this.layoutList.find((item) => item.sectionIndex === sectionIndex && item.itemIndex === itemIndex);
    LogUtil.logOnAll(TAG, "findItemInfoByTimestamp positionInfo:", positionInfo);
    if (positionInfo == null) {
      return { item: null, itemInfo: null };
    }
    return { item: item, itemInfo: positionInfo };
  }

  /**
   * @Author: byh
   * @Date: 2024/12/17
   * @explanation:
   * find last item info by timestamp
   ******************************************************** */
  findLastItemInfoByTimestamp(timestamp) {

    let sectionIndex = this.props.data.findIndex((item) => {
      let dStr1 = dayjs.unix(item.title / 1000).format('MM/DD/YYYY');
      let dStr2 = dayjs.unix(timestamp / 1000).format('MM/DD/YYYY');
      console.log("++++++++++++sectionIndex -1", dStr1, dStr2);

      return dStr1 === dStr2;
    });

    if (sectionIndex == -1) {
      console.log("++++++++++++sectionIndex -1");
      return { item: null, itemInfo: null };
    }
    let itemIndex = this.props.data[sectionIndex].data.length - 1;
    let item = this.props.data[sectionIndex].data[itemIndex];
    let itemsInfo = this.itemInfo;
    if (this.state.expand) {
      itemsInfo = this.itemExpandInfo;
    }
    let infoValues = [...itemsInfo.values()];
    console.log("+++++++++++itemIndex+", itemIndex, sectionIndex, infoValues);
    let positionInfo = this.layoutList.find((item) => item.sectionIndex === sectionIndex && item.itemIndex === itemIndex);
    console.log("++++++++++++positionInfo", positionInfo);

    return { item: item, itemInfo: positionInfo };

  }

  scrollToDayStartByTimestamp(timestamp, notify = false) {
    // 1、找到这个时间戳所在的section
    // 2、这个section中，最后的那个视频
    // 3、取这个item的位置信息
    // 4、滚动到这个item
    // 5、通知出去

    // let sectionIndex = this.props.data.findIndex((item) => section.title === item.title);
    // console.log("scrollToDayStartByTimestamp", this.props.data);
    // let sectionIndex = this.props.data.findIndex((item) => {
    //   let dStr1 = dayjs.unix(item.title / 1000).format('MM/DD/YYYY');
    //   let dStr2 = dayjs.unix(timestamp / 1000).format('MM/DD/YYYY');
    //   console.log("++++++++++++sectionIndex -1", dStr1, dStr2);
    //
    //   return dStr1 === dStr2;
    // });
    //
    // if (sectionIndex == -1) {
    //   console.log("++++++++++++sectionIndex -1");
    //   return;
    // }
    // let itemIndex = this.props.data[sectionIndex].data.length - 1;
    // let item = this.props.data[sectionIndex].data[itemIndex];
    // let itemsInfo = this.itemInfo;
    // if (this.state.expand) {
    //   itemsInfo = this.itemExpandInfo;
    // }
    // let infoValues = [...itemsInfo.values()];
    // console.log("+++++++++++itemIndex+", itemIndex, sectionIndex, infoValues);
    // let positionInfo2 = infoValues.find((item) => item.sectionIndex === sectionIndex && item.itemIndex === itemIndex);
    // let positionInfo = this.layoutList.find((item) => item.sectionIndex === sectionIndex && item.itemIndex === itemIndex);
    // console.log("++++++++++++positionInfo", positionInfo, positionInfo2);
    let { item, itemInfo } = this.findLastItemInfoByTimestamp(timestamp);
    if (item == null || itemInfo == null) {
      return;
    }

    let viewOffset = HeaderH - (itemInfo.length - ItemDividerW - ItemEventImageHalfW);
    console.log("++++++++++++scrollToTimestampWithNotify", itemInfo, viewOffset);
    let itemScrollIndex = itemInfo.itemIndex;

    let scrollParam = {
      animated: false,
      sectionIndex: itemInfo.sectionIndex,
      itemIndex: itemScrollIndex,
      viewPosition: 0,
      viewOffset: viewOffset
    };
    console.log("++++++++++++scrollParam", scrollParam);
    let offset;
    if (HeaderH > itemInfo.length) {
      offset = itemInfo.offset - (HeaderH - itemInfo.length) - ItemDividerW - ItemEventImageHalfW;
    } else {
      offset = itemInfo.offset + (itemInfo.length - HeaderH) - ItemDividerW - ItemEventImageHalfW;
    }
    this.scrollToOffset({ animated: false, offset: offset });
    // 通知外部
    this.props.onScrollEnd && this.props.onScrollEnd(item, itemInfo, 0);

  }

  scrollToPositionByTimestamp(item, itemInfo, timestamps) {
    if (item == null || itemInfo == null || !timestamps || this.isScrolling) {
      LogUtil.logOnAll(TAG, "scroll not do", this.isScrolling, !timestamps, itemInfo);
      return;
    }
    let viewOffset = HeaderH - (itemInfo.length - ItemDividerW - ItemEventImageHalfW);
    // 云存的duration单位是s，SD卡的是ms
    let duration = item.duration;
    let unitWidth = (itemInfo.length - ItemDividerW - ItemEventImageHalfW) / duration;
    let playTime = timestamps - item.startTime;
    playTime = this.props.type === LINE_TYPE.SD ? playTime : playTime / 1000;
    let playOffset = playTime * unitWidth;
    viewOffset = viewOffset + playOffset;
    let offset;
    if (HeaderH > itemInfo.length) {
      offset = itemInfo.offset - (HeaderH - itemInfo.length) - ItemDividerW - ItemEventImageHalfW;
    } else {
      offset = itemInfo.offset + (itemInfo.length - HeaderH) - ItemDividerW - ItemEventImageHalfW;
    }
    offset = offset - playOffset;
    this.scrollToOffset({ animated: false, offset: offset });
    // let scrollParam = { animated: false, sectionIndex: itemInfo.sectionIndex, itemIndex: itemInfo.index, viewPosition: 0, viewOffset: viewOffset };
    // this.scrollToLocation(scrollParam);
  }

  // 滚动到时间轴的最右侧
  scrollToEnd() {
    let scrollParam = { animated: false, sectionIndex: 0, itemIndex: 0, viewPosition: 0, viewOffset: 0 };
    this.scrollToLocation(scrollParam);
  }

  scrollToOffset(scrollParam = { animated: false, offset: 0 }) {
    if (this.mSectionList != null) {
      this.mSectionList && this.mSectionList._wrapperListRef._listRef.scrollToOffset(scrollParam);
      // this.ignoreScrollDelay && clearTimeout(this.ignoreScrollDelay);
      // this.ignoreScrollDelay = setTimeout(() => this.ignoreScroll = false, 100); // 延迟重置标志位
    }
  }

  scrollToLocation(scrollParam = { animated: false, sectionIndex: 0, itemIndex: 0, viewPosition: 0, viewOffset: 0 }) {
    if (this.mSectionList != null) {
      // this.ignoreScroll = true;
      this.mSectionList.scrollToLocation(scrollParam);
      // this.ignoreScrollDelay && clearTimeout(this.ignoreScrollDelay);
      // this.ignoreScrollDelay = setTimeout(() => this.ignoreScroll = false, 100); // 延迟重置标志位
    }
  }

  onMovePrev(oldItem, itemPosInfo) {
    // 查找上一个视频
    let itemsInfo;
    if (this.state.expand) {
      itemsInfo = this.itemExpandInfo;
    } else {
      itemsInfo = this.itemInfo;
    }
    if (itemsInfo == null || itemsInfo.size <= 0) {
      return;
    }
    let infoKeys = [...itemsInfo.keys()];
    if (infoKeys == null || infoKeys.length == 0) {
      return;
    }

    infoKeys.sort((a1, a2) => {
      return a1 - a2;
    });

    console.log("+++=+=+=+==+=+==+==", infoKeys);
    let findItemInfo = null;
    // 从后往前找
    for (let i = 0; i < infoKeys.length; i++) {
      let itemInfo = itemsInfo.get(infoKeys[i]);

      if (itemInfo.index > itemPosInfo.index) {
        // 找的比这个大
        // 判断这个小的是否是正常的item
        if (itemInfo.isHeaderFooter) {
          continue;
        }
        findItemInfo = itemInfo;
        break;
      }

    }

    // 找到了
    if (findItemInfo != null) {
      let item = this.props.data[findItemInfo.sectionIndex].data[findItemInfo.itemIndex];
      this.oneClick(item, findItemInfo.itemIndex, findItemInfo.sectionIndex);
    }

  }

  onMoveNext(oldItem, itemPosInfo) {
    // 查找下一个视频
    let itemsInfo;
    if (this.state.expand) {
      itemsInfo = this.itemExpandInfo;
    } else {
      itemsInfo = this.itemInfo;
    }
    console.log("=================", itemPosInfo);
    if (itemsInfo == null || itemsInfo.size <= 0) {
      return;
    }
    console.log("=================0");

    let infoKeys = [...itemsInfo.keys()];
    if (infoKeys == null || infoKeys.length == 0) {
      return;
    }
    console.log("=================1");

    infoKeys.sort((a1, a2) => {
      return a1 - a2;
    });

    let findItemInfo = null;
    // 从后往前找
    for (let i = infoKeys.length - 1; i >= 0; i--) {
      let itemInfo = itemsInfo.get(infoKeys[i]);

      if (itemInfo.index < itemPosInfo.index) {
        // 找的比这个小的
        // 判断这个小的是否是正常的item
        if (itemInfo.isHeaderFooter) {
          continue;
        }
        findItemInfo = itemInfo;
        break;
      }
    }

    // 找到了
    if (findItemInfo != null) {
      console.log("=================xxxxx");

      let item = this.props.data[findItemInfo.sectionIndex].data[findItemInfo.itemIndex];
      this.oneClick(item, findItemInfo.itemIndex, findItemInfo.sectionIndex);
    }
  }

  renderSectionFooter = ({ section: { title } }) => {
    return (
      <View style={ {
        backgroundColor: carStyles.lineDayBg.backgroundColor,
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 29,
        width: 78,
        height: 30,
        borderRadius: 39,
        marginLeft: Constants.DEFAULT_TEXT_MARGIN_BOTTOM
      } }>
        <Text style={ { fontSize: CarFont.Size._22 } }>{ DateFormater.instance().formatTimestampToMMDD(title) }</Text>
      </View>
    );
  };

  renderSectionHeader = ({ section }) => {
    return (
      <View style={ {
        backgroundColor: carStyles.lineDayBg.backgroundColor,
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 29,
        width: 380,
        height: 30,
        borderRadius: 39
      } }>
        <Text style={ { fontSize: CarFont.Size._22 } }>{ "占位view使列表可以滚动到item滚动到顶部" }</Text>
      </View>
    );
  };

  renderHeader = (info) => {
    return (
      <View style={ {
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 29,
        width: 380,
        height: 30,
        borderRadius: 39
      } }>
      </View>
    );
  };

  renderFoot = () => {
    return (
      <View
        style={ { padding: 10, marginTop: 29, width: 380, height: 30, borderRadius: 39 } }>
      </View>
    );
  };
}