<a name="module_miot/system"></a>

## miot/system
摇一摇

**Export**: public  
**Doc_name**: 手机摇一摇模块  
**Doc_index**: 11  
**Doc_directory**: system  
**Example**  
```js
import {System} from "miot"
import {ShakeEvent} from "miot"
...
System.shake.startShakeListener().then((res) => {
    alert(`startShakeListener: ${ JSON.stringify(res) }`);
  })
...
 System.shake.stopShakeListener().then((res) => {
    alert(`stopShakeListener: ${ JSON.stringify(res) }`);
  })
...
```

* [miot/system](#module_miot/system)
    * _static_
        * [.ShakeEvent](#module_miot/system.ShakeEvent)
    * _inner_
        * [~IShake](#module_miot/system..IShake)
            * [.startShakeListener()](#module_miot/system..IShake+startShakeListener) ⇒ <code>Promise</code>
            * [.stopShakeListener()](#module_miot/system..IShake+stopShakeListener) ⇒ <code>Promise</code>


* * *

<a name="module_miot/system.ShakeEvent"></a>

### miot/system.ShakeEvent
监听摇一摇事件。需要先调用startShakeListener开始监听,注意及时取消监听

**Kind**: static constant of [<code>miot/system</code>](#module_miot/system)  
**Since**: 10045  
**Example**  
```js
ShakeEvent.onShake.addListener(() => {
      console.log(`ShakeEvent`);
    });
```

* * *

<a name="module_miot/system..IShake"></a>

### miot/system~IShake
**Kind**: inner interface of [<code>miot/system</code>](#module_miot/system)  

* [~IShake](#module_miot/system..IShake)
    * [.startShakeListener()](#module_miot/system..IShake+startShakeListener) ⇒ <code>Promise</code>
    * [.stopShakeListener()](#module_miot/system..IShake+stopShakeListener) ⇒ <code>Promise</code>


* * *

<a name="module_miot/system..IShake+startShakeListener"></a>

#### iShake.startShakeListener() ⇒ <code>Promise</code>
开始监听摇一摇

**Kind**: instance method of [<code>IShake</code>](#module_miot/system..IShake)  
**Returns**: <code>Promise</code> - 成功时：{code:0,message:'success'}
失败时：{code:xxx, message:"xxx" }  
**Since**: 10045  
**Example**  
```js
System.shake.startShakeListener().then((res) => {
    alert(`startShakeListener: ${ JSON.stringify(res) }`);
  }).catch((error) => {
    alert(`startShakeListener: ${ JSON.stringify(error) }`);
  });
```

* * *

<a name="module_miot/system..IShake+stopShakeListener"></a>

#### iShake.stopShakeListener() ⇒ <code>Promise</code>
停止监听加速度数据

**Kind**: instance method of [<code>IShake</code>](#module_miot/system..IShake)  
**Returns**: <code>Promise</code> - 成功时：{code:0,message:'success'}
失败时：{code:xxx, message:"xxx" }  
**Since**: 10045  
**Example**  
```js
System.shake.stopShakeListener().then((res) => {
    alert(`stopShakeListener: ${ JSON.stringify(res) }`);
  }).catch((error) => {
    alert(`stopShakeListener: ${ JSON.stringify(error) }`);
  });
```

* * *

