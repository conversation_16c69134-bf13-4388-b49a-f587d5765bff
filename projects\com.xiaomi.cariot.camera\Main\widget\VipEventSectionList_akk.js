import React from 'react';
import EventGrid, { FooterH } from "./EventGrid";
import EventCard, { CardHeight, CardMB } from "./EventCard";
import { BaseStyles } from "../BasePage";
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import { Order } from "../framework/EventLoaderInf";
import { Device } from 'miot';
import dayjs from 'dayjs';

/*
UI data form
[
section0 = {
  title: XXX,
  data:[itm1, itm2, itm3]
}
.....
sectionX = {
  title: XXX,
  data:[itm1, itm2, itm3]
}
]
*/
const TAG = "VipEventSectionList";
export const EvSectionFilter = (aEv) => {
  return false;
};

export function evList2SectionList(aEvL) {
  let sectionLst = [];
  let dic = {};
  for (let i = 0; i < aEvL.length; i++) {
    let item = aEvL[i];
    buildSection(dic, item);
  }
  for (let key in dic) {
    let section = {
      title: key,
      data: dic[key],
      selected: false
    };
    sectionLst.push(section);
  }
  return sectionLst;
}

function buildSection(aSecDict, aItm) {
  let dStr = dayjs.unix(aItm.createTime / 1000).format(LocalizedStrings["yyyymmdd"]);
  
  let sec = aSecDict[dStr];
  if (!sec) {
    console.log(TAG, "buildSection create", dStr);
    aSecDict[dStr] = [aItm];
    return dStr;
  } else {
    sec.push(aItm);
    return null;
  }
}

export default class VipEventSectionList extends EventGrid {
  constructor(aProps) {
    super(aProps);
    this.mCardH = CardHeight + CardMB;
    this.mSecHeaderBg = BaseStyles.mainBg.backgroundColor;
  }

  getInitScroll() {
    let ev = this.state.events;
    if (this.props.playingId) {
      for (let sectionIndex = 0; sectionIndex < ev.length; ++sectionIndex) {
        let section = ev[sectionIndex].data;
        if (section == null || section.length <= 0) return null;
        for (let itemIndex = 0; itemIndex < section.length; ++itemIndex) {
          let itm = section[itemIndex];
          let mPId = itm.offset != null ? `${ itm.fileId }_${ itm.offset }` : `${ itm.fileId }`;
          if (mPId === this.props.playingId) {
            return { animated: false, itemIndex, sectionIndex, viewOffset: 0, viewPosition: 0 }; 
          }
        }
      }
    }
    return null;
  }

  async getData(date, event, isMore = false, aOrder = Order.Desc) {
    console.log(TAG, 'getData', this.props.event);
    let data = await this.mLoader.getEventByFileId(Device.deviceID, Device.model, this.props.event.isAlarm, this.props.event.fileId, this.props.evType);
    return data;
  }

  applyFilter(aEvs, aFilter) {
    let curEv = aEvs;
    for (let sec of curEv) {
      sec.data = sec.data.filter(aFilter);
    }
    return curEv;
  }

  findEvent(aEv) {
    if (this.state.events && this.state.events.length > 0 && this.state.events[0].hasOwnProperty("data")) {
      let events = this.state.events[0].data;
      let curOffset = aEv.offset;
      for (let i = 0; i < events.length; ++i) {
        if (events[i].offset == curOffset) {
          return events[i];
        }
      }
    }
    return null;
  }

  getAdjacentEventForMarkListitem(aEv) {
    if (this.state.events && this.state.events.length > 0 && this.state.events[0].hasOwnProperty("data")) {
      let events = this.state.events[0].data;
      let curOffset = aEv.offset;
      for (let i = 0; i < events.length; ++i) {
        if (events[i].offset > curOffset) {
          return events[i];
        }
      }
    }
    return null;
  }

  // events offset 按从小到大顺序
  getCurrentEventForMarkListitemByTime(time) {
    if (this.state.events && this.state.events.length > 0 && this.state.events[0].hasOwnProperty("data")) {
      let events = this.state.events[0].data;
      for (let i = events.length - 1; i > -1; --i) {
        // if ((events[i].offset * 3 + 0.4) <= time) { // 这是之前逻辑 加了个0.4 在051上有问题，先去掉
        if ((events[i].offset * 3) <= time) {
          return events[i];
        }
      }
      return events[0];
    }
    return null;
  }

  getPlayingOffset(aItm) { // 播放的offset可能没有对应事件的开始，需要即使是那个item正在被播放
    if (!aItm) return -1;
    if (this.state.events && this.state.events.length > 0 && this.state.events[0].hasOwnProperty("data")) {
      let events = this.state.events[0].data;
      // let events = this.getEventListOfCurentFile(aItm.fileId);

      let offset = aItm.offset;

      for (let i = 0; i < events.length; ++i) {
        if (events[i].offset == offset) {
          return offset;
        }
      }
      for (let i = 0; i < events.length; ++i) {
        if (events[i].offset > offset) {
          if (i > 0) {
            return events[i-1].offset;
          }
          return events[0].offset;
        }
      }
      if (events.length > 0) {
        return events[events.length - 1].offset;
      }
    }
    return aItm.offset;
  }
// 获取当前播放文件的事件列表
  getEventListOfCurentFile(fileId) {
    let res = [];
    if (this.state.events && this.state.events.length > 0 && this.state.events[0].hasOwnProperty("data")) {
      for (let j = 0; j < this.state.events.length; j++) {
        let events = this.state.events[j].data;
        let fId = fileId;
        for (let i = 0; i < events.length; i++) {
          if (fId == events[i].fileId) {
            res.push(events[i]);
          }
        }
      }
    }
    return res;
  }

  getAdjacentEvent(aEv) {
    if (!this.state.events) {
      return null;
    }
    let curEv = this.state.events;
    for (let i = 0; i < curEv.length; ++i) {
      let evGrp = curEv[i].data;
      let idx = evGrp.findIndex((aItm) => {
        return aItm.fileId == aEv.fileId;
      });
      if (idx != -1) {
        if (idx + 1 < evGrp.length) {
          return evGrp[idx + 1];
        } else {
          // check net grp
          if (i + 1 < curEv.length) {
            return curEv[i + 1].data[0];
          }
          // look back
          else if (idx - 1 >= 0) {
            return evGrp[idx - 1];
          }
          // pre section
          else if (i > 0) {
            return curEv[i - 1].data[0];
          } else {
            return null;
          }
        }
      }

    }
  }


  buildSection(aSecDict, aItm, aOrder) {
    return buildSection(aSecDict, aItm);
  }


  mRItem = ({ section, index }) => {
    // console.log(TAG, "mRItem aaa", section, "idx", index);
    let item = section.data[index];
    let mPId = item.offset != null ? `${ item.fileId }_${ item.offset }` : `${ item.fileId }`;

    return (
      <EventCard item={item}
        type={'details'}
        isPlaying = {this.props.playingId === mPId}
        cardPressed={(aItm) => {

          // this.naviTo('AlarmDetail', { item: item, event: this.state.selectedEventKey });
          item.isRead = true;
          let events = this.state.events;
          let nextDate = this.state.nextDate;
          this.props.onEventPress(item, { events, nextDate });
        }}/>);
  }
}
