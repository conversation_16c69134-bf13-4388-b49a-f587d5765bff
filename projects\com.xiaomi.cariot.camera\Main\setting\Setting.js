'use strict';

import { Device, Service, System, Host, PackageEvent, API_LEVEL, DarkMode, DeviceEvent } from "miot";
import { strings, Styles } from 'miot/resources';
import { CommonSetting, SETTING_KEYS } from "miot/ui/CommonSetting";
import { firstAllOptions, secondAllOptions } from "miot/ui/CommonSetting/CommonSetting";
import { MessageDialog, NavigationBar } from "mhui-rn";
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import React from 'react';
import { ScrollView, Text, View, Platform, PermissionsAndroid, StatusBar } from 'react-native';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import Toast from '../components/Toast';
import VersionUtil from "../util/VersionUtil";
import CameraConfig from '../util/CameraConfig';
import TrackUtil from '../util/TrackUtil';
import { DescriptionConstants } from "../Constants";
import VipUtil from "../util/VipUtil";
import LogUtil from "../util/LogUtil";
import BaseSettingPage from "../BaseSettingPage";
import { Settings } from "miot/ui";
import StorageKeys from "../StorageKeys";
import CameraPlayer from "../util/CameraPlayer";
import SdcardEventLoader from "../framework/sdcard/SdcardEventLoader";
import { RkButton } from "react-native-ui-kitten";

export default class Setting extends BaseSettingPage {

  constructor(props, context) {
    super(props, context);
    this.state = {
      showDot: [],
      showRedDot: this.props.navigation.getParam("hasFirmwareUpdate"),

      extraOptions: {
        excludeRequiredOptions: [
          // SETTING_KEYS.first_options.PRODUCT_BAIKE
        ]
      },
      name: Device.name,
      permissionRequestState: 0,
      showPermissionDialog: false
    };
    this.isCloudServer=CameraConfig.getIsCloudServer();
    console.log("🚀 ~ file: Setting.js ~ line 49 ~ Setting ~ constructor ~ this.isCloudServer", this.isCloudServer)
    
    this.firmwareUpdate = this.props.navigation.getParam("hasFirmwareUpdate");
    this.nasTips = this.props.navigation.getParam("hasNasTips");
    this.backFromItemTime = 0;
    // this.mSupportCloud = VersionUtil.isFirmwareSupportCloud(Device.model);
    this.mSupportCloud = CameraConfig.isDeviceSupportCloud();
    this.mSupportCloudCountry = CameraConfig.isSupportCloud(); // 云存储设置需要单独伶出来
    this.isPageForeGround = true;// 默认当前page在前台
    this.isPluginForeGround = true;// 默认当前插件在前台
    this.isAppForeground = true;
    this.sdcardCode = CameraPlayer.getInstance().sdcardCode;
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.isPageForeGround = true;
        this.isPluginForeGround = true;
        this.isAppForeground = true;
        this._onResume();
      }
    );
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'didBlur',
      () => {
        this.isPageForeGround = false;
        this._onPause();
      }
    );

    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }
      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      this.isAppForeground = true;// rnactivity调用了onresume
      this._onResume();
    });

    this.willPauseListener = PackageEvent.packageWillPause.addListener(() => {
      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }
      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      this.isAppForeground = false;// rnactivity调用了onresume
      this._onPause();
    });

    if (Platform.OS == "ios") {
      this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {
        if (!this.isPageForeGround) { // 如果当前插件不在前台
          return;
        }
        this.isPluginForeGround = true;// rnactivity调用了onresume
        this._onResume();
      });

      this.willDisappearListener = PackageEvent.packageViewWillDisappearIOS.addListener(() => {
        if (!this.isPageForeGround) { // 如果当前插件不在前台
          return;
        }
        this.isPluginForeGround = false;// rnactivity调用了onpause
        this._onPause();
      });
    }

    this.isVip = CameraConfig.isVip;
    this.isInExpireWindow = this.props.navigation?.state?.params?.inCloseWindow;
  }

  _onResume() {
    if (!this.isPageForeGround) {
      return;
    }

    if (!this.isPluginForeGround) {
      return;
    }

    if (!this.isAppForeground) {
      return;
    }
    // 之前在render里设置的颜色，从设置里退回，如果摄像头处于关闭状态 render不会掉用。

    DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');

    if (CameraConfig.isToUpdateVipStatue) {
      this.fetchVipData();
    }
    this.setState({});// 强制刷新一下
  }

  _onPause() {
    this.backFromItemTime = new Date().getTime();
  }

  getTitle() {
    return LocalizedStrings["setting"];
  }

  renderSettingContent() {
    let item1 = (
      <ListItem
        title={LocalizedStrings['s_camera_setting']}
        key={1}
        titleNumberOfLines={2}

        showSeparator={false}
        onPress={() => {
          console.log('ssss')
          TrackUtil.reportClickEvent("Setting_Camera_ClickNum")
          this._navigate('CameraSetting')
        }
        }
        titleStyle={{ fontWeight: 'bold' }}
        accessibilityLabel={DescriptionConstants.sz_4}
      />
    );

    let item2 = (
      <ListItemWithSwitch
        key={2}
        accessibilityLabel={DescriptionConstants.sz_4_8}
        title={LocalizedStrings['ptz_gesture_title']}
        showSeparator={false}
        subtitle={LocalizedStrings['ptz_gesture_sub_title']}
        unlimitedHeightEnable={true}
        subtitleNumberOfLines={4}
        titleNumberOfLines={2}
        value={this.state.isPtzRotationEnabled}
        onValueChange={(value) => this._onPtzRotationValueChange(value)}
        titleStyle={{ fontWeight: 'bold' }}
        accessibilitySwitch={{
          accessibilityLabel: DescriptionConstants.sz_4_8
        }}
      />
    );

    let item3 = (
      <ListItem
        title={LocalizedStrings['bug_cloud']}
        key={3}
        titleNumberOfLines={2}
        showSeparator={false}
        onPress={() => {
          TrackUtil.reportClickEvent("Setting_CloudStorage_ClickNum");
          if (!CameraConfig.isVip) {
            CameraConfig.isToUpdateVipStatue = true;
            API_LEVEL > 10068 ? Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: "setting_list" }) : Service.miotcamera.showCloudStorage(true, true);
          } else {
            Service.miotcamera.showCloudStorageSetting();
          }
        }
        }

        titleStyle={{ fontWeight: 'bold' }}
        accessibilityLabel={DescriptionConstants.sz_9}
      />
    );
    let item4 = (
      <ListItem title={LocalizedStrings['s_storage_setting']}
        key={4}
        titleNumberOfLines={2}
        showSeparator={false}
        showDot={this.nasTips && !CameraConfig.nasUpgradeDlgBtnChecked}
        onPress={() => {
          this._startAllStorageWithPermissionCheck();
        } }
        titleStyle={{ fontWeight: 'bold' }}
        accessibilityLabel={DescriptionConstants.sz_7}
      />
    );

    // 显示部分一级菜单项
    let firstOptions = [
      firstAllOptions.NAME,
      firstAllOptions.LOCATION,
      firstAllOptions.SHARE,
      firstAllOptions.IFTTT,
      firstAllOptions.FIRMWARE_UPGRADE,
      firstAllOptions.MORE,
      firstAllOptions.HELP,
      firstAllOptions.LEGAL_INFO
    ];
    // 显示部分二级菜单项
    let secondOptions = [
      secondAllOptions.SECURITY,
      secondAllOptions.FEEDBACK,
      secondAllOptions.TIMEZONE,
      secondAllOptions.ADD_TO_DESKTOP
    ];
    // 一级title显示不全问题
    let commonSettingStyle = {
      itemStyle: {
        titleNumberOfLines: 2,
        unlimitedHeightEnable: true
      }
    };

    if (CameraConfig.isSupportBtGateWay(Device.model)) {
      secondOptions = [
        secondAllOptions.SECURITY,
        secondAllOptions.FEEDBACK,
        secondAllOptions.TIMEZONE,
        secondAllOptions.BTGATEWAY,
        secondAllOptions.TIMEZONE,
        secondAllOptions.ADD_TO_DESKTOP
      ];
    }
    console.log("_----------------",this.mSupportCloudCountry)
    return (
      <View style={styles.container}>

        <View style={styles.featureSetting}
              key={8}
        >
          <View style={styles.titleContainer}>
            <Text style={styles.title} accessibilityLabel={DescriptionConstants.sz_3} >{strings.featureSetting}</Text>
          </View>
          {
            (!CameraConfig.isDeviceSupportCloud() || (!Device.isOwner || !this.mSupportCloudCountry)) // 不是设备主人or支持云存的国家 不让进云存储设置页面

                ? [CameraConfig.debugSpecialSupport() ? item1 : null, item2]
                : [CameraConfig.debugSpecialSupport() ? item1 : null, item2, item3]

          }

        </View>

        <View style={styles.whiteblank} />

        <View style={styles.featureSetting}
              key={9}
        >
          <View style={styles.titleContainer}>
            <Text style={styles.title} accessibilityLabel={DescriptionConstants.sz_3} >{strings.commonSetting}</Text>
          </View>
          {
            Device.isOwner ?
              <ListItem
                title={ LocalizedStrings['device_name'] }
                key={ "device_name" }
                titleNumberOfLines={ 2 }
                value={ this.state.name }
                showSeparator={ false }
                onPress={ () => {
                  Host.ui.openChangeDeviceName();
                }
                }
                titleStyle={ { fontWeight: 'bold' } }
                accessibilityLabel={ DescriptionConstants.sz_7 }
              /> : null
          }

          {/*{*/}
          {/*  Device.isOwner ? <ListItem*/}
          {/*    title={ LocalizedStrings['share_devices'] }*/}
          {/*    key={ 6 }*/}
          {/*    titleNumberOfLines={ 2 }*/}
          {/*    showSeparator={ false }*/}
          {/*    onPress={ () => {*/}
          {/*      Host.ui.openShareDevicePage();*/}
          {/*    }*/}
          {/*    }*/}
          {/*    titleStyle={ { fontWeight: 'bold' } }*/}
          {/*    accessibilityLabel={ DescriptionConstants.sz_7 }*/}
          {/*  /> : null*/}
          {/*}*/}

          {
            Device.isOwner ? <ListItem
              title={ LocalizedStrings['firmwareUpgrade'] }
              key={ "upgrade" }
              titleNumberOfLines={ 2 }
              showSeparator={ false }
              showDot={this.state.showRedDot}
              onPress={ () => {
                Host.ui.openDeviceUpgradePage(0);
              }
              }
              titleStyle={ { fontWeight: 'bold' } }
              accessibilityLabel={ DescriptionConstants.sz_15 }
            /> : null
          }

          <ListItem
            title={ LocalizedStrings['more_setting'] }
            key={ "more_setting" }
            titleNumberOfLines={ 2 }
            showSeparator={ false }
            onPress={ () => {
              this.props.navigation.navigate("MoreSetting", {excludeRequiredOptions: [secondAllOptions.BTGATEWAY, secondAllOptions.SECURITY]});
            }
            }
            titleStyle={ { fontWeight: 'bold' } }
            accessibilityLabel={ DescriptionConstants.sz_7 }
          />

        </View>

        {/*<CommonSetting*/}
        {/*  key={10}*/}
        {/*  navigation={this.props.navigation}*/}
        {/*  firstOptions={firstOptions}*/}
        {/*  secondOptions={secondOptions}*/}
        {/*  extraOptions={this.state.extraOptions}*/}
        {/*  showDot={this.state.showDot}*/}
        {/*  useNewType={true}*/}

        {/*/>*/}
        <View style={{ height: 20 }} />

        <MessageDialog
          visible={this.state.showAIBuyCloudVip}
          message={this.isCloudServer?LocalizedStrings['eu_c_cloudvip_need']:LocalizedStrings['c_cloudvip_need']}
          buttons={[
            { text: strings.cancel, callback: () => this.setState({ showAIBuyCloudVip: false }) },
            {
              text: LocalizedStrings['c_cloudvip_buy'],
              callback: () => this._buyCloudVip()
            }
          ]}
        />
        {this._renderPermissionDialog()}
      </View>
    );
  }

  renderSettingBottomContent() {
    return (
      <View
        style={{ flexDirection: 'row', bottom: 27 }} key={"delete_device"}
      >
        <RkButton
          style={{ margin: Styles.common.padding, flexGrow: 1, height: 46, borderRadius: 23, backgroundColor: '#f5f5f5', display: 'flex', bottom: -20 }}
          onPress={() => {
            Host.ui.openDeleteDevice();
          }
          }
          activeOpacity={0.8}
        >
          <Text style={{ color: '#F43F31', fontSize: 16, fontWeight: 'bold', textAlign: 'center' }}>
            {LocalizedStrings['deleteDevice']}
          </Text>
        </RkButton>
      </View>
    )
  }


  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 存储卡/相册
    // status == 1 麦克风
    // 
    let message = "";
    if (this.state.permissionRequestState == 0) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);
    } else {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_microphone"]);
    }
    return (
      <MessageDialog
        title={LocalizedStrings["tips"]}
        message={message}
        messageStyle={{
          fontSize: 14,
      }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ showPermissionDialog: false });
            }
          },
          {
            text: LocalizedStrings["setting"],
            callback: () => {
              Host.ui.openTerminalDeviceSettingPage(1);
              this.setState({ showPermissionDialog: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog} />
    );
  }

  // onBack = () => {
  //   this.props.navigation.goBack();
  //   return true;
  // }
  _onPtzRotationValueChange(value) {
    StorageKeys.IS_PTZ_ROTATION_ENABLE = value;
    this.setState({
      isPtzRotationEnabled: value
    });
    Toast.success('c_set_success');
  }
  componentDidMount() {
    super.componentDidMount();
    this._deviceNameChangedListener = DeviceEvent.deviceNameChanged.addListener((device) => {
      console.log("==================", device);
      this.setState({
        name: device.name
      });
    });
    CameraConfig.lockToPortrait();// 切换回竖屏
    this.fetchVipData();
    let title = LocalizedStrings["setting"];
    LogUtil.logOnAll("setting page title:" + title);
    StorageKeys.IS_SHOW_SDCARD_PAGE_ALL_STORAGE.then((res) => {
      console.log("++++++++++++++++++++",res)
      if (res === "" || res == null) {
        StorageKeys.IS_SHOW_SDCARD_PAGE_ALL_STORAGE = false;
        res = false;
      }
      this.showSdcardPageAllStorage = res;
    });
    StorageKeys.IS_PTZ_ROTATION_ENABLE.then((res) => {
      this.setState({
        isPtzRotationEnabled: res
      });
    }).catch((err) => {
      this.setState({
        isPtzRotationEnabled: false
      });
    });
    let showReddot = this.firmwareUpdate ? [firstAllOptions.FIRMWARE_UPGRADE] : [];
    // TODO: 拉数据
    this.setState({
      showDot: showReddot
    });

    
    this.setState({
      extraOptions: {
        showUpgrade: true,
        option: null,
        syncDevice: false,
        networkInfoConfig: 1,
        excludeRequiredOptions: [
          SETTING_KEYS.first_options.BTGATEWAY,
          SETTING_KEYS.first_options.IFTTT,
          SETTING_KEYS.first_options.LOCATION,
          SETTING_KEYS.first_options.FREQ_DEVICE,
          SETTING_KEYS.first_options.FREQ_CAMERA,
          SETTING_KEYS.first_options.SECURITY,
          SETTING_KEYS.first_options.PRODUCT_BAIKE,
          SETTING_KEYS.first_options.HELP

        ]
      }
    });
  }

  fetchVipData() {
    VipUtil.getVipStatus().then((data) => {
      this.isVip = data.isVip;
      this.inWindow = data.inCloseWindow;
      CameraConfig.isToUpdateVipStatue = false;
    }).catch((err) => {
      LogUtil.logOnAll("Settings VipUtil.getVipStatus() err = ", JSON.stringify(err));
    });
  }

  componentWillUnmount() {
    this.didFocusListener && this.didFocusListener.remove();
    this.didBlurListener && this.didBlurListener.remove();
    this.didResumeListener && this.didResumeListener.remove();
    this.willPauseListener && this.willPauseListener.remove();
    this._deviceNameChangedListener && this._deviceNameChangedListener.remove();
    if (this.willAppearListener) {
      this.willAppearListener.remove();
    }
    if (this.willDisappearListener) {
      this.willDisappearListener.remove();
    }
    // if (Platform.OS === "android") {
    //   BackHandler.removeEventListener("hardwareBackPress", this.onBack);
    // }
  }
  _navigate(name, params = null) {
    this.props.navigation.navigate(name, params);
  }

  _navigateAISetting() {
    // this._navigate("AISetting", { showFace: this.showFace, showBabyCry: this.showBabyCry, showPet: this.showPet });
    this._navigate("AICameraSettingsV2", { showFace: this.showFace, showBabyCry: this.showBabyCry, showPet: this.showPet });
  }

  _startAllStorageWithPermissionCheck() {
    if (Platform.OS === "android") {
      this.isCheckingPermission = true;
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          this.isCheckingPermission = false;
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this._showAllStorage();
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            // Toast.success("camera_no_write_permission");
            this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
          } else {
            Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
        this.isCheckingPermission = false;
        Toast.success("action_failed");
      });
    } else {
      // no ios's photos const use hardcode
      this.isCheckingPermission = true;
      System.permission.request("photos").then((res) => {
        this.isCheckingPermission = false;
        this._showAllStorage();
      }).catch((error) => {
        this.isCheckingPermission = false;
        // Toast.success("camera_no_write_permission");
        this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
      });

    }
    TrackUtil.reportClickEvent('Camera_ScreenShot_ClickNum');
  }

  _showAllStorage() {
    TrackUtil.reportClickEvent("Camera_Storage_ClickNum"); // Camera_Storage_ClickNum

    let index = this.showSdcardPageAllStorage ? 2 : 0;
    if (this.showSdcardPageAllStorage) {
      if (!(this.sdcardCode == 0 || this.sdcardCode == 2)) {
        // 不支持sdcard or 无卡，就不需要再跳记忆的sdcard页面了。
        index = 0;
      }
    } else { // 跳云存或者初次进来。
      if (this.isVip || this.inWindow) {
        index = 0;
      } else if (this.sdcardCode == 0 || this.sdcardCode == 2) {
        index = 2;
      }
    }

    let navigationParam = { initPageIndex: CameraConfig.isDeviceSupportCloud() ? index : 1, vip: this.isVip, isSupportCloud: CameraConfig.isDeviceSupportCloud() };
    LogUtil.logOnAll("AllStorage UI s param:", navigationParam, " isConnected:", CameraPlayer.getInstance().isConnected());
    // 进入回看前 清空一次SdFileManager里的列表。避免缓存的问题
    SdcardEventLoader.getInstance().clearSdcardFileList();
    this.props.navigation.navigate("AllStorage", navigationParam);
  }


}
