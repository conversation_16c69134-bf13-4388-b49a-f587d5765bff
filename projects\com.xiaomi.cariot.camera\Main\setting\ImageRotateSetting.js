'use strict';

import { Device, DarkMode, Service } from "miot";
import { Styles } from 'miot/resources';
import Separator from 'miot/ui/Separator';
import ImageButton from "miot/ui/ImageButton";
import React from 'react';
import { StyleSheet, View, Image, Text, SafeAreaView, Platform, Touchable, TouchableOpacity } from 'react-native';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import Toast from '../components/Toast';
import VersionUtil from '../util/VersionUtil';
import AlbumHelper, { SNAPSHOT_FLIP_IMG_PATH, SNAPSHOT_SETTING_IMG_PATH } from "../util/AlbumHelper";
import Host from "miot/Host";
import StorageKeys from "../StorageKeys";
import CameraConfig, { IMG_DARKMODE_TINT } from '../util/CameraConfig';
import { CAMERA_CONTROL_SEPC_PARAMS, CAMERA_CONTROL_SIID } from "../Constants";
import NavigationBar from "miot/ui/NavigationBar";
import RPC from "../util/RPC";

import { DescriptionConstants } from "../Constants";
import LogUtil from "../util/LogUtil";
import { TouchableView } from "mhui-rn";
import AlarmUtilV2, { PIID_IMAGE_ROLLOVER, SIID_CAMERA_CONTROL } from "../util/AlarmUtilV2";
// const IMG_DARKMODE_TINT = "#DDDDDDFF";

export default class ImageRotateSetting extends React.Component {

  constructor(props, context) {
    super(props, context);

    this.state = {
      isFetchingData: true,
      isFlip: false,
      snapshotFlip: false,
      rotation: 0,
      source: require('../../Resources/Images/camera_default_rote.jpg')
    };
  }

  render() {
    let bodyImgStyle = styles.bodyImage;
    if (this.state.isFlip) {
      if (this.state.snapshotFlip) {
        bodyImgStyle = styles.bodyImage;
      } else {
        bodyImgStyle = styles.bodyImageFlip;
      }
    } else if (this.state.rotation == 90) {
      if (this.state.snapshotFlip) {
        bodyImgStyle = styles.bodyImageRotation270;
      } else {
        bodyImgStyle = styles.bodyImageRotation90;
      }
    } else if (this.state.rotation == 270) {
      bodyImgStyle = styles.bodyImageRotation270;
      if (this.state.snapshotFlip) {
        bodyImgStyle = styles.bodyImageRotation90;
      } else {
        bodyImgStyle = styles.bodyImageRotation270;
      }
    } else {
      if (this.state.snapshotFlip) {
        bodyImgStyle = styles.bodyImageFlip;
      } else {
        bodyImgStyle = styles.bodyImage;
      }
    }

    let bottomButtonStyle = {
      width: 32,
      height: 32,
      alignItems: 'center'
    };

    let colorScheme = DarkMode.getColorScheme();
    let source = null;
    if (colorScheme == 'dark') {
      bottomButtonStyle.tintColor = IMG_DARKMODE_TINT;
      source = require('../../Resources/Images/mjv3_setting_btn_rotate_dark.png');
    } else {
      source = require('../../Resources/Images/mjv3_setting_btn_rotate_highlighted.png');
    }

    return (
      <View style={styles.container}>
        {/* {this._renderTitleView()} */}

        <View
          accessibilityLabel={DescriptionConstants.sz_4_71}
          style={styles.body}
        >
          <Image
            // style={this.state.isFlip ? styles.bodyImageFlip : styles.bodyImage}
            style={bodyImgStyle}
            source={this.state.source}
          />
        </View>
        <TouchableOpacity style={{display: "flex", alignItems: "center", justifyContent: "center", window: "100%"}}
          onPress={() => this._rotateImage()}
          accessibilityRole={"button"}
          accessibilityLabel={LocalizedStrings['irs_rotate']}
        >
          <Image
            style={bottomButtonStyle}
            source={source}
            disabled={this.state.isFetchingData}
          />
          <Text
            accessible={false}
            style={styles.bottomText}>
            {LocalizedStrings['irs_rotate']}
          </Text>
        </TouchableOpacity>
        <SafeAreaView></SafeAreaView>
      </View>
    );
  }

  componentDidMount() {
    const textStyle = {
      color: Styles.common.MHGreen,
      width: 60,
      fontSize: 14
    };
    const textRightStyle = {
      color: Styles.common.MHGreen,
      width: 60,
      fontSize: 14
    };
    // 这里是旋转画面 没有起到作用 后续看用navigationBar怎们修改
    this.props.navigation.setParams({
      title: LocalizedStrings['irs_rotate'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.CLOSE,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      right: [
        {
          key: NavigationBar.ICON.COMPLETE,
          onPress: () => {
            Toast.loading('c_setting');
            if (VersionUtil.isUsingSpec(Device.model)) {
              AlarmUtilV2.setSpecPValue([{sname: SIID_CAMERA_CONTROL, pname: PIID_IMAGE_ROLLOVER,value: this.state.isFlip ? 180 : 0}])
              // Service.spec.setPropertiesValue([{ ...CAMERA_CONTROL_SEPC_PARAMS[3], value: this.state.isFlip ? 180 : 0 }])
                .then((res) => {
                  if (res[0].code == 0) {
                    this.props.navigation.goBack();
                    Toast.success('c_set_success');
                    StorageKeys.IS_IMAGE_FLIP = this.state.isFlip;
                    StorageKeys.IMAGE_ROTATION = this.state.rotation;
                    Toast.success("c_set_success");
                  } else {
                    Toast.fail('c_set_fail');
                  }
                })
                .catch((err) => {
                  Toast.fail('c_set_fail', err);
                });
            } else {
              RPC.callMethod("set_flip", [
                this.state.isFlip ? 'on' : 'off'
              ]).then((res) => {
                if (res.result[0] == 'OK') {
                  this.props.navigation.goBack();
                  Toast.success('c_set_success');
                  StorageKeys.IS_IMAGE_FLIP = this.state.isFlip;
                  StorageKeys.IMAGE_ROTATION = this.state.rotation;
                } else {
                  Toast.fail('c_set_fail');
                }
              }).catch((err) => {
                Toast.fail('c_set_fail', err);
              });
            }
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        // color: '#333333',
        fontWeight: 500
      }

    });

    this._updateImage();
    if (VersionUtil.isUsingSpec(Device.model)) {
      Service.spec.getPropertiesValue([CAMERA_CONTROL_SEPC_PARAMS[3]])
        .then((result) => {
          LogUtil.logOnAll("Fetch Remote Rotate Status SUCCESS:" + new Date().getTime());
          let isOk = result[0].code == 0;
          if (isOk) {
            
            let degree = result[0].value;
            let isFlipOn = degree == 180;
            this.setState({ isFetchingData: false })
            if (this.state.isFlip != isFlipOn) {
              if (isFlipOn) {
                this.setState({ isFlip: isFlipOn, rotation: 180 });
              } else {
                this.setState({ isFlip: isFlipOn, rotation: 0 });
              }
              StorageKeys.IS_IMAGE_FLIP = isFlipOn;
              console.log(`why!, update flip: ${isFlipOn}`);
              setTimeout(() => {
                this._updateImage();
              });
            }
          } else {
            Toast.fail('c_get_fail');
          }
        })
        .catch((err) => {
          Toast.fail('c_get_fail', err);
        });
    } else {
      RPC.callMethod("get_prop", [
        'flip'
      ])
        .then((res) => {
          console.log(`why!, flip res: ${JSON.stringify(res)}`);
          console.log(res);
          this.setState({ isFetchingData: false })
          let isFlipOn = res.result[0] == 'on';
          if (this.state.isFlip != isFlipOn) {
            if (isFlipOn) {
              this.setState({ isFlip: isFlipOn, rotation: 180 });
            } else {
              this.setState({ isFlip: isFlipOn, rotation: 0 });
            }
            StorageKeys.IS_IMAGE_FLIP = isFlipOn;
            console.log(`why!, update flip: ${isFlipOn}`);
            setTimeout(() => {
              this._updateImage();
            });
          }
        })

        .catch((err) => {
          Toast.fail('c_get_fail', err);
        });
    }


  }

  _updateImage() {
    StorageKeys.IS_IMAGE_FLIP.
      then((isFlipOn) => {
        return new Promise((resolve, reject) => {
          console.log(`why!, _updateImage isFlipOn: ${isFlipOn}`);

          this.setState({ isFlip: isFlipOn });
          resolve();
        });
      })
      .then(() => {
        return new Promise((resolve, reject) => {
          StorageKeys.IMAGE_ROTATION.
            then((imgRotation) => {
              this.setState({ rotation: imgRotation });
              resolve();
            });
        });
      })
      .then(() => {
        // 优先获取和摄像头flip设置一致的截图，如flip设置为false就获取正向截图，否者获取翻转截图
        let isFlipOn = this.state.isFlip;
        let path = isFlipOn ? SNAPSHOT_FLIP_IMG_PATH : SNAPSHOT_SETTING_IMG_PATH;
        return new Promise((resolve, reject) => {
          Host.file.isFileExists(path)
            .then((result) => {
              if (result) {
                resolve(path);
              } else {
                resolve(null);
              }
            });
        });
      })
      .then((result) => {
        if (result == null) {
          // 没有方向一致的截图，检查反方向截图是否存在
          return new Promise((resolve, reject) => {
            let isFlipOn = this.state.isFlip;
            let flippath = isFlipOn ? SNAPSHOT_SETTING_IMG_PATH : SNAPSHOT_FLIP_IMG_PATH;
            Host.file.isFileExists(flippath)
              .then((result) => {
                if (result) {
                  resolve(flippath);
                } else {
                  resolve(null);
                }
              });
          });
        } else {
          return new Promise((resolve, reject) => {
            resolve(result);
          });
        }
      })
      .then((result) => {
        if (result != null) {
          let timestamp = new Date().getTime();
          // let path = `${ result }?timestamp=${ timestamp }`;
          let path = `${result}?timestamp=${timestamp}`;
          if (Platform.OS === "ios") {
            path = result;
          }
          let isSnapShotFlip = (result == SNAPSHOT_FLIP_IMG_PATH) ? true : false;
          this.setState({ source: { uri: `${Host.file.storageBasePath}/${path}` }, snapshotFlip: isSnapShotFlip });
        }
      })
      .catch((err) => {
        console.log(err);
      });
  }

  _rotateImage() {
    LogUtil.logOnAll("Press RotateImage:" + new Date().getTime());
    let flip = !this.state.isFlip;
    this.setState({
      isFlip: flip,
      rotation: flip ? 0 : 180
    });
  }

}

export const styles = StyleSheet.create({
  container: {
    backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : '#fff',
    flexDirection: 'column',
    height: '100%'
  },
  body: {
    width: '100%',
    flexGrow: 1,
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : '#fff',

  },
  bodyImage: {
    width: "100%",
    aspectRatio: 1920.0 / 1080.0
  },
  bodyImageRotation90: {
    width: "100%",
    aspectRatio: 1920.0 / 1080.0,
    transform: [{ rotate: '90deg' }]
  },
  bodyImageRotation270: {
    width: "100%",
    aspectRatio: 1920.0 / 1080.0,
    transform: [{ rotate: '270deg' }]
  },
  bodyImageFlip: {
    width: "100%",
    aspectRatio: 1920.0 / 1080.0,
    transform: [{ rotate: '180deg' }]
  },
  bottom: {
    alignItems: 'center'
  },
  bottomButton: {
    width: 55,
    height: 55,
    alignItems: 'center'
  },
  bottomText: {
    marginTop: 10,
    marginBottom: 20,
    ...Styles.common.buttonText
  }
});
