import React from 'react';
import {
  <PERSON><PERSON>View,
  View,
  BackHandler,
  FlatList,
  Platform,
  Text,
  StyleSheet,
  Image,
  TouchableWithoutFeedback,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions, NativeModules, TouchableHighlight, PermissionsAndroid
} from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { AbstractDialog, ChoiceDialog, ImageButton, InputDialog, MessageDialog, NavigationBar } from 'mhui-rn';
import StorageKeys from '../StorageKeys';
import Toast from '../components/Toast';
import { Service, Host, Device, Entrance } from 'miot';
import LogUtil from '../util/LogUtil';
import { PackageEvent } from 'miot/Package';
import API from '../API';
import { ListItem, ListItemWithSwitch } from "miot/ui/ListItem";
import { styles } from "../setting/SettingStyles";
import AlarmUtilV2, {
  PIID_COUGH_SENSITIVITY,
  PIID_COUGH_SWITCH,
  PIID_CRY_SENSITIVITY,
  PIID_CRY_SWITCH,
  PIID_MOVE_SENSITIVITY,
  PIID_MOVE_SWITCH,
  PIID_PEOPLE_SWITCH,
  PIID_SOUND_SENSITIVITY,
  PIID_SOUND_SWITCH, SIID_AI_DETECTION, SIID_AI_SENSITIVITY
} from "../util/AlarmUtilV2";
import { Event } from '../config/base/CfgConst';
import Util from '../util2/Util';
import ListItemWithIcon from "../widget/ListItemWithIcon";
import ChoiceItem from "mhui-rn/dist/components/listItem/ChoiceItem";
import { BaseStyles } from "../BasePage";
import DeviceSettingUtil from "../util/DeviceSettingUtil";
import CallUtil, { CALL_TYPE } from "../util/CallUtil";
import BaseSettingPage from "../BaseSettingPage";
import { RkButton } from "react-native-ui-kitten";
import { strings, Styles } from "miot/resources";
import TrackUtil from "../util/TrackUtil";
import { FontSecondary } from "mhui-rn/dist/constants/font";
import { AccessibilityRoles, getAccessibilityConfig } from "mhui-rn/dist/utils/accessibility-helper";
import Switch from "miot/ui/Switch";
import FDSUtil from "../util/FDSUtil";
import { Locale } from "mhui-rn/dist/locale";
import PopButton from "mhui-rn/dist/components/popButton/PopButton";
import PermissionUtil from "../util/PermissionUtil";
import ImagePicker from "react-native-image-picker";

const { width: screenWidth, height: screenHeight } = Dimensions.get("screen");

const viewHeight = (Dimensions.get('window').width - 48) * 9 / 16;

const TAG = "MotionDetectionPage";


/**
 * @Author: byh
 * @Date: 2023/11/13
 * @explanation:
 * 微信音视频通话
 *********************************************************/
export default class WXCallSetting extends BaseSettingPage {

  constructor(props, context) {
    super(props, context);
    this.pageType = this.props.navigation.getParam("type");
    this.state = {
      isLoading: false,
      isVip: false,
      switchValue: true,
      clickSwitchValue: false,
      gestureSwitchValue: false,
      sensitiveIndex: 0,
      sensitivityVisible: false,
      showRenameDialog: false,
      showEditContactDialog: false,
      showAvatarIntroDialog: false,
      commentErr: null,
      oneClickSet: true,
      doubleClickSet: false,
      longClickSet: false,
      contactsData: [],
      showEmpowerDialog: false,
      showOwnerEmpowerDialog: false,
      showEmpowerTip: false,
      showServiceReminderDialog: false,
      contactIsFull: false,
      showAvatarContactDialog: false,
      showDelDialog: false
    };
    this.currentItem = null;
    this.isReadOnlyShared = Device.isReadonlyShared;
    this.titleStr = LocalizedStrings['detect_move'];
    this.detectionDesc = LocalizedStrings['ai_move_desc'];
    this.attentionDesc = LocalizedStrings['ai_note_attention'];
    this.topImageSrc = require("../../Resources/Images/wx_call_banner.webp");
  }
  getTitle(): string {
    return LocalizedStrings['cs_wx_call'];
  }

  componentDidMount() {
    super.componentDidMount();
    this._getSetting(true);
    // CallUtil.checkIsWarningToShowDialog(1).then((value) => {
    //   console.log("judgeToAddOwnerToSingleClickCall",value)
    //   if (value) {
    //     // 需要显示提醒弹框
    //     this.setState({ showOwnerEmpowerDialog: true });
    //   }
    // });
    console.log(TAG, "this.isReadOnlyShared", this.isReadOnlyShared);
    // 获取vip的状态
    StorageKeys.IS_VIP_STATUS.then((res) => {
      StorageKeys.IN_CLOSE_WINDOW.then((res1) => {
        this.setState({ isVip: res || res1 });
      });
      console.log("看看vip的状态：", this.state.isVip);
    }).catch(() => {
      this.setState({ isVip: false });
    });
    // 获取浮条状态
    StorageKeys.WX_EMPOWER_TIP.then((res) => {
      console.log("+++++++++++++++++++",res)
      let empTips = res;
      if (typeof (res) === "string" || res == "" || res == null || res == undefined) {
        empTips = false;
      }
      this.setState({ showEmpowerTip: !empTips });
    }).catch(() => {
      this.setState({ showEmpowerTip: true });
    });

    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {

      }
    );
    this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {//// 原生页面关闭后回调
      // this.autoPlay();
      LogUtil.logOnAll(TAG, "did resume ios");
      this._onResume();
    });
    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      LogUtil.logOnAll(TAG, "did resume");
      this._onResume();
    });


  }

  componentWillUnmount() {
    console.log("DailyStoryFirstEnter componentWillUnmount.....");
    this.willFocusSubscription && this.willFocusSubscription.remove();
    this.didResumeListener && this.didResumeListener.remove();
    this.willAppearListener && this.willAppearListener.remove();
  }

  _onResume() {
    this._fetchVipStatus();
  }

  _fetchVipStatus() {
    console.log(TAG, "_fetchVipStatus start");
    // todo:需要框架端提供能够指定hostName前缀的api
    API.get("/miot/camera/app/v1/vip/status", "business.smartcamera")
      .then((result) => {
        if (result.code != 0) {
          return;
        }
        let data = result.data;
        if (data == null) {
          return;
        }
        console.log(TAG, "_fetchVipStatus", JSON.stringify(result));
        let vip = data["vip"];
        let status = data["status"];
        let inWindow = !data["closeWindow"]; // false表示在窗口内，true表示不在
        let endTime = data["endTime"];
        StorageKeys.IS_VIP_STATUS = vip;
        StorageKeys.IN_CLOSE_WINDOW = inWindow;
        StorageKeys.VIP_DETAIL = data;
        this.setState({ isVip: vip || inWindow });
      })
      .catch((err) => {
        console.log(err);
      });
  }


  _getSetting(checkDialog = false) {
    // DeviceSettingUtil.getDeviceSettingByPrefix("call_").then((res2) => {
    //   console.log("+++++++=getDeviceSettingByPrefix success",res2);
    // }).catch(err => {
    //   console.log("+++++++=getDeviceSettingByPrefix success",err);
    // });
    DeviceSettingUtil.getDeviceSettingByPrefix(DeviceSettingUtil.callPrefixKey).then(res => {
      if (res.code == 0) {
        let settingsData = res.result.settings;
        if (settingsData && settingsData.call_setting) {
          let data = JSON.parse(settingsData.call_setting);
          this.callSettingData = JSON.parse(JSON.stringify(data));
          let stateProps = {};
          let contacts = [];
          if (data.hasOwnProperty("switch")) {
            stateProps.switchValue = data.switch.mijia;
            stateProps.clickSwitchValue = data.switch.mijia;
            // stateProps.gestureSwitchValue = data.switch.hand;
          }

          Object.keys(data).forEach((key) => {
            if (key.indexOf('key') != -1) {
              let item = data[key];
              item.key = key;
              contacts.push(item);
            }
          });

          // 取头像
          Object.keys(settingsData).forEach((key) => {
            console.log("{{{{{{{{{{{{{{{{",key);
            if (key.indexOf('call_key') != -1) {
              // 图片后缀key,需要匹配到联系人中去
              let iconSuffixKey = key.substring(key.lastIndexOf("_") + 1);
              let iconData = JSON.parse(settingsData[key]);
              console.log("{{{{{{{{{{{{{{{{",iconSuffixKey,iconData,typeof (iconData));
              contacts.map((item) => {
                if (item.key === iconSuffixKey) {
                  item.icon = iconData.icon;
                  item.fdsUrl = iconData.fdsUrl ? iconData.fdsUrl : '';
                  item.objName = iconData.objName ? iconData.objName : '';
                  return item;
                }
                return item;
              });
            }
          });

          // 先去重
          let hash = {};
          contacts = contacts.reduce((item:any, next:any)=>{
            // hash[next['mijia']] ? '' : ((hash[next['mijia']] = true) && item.push(next));
            if (!hash[next['mijia']]) {
              hash[next['mijia']] = true;
              item.push(next)
            }
            return item;
          }, []);
          contacts.map((item, index) => {
            if (item.mijia == Service.account.ID) {
              contacts.unshift(contacts.splice(index, 1)[0]);
            }
          });

          stateProps.contactsData = contacts;
          this.setState(stateProps,()=>{
            this.downloadImage();
          });

          if (checkDialog) {
            this.checkDialogShouldShow(settingsData).then((value) => {
              console.log("=======================dialog",value);
              if (value) {
                // 需要显示提醒弹框
                this.setState({ showOwnerEmpowerDialog: true });
              }
            }).catch((err) => {
            });
          }
        }0

      }
    }).catch(error => {
      console.log("======= error: ", error);
    });
  }

  checkDialogShouldShow(settingsData) {
    return new Promise((resolve, reject) => {
      CallUtil.shouldShowWarningDialog(settingsData,1,resolve,reject);
    })
  }

  async downloadImage() {
    if (this.state.contactsData.length == 0) {
      return;
    }
    for (let i = 0; i < this.state.contactsData.length; ++i) {
      try {
        let item = this.state.contactsData[i];
        console.log("=====================00000000download");
        if (item.objName) {
          item.localFileName = await FDSUtil.getLocalImgUrl(item.objName);
          this.forceUpdate();
        }
      } catch (err) {
        console.log("download fds error", err);
        LogUtil.logOnAll(TAG, `download fds Imag ${JSON.stringify(err)}`);
      }
    }
    // this.setState({});
  }
  _onSwitchValue(value) {
    // if (!value) {
    //   //关闭的时候弹框提示
    //   this.updateCallSettingSwitch(value);
    // } else {
    //   this.updateCallSettingSwitch(value);
    // }
    this.updateCallSettingSwitch(value);
  }

  _onWxSwitchValue(value,item,index) {
    if (!value) {
      //关闭的时候弹框提示
      this.updateWxCallSettingSwitch(value,item,index);
    } else {
      this.updateWxCallSettingSwitch(value,item,index);
    }
  }

  renderSettingContent() {
    let containerStyleAllRadius = {
      backgroundColor: "#ffffff",
      marginLeft: 12,
      marginRight: 12,
      marginTop: 12,
      width: screenWidth - 24,
      borderBottomLeftRadius: 16,
      borderBottomRightRadius: 16,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      paddingLeft: 20,
      height: 70
    };

    let containerStyleTopRadius = {
      backgroundColor: "#ffffff",
      marginLeft: 12,
      marginRight: 12,
      marginTop: 12,
      width: screenWidth - 24,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      paddingLeft: 20,
      height: 70
    };

    let containerStyleBottomRadius = {
      backgroundColor: "#ffffff",
      marginLeft: 12,
      marginRight: 12,
      width: screenWidth - 24,
      borderBottomLeftRadius: 16,
      borderBottomRightRadius: 16,
      paddingLeft: 20,
      height: 70
    };

    return (
      <View style={ {
        display: "flex",
        height: "100%",
        flex: 1,
        flexDirection: "column",
        backgroundColor: Util.isDark() ? "#xm000000" : '#FFFFFF',
        alignItems: "center"
      } }>
        <View key={ 102 }>

          <View style={ { alignItems: "center", marginHorizontal: 24 } }>
            <Image style={ { width: '100%', height: viewHeight, borderRadius: 9 } }
                   source={ Util.isDark() ? require("../../Resources/Images/wx_call_banner_dark.webp") : require("../../Resources/Images/wx_call_banner.webp") }/>
          </View>

          <Text style={ [styles.desc_subtitle,{fontWeight: "400", paddingHorizontal: 28, marginTop: 20}] }>{ LocalizedStrings['ai_wx_call_desc'] }</Text>

          <View style={ stylesWX.whiteblank }/>

          {
            this.state.switchValue ? <Text style={ {
              color: "#8C93B0",
              fontSize: 12,
              marginTop: 8,
              fontFamily: "MI Lan Pro",
              marginHorizontal: 27,
              marginBottom: 8
            } }>{ LocalizedStrings['contacts'] }</Text> : null
          }

          { this.state.contactsData && this.state.contactsData.length > 0 ? this._renderEmpowerTips() : null }

          <FlatList
            data={ this.state.contactsData }
            style={ { marginTop: 10, paddingBottom: 12 } }
            renderItem={ this._renderContactItem }
            ListEmptyComponent={this._renderEmptyView}
            ItemSeparatorComponent={ () => <View style={ { height: 12 } }/> }
            keyExtractor={ (item, index) => `key_${ index }` }
          />
          {/*{ this.state.contactsData.length > 0 ? this._renderBottomItem() : null }*/}

          <View style={ {
            zIndex: 1,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            marginTop: 46,
            bottom: 30,
            left: 0,
            right: 0
          } } key={ 12 }>
            <TouchableOpacity
              onPress={ () => {
                this.setState({ showCancelEmpowerDialog: true });
              } }
            >
              <Text style={ {
                color: '#32BAC0',
                fontSize: 14,
                fontWeight: '400',
                textAlign: 'center',
                textDecorationLine: 'underline',
                paddingTop: 10,
                paddingHorizontal: 10
              } }>
                { LocalizedStrings['cs_wx_cancel_invite'] }
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        { this._renderRenameDialog() }
        { this._renderEditContactDialog() }
        { this._renderAvatarIntroDialog() }
        { this._renderToEmpowerDialog() }
        { this._renderCancelEmpowerDialog() }
        { this._renderServerWarning() }
        { this._renderChooseAvatarDialog() }
        { this.renderDeleteDialog() }
        { this._renderOwnerToEmpowerDialog() }
        { this._renderPermissionDialog() }
      </View>
    );
  }

  _renderOwnerToEmpowerDialog() {
    return (
      <AbstractDialog
        visible={ this.state.showOwnerEmpowerDialog }
        title={ LocalizedStrings['cs_invite_empower_warning'] }
        subtitle={ LocalizedStrings['cs_invite_empower_warning_desc'] }
        dialogStyle={ {
          titleStyle: {
            fontSize: 16.5
          }
        } }
        showSubtitle={ false }
        useNewTheme
        canDismiss={ false }
        onDismiss={ () => {
          this.setState({ showOwnerEmpowerDialog: false });
        } }
        buttons={ [
          {
            text: LocalizedStrings["action_cancle"],
            style: Util.isLanguageCN() ? null : {fontSize: 12},
            callback: (_) => {
              StorageKeys.WX_WARNING_DIALOG = true;
              this.setState({ showOwnerEmpowerDialog: false });
            }
          },
          {
            text: LocalizedStrings["cs_wx_empower"],
            style: Util.isLanguageCN() ? null : {fontSize: 12},
            callback: (_) => {
              // 标记，我已经弹出过，下次正常进插件就不会再弹出来
              StorageKeys.WX_WARNING_DIALOG = true;
              StorageKeys.WX_WARNING_LIST_DIALOG = true;
              this.setState({ showOwnerEmpowerDialog: false });
              let params = {
                paramType: "requestWxDeviceVoIP",
                did: Device.deviceID.toString(),
                userId: Service.account.ID.toString(),
                deviceName: Device.name,
                model: Device.model
              }
              Host.ui.requestWxDeviceVoIP(params)
            }
          }
        ] }>
        <View>
          <Text style={ {
            fontSize: 16,
            fontWeight: "400",
            marginHorizontal: 28,
            lineHeight: 24
          } }>{ LocalizedStrings["cs_invite_empower_warning_desc"] }</Text>

          <View style={{
            margin: 28,
            display: 'flex',
            flexDirection: "row",
          }}>
            <Image style={ {
              height: 170,
              borderRadius: 12,
              flex: 1
            } }
                   source={ require("../../Resources/Images/wx_empower.webp") }/>
          </View>
        </View>
      </AbstractDialog>
    );
  }

  renderSettingBottomContent() {
    if (!Device.isOwner) {
      // 共享用户不显示添加按钮
      return null;
    }
  
    const disabled = this.state.contactsData && this.state.contactsData.length >= 10
    return (
      <TouchableOpacity
        style={ { position: 'absolute', right: 0, bottom: 0 } }
      
        onPress={ () => {
          // this.onAddItem();
          if (disabled) {
            Toast.fail("add_contact_already_max");
            return;
          }
          this.props.navigation.navigate("ChoiceContactPage",{ callback: (data) => {
            this._getSetting();
          }});
        } }>
        <Image style={ { width: 120, height: 120 } }
               source={ disabled ? require("../../Resources/Images/icon_add_timer_new_dis.webp") : require("../../Resources/Images/icon_add_timer_new.webp") }/>
      </TouchableOpacity>
    )
  }

  _renderContactItem = ({ item, index }) => {

    // console.log("_renderDayItem", item);
    let isSelected = index === 0;
    let itemBgColor = isSelected ? "rgba(50, 186, 192, 0.10)" : Util.isDark() ? "#FFFFFFB2" : "#F0F0F0";
    let disabled = index === 1;
    let opacity = disabled ? { opacity: 0.5 } : { opacity: 1 };
    let nickname = item.callName ? item.callName : "";
    let isOwner = false;
    if (item.mijia == Service.account.ID) {
      nickname = `${ nickname }${ LocalizedStrings["call_me"] }`;
      isOwner = true;
    }

    let icon = null;
    if (item.objName && item.localFileName) {
      icon = "file://" + Host.file.storageBasePath + "/" + item.localFileName;
    } else if (item.icon) {
      icon = item.icon;
    }
    return (
      <View style={{borderRadius: 16, backgroundColor: '#F6F6F6', marginHorizontal: 12, width: screenWidth - 24,}}>
        <View style={{ display: "flex", flexDirection: "row", alignItems: "center", padding: 20, width: "100%" }}>
          <Image
            style={{ width: 40, height: 40, borderRadius: 20 }}
            source={icon != null ? { uri: icon } : require("../../Resources/Images/icon_user.png")}
          />
          <View style={{ display: "flex", flexDirection: "row", flexGrow: 1, paddingLeft: 20, paddingRight: 15, flex: 1 ,alignItems: "center"}}>
            <View style={{ display: "flex", flexDirection: "column"}}>
              <Text style={{ fontSize: 16, fontWeight: 'bold' }}>{nickname}</Text>
              <Text style={{ color: "#999999", fontSize: 11 }}>{item.mijia}</Text>
            </View>
          </View>
          <View style={{
            width: 10
          }} />
          {Device.isOwner && <ImageButton
            style={{ width: 32, height: 32 }}
            source={Util.isDark() ? require("../../Resources/Images/icon_call_edit_dark.png") : require("../../Resources/Images/icon_call_edit.png")}
            onPress={() => {
              console.log("=======edit", item);
              if (Device.isReadonlyShared) {
                Toast.success("cloud_share_hint");
                return;
              }
              this.currentItem = item;


              this.setState({ showEditContactDialog: true });


            }} />}
          <View style={[{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start'
          }, { maxWidth:'30%' }]}>
            {this.props.value ? <Text numberOfLines={valueLine}  ellipsizeMode="tail" style={[Styles.common.subtitle, {
              color: 'rgba(0, 0, 0, 0.40)',
              ...FontSecondary
            }, valueStyle]} {...getAccessibilityConfig({
              accessible: false
            })}>
              {this.props.value}
            </Text> : null}
          </View>

        </View>
        <ListItem
          titleNumberOfLines={ 3 }
          unlimitedHeightEnable={ true }
          showSeparator={ false }
          showVerticalLine={true}
          // title={ LocalizedStrings['wx_call'] }
          title={ LocalizedStrings['cs_wx_call'] }
          value={ isOwner ? LocalizedStrings['cs_wx_empower'] : LocalizedStrings['cs_wx_invite_empower'] }
          valueNumberOfLines={3}
          valueMaxWidth={"40%"}
          onValueChange={ (val) => this._onWxSwitchValue(val,item,index) }
          containerStyle={ { backgroundColor: '#F6F6F6',width: screenWidth - 24 , borderRadius: 16, paddingHorizontal: 20, marginBottom: 10 } }
          titleStyle={ { fontWeight: 'bold' } }
          accessibilitySwitch={ {
            accessibilityLabel: LocalizedStrings['care_screen_close_show_protect']
          } }
          onPress={() => {
            if (isOwner) {
              // 本账号点击去授权，后续进入插件不再弹出授权引导
              StorageKeys.WX_WARNING_DIALOG = true;
              StorageKeys.WX_WARNING_LIST_DIALOG = true;
              // 本账号直接授权
              let params = {
                paramType: "requestWxDeviceVoIP",
                did: Device.deviceID.toString(),
                userId: Service.account.ID.toString(),
                deviceName: Device.name,
                model: Device.model
              };
              Host.ui.requestWxDeviceVoIP(params);
            } else {
              if (Device.isReadonlyShared) {
                Toast.success("cloud_share_hint");
                return;
              }
              // 其他账号，弹出邀请授权弹框
              this.inviteUser = item;
              this.setState({ showEmpowerDialog: true });
            }
          }}
        />
      </View>
    );
  };

  _renderEmptyView = () => {
    return(
      <View style={ { alignItems: 'center', marginBottom: 60, marginTop: 40 } }>
        <Image
          style={ { height: 60, width: 92 } }
          source={ require('../../Resources/Images/icon_share_no.webp') }/>
        <Text style={ {color: '#********', fontSize: 14, marginTop: 12 }}>{ LocalizedStrings["no_contact"] }</Text>
        <Text style={ {color: '#********', fontSize: 14, marginTop: 3 }}>{ LocalizedStrings["no_contact_add_desc"] }</Text>
      </View>
    )
  }
  _renderBottomItem() {
    return (
      <View style={{borderRadius: 16, backgroundColor: '#F6F6F6', marginHorizontal: 12, width: screenWidth - 24, padding: 20, marginBottom: 60}}>
        <TouchableOpacity
          style={{alignSelf:'flex-start'}}
          onPress={()=> {
            this.setState({ showServiceReminderDialog: true });
          }}>
          <Text style={{color: '#32BAC0',  fontSize: 14, textDecorationLine: 'underline',lineHeight: 18}}>{LocalizedStrings['wx_server']}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={{ marginTop: 12, alignSelf:'flex-start' }}
          onPress={ () => {
            this.setState({ showCancelEmpowerDialog: true });
          } }>
          <Text style={{color: '#32BAC0', fontSize: 14, textDecorationLine: 'underline',lineHeight: 18}}>{LocalizedStrings['cs_wx_cancel_invite']}</Text>
        </TouchableOpacity>
      </View>
    );
  };

  _renderEmpowerTips = () => {
    if (!this.state.showEmpowerTip) {
      return null;
    }

    let backgroundColor = Util.isDark() ? "#25A9AF32" : '#32BAC019';
    let channel = "videodetails_button";
    let tipsText = LocalizedStrings['cs_wx_call_empower_tips'];
    let tipsTextColor = Util.isDark() ? "#25A9AF" : "#32BAC0";

    let backIcon = require('../../Resources/Images/close_tips.png');
    let style = {
      flexDirection: "row",
      flexWrap: 'nowrap',
      alignItems: "center",
      justifyContent: "space-between",
      backgroundColor: backgroundColor,
      paddingLeft: 16,
      paddingRight: 8,
      paddingVertical: 11,
      borderRadius: 16
    };
    let mBgStyleBase = { alignItems: "center", justifyContent: "center" };
    let mBgStyle = [mBgStyleBase, {
      marginTop: 10,
      backgroundColor: Util.isDark() ? "#000000" : "#FFFFFF",
      marginHorizontal: 12,
      borderRadius: 16
    }];
    return (
      <View style={ mBgStyle }>
        <TouchableOpacity
          style={ style }

          ref={ (ref) => this.mCloudBuyTip = ref }
          onLayout={ (e) => {
            this.mCloudBuyTipWidth = e.nativeEvent.layout.width;
          } }
          // accessibilityLabel={!this.state.fullScreen ? DescriptionConstants.kj_2_5 : DescriptionConstants.kj_2_30}
        >
          <Text
            numberOfLines={ 3 }
            style={ [BaseStyles.text14, {
              paddingRight: 10,
              color: tipsTextColor,
              textAlign: "left",
              textAlignVertical: 'center',
              fontSize: 13,
              flex: 1
            }] }>{ tipsText }</Text>
          <ImageButton
            onPress={ () => {
              StorageKeys.WX_EMPOWER_TIP = true;
              this.setState({ showEmpowerTip: false });
            } }
            style={ { width: 22, height: 22 } }
            source={ backIcon }
          />
        </TouchableOpacity>
      </View>
    );
  };

  _renderToEmpowerDialog() {
    return (
      <AbstractDialog
        visible={ this.state.showEmpowerDialog }
        title={ LocalizedStrings['cs_invite_empower'] }
        subtitle={ LocalizedStrings['cs_invite_empower_desc'] }
        dialogStyle={ {
          titleStyle: {
            fontSize: 16.5
          }
        } }
        showSubtitle={ false }
        useNewTheme
        canDismiss={ false }
        onDismiss={ () => {
          this.setState({ showEmpowerDialog: false });
        } }
        buttons={ [
          {
            text: LocalizedStrings["cs_not_invite"],
            callback: (_) => {
              this.setState({ showEmpowerDialog: false });
            }
          },
          {
            text: LocalizedStrings["cs_wx_invite"],
            callback: (_) => {
              this.setState({ showEmpowerDialog: false });
              if (this.inviteUser) {
                let params = {
                  // paramType: "requestWxDeviceVoIP",
                  paramType: "shareWxDeviceVoIP",
                  did: Device.deviceID.toString(),
                  userId: `${ this.inviteUser.mijia }`,
                  deviceName: Device.name,
                  model: Device.model
                };
                console.log("======", params);
                // Host.ui.requestWxDeviceVoIP(params)
                Host.ui.shareWxDeviceVoIP(params);
              }
            }
          }
        ] }>
        <View>
          <Text style={ {
            fontSize: 16,
            fontWeight: "400",
            marginHorizontal: 28,
            lineHeight: 24
          } }>{ LocalizedStrings["cs_invite_empower_desc"] }</Text>

          <View style={{
            margin: 28,
            display: 'flex',
            flexDirection: "row",
          }}>
            <Image style={ {
              height: 170,
              borderRadius: 12,
              flex: 1
            } }
                   source={ require("../../Resources/Images/wx_empower.webp") }/>
          </View>
        </View>
      </AbstractDialog>
    );
  }

  _renderServerWarning() {
    return (
      <MessageDialog
        visible={ this.state.showServiceReminderDialog }
        title={ LocalizedStrings['wx_server'] }
        message={ LocalizedStrings['wx_server_warning'] }
        useNewType={ true }
        onDismiss={ () => {
          this.setState({ showServiceReminderDialog: false });
        } }
        buttons={ [{
          text: LocalizedStrings['offline_divice_ok'],
          colorType: 'grayLayerBlack',
          callback: () => {
            this.setState({ showServiceReminderDialog: false });
          }
        }] }/>
    );
  }

  _renderCancelEmpowerDialog() {
    return (
      <AbstractDialog
        visible={ this.state.showCancelEmpowerDialog }
        title={ LocalizedStrings['cs_wx_cancel_invite_title'] }
        dialogStyle={ {
          titleStyle: {
            fontSize: 16.5
          }
        } }
        showSubtitle={ false }
        useNewTheme
        canDismiss={ false }
        onDismiss={ () => {
          this.setState({ showCancelEmpowerDialog: false });
        } }
        buttons={ [
          {
            text: LocalizedStrings["offline_divice_ok"],
            colorType: 'grayLayerBlack',
            callback: (_) => {
              this.setState({ showCancelEmpowerDialog: false });
            }
          }
        ] }>
        <View style={{

          // flex: 1
        }}>
          <Text style={ {
            fontSize: 16,
            fontWeight: "400",
            marginHorizontal: 28,
            marginBottom: 26,
            lineHeight: 24
          } }>{ LocalizedStrings["cs_wx_cancel_invite_msg"] }</Text>

          {/*<View style={{*/}
          {/*  margin: 28,*/}
          {/*  display: 'flex',*/}
          {/*  flexDirection: "row",*/}
          {/*}}>*/}
          {/*  <Image style={ {*/}
          {/*    height: 170,*/}
          {/*    borderRadius: 12,*/}
          {/*    flex: 1*/}
          {/*  } }*/}
          {/*         source={ require("../../Resources/Images/pic_cancel_empower.webp") }/>*/}
          {/*</View>*/}

        </View>
      </AbstractDialog>
    );
  }

  openAllCallSettingSwitch() {
    if (!this.callSettingData) {
      Toast.fail("c_set_fail");
      return;
    }

    let upData = JSON.parse(JSON.stringify(this.callSettingData));
    upData.switch.wx = 1;
    upData.switch.mijia = 1;
    upData.switch.hand = 1;
    DeviceSettingUtil.setDeviceSetting(DeviceSettingUtil.clickCallSetting, JSON.stringify(upData)).then((res) => {
      this.callSettingData = upData;
      CallUtil.updateSettingToDevice([JSON.stringify(upData)]);
      this.setState({ switchValue: true, clickSwitchValue: true, gestureSwitchValue: true, showServiceReminderDialog: true });
    }).catch((error) => {
      this.setState({ switchValue: false });
      Toast.fail("c_set_fail");
    });
  }

  updateWxCallSettingSwitch(value,curItem,index) {
    let upData = JSON.parse(JSON.stringify(this.callSettingData));
    if (curItem && upData[curItem.key]) {
      upData[curItem.key].wx_switch = value ? 1 : 0;
    }
    // upData.switch.wx = value ? 1 : 0;
    DeviceSettingUtil.setDeviceSetting(DeviceSettingUtil.clickCallSetting, JSON.stringify(upData)).then((res) => {
      this.callSettingData = upData;
      upData['method'] = "1";
      CallUtil.updateSettingToDevice([JSON.stringify(upData)]);
      // contactsData
      this.setState({
        showServiceReminderDialog: value,
        contactsData: this.state.contactsData.map((item,idx)=>{
          if (index == idx) {
            return {...item, wx_switch: value ? 1 : 0};
          } else {
            return item;
          }
        })
      });
    }).catch((error) => {
      this.setState({ switchValue: !value });
      Toast.fail("c_set_fail");
    });
  }

  updateCallSettingSwitch(value) {
    let upData = JSON.parse(JSON.stringify(this.callSettingData));
    upData.switch.mijia = value ? 1 : 0;
    // todo delete
    upData.switch.wx = value ? 1 : 0;
    DeviceSettingUtil.setDeviceSetting(DeviceSettingUtil.clickCallSetting, JSON.stringify(upData)).then((res) => {
      this.callSettingData = upData;
      CallUtil.updateSettingToDevice([JSON.stringify(upData)]);
      this.setState({ switchValue: value });
    }).catch((error) => {
      this.setState({ switchValue: !value });
      Toast.fail("c_set_fail");
    });
  }

  _renderRenameDialog() {
    return (
      <InputDialog
        visible={ this.state.showRenameDialog }
        title={ LocalizedStrings["ai_call_name"] }
        inputs={ [{
          placeholder: LocalizedStrings["please_enter_name"],
          defaultValue: this.state.tempName,
          textInputProps: {
            autoFocus: true
          },
          onChangeText: (text) => {
            if (this.state.commentErr != null) {
              this.setState({ commentErr: null });
            }
            let isEmoji = Util.containsEmoji(text);
            let length = text.length;
            // let isCommon = this.isTextcommon(result);
            if (isEmoji) {
              this.setState({ isErrorName: true, commentErr: LocalizedStrings["Special_symbol_input_not_supported_temporarily"] });
            } else if (length > 10) {
              this.setState({ isErrorName: true, commentErr: LocalizedStrings["input_name_too_long2"] });
            } else if (length <= 0 || text.trim().length == 0) {
              this.setState({ isErrorName: true, commentErr: LocalizedStrings["add_feature_empty_tips"] });
            } else {
              this.setState({ isErrorName: false, commentErr: "error" });
            }
          },
          type: 'DELETE',
          isCorrect: !this.state.isErrorName
        }] }
        inputWarnText={ this.state.commentErr }
        noInputDisButton={ true }
        buttons={ [
          {
            callback: () => this.setState({ showRenameDialog: false, commentErr: null, isErrorName: false })
          },
          {
            callback: (result) => {
              console.log(`结果`, result.textInputArray[0]);
              if (this.state.isErrorName) {
                return;
              }
              let text = result.textInputArray[0].trim();
              if (text.length > 0 && !Util.containsEmoji(text)) {
                this.setState({ showRenameDialog: false, commentErr: null });
                if (!Device.isOnline) {
                  Toast.success("c_set_fail");
                  return;
                }
                let needUpData = JSON.parse(JSON.stringify(this.callSettingData));
                LogUtil.logOnAll("KeyCallSetting",this.changeContactType,needUpData.hasOwnProperty("key1"),needUpData.hasOwnProperty("key2"),needUpData.hasOwnProperty("key3"))
                if (this.currentItem && needUpData[this.currentItem.key]) {
                  needUpData[this.currentItem.key].callName = text;
                }
                let paramsStr = JSON.stringify(needUpData);
                DeviceSettingUtil.setDeviceSetting(`${ DeviceSettingUtil.clickCallSetting }`, paramsStr).then((res) => {
                  this.callSettingData = needUpData;
                  needUpData['method'] = "0";
                  let updateParams = JSON.stringify(needUpData);
                  CallUtil.updateSettingToDevice([updateParams]);
                  this.setState({
                    contactsData: this.state.contactsData.map((item, _index) => {
                      console.log("==================",item.key,this.currentItem.key);
                      if (item.key == this.currentItem.key) {
                        return {...item, callName: text};
                      } else {
                        return item;
                      }
                    })
                  })
                }).catch(error => {
                  Toast.fail("c_set_fail");
                });

              } else {
                if (Util.containsEmoji(text)) {
                  this.setState({ commentErr: LocalizedStrings["Special_symbol_input_not_supported_temporarily"] });
                }
              }
            }
          }
        ] }
        onDismiss={ () => this.setState({ showRenameDialog: false, commentErr: null, isErrorName: false }) }
      />
    );
  }

  _renderEditContactDialog() {
    let itemData = [{
      "title": LocalizedStrings['edit_contact_avatar'],
      textColor: '#000000',
      onPress: () => {
        if (!Device.isOnline) {
          // 设备已经离线了
          this.setState({ showEditContactDialog: false });
          Toast.success("c_set_fail");
          return;
        }
        this.setState({ showEditContactDialog: false, showAvatarIntroDialog: true });
      }
    }, {
      "title": LocalizedStrings['rename_contact'],
      textColor: '#000000',
      onPress: () => {
        this.setState({
          showEditContactDialog: false,
          showRenameDialog: true,
          tempName: this.currentItem ? this.currentItem.callName : ''
        });
        // this.setState({ showEditContactDialog: false });
        // this.props.navigation.navigate("ChoiceContactPage", { type: 1 });
      }
    }]
    if (Device.isOwner) {
      itemData.push({
        "title": LocalizedStrings['delete_contact'],
        textColor: '#F43F31',
        onPress: () => {
          this.setState({ showEditContactDialog: false, showDelDialog: true });
        }
      });
    }
    return (
      <AbstractDialog
        visible={ this.state.showEditContactDialog }
        useNewTheme={ true }
        title={ LocalizedStrings["edit_contact"] }
        dialogStyle={ {
          allowFontScaling: false,
          titleStyle: {
            fontSize: 16,
            fontWeight: "bold"
          }
        } }
        onDismiss={ (_) => this.setState({ showEditContactDialog: false }) }
        buttons={ [
          {
            text: LocalizedStrings["action_cancle"],
            colorType: "grayLayerBlack",
            callback: () => {
              this.setState({ showEditContactDialog: false });
            }
          }
        ] }
      >
        <View style={ {
          marginBottom: 16
        } }>
          { itemData.map((option, index) => <View key={ (option.title || '') + index }>
            <ChoiceItem
              type={ ChoiceItem.TYPE.SINGLE }
              titleStyle={ {
                fontSize: 16,
                fontWeight: "bold",
                color: option.textColor || '#000000'
              } }
              title={ option.title || '' }
              onPress={ () => option.onPress() }/>
          </View>) }
        </View>
      </AbstractDialog>
    );
  }

  renderDeleteDialog() {
    return (
      <MessageDialog
        visible={ this.state.showDelDialog }
        message={ LocalizedStrings['delete_contact_confirm'] }
        messageStyle={{ textAlign: 'center' }}
        canDismiss={ true }
        onDismiss={() => this.setState({ showDelDialog: false })}
        buttons={ [
          {
            text: LocalizedStrings["btn_cancel"],
            callback: (_) => {
              this.setState({ showDelDialog: false });
            }
          },
          {
            text: LocalizedStrings["delete_files"],
            callback: (_) => {
              this.setState({ showDelDialog: false });
              // 删除按键联系人
              this.deleteClickContact();
            }
          }
        ] }
      />
    );
  }

  deleteClickContact() {
    if (!Device.isOnline) {
      Toast.success("c_set_fail");
      return;
    }
    let needUpData = JSON.parse(JSON.stringify(this.callSettingData));
    if (this.currentItem && needUpData[this.currentItem.key]) {
      delete needUpData[this.currentItem.key];
    }
    let paramsStr = JSON.stringify(needUpData);
    DeviceSettingUtil.setDeviceSetting(`${ DeviceSettingUtil.clickCallSetting }`, paramsStr).then((res) => {
      // 成功后
      this.callSettingData = needUpData;
      Toast.success('delete_contact_success');
      needUpData['method'] = "0";
      let updateParams = JSON.stringify(needUpData);
      CallUtil.updateSettingToDevice([updateParams]);
      this.setState({
        contactsData: this.state.contactsData.filter((item, _index) => {
          if (item.key != this.currentItem.key) {
            return item;
          }
        })
      });
      // 删除setting中的头像数据
      let deleteKey = `call_${this.currentItem.key}`;
      DeviceSettingUtil.delDeviceSetting([deleteKey]).then((res) => {});
      // 删除本地缓存图片
      if (this.currentItem.objName && this.currentItem.objName != "") {
        let fileName = this.currentItem.objName.substring(this.currentItem.objName.lastIndexOf("/") + 1);
        Host.file.deleteFile(fileName).then(()=>{});
      }
    }).catch(error => {
      Toast.fail("c_set_fail");
    });
  }

  _renderAvatarIntroDialog() {
    return (
      <AbstractDialog
        visible={ this.state.showAvatarIntroDialog }
        useNewTheme={ true }
        title={ LocalizedStrings["edit_contact_avatar"] }
        dialogStyle={ {
          allowFontScaling: false,
          titleStyle: {
            fontSize: 16,
            fontWeight: "bold"
          }
        } }
        onDismiss={ (_) => this.setState({ showAvatarIntroDialog: false }) }
        buttons={ [
          {
            text: LocalizedStrings["action_cancle"],
            colorType: "grayLayerBlack",
            callback: () => {
              this.setState({ showAvatarIntroDialog: false });
            }
          }
        ] }
      >
        <View style={ {
          marginBottom: 16,
          flexDirection: 'column',
          alignItems: 'center'
        } }>
          <Image style={{width: 72, height: 72}} source={require("../../Resources/Images/icon_user.png")}/>
          <Text style={{fontSize: 12, color: 'rgba(0, 0, 0, 0.6)', marginTop: 6}}>{LocalizedStrings['edit_contact_avatar_desc']}</Text>
          <View style={{width: '100%', marginTop: 26}}>
            <TouchableHighlight
              style={{marginHorizontal: 27,  backgroundColor: '#32BAC0',justifyContent: 'center', alignItems: 'center', borderRadius: 23, height: 46}}
              underlayColor={"#32BAC0CC"}
              onPress={() =>{
                this.setState({ showAvatarContactDialog: true, showAvatarIntroDialog: false });
              }}
            >
              <Text style={{fontSize: 16, color: '#FFFFFF', textAlign: 'center'}}>{LocalizedStrings['upload_avatar']}</Text>
            </TouchableHighlight>
          </View>
        </View>
      </AbstractDialog>
    );
  }

  _renderChooseAvatarDialog() {
    return (
      <AbstractDialog
        visible={ this.state.showAvatarContactDialog }
        useNewTheme={ true }
        title={ LocalizedStrings["edit_contact_avatar"] }
        dialogStyle={ {
          allowFontScaling: false,
          titleStyle: {
            fontSize: 16,
            fontWeight: "bold"
          }
        } }
        onDismiss={ (_) => this.setState({ showAvatarContactDialog: false }) }
        buttons={ [
          {
            text: LocalizedStrings["action_cancle"],
            colorType: "grayLayerBlack",
            callback: () => {
              this.setState({ showAvatarContactDialog: false });
            }
          }
        ] }
      >
        <View style={ {
          marginBottom: 16
        } }>
          { [{
            "title": LocalizedStrings['takePhoto'],
            onPress: () => {
              this.setState({ showAvatarContactDialog: false });
              this.takePicture();
            }
          },  {
            "title": LocalizedStrings['select_dialog_album'],
            "redText": true,
            onPress: () => {
              this.setState({ showAvatarContactDialog: false });
              this.selectPicture();
            }
          }].map((option, index) => <View key={ (option.title || '') + index }>
            <ChoiceItem
              type={ ChoiceItem.TYPE.SINGLE }
              titleStyle={ {
                fontSize: 16,
                fontWeight: "bold"
              } }
              title={ option.title || '' }
              onPress={ () => option.onPress() }/>
          </View>) }
        </View>
      </AbstractDialog>
    );
  }

  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 存储卡/相册
    // status == 1 相机
    //
    let message = "";
    if (this.state.permissionRequestState == 0) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);
    } else {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_camera"]);
    }
    return (
      <MessageDialog
        title={LocalizedStrings["tips"]}
        message={message}
        messageStyle={{
          fontSize: 14,
        }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ showPermissionDialog: false });
            }
          },
          {
            text: LocalizedStrings["setting"],
            callback: () => {
              Host.ui.openTerminalDeviceSettingPage(1);
              this.setState({ showPermissionDialog: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog} />
    );
  }

  takePicture() {
    PermissionUtil.checkCameraPermission().then((res) => {
      console.log(res);
      this.props.navigation.navigate('AvatarDisplay',{
        type: 'camera',
        callback: (data) => {
          if (data) {
            this.updateAvatar(data);
          }

        }
      });
    }).catch((err) => {
      // 没有相机权限
      this.isCheckingPermission = false;
      if (err == PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
        this.setState({ showPermissionDialog: true, permissionRequestState: 1 });
      }
    });
  }

  selectPicture() {
    PermissionUtil.checkCameraPermission().then((res) => {
        PermissionUtil.checkStoragePermission().then((res) => {
          console.log(res);
          this.selectPhotoTapped();
        }).catch((err) => {
          // 没有读写手机存储权限
          if (err == PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
          }
        });
      }
    ).catch((err) => {
      // 没有相机权限
      if (err == PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
        this.setState({ showPermissionDialog: true, permissionRequestState: 1 });
      }
    })
  }

  selectPhotoTapped() {
    const options = {
      quality: 1.0,
      maxWidth: 1200,
      maxHeight: 1200,
      storageOptions: {
        skipBackup: true
      }
    };
    setTimeout(() => {
      ImagePicker.launchImageLibrary(options, (response) => {
        console.log('response',response)
        if (response.didCancel) {
          console.log('User cancelled photo picker');
          // Toast.fail("bind_error");
          return;
        } else if (response.error) {
          console.log('ImagePicker Error: ', response.error);
          Toast.fail("bind_error");
          return;
        } else if (response.customButton) {
          console.log('User tapped custom button: ', response.customButton);
          Toast.fail("bind_error");
          return;
        } else {
          // Toast.loading('c_setting');
          let path = response.uri.slice(7)
          console.log(path,"path");
          this.cropImage(response);
          // this.props.navigation.navigate('AvatarDisplay',{
          //   type: 'photo',
          //   data: response,
          //   callback: (data) => {
          //     if (data) {
          //       if (data.action == 'cancel') {
          //         this.selectPhotoTapped();
          //       }
          //     }
          //
          //   }
          // });

        }

      })
    }, 100)


  }

  async cropImage(data) {
    try {
      let width = data.width;
      let height = data.height;
      let targetName = `avatar/crop_avatar.jpg`;
      let tempTargetName = `avatar/crop_avatar_temp.jpg`;
      let imgMin = Math.min(width,height);
      let cropSize = imgMin;
      let imgMax = Math.max(width,height);
      let x = 0, y = 0;
      if (width >= height) {
        x = (imgMax - imgMin) / 2;
      } else {
        y = (imgMax - imgMin) / 2;
      }
      Host.file.deleteFile(targetName).then((res) => {
        console.log("================",res)
        this.doCropImage(data,targetName,tempTargetName,cropSize,x,y);
      }).catch((error) => {
        console.log("================error",error);
        this.doCropImage(data,targetName,tempTargetName,cropSize,x,y);
      });

    } catch (e) {
      console.log("crop avatar error2",e)
      Toast.fail("c_set_fail");
    }
  }

  doCropImage(data,targetName,tempTargetName,cropSize,x,y) {
    Host.file.writeFileThroughBase64(tempTargetName, data.data).then(async result => {
      console.log("write file success", result);
      let params = {
        offset: { x: x, y: y },
        size: { width: cropSize, height: cropSize },
        displaySize: { width: 238, height: 238 }
      };
      console.log("crop params", params);
      Host.file.cropImage(targetName,tempTargetName, params).then((res) => {
        console.log("is success", res);
        let filepath = `${ Host.file.storageBasePath }/${ targetName }`;

        this.props.navigation.navigate('AvatarDisplay',{
          type: 'photo',
          // data: { filename: targetName },
          data: { filename: targetName, originFilename: tempTargetName },
          callback: (data) => {
            if (data) {
              if (data.action == 'cancel') {
                this.selectPhotoTapped();
              } else {
                this.updateAvatar(data)
              }
            }

          }
        });
      }).catch((error) => {
        console.log("crop avatar error1", error);
      });

    }).catch(err => {
      console.log("write file error", err);
      Toast.fail("c_set_fail");
    })
  }

  /**
   * @Author: byh
   * @Date: 2024/8/8
   * @explanation:
   * 更新联系人头像，更新方式变更
   * 不在更新基础信息数据，只更新联系人头像
   *********************************************************/
  updateAvatar(data) {
    // 更新联系人信息
    // let needUpData = JSON.parse(JSON.stringify(this.callSettingData));
    let needUpData = {};
    if (this.currentItem && this.currentItem.key) {
      needUpData.icon = this.currentItem.icon ? this.currentItem.icon: "";
      needUpData.fdsUrl = data.downloadUrl;
      needUpData.objName = data.objName;
    }

    let paramsStr = JSON.stringify(needUpData);
    // 更新头像
    let contactKey = `call_${this.currentItem.key}`;
    DeviceSettingUtil.setDeviceSetting(contactKey, paramsStr).then((res) => {
      Toast.success("c_set_success");
      // 这里只下发了头像
      let updateObj = {method: "1", [contactKey]: needUpData};
      let updateParams = JSON.stringify(updateObj);
      console.log("change avatar",updateParams);
      CallUtil.updateSettingToDevice([updateParams]);
      // 更新修改后的头像
      // 已fds图片来存到本地
      FDSUtil.getLocalImgUrl(data.objName,data.downloadUrl).then((local) => {
        this.setState({
          contactsData: this.state.contactsData.map((item, _index) => {
            console.log("==================",item.key,this.currentItem.key);
            if (item.key == this.currentItem.key) {
              return {...item, objName: data.objName, fdsUrl: data.downloadUrl, localFileName: local};
            } else {
              return item;
            }
          })
        })
      }).catch((err) => {

      });

    }).catch(error => {
      Toast.fail("c_set_fail");
    });
  }

}

const stylesWX = StyleSheet.create({
  white_blank: {
    height: 0.5,
    marginTop: 20
  },

  whiteblank: {
    height: 0.5,
    marginHorizontal: 24,
    backgroundColor: "#e5e5e5",
    marginBottom: 30,
    marginTop: 38

  }

});