import React from 'react';
import { Text, View, Image } from 'react-native';
import PropTypes from 'prop-types';
import { ConfigContext } from "mhui-rn/dist/components/configProvider";
import { styles, Color, Constants, Opacity } from '../../common/styles/Styles';
import SlideGearWithoutBlock from './SlideGearWithoutBlock';


const DEFAULT_IMAGE_SIZE = 40;
const DEFAULT_IMAGE_MARGIN_RIGHT = 24;

const DEFAULT_SLIDER_HEIGHT = 40;


/**
 * @export public
 * @module Slider
 * @description Slider for CarIoT
 * @property {string} title - 标题
 * @property {object} imageSource - 图片source
 * @property {object} imageSourceDark - 深色模式图片source（如果不设置，则深浅色模式都显示imageSource）
 * @property {int} value - 初始显示数值，[minValue, maxValue]
 * @property {int} minValue - 最小数值
 * @property {int} maxValue - 最大数值
 * @property {int} step - 步长
 * @property {function} onValueChange - 滑动变化回调方法
 * @property {function} onSlidingComplete - 滑动结束的回调方法
 * @property {bool} disabled - 是否禁用，默认值 false
 * @property {object} style - 自定义style
 */
class Slider extends React.PureComponent {
  static contextType = ConfigContext;
    static propTypes = {
      title: PropTypes.string,
      imageSource: PropTypes.object,
      imageSourceDark: PropTypes.object,
      value: PropTypes.number,
      minValue: PropTypes.number,
      maxValue: PropTypes.number,
      step: PropTypes.number,
      onValueChange: PropTypes.func,
      onSlidingComplete: PropTypes.func,
      disabled: PropTypes.bool,
      style: PropTypes.object
    };

    constructor(props) {
      super(props);

      this.defaultMinValue = 0;
      this.defaultMaxValue = 100;
      this.defaultStep = 1;
      this.defaultValue = 50;

    }

    componentDidMount() {
    
    }

    render() {
      const {
        colorScheme
      } = this.context;
      const opacity = this.props.disabled ? Opacity.Disabled : Opacity.Normal;

      const sourceDark = this.props.imageSourceDark ? this.props.imageSourceDark : this.props.imageSource;
      const minValue = this.props.minValue ? this.props.minValue : this.defaultMinValue;
      const maxValue = this.props.maxValue ? this.props.maxValue : this.defaultMaxValue;
      const step = this.props.step ? this.props.step : this.defaultStep;

      return (
        <View style={[{
          marginTop: Constants.DEFAULT_MARGIN_TOP_BOTTOM, 
          marginBottom: Constants.DEFAULT_MARGIN_TOP_BOTTOM,
          opacity
        }, this.props.style]}
        pointerEvents={this.props.disabled ? "none" : "auto"}
        >
          {this.props.title ? <View style={{ flexDirection: "row", marginBottom: Constants.DEFAULT_TEXT_MARGIN_BOTTOM }}>
            {(this.props.imageSource || this.props.imageSourceDark) ? <Image style={{ width: DEFAULT_IMAGE_SIZE, height: DEFAULT_IMAGE_SIZE, marginRight: DEFAULT_IMAGE_MARGIN_RIGHT }} 
              source={colorScheme == 'dark' ? sourceDark : this.props.imageSource} 
            /> : null}
            <Text numberOfLines={1} style={[styles.titleTextStyle]}>{this.props.title}</Text>
          </View> : null}
          <View 
            style={[styles.borderStyle, { justifyContent: 'center', height: DEFAULT_SLIDER_HEIGHT, borderRadius: DEFAULT_SLIDER_HEIGHT / 2 }]}>
            <SlideGearWithoutBlock
              containerStyle={{ height: DEFAULT_SLIDER_HEIGHT }}
              showEndText={false}
              minimumTrackTintColor={colorScheme == 'dark' ? Color.Dark.AccentPrimary : Color.Light.AccentPrimary }
              maximumTrackTintColor="transparent"
              value={this.props.value}
              optionMin={minValue}
              optionMax={maxValue}
              optionStep={step}
              onValueChange={(value) => {           
                if (this.props.onValueChange) {
                  this.props.onValueChange(value);
                }
              }}
              onSlidingComplete={(value) => {
                if (this.props.onSlidingComplete) {
                  this.props.onSlidingComplete(value);
                }
              }} 
            />
          </View>
        </View>
      );
    }
}

export default Slider;
export { Slider };