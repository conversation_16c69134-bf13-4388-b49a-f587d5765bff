import React from 'react';
import {
  View,
  ScrollView,
  Text,
  TouchableOpacity,
  Image,
  Animated,
  NativeModules,
  TouchableHighlight, ActivityIndicator
} from 'react-native';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import {
  styles,
  Dividers,
  HeaderComponent,
  Constants,
  Radius,
  Button,
  Opacity,
  Font, SegmentControl
} from 'micariot-ui-sdk';
import { CarPluginWindow, carStyles } from './common/Styles';
import { DarkMode, Device, Package, PackageEvent } from "miot";
import StorageKeys from "../StorageKeys";
import AlarmUtilV2, { SPEC_PIID_KEY_VOLUME, SPEC_SIID_KEY_SPEAKER } from "../util/AlarmUtilV2";
import Toast from "../components/ToastCar";
import ClickableCardButton from "../widget/ClickableCardButton";
import Singletons from "../framework/Singletons";
import { Tabs } from "../allVideo/AllStorage";
import { DefFilter } from "../widget/EventList";
import { CldDldTypes } from "../framework/CloudEventLoader";
import ABTest from "../util/ABTest";
import EventGridCar from "../widget/EventGridCar";
import SdcardEventLoader from "../framework/sdcard/SdcardEventLoader";
import { strings as I18n } from "miot/resources";
import CarCommonDialog from "./widget/CarCommonDialog";
import CameraConfig from "../util/CameraConfig";
import LogUtil from "../util/LogUtil";
import NearHandDialog from "./widget/NearHandDialog";

// export const MaxSel = 50;
export const MaxSel = 20;
const underlayColor = 'rgba(0,0,0,.05)';
const TAG = "CarStorage";
export default class CarStorage extends React.Component {
  static navigationOptions = (navigation) => {
    return {
      headerTransparent: true,
      header: null
    };
  };

  constructor(props, context) {
    super(props, context);
    this.mSelEv = [];
    this.mCouldLoader = Singletons.CloudEventLoader;
    this.mSdcardLoader = SdcardEventLoader.getInstance();
    this.state = {
      currentIndex: 0,
      isVideoSaveOpen: true,
      mDate: new Date(),
      isEditing: true,
      showDeleteDialog: false,
      showLoading: false
    };
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'willFocus',
      () => {
        LogUtil.logOnAll(TAG, "page willFocus");
        this.getSaveVideoSwitchState();
      }
    );
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'didBlur',
      () => {
        this.isPageForeGround = false;
      }
    );

    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      LogUtil.logOnAll(TAG, "page _onResume");
    });
  }

  componentDidMount() {
    this.getSaveVideoSwitchState();
  }

  componentWillUnmount() {
    this.didFocusListener && this.didFocusListener.remove();
    this.delayToDoNextStep && clearTimeout(this.delayToDoNextStep);
  }

  getSaveVideoSwitchState() {
    StorageKeys.CAR_STORAGE.then((res) => {
      console.log("CAR_STORAGE:", res);
      if (typeof (res) === "string" || res == null) {
        this.setState({ isVideoSaveOpen: false });
      } else {
        this.setState({ isVideoSaveOpen: res });
      }
    }).catch((error) => {
      console.log("CAR_STORAGE error:", error);
    });

  }

  render() {

    return (
      <View style={ carStyles.mediumLargeContainerStyle }>
        { this.renderTitle() }
        { this.renderSwitchIsOp() }
        { this.renderContentView() }

        {this.renderLoadingView()}
        {this._renderSelectItemDialog()}

      </View>
    );
  }

  renderTitle() {
    let titleStr = LocalizedStrings['video_manager'];
    if (this.mSelEv.length > 0) {
      titleStr = LocalizedStrings["selected_cloud_count_car"].replace("%1$d", this.mSelEv.length);
    }
    return (
      <View style={ [{
        flexDirection: 'row',
        alignItems: 'center',
        height: 148,
        zIndex: 5,
        backgroundColor: carStyles.mediumLargeContainerStyle.backgroundColor
      }, this.props.style] }>
        <TouchableOpacity
          onPress={ () => {
            this.props.navigation.goBack();
          } }
        >
          <Image
            style={ { width: 48, height: 48 } }
            source={ DarkMode.getColorScheme() == 'dark' ? require("../../Resources/Images/car/ic_nav_arrow.png") : require("../../Resources/Images/car/ic_nav_arrow_light.png") }/>
        </TouchableOpacity>
        <View
          style={ {
            flexDirection: 'row',
            alignItems: 'center',
            flex: 1,
            justifyContent: 'center',
            width: '100%',
            // height: '100%',
            position: 'absolute'
          } }
        >
          <Text numberOfLines={ 1 } style={ [styles.titleTextStyle, {
            fontSize: Font.Size._36
          }] }>{ titleStr }</Text>
        </View>
        {
          CameraConfig.isDeviceSupportCloud() ?
            <TouchableOpacity style={{position: 'absolute', right: 0}} onPress={() => {
              this.setState({ showSelectDialog: true });
            }}>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <Text style={{ fontSize: 28, color: styles.subTitleTextStyle.color }}>{ this.state.currentIndex === 0 ? LocalizedStrings['car_sd_title'] : LocalizedStrings['s_cloud_setting']}</Text>
                <Image
                  style={ { width: 48, height: 48 } }
                  source={ DarkMode.getColorScheme() == 'dark' ? require('../../Resources/Images/car/down_arrow.png') : require('../../Resources/Images/car/down_arrow_light.png') }/>
              </View>
            </TouchableOpacity> : null
        }

        <TouchableOpacity
          style={{position: 'absolute', right: 0}}
          onPress={() => {
            this.props.navigation.navigate('IntroducePage');
          }}>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Text style={{ fontSize: 28, color: carStyles.selectAllStyle.color }}>{ LocalizedStrings['how_export_video']}</Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  }

  _renderSelectItemDialog() {
    let modalStyle = {
      position: 'absolute',
      width: 320,
      height: 240,
      // top: 230,180
      top: 245,
      right: 513,
      alignSelf: 'center',
      borderRadius: Radius.PanelLevel
    };
    // #00000066
    let modalShadowStyle = {
      position: 'absolute',
      width: 480,
      height: 400,
      top: 173,
      right: 433,
      alignSelf: 'center',
      borderRadius: Radius.PanelLevel
    };
    return (
      <NearHandDialog
        style={ modalStyle }
        shadowStyle={ modalShadowStyle }
        showTitle={ false }
        visible={ this.state.showSelectDialog }
        showButton={ false }
        onDismiss={ () => {
          this.setState({ showSelectDialog: false });
        } }
        canDismiss={ true }
        useNewTheme={ true }
      >
        <View>
          <TouchableHighlight
            style={ [{
              display: "flex",
              flexDirection: 'row',
              alignItems: 'center',
              minHeight: 120,
              borderTopLeftRadius: Radius.PanelLevel,
              borderTopRightRadius: Radius.PanelLevel,
              backgroundColor: this.state.currentIndex === 0 ? carStyles.nearHandSelectedItemStyle.backgroundColor : carStyles.nearHandStyle.backgroundColor
            }] }
            underlayColor={ carStyles.nearHandItemStyle.backgroundColor }
            onPress={ () => {
              for (let ev of this.mSelEv) {
                ev.selected = undefined;
              }
              this.mSelEv = [];
              this.setState({ currentIndex: 0, isEditing: true, showSelectDialog: false });
            } }>
            <View style={{
              display: "flex",
              flexDirection: 'row',
              alignItems: 'center'
            }}>
              <Text style={ [styles.titleTextStyle, {
                paddingLeft: 40,
                paddingRight: 32,
                flex: 1,
                color: this.state.currentIndex === 0 ? carStyles.nearHandSelectedItemStyle.color : styles.titleTextStyle.color
              }] }>{ LocalizedStrings['car_sd_title'] }</Text>
              {this.state.currentIndex === 0 ? <Image style={{ width: 40, height: 40, marginRight: 40 }} source={DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/car/near_hand_check.png') : require('../../Resources/Images/car/near_hand_check_light.png')}/> : null}

            </View>

          </TouchableHighlight>

          <TouchableHighlight
            style={ [{
              display: "flex",
              flexDirection: 'column',
              justifyContent: 'center',
              minHeight: 120,
              borderBottomLeftRadius: Radius.PanelLevel,
              borderBottomRightRadius: Radius.PanelLevel,
              backgroundColor: this.state.currentIndex === 1 ? carStyles.nearHandSelectedItemStyle.backgroundColor : carStyles.nearHandStyle.backgroundColor
            }] }
            // activeOpacity={0.6}
            underlayColor={ carStyles.nearHandItemStyle.backgroundColor }

            onPress={ () => {
              for (let ev of this.mSelEv) {
                ev.selected = undefined;
              }
              this.mSelEv = [];
              this.setState({ currentIndex: 1, isEditing: true, showSelectDialog: false});
            } }>
            <View style={{
              display: "flex",
              flexDirection: 'row',
              alignItems: 'center'
            }}>
              <Text style={ [styles.titleTextStyle, {
                paddingLeft: 40,
                paddingRight: 32,
                flex: 1,
                color: this.state.currentIndex === 1 ? carStyles.nearHandSelectedItemStyle.color : styles.titleTextStyle.color
              }] }>{ LocalizedStrings['s_cloud_setting'] }</Text>
              {this.state.currentIndex === 1 ? <Image style={{ width: 40, height: 40, marginRight: 40 }} source={DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/car/near_hand_check.png') : require('../../Resources/Images/car/near_hand_check_light.png')}/> : null}

            </View>
          </TouchableHighlight>
        </View>

      </NearHandDialog>
    );
  }

  /**
   * 是否打开过存储开发按钮
   */
  renderSwitchIsOp() {
    if (this.state.isVideoSaveOpen) {
      return null;
    }
    return (
      <View style={ { flex: 1, alignItems: 'center' } }>
        <Image
          style={ carStyles.videoStorageNotOpenImage }
          source={ DarkMode.getColorScheme() === 'dark' ? require('../../Resources/Images/car/empty_not_open.png') : require('../../Resources/Images/car/empty_not_open_light.png') }/>
        <Text style={ styles.loadingFailedTextStyle }>{ LocalizedStrings['video_save_not_open'] }</Text>
        <ClickableCardButton
          showImage={ false }
          style={ { width: 248, justifyContent: 'center', marginTop: 48 } }
          title={ LocalizedStrings['targetPushTitle_subtitle1'] }
          onPress={ () => {
            this.props.navigation.navigate('CarSetting');
          } }
        />
      </View>
    );
  }

  renderContentView() {
    if (!this.state.isVideoSaveOpen) {
      return null;
    }
    return (
      <>
        {/*<View style={{ zIndex: 5, backgroundColor: carStyles.mediumLargeContainerStyle.backgroundColor }}>*/}
        {/*  <SegmentControl*/}
        {/*    values={ [LocalizedStrings['car_sd_title'], LocalizedStrings['s_cloud_setting']] }*/}
        {/*    selectedIndex={ this.state.currentIndex }*/}
        {/*    style={ { marginBottom: Constants.DEFAULT_MARGIN_TOP_BOTTOM} }*/}
        {/*    onChange={ (index) => {*/}
        {/*      console.log(`SegmentControl selected: ${ index }`);*/}
        {/*      for (let ev of this.mSelEv) {*/}
        {/*        ev.selected = undefined;*/}
        {/*      }*/}
        {/*      this.mSelEv = [];*/}
        {/*      this.setState({ currentIndex: index, isEditing: true});*/}
        {/*    } }*/}
        {/*    disabled={ false }*/}
        {/*  />*/}
        {/*</View>*/}
        <EventGridCar
          ref={ (aEvLst) => {
            this.mSdEvLst = aEvLst;
          } }
          loader={ SdcardEventLoader.getInstance() }
          showTab={ this.state.currentIndex == 0 }
          loaderArgs={ { filter: DefFilter, startDate: this.state.mDate } }
          selectAll={ this.props.selectAll }
          eventHeaderView={ this.props.isEditing ? null : this.state.lstHeader }
          eventHeaderHeight={ this.state.headerHeight }
          eventEmptyView={ this.state.listEmptyView }
          onEventPress={ this.mEvPressed }
          onEventLongPress={ this.mEvLongPressed }
          onScroll={ this.mScroll }
          emptyDes={ this.state.emptyDes == null ? this.props.emptyDes : this.state.emptyDes }
          emptyIcon={ this.state.emptyIcon }
          isEditing={ this.state.isEditing }
          isSltDay={ this.mSltDay }
          // isSltDayMore={this.mSltDayMore}
          onShowCalendar={ this.mShowPopupView }
          onStartEdit={ this.mStartEdit }
          onGetDataDone={ this.mGetDataDone }
          refreshCallback={ this.mRefreshCallback }
          onSwitchSltDayMore={ this.mSwitchSltDayMore }
          // onSelectAllCB={ this.mSelectAllCB }
          typeTab={ Tabs.Card }
          type={ null }
          autoRefresh={ false }
          abType={ ABTest.Types.DFT }
        />
        {
          CameraConfig.isDeviceSupportCloud() ?
            <EventGridCar
              ref={ (aEvLst) => {
                this.mEvLst = aEvLst;
              } }
              loader={ Singletons.CloudEventLoader }
              showTab={ this.state.currentIndex == 1 }
              loaderArgs={ { filter: DefFilter, startDate: this.state.mDate } }
              selectAll={ this.props.selectAll }
              eventHeaderView={ this.props.isEditing ? null : this.state.lstHeader }
              eventHeaderHeight={ this.state.headerHeight }
              eventEmptyView={ this.state.listEmptyView }
              onEventPress={ this.mEvPressed }
              onEventLongPress={ this.mEvLongPressed }
              refreshCallback={ this.mRefreshCallback }
              onScroll={ this.mScroll }
              emptyDes={ this.state.emptyDes == null ? this.props.emptyDes : this.state.emptyDes }
              emptyIcon={ this.state.emptyIcon }
              isEditing={ this.state.isEditing }
              isSltDay={ this.mSltDay }
              // isSltDayMore={this.mSltDayMore}
              onShowCalendar={ this.mShowPopupView }
              onStartEdit={ this.mStartEdit }
              onGetDataDone={ this.mGetDataDone }
              onSwitchSltDayMore={ this.mSwitchSltDayMore }
              // onSelectAllCB={ this.mSelectAllCB }
              typeTab={ Tabs.Cloud }
              type={ CldDldTypes.Files }
              autoRefresh={ true }
              abType={ ABTest.Types.DFT }
            /> : null
        }

        {this.renderBottomButtons()}

        {this.deleteConfirmDialog()}
      </>
    );
  }


  mGetDataDone = (count, allItems = []) => {

  }
  // 刷新回调，清除标记的选中数据
  mRefreshCallback = () => {
    for (let ev of this.mSelEv) {
      ev.selected = undefined;
    }
    this.mSelEv = [];
    console.log("+++++++++++++mRefreshCallback", this.mSelEv.length);
    this.state.currentIndex === 0 ? this.mSdEvLst.clearSelect() : this.mEvLst.clearSelect();
    this.setState({ isEditing: true });
  }

  renderBottomButtons() {

    return <View style={ [{
      height: 88,
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 62,
      marginTop: 42
    }] }>
      <TouchableHighlight
        style={ {
          flex: 1,
          borderRadius: Radius.PanelLevel,
          marginRight: 12
        } }
        onPress={ () => {
          console.log("{{{{{{{{{{{{{{{{{{{{{{{{");
          if (this.mSelEv.length < 1) {
            Toast.fail("bottom_action_tip");
          } else {
            this.setState({ showDeleteDialog: true });
          }
        } }
        disabled={this.mSelEv.length < 1}
        underlayColor={ underlayColor }>
        <View style={ {
          flex: 1,
          backgroundColor: styles.itemHighlightStyle.backgroundColor,
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: Radius.WidgetLevel,
          opacity: this.mSelEv.length < 1 ? 0.35 : 1
        } }>
          <Text style={ [{
            fontSize: styles.buttonTextStyle.fontSize,
            lineHeight: 35,
            color: styles.buttonTextStyle.color,
            fontFamily: 'D-DINCondensed-Bold'
          }, carStyles.dialogButtonSureStyle] }>
            { LocalizedStrings['delete_files'] }
          </Text>
        </View>
      </TouchableHighlight>

      <TouchableHighlight
        style={ { flex: 1, marginLeft: 12, borderRadius: Radius.WidgetLevel } }
        onPress={ () => {
          this.exitEdit();
          this.props.navigation.goBack();
        } }
        underlayColor={ underlayColor }>
        <View style={ {
          flex: 1,
          backgroundColor: styles.itemSecondaryStyle.backgroundColor,
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: Radius.WidgetLevel,

        } }>
          <Text style={ [{
            fontSize: styles.buttonTextStyle.fontSize,
            lineHeight: 35,
            color: styles.buttonTextStyle.color,
            fontFamily: 'D-DINCondensed-Bold' // TODO: 英文字体，中文加粗效果

          }, carStyles.dialogButtonCancelStyle] }>
            { LocalizedStrings['action_cancle'] }
          </Text>
        </View>
      </TouchableHighlight>
    </View>;
  }

  renderLoadingView() {
    if (!this.state.showLoading) {
      return null;
    }
    let isDark = DarkMode.getColorScheme() == "dark";
    return (
      <View style={{ position: "absolute", width: CarPluginWindow.MediumLarge.Width, height: "100%", display: "flex", justifyContent: "center", alignItems: "center", zIndex:9}}>
        <ActivityIndicator
          style={{ width: 80, height: 80 }}
          color={isDark ? "xm#ffffff" : "xm#000000"}
          size={40}
        />
        <Text style={[{ fontSize: 28, marginTop: 10, color: "#000000" }]}>
          {LocalizedStrings["in_deleting"]}
        </Text>
      </View>
    );
  }

  deleteConfirmDialog() {
    return (
      <CarCommonDialog
        visible={ this.state.showDeleteDialog }
        title={ LocalizedStrings['delete_video'] }
        message={ LocalizedStrings["delete_video_desc"].replace("%d", this.mSelEv.length)}
        onDismiss={ () => this.setState({ showDeleteDialog: false }) }
        buttons={ [
          {
            text: LocalizedStrings['delete_confirm'],
            backgroundColor: DarkMode.getColorScheme() == "dark" ? "xm#B52C2E" : "xm#CC4239",
            callback: () => {
              this.setState({ showDeleteDialog: false });
              this.deletePressed();
            }
          }, {
            text: I18n.cancel,
            callback: (res) => {
              this.setState({ showDeleteDialog: false });
            }
          }
        ] }
      />
    );
  }

  deletePressed = () => {
    let selectedItems = [];
    for (let i = 0; i < this.mSelEv.length; i++) {
      let selEv = this.mSelEv[i];
      if (selEv.mediaType === "sdcard" && selEv.subTimestamps != null) { // 针对sd卡做一点处理。
        for (let i = 0; i < selEv.subTimestamps.length; i++) {
          let timestamp = selEv.subTimestamps[i].startTime;
          let save = selEv.subTimestamps[i].save;
          selectedItems.push({ startTime: timestamp, save: save });
        }
      } else {
        selectedItems.push(selEv);
      }
    }

    this.setState({ showLoading: true });
    let mLoader = this.state.currentIndex === 0 ? this.mSdcardLoader : this.mCouldLoader;
    const size = selectedItems.length;
    console.log("++++++++++++++++++delete size",size);
    mLoader.delete(selectedItems, true)
      .then((res) => {
        if (size > 400) {
          this.delayToDoNextStep && clearTimeout(this.delayToDoNextStep);
          this.delayToDoNextStep = setTimeout(() => {
            this.doAfterDelete();
          }, 5000);
        } else {
          this.doAfterDelete();
        }
      }).catch((aErr) => {
        console.log(this.tag, "del error", aErr);
        Toast.fail('delete_failed');
        this.setState({ showLoading: false });
      });
  }

  doAfterDelete() {
    Toast.show("delete_success");
    this.state.currentIndex === 0 ? this.mSdEvLst.removeEvents((aEv) => { return !aEv.selected; }) : this.mEvLst.removeEvents((aEv) => { return !aEv.selected; });
    // this.mSelectAllCB(false);
    this.exitEdit();
    this.setState({ showLoading: false });
    if (this.state.currentIndex === 1) {
      // 删除了云存视频
      CameraConfig.needRefreshCloudVideo = true;
      CameraConfig.needCheckCloudVideoExist = true;
    }
  }

  exitEdit() {
    // this.props.startEdit(null);
    for (let ev of this.mSelEv) {
      ev.selected = undefined;
    }
    this.mSelEv = [];
    this.state.currentIndex === 0 ? this.mSdEvLst.clearSelect() : this.mEvLst.clearSelect();
    this.setState({ isEditing: true });
  }

  mEvLongPressed = (aItm) => {
    if (this.state.isEditing) {
      return;
    }
    if (!aItm.selected) {
      if (MaxSel === this.mSelEv.length) {
        Toast.show(LocalizedStrings['max_select_car_noti']);
      } else {
        this.mSelEv.push(aItm);
        aItm.selected = true;
      }
    }

    this.setState({ isEditing: true });
  };

  mEvPressed = (aItm, aExtra = null, tapSelectAll = false) => {
    console.log(this.tag, "mEvPressed", aItm);
    let ret = true;
    if (this.state.isEditing) {
      if (!aItm.selected) {
        if (MaxSel === this.mSelEv.length) {
          Toast.show(LocalizedStrings['max_select_car_noti']);
          ret = false;
        } else {
          this.mSelEv.push(aItm);
          aItm.selected = true;
        }
      } else {
        this.mSelEv = this.mSelEv.filter((arg) => {
          console.log("=================", arg.fileId, aItm.fileId);
          return arg.fileId !== aItm.fileId;
        });
        aItm.selected = false;
      }

      // let str = Util.fmtStr(LocalizedStrings["storage_sel"], this.mSelEv.length);
      // this.props.startEdit({ title: str });
      // console.log(this.tag, str);
      this.setState({});
    }
    return ret;
  };

}