'use strict';

import React from 'react';
import {
  BackHandler,
  Dimensions,
  Image,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import ChoiceItem from "../widget/ChoiceItem";
import {localStrings as LocalizedStrings} from '../MHLocalizableString';

const {width: screenWidth} = Dimensions.get("screen");
import Toast from '../components/Toast';
import AlarmUtil from '../util/AlarmUtil';
import NavigationBar from "miot/ui/NavigationBar";
import AbstractDialog from "miot/ui/Dialog/AbstractDialog";
import MHDatePicker from "miot/ui/MHDatePicker";
import SpecUtil from "../util/SpecUtil";
import {ChoiceDialog} from "miot/ui/Dialog";
import {ChoiceItem as MHChoiceItem, ListItem, MessageDialog} from 'mhui-rn';
import { DarkMode, Host } from "miot";
import AlarmUtilV2, {
  PIID_DETECTION_REPEAT,
  PIID_MOTION_DETECTION_END_TIME,
  PIID_MOTION_DETECTION_START_TIME,
  SIID_MOTION_DETECTION
} from "../util/AlarmUtilV2";

export default class SurveillancePeriodSettingV2 extends React.Component {

  static navigationOptions = ({navigation}) => {
    return {
      header: (
        <NavigationBar
          type={NavigationBar.TYPE.LIGHT}
          backgroundColor={"#F6F6F6"}
          left={navigation.getParam('left')}
          right={navigation.getParam('right')}
          title={navigation.getParam("navTitle")}
        />
      )
    };
  };

  constructor(props, context) {
    super(props, context);
    this.state = {
      type: 0,
      startTime: this.props.navigation.state.params ? this.props.navigation.state.params.startTime : "00:00",
      endTime: this.props.navigation.state.params ? this.props.navigation.state.params.endTime : "23:59",
      periodRepeat: this.props.navigation.state.params ? this.props.navigation.state.params.repeat : [1, 1, 1, 1, 1, 1, 1],

      showTimeDialog: false,
      showRepeatModeDialog: false,
      showRepeatWeekDialog: false,
      showSaveDialog: false,

      enablePeriod: "00:00-23:59",
      canSave: false
    };
    if (this.props.navigation.state.params) {
      /*  this.startTime = this.props.navigation.state.params.startTime;
        this.endTime = this.props.navigation.state.params.endTime;*/
      this.commitCallback = this.props.navigation.state.params.commitCallback;
      this.igRemoteWork = this.commitCallback != null;
    }
    this.repeatArrayCopy = JSON.parse(JSON.stringify(this.state.periodRepeat));
  }

  render() {
    return (
      <View style={{flex: 1, backgroundColor: "#F6F6F6"}}>
        <ScrollView showsVerticalScrollIndicator={false}
                    scrollEventThrottle={1}
                    onScroll={this.scrollViewScroll}
        >

          <View style={{flexDirection: "row", flexWrap: "wrap"}} key={6}>
            <Text style={{
              fontSize: 30,
              color:  "rgba(0, 0, 0, 0.80)",
              position: "relative",
              fontWeight:"300",
              marginLeft: 25,
              marginTop: 5,
              marginBottom: 22,
              fontFamily:'MI-LANTING--GBK1-Light'
            }}>
              {this.props.navigation.state.params.title}
            </Text>
          </View>


          <View style={{backgroundColor: "#F6F6F6"}}
                key={2}
          >
            {
              [
                {name: 'sps_all', subtitle: "sps_all_detail", type: 1},
                {name: 'sps_day', subtitle: "sps_day_detail", type: 2},
                {name: 'sps_night', subtitle: "sps_night_detail", type: 3}
              ].map((item, i) => {
                return (
                  <ChoiceItem title={LocalizedStrings[item.name]}
                              subtitle={LocalizedStrings[item.subtitle]}
                              key={i + 100}
                              keyString={i + 100}
                              containerStyle={{marginHorizontal: 22, marginVertical: 6, borderRadius:16}}
                              checked={this.state.type == item.type}
                              backgroundColor={this.darkMode ? 'dark' : 'white'}
                              titleColor={'rgba(0, 0, 0, 1)'}
                              subtitleColor={'rgba(0, 0, 0, 0.6)'}
                              selectIcon={require("../../Resources/Images/icon_single_checked.png")}
                              unselectIcon={DarkMode.getColorScheme() == "dark" ? require("../../Resources/Images/icon_single_unchecked_d.png") : require("../../Resources/Images/icon_single_unchecked.png")}
                              onlyChecked={true}
                              onValueChange={(value) => {
                                value && this.setState({type: item.type});
                                this._onSelectedItem(item.type);
                              }}
                  />
                );
              })
            }

            <ChoiceItem title={LocalizedStrings['sps_custom']} keyString={"key_2"}
                        containerStyle={{
                          height:74,
                          marginHorizontal: 22,
                          marginTop: 6,
                          borderRadius:16,
                          borderBottomLeftRadius: this.state.type == 4 ? 0 : 16,
                          borderBottomRightRadius: this.state.type == 4 ? 0 : 16
                        }}
                        subtitle={this.state.type == 4 ? "" : LocalizedStrings["sps_custom_detail"]}
                        checked={this.state.type == 4}
                        backgroundColor={this.darkMode ? 'dark' : 'white'}
                        titleColor={'rgba(0, 0, 0, 1)'}
                        subtitleColor={'rgba(0, 0, 0, 0.6)'}
                        selectIcon={require("../../Resources/Images/icon_single_checked.png")}
                        unselectIcon={DarkMode.getColorScheme() == "dark" ? require("../../Resources/Images/icon_single_unchecked_d.png") : require("../../Resources/Images/icon_single_unchecked.png")}
                        onlyChecked={true}
                        onValueChange={(value) => {
                          value && this.setState({type: 4});
                        }}
            />

            <ListItem key={"key_3"}
                      allowFontScaling={false}
                      containerStyle={{
                        width: screenWidth - 44,
                        height: 60,
                        marginHorizontal: 22,
                        paddingVertical: 20,
                        paddingHorizontal:20,
                        backgroundColor: this.darkMode ? 'dark' : 'white',
                        display: this.state.type == 4 ? "flex" : "none"
                      }}
                      titleStyle={{fontSize: 16, color: "#000000"}}
                      valueStyle={{fontSize: 13, color: "#999999"}}
                      title={LocalizedStrings.csps_start}
                      value={this.state.startTime}
                      onPress={(_) => {
                        this.setState({setTime: 0, showTimeDialog: true});
                      }}
                      showSeparator={false}/>

            <ListItem key={"key_4"}
                      allowFontScaling={false}
                      containerStyle={{
                        width: screenWidth - 44,
                        height: 60,
                        marginHorizontal: 22,
                        paddingVertical: 20,
                        paddingHorizontal:20,
                        backgroundColor: this.darkMode ? 'dark' : 'white',
                        borderBottomLeftRadius: 16,
                        borderBottomRightRadius: 16,
                        display: this.state.type == 4 ? "flex" : "none"
                      }}
                      titleStyle={{fontSize: 16, color: "#000000"}}
                      valueStyle={{fontSize: 13, color: "#999999"}}
                      title={LocalizedStrings.csps_end}
                      value={this.getEndTimeText()}
                      valueNumberOfLines={3}
                      onPress={(_) => {
                        this.setState({setTime: 1, showTimeDialog: true});
                      }}
                      showSeparator={false}/>


            <View style={[{height: 0.5,
              marginHorizontal: 24,
              backgroundColor: "#e5e5e5",
              marginBottom: 30,
              marginTop: 20}, {marginTop: 20, marginBottom: 20}]}/>

            <ListItem key={"key_5"}
                      allowFontScaling={false}
                      containerStyle={{
                        width: screenWidth - 44,
                        height: 80,
                        marginHorizontal: 22,
                        paddingVertical: 20,
                        paddingHorizontal:20,
                        backgroundColor: this.darkMode ? 'dark' : 'white',
                        borderRadius: 16
                      }}
                      titleStyle={{fontSize: 16, color: "#000000"}}
                      valueStyle={{fontSize: 13, color: "#999999"}}
                      title={LocalizedStrings['plug_timer_repeat']}
                      value={this._getRepeatValueText()}
                      valueMaxWidth={"45%"}
                      onPress={(_) => {
                        this.tempRepeatCopy = this.state.periodRepeat;
                        this.setState({showRepeatModeDialog: true});
                      }}
                      showSeparator={false}/>

          </View>

        </ScrollView>

        {this._renderTimeDialog()}
        {/*{this._repeatModeDialog()}*/}
        {this._renderRepeatViewDialog()}
        {this._repeatWeekDialog()}
        {this._renderBackDialog()}
      </View>
    );
  }

  scrollViewScroll = (event) => {
    const y = event.nativeEvent.contentOffset.y;
    // 设置标题栏和状态栏的透明度 titleOpacity
    // 当页面滚动的距离等于标题栏的高度时，其透明度变为1
    let flag = y > 28;
    if (this.showTitle == flag) {
      return;
    }
    if (flag) {
      this.showTitle = true;
      this.props.navigation.setParams({navTitle: this.props.navigation.state.params.title});
    } else {
      this.showTitle = false;
      this.props.navigation.setParams({navTitle: ""});
    }
  };

  componentDidMount() {
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
    this.refreshNavigationBar();
    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {
        if (this.igRemoteWork) {
          let startTime = AlarmUtil.formatTimeString(this.state.startTime);
          let endTime = AlarmUtil.formatTimeString(this.state.endTime);

          let detectionType = this._type(startTime, endTime);
          console.log("xxxx startTime:" + startTime + " endTime:" + endTime + " detectionType:" + detectionType + " xxxxx");
          this.startTime = startTime;
          this.endTime = endTime;


          this.setState({type: detectionType});
          return;
        }
      }
    );
  }

  onBackHandler = () => {
    if (this.state.canSave) {
      this.setState({showSaveDialog: true});
      return true;
    }
    return false;
  };

  refreshNavigationBar() {

    this.props.navigation.setParams({
      backgroundColor: "#F6F6F6",
      title: LocalizedStrings['sps_title'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: (this.state.canSave) ? NavigationBar.ICON.CLOSE : NavigationBar.ICON.BACK,
          onPress: () => {
            if (this.state.canSave) {
              this.setState({showSaveDialog: true});
              return;
            }
            this.props.navigation.goBack();
          }
        }
      ],
      right: [
        {
          key: (this.state.canSave) ? NavigationBar.ICON.COMPLETE : null,
          onPress: () => {
            if (this.state.startTime == this.state.endTime) {
              Toast.success("imi_start_equal_end");
              return;
            }
            this._saveChanges();
            this.props.navigation.getParam('commitCallback')(this.state.startTime, this.state.endTime, this.state.periodRepeat);
            this.props.navigation.goBack();
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });

  }

  componentWillUnmount() {
    this.willFocusSubscription.remove();
  }

  formatTimeString(timeStr) {
    if (!timeStr || typeof (timeStr) != 'string') {
      return timeStr;
    }
    let subs = timeStr.split(':');
    if (subs.length < 3) {
      return timeStr;
    }
    let ret = '';
    for (let i = 0; i < subs.length; ++i) {
      let sub = subs[i];
      if (sub.length < 2) {
        sub = `0${sub}`;
      }
      ret = ret + sub;
      if (i < subs.length - 1) {
        ret = `${ret}:`;
      }
    }
    return ret;
  }


  _type(startTime, endTime) {
    if (startTime == this._startTime(1)
      && endTime == this._endTime(1)) {
      return 1;
    } else if (startTime == "00:00"
      && endTime.includes("23:59")) {
      return 1;
    } else if (startTime == this._startTime(2)
      && endTime == this._endTime(2)) {
      return 2;
    } else if (startTime == this._startTime(3)
      && endTime == this._endTime(3)) {
      return 3;
    } else {
      return 4;
    }
  }

  _startTime(type) {
    switch (type) {
      case 1:
        return '00:00';
      case 2:
        return '08:00';
      case 3:
        return '20:00';
      default:
        return '12:34';
    }
  }

  _endTime(type) {
    switch (type) {
      case 1:
        return '23:59';
      case 2:
        return '20:00';
      case 3:
        return '08:00';
      default:
        return '23:45';
    }
  }

  _onSelectedItem(type) {
    this.setState({
      type: type,
      startTime: this._startTime(type),
      endTime: this._endTime(type),
      canSave: true
    }, () => this.refreshNavigationBar());
  }

  getEndTimeText() {
    let {startTime, endTime} = this.state;
    let text = `${endTime}`;
    let startValue = parseInt(startTime.split(":")[0]) * 60 + parseInt(startTime.split(":")[1]);
    let endValue = parseInt(endTime.split(":")[0]) * 60 + parseInt(endTime.split(":")[1]);
    if (startValue > endValue) {
      text = `${LocalizedStrings['setting_monitor_next_day']}${Host.locale.language == "zh" ? "" : " "}${endTime}`;
    }
    return text;
  }

  _renderBackDialog() {
    return (
      <MessageDialog
        visible={this.state.showSaveDialog}
        message={LocalizedStrings['exit_change_disappear']}
        messageStyle={{textAlign: "center"}}
        canDismiss={false}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: (_) => {
              this.setState({showSaveDialog: false});
            }
          },
          {
            text: LocalizedStrings["exit"],
            callback: (_) => {
              this.setState({showSaveDialog: false});
              this.props.navigation.goBack();
            }
          }
        ]}
      />
    )
  }

  _renderTimeDialog() {
    return (
      <MHDatePicker
        visible={this.state.showTimeDialog}
        title={this.state.setTime == 0 ? LocalizedStrings['csps_start'] : LocalizedStrings['csps_end']}
        type={MHDatePicker.TYPE.TIME24}
        onDismiss={() => this.setState({showTimeDialog: false})}
        datePickerStyle={{
          rightButtonStyle: {color: "#FFFFFF"},
          rightButtonBgStyle: {bgColorNormal: "#32BAC0", bgColorPressed: "#32BAC099"}
        }}
        onSelect={(res) => {
          let str = `${res.rawArray[0]}:${res.rawArray[1]}`;
          this.state.setTime == 0 ?
            this.setState({
              startTime: str,
              enablePeriod: `${str}-${this.state.endTime}`,
              canSave:true
            },() => this.refreshNavigationBar()) :
            this.setState({
              endTime: str,
              enablePeriod: `${this.state.startTime}-${str}`,
              canSave:true
            },() => this.refreshNavigationBar());
        }}
        current={this.state.setTime == 0 ? (this.state.startTime ? this.state.startTime.split(':') : new Date()) :
          (this.state.endTime ? this.state.endTime.split(':') : new Date())}
      />
    );
  }

  _getRepeatValueText() {
    let valueText = "";
    let repeat = this.state.periodRepeat;
    switch (JSON.stringify(repeat)) {
      case  JSON.stringify([1, 1, 1, 1, 1, 1, 1]):
        valueText = LocalizedStrings.plug_timer_everyday;
        break;
      default:
        valueText = repeat[0] == 1 ? `${LocalizedStrings.monday1}、` : "";
        valueText += repeat[1] == 1 ? `${LocalizedStrings.tuesday1}、` : "";
        valueText += repeat[2] == 1 ? `${LocalizedStrings.wednesday1}、` : "";
        valueText += repeat[3] == 1 ? `${LocalizedStrings.thursday1}、` : "";
        valueText += repeat[4] == 1 ? `${LocalizedStrings.friday1}、` : "";
        valueText += repeat[5] == 1 ? `${LocalizedStrings.saturday1}、` : "";
        valueText += repeat[6] == 1 ? `${LocalizedStrings.sunday1}、` : "";
        valueText = valueText.endsWith("、") ? valueText.slice(0, valueText.length - 1) : valueText;

    }
    return valueText
  }


  _repeatModeDialog() {
    return (
      <AbstractDialog
        useNewTheme={true}
        visible={this.state.showRepeatModeDialog}
        showSubtitle={false}
        onDismiss={() => {
          this.setState({showRepeatModeDialog: false, periodRepeat: this.repeatArrayCopy});
        }}
        showTitle={false}
        buttons={[
          {
            text:LocalizedStrings.btn_cancel,
            colorType: 'grayLayerBlack',
            callback: (_) => {
              this.setState({showRepeatModeDialog: false, periodRepeat: this.repeatArrayCopy});
            }
          },
          {
            text: LocalizedStrings.btn_confirm,
            colorType: 'blueLayerWhite',
            callback: (_) => {
              if (this.repeatItemClicked == 1) {//选择了自定义
                this.setState({showRepeatWeekDialog: true, showRepeatModeDialog: false});
              } else {
                this.setState({showRepeatModeDialog: false,canSave:true},()=>this.refreshNavigationBar());
              }
            }
          }
        ]}
      >
        {this._renderRepeatViewOld()}
      </AbstractDialog>
    );
  }

  _renderRepeatViewOld() {
    let repeat = JSON.stringify(this.state.periodRepeat);
    this.repeatItems = [
      {
        itemTile: LocalizedStrings.plug_timer_everyday,
        select: repeat == JSON.stringify([1, 1, 1, 1, 1, 1, 1])
      },
      {
        itemTile: LocalizedStrings.plug_timer_sef_define,
        select: repeat != JSON.stringify([1, 1, 1, 1, 1, 1, 1]) && repeat != JSON.stringify([0, 1, 1, 1, 1, 1, 0])
          && repeat != JSON.stringify([1, 0, 0, 0, 0, 0, 1])
      }
    ];
    return (
      <View style={{alignItems: "center"}}>
        <Text
          style={{
            fontSize: 16, color: "#000000", marginTop: 25, fontWeight: "700"
          }}
        >
          {LocalizedStrings["plug_timer_repeat"]}
        </Text>
        <View style={{marginTop: 15, width: '100%',marginBottom:25}}>
          {this.repeatItems.map((item, index) => {
            if (item.select) {
              this.repeatItemClicked = index; //当前重复模式选择的条目
            }
            return (
              <MHChoiceItem key={`key_6_${index}`}
                            type={'single'}
                            itemStyleType={2}
                            title={item.itemTile}
                            selected={item.select}
                            onPress={() => {
                              this.onClickRepeatItem({index: index});
                            }}
              />
            );
          })}
        </View>
      </View>
    );
  }

  _renderRepeatViewDialog() {
    return (
      <AbstractDialog
        style={[styles.repeatViewStyle]}
        visible={this.state.showRepeatModeDialog}
        showSubtitle={false}
        onDismiss={() => {
          this._repeatDismiss();
          // this.setState({ repeatViewVisible: false });
        }}
        showTitle={false}
        showButton={false}
        // canDismiss={false}
      >
        {this._renderRepeatView()}
      </AbstractDialog>
    );
  }

  _repeatDismiss() {
    this.setState({ showRepeatModeDialog: false, periodRepeat: this.repeatArrayCopy },()=>{
      this._updateRepeatItems();
    });
  }

  _updateRepeatItems() {
    // this.data.IndexArray = [];
    // let repeat = this.state.periodRepeat
    // [0, 1, 2, 3, 4, 5, 6].forEach((item) => {
    //   if (repeat[item] == 1) {
    //     this.data.IndexArray.push(item);
    //   }
    // });

  }

  _renderRepeatView() {
    let repeatStr = JSON.stringify(this.state.periodRepeat);
    this.repeatItems = [
      { itemTile: LocalizedStrings.plug_timer_everyday,
        select: repeatStr == "[1,1,1,1,1,1,1]"
      },
      { itemTile: LocalizedStrings.plug_timer_sef_define,
        select: repeatStr != "[0,0,0,0,0,0,0]" && repeatStr != "[1,1,1,1,1,1,1]"
      }
    ];
    return (
      <View style={{ alignItems: "center", marginBottom: 16 }}>
        <Text
          style={{
            fontSize: 16, color: "#000000", marginTop: 25, fontWeight: "700"
          }}
        >
          {LocalizedStrings["plug_timer_repeat"]}
        </Text>
        <View style={{ marginTop: 15 }}>
          {this.repeatItems.map((item, index) => {
            return (
              <TouchableOpacity
                key={index}
                onPress={() => {
                  this.onClickRepeatItem({ index: index });
                }}
              >
                <View
                  style={{ maxWidth: "100%",
                    width: screenWidth, height: 54,
                    backgroundColor:
                      item.select == true ? "rgba(50,186,192,0.1)" : "#ffffff", flexDirection: "row", alignItems: "center", justifyContent: "space-between"
                  }}
                  key={index}
                >
                  <Text
                    style={{
                      marginLeft: 30, fontSize: 16, color: item.select == true ? "#32BAC0" : "#000000", fontWeight: "500"
                    }}
                  >
                    {item.itemTile}
                  </Text>
                  {item.select == true && (
                    <Image
                      style={{ width: 22, height: 22, marginRight: 22 }}
                      source={require("../../Resources/Images/plugin_icon_customVoice_selectItem.png")}
                    ></Image>
                  )}
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
        <View style={{ flexDirection: "row", justifyContent: "space-between", marginTop: 20, marginLeft: 0, marginBottom: 10 }}>
          <TouchableOpacity
            onPress={() => {
              this._repeatDismiss();
            }}
          >
            <View style={{ width: 147, height: 46, backgroundColor: "#F5F5F5", borderRadius: 23, justifyContent: "center", alignItems: "center" }}>
              <Text style={{ fontSize: 16, color: "#4C4C4C" }}>{LocalizedStrings.btn_cancel}</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              this.repeatArrayCopy = JSON.parse(JSON.stringify(this.state.periodRepeat));
              this.setState({ showRepeatModeDialog: false, canSave: true },()=>{
                this.refreshNavigationBar()
              });
            }}
          >
            <View style={{ width: 147, height: 46, backgroundColor: "#32BAC0", borderRadius: 23, justifyContent: "center", alignItems: "center", marginLeft: 20 }}>
              <Text style={{ fontSize: 16, color: "#ffffff" }}>{LocalizedStrings.btn_confirm}</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  onClickRepeatItemV2(item) {
    this.repeatItemClicked = item.index;
    this.repeatArrayCopy = JSON.parse(JSON.stringify(this.state.periodRepeat));
    if (item.index == 0) {
      this.setState({periodRepeat: [1, 1, 1, 1, 1, 1, 1]});
    } else {
      this.setState({periodRepeat: [0, 0, 0, 0, 0, 0, 0]});
    }
  }

  onClickRepeatItem(item) {
    if (item.index == 0) {
      // 每天
      this.tempRepeatCopy = [1, 1, 1, 1, 1, 1, 1];
      this.setState({periodRepeat: [1, 1, 1, 1, 1, 1, 1]});
    } else if (item.index == 1) {
      this.tempRepeatCopy = this.state.periodRepeat;
      this.setState({ showRepeatWeekDialog: true });
    }

  }

  _repeatWeekDialog() {
    let initSelectData = [];
    this.state.periodRepeat.forEach((item, index) => {
      item == 1 && initSelectData.push(index)
    });
    return (
      <ChoiceDialog
        type={ChoiceDialog.TYPE.MULTIPLE}
        visible={this.state.showRepeatWeekDialog}
        title={LocalizedStrings['plug_timer_custom_repeat']}
        options={[
          {title: LocalizedStrings['monday1']},
          {title: LocalizedStrings['tuesday1']},
          {title: LocalizedStrings['wednesday1']},
          {title: LocalizedStrings['thursday1']},
          {title: LocalizedStrings['friday1']},
          {title: LocalizedStrings['saturday1']},
          {title: LocalizedStrings['sunday1']},
        ]}
        selectedIndexArray={initSelectData}
        color="#32BAC0"
        buttons={[
          {callback:()=>{
              this.setState({showRepeatWeekDialog: false, periodRepeat: this.tempRepeatCopy});
            }},
          {
            callback: (result) => {
              let repeatArray = [0, 0, 0, 0, 0, 0, 0];
              if (result && result.length <= 0) {
                repeatArray = this.tempRepeatCopy;
              } else {
                result.forEach((item) => {
                  repeatArray[item] = 1;
                });
              }
              this.setState({
                periodRepeat: repeatArray,
                showRepeatWeekDialog: false
              });


            }
          }
        ]}
        onDismiss={() => this.setState({showRepeatWeekDialog: false, periodRepeat: this.tempRepeatCopy})}
      />);
  }


  _saveChanges() {
    let startTime =`${this.state.startTime}:00`;
    let endTime = `${this.state.endTime}:00`;
    let repeatArray = JSON.stringify(this.state.periodRepeat);

    let params = [{"sname": SIID_MOTION_DETECTION, "pname": PIID_MOTION_DETECTION_START_TIME, value: startTime},
      {"sname": SIID_MOTION_DETECTION, "pname": PIID_MOTION_DETECTION_END_TIME, value: endTime},
      {"sname": SIID_MOTION_DETECTION, "pname": PIID_DETECTION_REPEAT, value: repeatArray}];
    AlarmUtilV2.setSpecPValue(params).then(res => {
      if (res[0].code !== 0 || res[1].code !== 0) {
        Toast.fail('c_set_fail');
      } else {
        Toast.success('c_set_success');
      }
    }).catch(error => {
      Toast.fail('c_set_fail');
    });
  }


}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f7f7f7"
  },
  titlStyle: {
    fontSize: 16,
    color: "black"
  },
  separator: {
    alignItems: "flex-start",
    height: 0.5,
    backgroundColor: "rgba(0, 0, 0, 0.15)"
  },
  effectiveTimeStyle: {
    marginLeft: 25,
    marginRight: 25,
    marginTop: 5,
    fontSize: 30,
    color: "#000000"
  },
  repeatViewStyle: {
    width: screenWidth,
    bottom: 0,
    // borderTopWidth:20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderBottomLeftRadius: Host.isPad ? 20 : 0,
    borderBottomRightRadius: Host.isPad ? 20 : 0,
    marginHorizontal: 0
    // height: 400
    // backgroundColor:'white'
  }
});
