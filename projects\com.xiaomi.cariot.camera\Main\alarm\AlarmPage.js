import React from 'react';
import { Text, StyleSheet, View, Image, TouchableOpacity, SafeAreaView, TouchableWithoutFeedback,Platform } from 'react-native';
import { DarkMode, Device, Host, Package, Service, PackageEvent } from 'miot';
import DateFilterView from './components/DateFilterView';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import StorageKeys from '../StorageKeys';
import BasePage, { BaseStyles } from "../BasePage";
import EventList from "../widget/EventList";
import { evList2SectionList } from "../widget/EventSectionList";
import { ChoiceDialog } from "miot/ui/Dialog";
import { MessageDialog } from 'miot/ui/Dialog';
import { ChoiceDlgWithIconSel } from "../widget/ChoiceDlgEx";
import Util, { DayInMilli, EvArray, EvMap } from "../util2/Util";
import dayjs from 'dayjs';
import Toast from '../components/Toast';
import { Event } from "../config/base/CfgConst";
import Singletons from "../framework/Singletons";
import { CldDldTypes } from "../framework/CloudEventLoader";
import ImageButton from "miot/ui/ImageButton";
import { Separator } from 'mhui-rn';
import VersionUtil from '../util/VersionUtil';
import { SCREEN_WIDTH } from '../util2/Const';
import TrackUtil from '../util/TrackUtil';
import CameraConfig from '../util/CameraConfig';
import { DescriptionConstants } from '../Constants';
import { StatusBar } from 'react-native';
import AlarmUtil from '../util/AlarmUtil';
import { getStack } from '..';

import { constants } from 'buffer';
import CustomImageButton from '../widget/CustomImageButton';
import ParsedText from 'react-native-parsed-text';
import CameraPlayer from "../util/CameraPlayer";
import { MISSError } from "miot/service/miotcamera";

const LiveHeaderH = 158;
const CommonHeaderH = 46;
const DefFilter = "Default";
// { key: 'ObjectMotion' },
//   { key: 'Pet' },
//   { key: 'PeopleMotion' },
//   { key: 'LouderSound' },
//   { key: 'BabyCry' },
//   { key: 'Face' },
//   // { key: 'KnownFace' },
//   { key: 'CameraCalling' },
//   { key: 'AI' },
const Filters = [{ name: LocalizedStrings.all_events, key: DefFilter },
  { name: LocalizedStrings['event_desc_obj_motion'], key: Event.ObjectMotion, needVip: false },
  { name: LocalizedStrings['event_desc_people_motion'], key: Event.PeopleMotion, needVip: false },
  { name: LocalizedStrings['loud_desc'], key: Event.LouderSound, needVip: false },
  { name: LocalizedStrings['event_desc_unknown_people'], key: Event.KnownFace, needVip: true }, // 目前本地人脸识别只有022，暂时不更新
  // { name: LocalizedStrings['event_desc_unknown_people'], key: Event.Face, needVip: true },
  { name: LocalizedStrings['baby_cry_desc'], key: Event.BabyCry, needVip: true, isBabyCry: true }, // 009 019 v3需要考虑是否是vip状态
  { name: LocalizedStrings['voice_video_call'], key: Event.CameraCalling }, // 009 019 v3需要考虑是否是vip状态
  { name: LocalizedStrings['event_desc_ai_scene'], key: Event.AI, needVip: false }
];
const dayOfYear = require('dayjs/plugin/dayOfYear');

export default class AlarmPage extends BasePage {

  constructor(props, context) {
    super(props, context);
    this.initState({
      modalVisible: false, // 弹出筛选框是否可见
      curDate: new Date(), // 当前选中的日期
      evFilter: Filters[0],
      selectedEventKey: DefFilter,
      isOnline: false,
      showEvFilter: false,
      isVip: undefined,
      dateItems: [],
      showWatchHouseDialog: true,
      prompt: null,
      venderMsg: null,
      extFilterTop: 114,
      isShow: true,
      barExpire: true,
      showExtFilter: true,
    });
    this.motionDetection = false;
    this.mLoader = Singletons.CloudEventLoader;
    this.updateFaceInfo = false;
    this.mSupportNonVipBabyCry = CameraConfig.isSupportNonVipBabyCry(Device.model);
    this.isFromPush = this.props.navigation.state.params.lstType == "push";
    this.isStartUp = this.props.navigation.state.params.pushType == "startup";
    this.is022or051 = VersionUtil.is022Model(Device.model);
    this.mPacketEndTime = 0;
    // didBlur在ios上调的时间晚，会在其他页面的onResume之后，换成willBlur
    this.willBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'willBlur',
      () => {
        this.setState({ showWatchHouseDialog: false });
      }
    );
    this.mSupportCloudCountry = CameraConfig.isSupportCloudCountry();
    this.isInternationalServer = CameraConfig.getInternationalServerStatus();
  }

  onPause() {
    super.onPause();
  }

  onResume() {
    super.onResume();
    DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');
    this.setState({ modalVisible: false });
    // this.fetchLocalVipStatus();
    if (this.updateFaceInfo) {
      this.updateFaceInfo = false;
      this.mEvLst.updateAllItems();
    }
  }

  msgAck = () => {
    console.log(this.tag, "msgAck", this.state.venderMsg);
    if (this.state.venderMsg) {
      this.setState({ venderMsg: null });
    }

    this.mAckR(0);
  }
  componentWillUnmount() {
    CameraPlayer.getInstance().bindNetworkInfoCallback(null);

    this.setState({ showWatchHouseDialog: false });
  }
  componentDidMount() {
    console.log("这里的判断条件是：", VersionUtil.isAiCameraModel(Device.model))
    super.componentDidMount();
    CameraPlayer.getInstance().bindNetworkInfoCallback(this._networkChangeHandler);

    DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');
    this.props.navigation.setParams({
      show: true
    });
    this.init();
    
    AlarmUtil.loadAutomaticScenes(Device.deviceID).then(() => {
      this.fetchVipStatus();
    }).catch(() => {
      this.fetchVipStatus();
    });

    // 查看是否在云存期内
    StorageKeys.IS_VIP_STATUS.then((res) => {
      if (typeof (res) === "string" || res == null) {
        StorageKeys.IS_VIP_STATUS = false;
        this.isVip = false;
      } else {
        this.isVip = res;
      }
      StorageKeys.IN_CLOSE_WINDOW.then((res) => {
        if (typeof (res) === "string" || res == null) {
          StorageKeys.IS_VIP_STATUS = false;
          this.isInExpireWindow = true;
        } else {
          this.isInExpireWindow = res;
        }
        // this._initData();
      });
    });
    // 查看每日故事开关
    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {
        this._getSetting();
      }
    );
  }

  _networkChangeHandler = (networkState) => {
    if (networkState == 0 || networkState == -1) { // 网络断开了连接 showError?
      return;
    }

    if (!this.state.dateItems || this.state.dateItems.length === 0) {
      setTimeout(() => {
        this.fetchVipStatus();
      }, 500);
    }
  }

  async init() {
    const res = await StorageKeys.IS_WATCH_HOUST;
    const res1 = await StorageKeys.IS_FLOAT_BAR;
    let isShow, barExpire;
    typeof (res) === 'boolean' ? isShow = res : isShow = false;
    typeof (res1) === 'boolean' ? barExpire = res1 : barExpire = true;
    this.setState({ isShow, barExpire });
  }
  // 通过后端获取开关信息
  _getSetting() {
    AlarmUtil.getAlarmConfig().then((res) => {
      if (res.code != 0) {
        console.log("getdailyStorySwitch:", JSON.stringify(-1));
        Toast.fail('c_get_fail');
        return;
      }
      this.setState({
        dailyStorySwitch: res.data.dailyStorySwitch
      });
      console.log('先看看开关的状态:', res.data.dailyStorySwitch); // 开关为true
    }).catch((err) => {
      console.log("getdailyStorySwitch:", JSON.stringify(err));
      Toast.fail('c_get_fail', err);
    });
  }

  updateVipRelated(aVipDat) {
    console.log("aVipDat:", aVipDat)
    // aVipDat.vip = false;
    let isVip = false;
    let saveDays = 7;
    if (aVipDat != null) {
      isVip = aVipDat.vip;
      saveDays = Math.ceil(aVipDat.rollingSaveInterval / DayInMilli);
      if (!isVip) {
        this.mPacketEndTime = aVipDat?.endTime;
        if (aVipDat?.closeWindow) { // false: 在窗口期, true: 不在窗口期
          saveDays = 7;
        }
      }
      this.setState({
        vipEndTime: aVipDat.endTime,
        pacakgeType: aVipDat.rollingSaveInterval / (1000 * 3600 * 24)
      })
    }
    if (isVip != this.state.isVip) {
      this.mEvFilter = Filters.filter((aVal) => {
        // 本地人脸识别的尚未添加  还有宠物的也没有添加
        // 某些属性需要判断是否是vip，如果该属性下，不是vip，再看是否是设备支持的本地能力
        let vipSupport = aVal.needVip ? (isVip ? true : (aVal.isBabyCry ? this.mSupportNonVipBabyCry : false)) : true;
        if (aVal.key == Event.Pet && !CameraConfig.isSupportPet(Device.model)) {
          return false;
        }

        if (aVal.key == Event.AI && !CameraConfig.intelligentScenePush(Device.model)) {
          return false;
        }
        if (aVal.key == Event.BabyCry && !CameraConfig.CloudBabyCry(Device.model)) {
          return false;
        }
        if (aVal.key == Event.KnownFace && !CameraConfig.intelligentScenePush(Device.model)) {
          return false;
        }
        if (aVal.key == Event.Pet && !CameraConfig.isSupportPet(Device.model)) {
          return false;
        }

        if (aVal.key == Event.CameraCalling && !CameraConfig.displayCameraCallingTimeline(Device.model)) {
          return false;
        }

        if (aVal.key == Event.BabyCry) {
          if (this.isInternationalServer && !this.mSupportNonVipBabyCry) { // 海外只支持本地哭声，不支持vip哭声，特殊case后面特殊处理
            vipSupport = false;
          }
          // 宝宝哭声有特别的行为。 如果是特别的设备，特别的场景，就认为具备本地宝宝哭声能力
          let sBabyCry = CameraConfig.babyCrySpecialModel(Device.model);
          if (sBabyCry != -1) {
            vipSupport = sBabyCry;
          }
        }

        return vipSupport;
      });
      let dateItems = this.genDateItem(isVip, saveDays);
      this.setState({ isVip, dateItems, curDate: new Date(dateItems[dateItems.length - 1].ts) });
    }

    this.evFilterOnModel();
  }

  evFilterOnModel() {
    if (VersionUtil.judgeIsV1(Device.model)) {
      this.mEvFilter = this.mEvFilter.filter((aVal) => {
        return (aVal.key == Event.BabyCry || aVal.key == Event.KnownFace) ? false : true;
      });
    }
    if (VersionUtil.judgeIsV3(Device.model)) {
      this.mEvFilter = this.mEvFilter.filter((aVal) => {
        return (aVal.key == Event.BabyCry && CameraConfig.getInternationalServerStatus()) ? false : true;
      });
    }
  }


  async fetchVipStatus() {
    try {
      let newVipD = await Util.fetchVipStatus();
      StorageKeys.VIP_DETAIL = newVipD;
      this.updateVipRelated(newVipD);
    } catch (aErr) {
      console.log(this.tag, "fetchVipStatus err", aErr);
    }
  }

  async fetchLocalVipStatus() {
    try {
      let vipD = await StorageKeys.VIP_DETAIL;
      if (vipD != null && vipD.hasOwnProperty("vip")) {
        this.updateVipRelated(vipD);
      }
    } catch (aErr) {
      console.log(this.tag, "apply cached vip state err", aErr);
    }
  }


  genDateItem(aVip = false, aSaveLength = 7) {
    let dateItems = [];
    let today = new Date();
    let len = Math.max(aSaveLength + 1, 7);
    console.log(this.tag, "genDateItem", len, aVip, aSaveLength, DayInMilli);
    for (let i = 0; i < len; i++) {
      let date = today.getDate() - i;
      let tgt = new Date();
      let timestamp = tgt.setDate(date);
      let dateStr = tgt.getDate().toString();
      let item = {
        date: dateStr,
        wkDay: Util.getMoment(tgt.getTime() / 1000).format("dd"),
        ts: timestamp,
        enabled: i > aSaveLength ? false : true,
        selected: (i === 0) ? true : false
      };
      dateItems.unshift(item);
    }
    return dateItems;
  }


  filterPressed(idx) {
    let visible = !this.state.modalVisible;
    if (idx !== this.state.filterIndex) {
      visible = true;
    }
    // 弹出筛选框的 idx。0: 日期筛选，1: 类型筛选
    this.setState({
      modalVisible: visible,
      filterIndex: idx
    });
  }

  selectDate(aItm) {
    let date = new Date(aItm.ts);
    for (let di of this.state.dateItems) {
      if (di.ts === aItm.ts) {
        if (di.selected) {
          return;
        }
        di.selected = true;
      } else {
        di.selected = false;
      }
    }
    this.setState({ curDate: date, dateItems: this.state.dateItems, showExtFilter: false });
    // this.mDfTs = moment(aItm.ts);
    this.mDfTs = dayjs(aItm.ts);

    this.mEvLst.switchDay(date, true);// remove all exist and refresh
    TrackUtil.reportClickEvent('Monitoring_Date_ClickNum');

  }

  dismissModalView() {
    this.setState({
      modalVisible: false
    });
  }


  render() {
    dayjs.extend(dayOfYear);
    let now = new Date();
    // let isToday = moment().dayOfYear() === moment(this.state.curDate).dayOfYear();
    let isToday = dayjs().dayOfYear() === dayjs(this.state.curDate).dayOfYear();

    let emptyFaq = "这里有个问题";
    let mHeaderHeigth = 40 + 50;
    if (Host.isPad) {
      mHeaderHeigth = 50;
    }
    let mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.all'];
    switch (this.state.evFilter.key) {
      case Event.BabyCry:
        mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.baby.cry'];
        break;
      case Event.PeopleMotion:
        mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.people.motion'];
        break;
      // case Event.Face:
      //   mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.face'];
      //   break;
      case Event.KnownFace:
        mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.face'];
        break;
      case Event.ObjectMotion:
        mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.object.motion'];
        break;
      case Event.AI:
        mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.ai'];
        break;
      case Event.Pet:
        mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.pet'];
        break;
      case Event.CameraCalling:
        mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.cameracall'];
        break;
      default:
        break;
    }

    return (
      <View style={[BaseStyles.pageRoot, { flexDirection: 'column', backgroundColor: BaseStyles.mainBg.backgroundColor }]}>
        {this.renderHeader()}
        <View style={{ flex: 1, backgroundColor: BaseStyles.mainBg.backgroundColor, marginTop: mHeaderHeigth }}>
          <DateFilterView
            onLayout={({ nativeEvent: { layout: { height } } }) => {
              if (isToday) {
                this.setState({ extFilterTop: height });
              }
            }}
            ref={(aDfv) => { this.mDfv = aDfv; }}
            totalWidth={SCREEN_WIDTH}
            dataSource={this.state.dateItems}
            dayButtonPressed={(item) => { this.selectDate(item); }}
            extraHeader={null}
          />
          <View style={{ alignItems: 'center' }}>
            <Separator style={{ width: "90%", backgroundColor: '#E5E5E5' }} />
          </View>
          {/* 114 top backgroundColor: "#F7F7F7" */}
          <View style={[BaseStyles.row, { backgroundColor: "#F7F7F7", left: 0, marginTop: 10, marginBottom: 8 }]}
            zIndex={1}>
            {this.mCommonHeader(0)}
          </View>
          <EventList
            style={{ backgroundColor: Util.isDark() ? this.context.theme?.colorWhite : BaseStyles.mainBg.backgroundColor }}
            eventHeaderView={this.mLiveHeader}
            eventHeaderHeight={LiveHeaderH}
            type={CldDldTypes.Events}
            ref={(aEvLst) => { this.mEvLst = aEvLst; }}
            loader={this.mLoader}
            isSltDay={true}
            loaderArgs={{ startDate: this.state.curDate, filter: this.state.evFilter.key }}
            emptyAdjust={CommonHeaderH}
            contentContainerStyle={{ paddingHorizontal: 15 }}
            onEventPress={(aItm, aExtra) => {
              // Stat.reportEvent(StatEV.ALARM, null);
              let items = aExtra.events;
              let nextDate = aExtra.nextDate;
              let mLstType = (!aItm.isAlarm || this.state.isVip || (!this.state.isVip && (this.mPacketEndTime != 0) && (this.mPacketEndTime > aItm?.createTime))) ? "list_vip" : "list";
              this.naviTo('AlarmVideoUI', {
                item: aItm, cfg: aItm.playCfg,
                lstType: mLstType,
                items: this.state.isVip ? null : evList2SectionList(items),
                loaderArgs: { startDate: this.state.curDate, filter: this.state.evFilter.key, nextDate: nextDate },
                updateFaceInfoCb: () => { this.updateFaceInfo = true; },
                deleteCb: (aIds) => { this.mEvLst.removeEvents((aItm) => { aIds.has(aItm.fileId); }); },
                evType: this.state.evFilter.key ? this.state.evFilter.key : "Default"
              });
              TrackUtil.reportClickEvent('Monitoring_Video_ClickNum');
            }}
            emptyDes={mEmptyDes}
            extraEmptyDes={emptyFaq ?
              {
                value: emptyFaq.des, action: () => {
                  this.naviTo("WebGuide", { uri: emptyFaq.uri, title: emptyFaq.title });
                }
              }
              : null}
          />
          {this.renderEvFilterDlg()}
          {this.renderMsgDlg()}
        </View>
        {/* 这个位置首先要判断开关状态，如果每日故事开关为true，跳转到每日故事列表页；如果每日故事开始为false，跳转到新用户页面 */}
        { VersionUtil.isAiCameraModel(Device.model) ? 
        <SafeAreaView >
          <TouchableOpacity style={{ height: 46, width: "90%", backgroundColor: "#32BAC0", borderRadius: 23,marginLeft:20, marginBottom: 15}}
            onPress={() => {
              let is022or051 = VersionUtil.is022Model(Device.model) ? true : (this.isVip || this.isInExpireWindow) 
                if ( is022or051 && this.state.dailyStorySwitch) { // inexpireWindow== closeWindow= true 代表已经彻底过期了。
                  console.log("是否开启每日故事：", this.state.dailyStorySwitch);
                  this.props.navigation.navigate('DailyStoryList');
                } else {
                  // console.log("是不是vip：",this.state.isVip)
                  // this.props.navigation.navigate('DailyStoryList');
                  this.props.navigation.navigate('DailyStoryFirstEnter');
                }
            }}>
            <Text style={{ textAlign: "center", fontSize: 15, lineHeight:46, color: "#ffffff" }}>
              {LocalizedStrings["ss_daily_story"]}
            </Text>
          </TouchableOpacity> 
          </SafeAreaView>
          : null}
        {this.props.navigation.state.params.freeHomSurStatus ? this._renderPermissionDialog() : null}
        {/* {this.props.navigation.state.params.freeHomSurStatus ? this.floatingBar() : this.floatingBar()} */}

      </View>
    );
  }
  renderText(matchingString, matches) {
    let find = '\\[|\\]|s?';
    let re = new RegExp(find, 'g');
    return matchingString.replace(re, '');
  }
  // 这里是弹出对话框
  _renderPermissionDialog () {
    if (this.state.isShow) {
      return;
    }
    
    const time = dayjs.unix(this.props.navigation.state.params.freeHomeSurExpireTime / 1000).format(LocalizedStrings["yyyymmdd"]);
    const timeMsg = LocalizedStrings["free_service_reminder_desc"].replace('%s', time);
    let message = <ParsedText
        parse={
           [
            { pattern: /\[s(.+?)\]/g, style: { fontWeight: 'bold' },  renderText: this.renderText },
           ]
         }
         childrenProps={{ allowFontScaling: false }}
       >
         {timeMsg}
    </ParsedText>
    return (
      <MessageDialog
        title={LocalizedStrings["free_service_reminder"]}
        message={message}
        canDismiss={false}
        messageStyle={{
          fontSize: 14
        }}
        buttons={
          [
            {
              text: LocalizedStrings["i_understand"],
              callback: () => {
                StorageKeys.IS_WATCH_HOUST = true;
                this.setState({ showWatchHouseDialog: false });
              }
            }
          ]
        }
        visible={this.state.showWatchHouseDialog} />
    );

  }
  languageCompatibility(time) {
    if (time == 1) {
      return LocalizedStrings["not_expired_tips_1"].replace('%s', time);
    } else {
      if (Host.locale.language === "pl") {
        if ((time >= 2 && time <= 14) || time >= 22 && time <= 24) {
          return  LocalizedStrings["not_expired_tips_2"].replace('%s', time);
        }
      }
      if (Host.locale.language === "ru") {
        if (time == 21) {
          return  LocalizedStrings["not_expired_tips_1"].replace('%s', time);
        } else if (time == 0 || (time >= 5 && time <= 19)){
         return  LocalizedStrings["not_expired_tips_2"].replace('%s', time);
        }
      }
      return  LocalizedStrings["not_expired_tips_3"].replace('%s', time);
    }
  }
  // 常驻浮条
  floatingBar() {
    const time = dayjs(this.props.navigation.state.params.freeHomeSurExpireTime).diff(dayjs(), 'days');
    const not_expired_tips = this.languageCompatibility(time);
    const { isVip } = this.state;
    if (time > 30) {
      return null;
    } else {
      const barCss = {
        width: '100%',
        flexDirection: "row",
        flexWrap: 'nowrap',
        alignItems: "center",
        justifyContent: "center",
        marginTop: 0,
        paddingTop: 0,
        marginBottom: 12
      };
      const contentBar = {
        backgroundColor: 'rgba(245, 166, 35, 0.1)',
        width: '100%',
        height: 62,
        paddingHorizontal:23,
        flexDirection: "row",
        flexWrap: 'nowrap',
        borderRadius: 12,
        alignItems: "center",
        justifyContent: "center"
      };
      if (Platform.OS == "ios") {
        contentBar.paddingHorizontal = 10;
      }
      const barCssText = {
        color: '#F5A623',
        fontWeight: "400",
        paddingRight: 30,
        fontSize: 13,
        lineHeight: 17
      };
      const barClick = (type) => {
        if (Device.permitLevel === 16) { // 自己的设备
          Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: type ? 'videolist_snackbar' : 'videolist_snackbar_expired' });
        } else {
          Toast.success("share_user_permission_hint");
        }
      };
      const barClose = () => {
        StorageKeys.IS_FLOAT_BAR = false;
        this.setState({
          barExpire: false
        });
      };
      let textDes = null;
      if (time < 0) {
        textDes = this.state.barExpire ? (
          <TouchableOpacity style={[barCss]}
            onPress={() => {
              barClick(false);
            }}
          >
            <View style={[contentBar]}>
              <Text numberOfLines={3} ellipsizeMode={'tail'} style={barCssText}>{LocalizedStrings["expired_tips"]}</Text>
              <TouchableOpacity
                onPress={() => {
                  barClose();
                }}
              >
                <Image
                  style={{
                    paddingRight: 10
                  }}
                  source={
                    require("../../Resources/Images/bar_close.png")
                  } 
                /> 
              </TouchableOpacity>
            </View>
          </TouchableOpacity>) : null;

      } else {
        textDes = (
          <TouchableOpacity style={[barCss]}
            onPress={() => {
              barClick(true);
            }}
          >
            <View style={[contentBar]}>
              <Text numberOfLines={3} ellipsizeMode={'tail'} style={barCssText}>{not_expired_tips}</Text>
              <Image
                source={
                  require("../../Resources/Images/bar_tips.png")
                } 
              /> 
            </View>
          </TouchableOpacity>);
      }
      return textDes;
    }
  }

  renderMsgDlg() {
    // internal
    if (this.state.prompt != null) {
      return (
        <MessageDialog
          title={this.state.prompt.title}
          message={this.state.prompt.message}
          buttons={this.state.prompt.buttons}
          visible={this.state.prompt != null}
        />
      );
    }
    // vendor
    else if (this.state.venderMsg != null) {
      return this.state.venderMsg.action(this.msgAck);
    } else {
      return null;
    }
  }

  renderEvFilterDlg() {
    if (this.state.showEvFilter && this.mEvFilter) {
      let options = this.mEvFilter.map((itm) => { return { title: itm.name, icon: Util.getIconFromType(itm.key, 'anyname') }; });
      console.log(this.tag, options, this.eventFilterItems);
      return (<ChoiceDlgWithIconSel
        animationType={'slide'}
        accessible={true}
        type={ChoiceDialog.TYPE.SINGLE}
        useNewType={true}
        title={LocalizedStrings["ev_filter_title"]}

        visible={this.state.showEvFilter}
        selectedIndexArray={[this.mEvFilter.indexOf(this.state.evFilter)]}
        options={options}
        onDismiss={() => {
          this.setState({ showEvFilter: false });
        }}

        onSelectEx={(aSel) => {
          console.log(this.tag, "sel", aSel, this.mEvFilter[aSel[0]]);
          this.setState({ evFilter: this.mEvFilter[aSel[0]], showEvFilter: false });
          let sValue = 1;
          switch (this.mEvFilter[aSel[0]].key) {
            case Event.KnownFace:
              sValue = 5;
              break;
            case Event.PeopleMotion:
              sValue = 3;
              break;
            case Event.BabyCry:
              sValue = 4;
              break;
            case Event.AI:
              sValue = 6;
              break;
            case Event.ObjectMotion:
              sValue = 2;
              break;
            default:
              sValue = 1;
          }
          console.log(this.tag, 'Monitoring_Motion_Status', sValue);
          TrackUtil.reportResultEvent('Monitoring_Motion_Status', 'type', sValue);
        }}
        buttons={
          [
            {
              text: LocalizedStrings['action_cancle'],
              callback: () => {
                this.setState({ showEvFilter: false });
              },
              colorType: "grayLayerBlack"
            }
          ]
        }
      />);
    }
  }

  buildPrompt(aPrompt) {
    let prompt = {};
    prompt.title = aPrompt.title;
    prompt.message = aPrompt.message;
    if (aPrompt.buttons != null) {
      prompt.buttons = aPrompt.buttons;
      for (let b of prompt.buttons) {
        let origin = b.callback;
        let wrapped = (args) => {
          this.setState({ prompt: null });
          if (origin != null) {
            origin(args);
          }
        };
        b.callback = wrapped;
      }
    } else {
      prompt.buttons = [{
        text: LocalizedStrings['csps_right'],
        callback: () => {
          this.setState({ prompt: null });
        }
      }];
    }

    this.setState({ prompt: prompt });
  }

  mCommonHeader = (aExtraPadding = 0) => {
    return (
      <TouchableOpacity style={[BaseStyles.row, {
        alignItems: "center", justifyContent: "flex-start",
        height: CommonHeaderH, paddingTop: 14, paddingBottom: 12, paddingRight: 10, paddingLeft: 28
      }]}
      onPress={() => { this.setState({ showEvFilter: true }); }}>
        <Text

          style={[BaseStyles.text12, { paddingLeft: aExtraPadding, color: "#8C93B0", fontWeight: "bold" }]}>{this.state.evFilter.name}</Text>
        <Image
          accessibilityLabel={DescriptionConstants.kj_1_8}
          style={{ width: 20, height: 20 }} source={require("../../resources2/images/icon_ev_sel.png")} />
      </TouchableOpacity>
    );
  }


  mLiveHeader = () => {
    const { isVip } = this.state;
    if (this.props.navigation.state.params.freeHomSurStatus && typeof(isVip) === "boolean" && !isVip) {
      return this.floatingBar();
    } else {
      return null;
    }
    // return null;
  }

  renderHeader() {
    if (this.state.fullScreen) {
      return null;
    }
    let marginTop = 40;
    if (Host.isPad) {
      marginTop = 0;
    }
    return (
      <View style={[BaseStyles.row, {
        position: "absolute", height: 50, paddingLeft: 12, marginTop: marginTop, paddingRight: 12, // 12+40
        backgroundColor: BaseStyles.mainBg.backgroundColor,
        width: "100%"
      }]}>

        <ImageButton
          accessibilityLabel={DescriptionConstants.kj_1_1}
          style={BaseStyles.icon40}
          source={Util.isDark() ? require("../../Resources/Images/icon_back_black_nor_dark.png") : require("../../Resources/Images/icon_back_black.png")}
          onPress={() => {
            this.naviBack();
          }} />
        <Text
          accessibilityLabel={DescriptionConstants.kj_1_2}
          style={[BaseStyles.text18, { fontWeight: "bold", textAlign: 'center', width: '80%' }]}>
          {LocalizedStrings["house_keeping"]}
        </Text>

        {
          !Device.isReadonlyShared ?
            <CustomImageButton
              style={BaseStyles.icon40}
              source={Util.isDark() ? require("../../Resources/Images/camera_alarm_setting_nor.png") : require("../../Resources/Images/camera_alarm_setting.png")}
              onPress={() => {
                TrackUtil.reportClickEvent('Monitoring_Setting_ClickNum');
                this.naviTo("SurvelillanceSetting", { vip: this.state.isVip });
              }}
              accessibilityLabel={DescriptionConstants.sz_5}
              accessibilityRole={"button"}
              />
            :
            <TouchableOpacity style={BaseStyles.icon40}></TouchableOpacity>
        }

        {/* <View></View> */}
      </View>
    );
  }

  // override
  naviBack() {
    this.mPauseL();
    if (this.props.navigation.state.params.shouldPopTopWhenBack) {
      this.props.navigation.popToTop(); // 弹出到栈顶。
    } else if (this.isFromPush && this.isStartUp) {
      Package.exit();
      this.onPause();
      return true;
    } else {
      let rootStack = getStack();
      const routes = rootStack._navigation.state.routes;
      if (routes.length > 1) {
        this.props.navigation.goBack();
      } else {
        Package.exit();
      }
    }
  }

  onBack() {
    if (this.props.navigation.state.params.shouldPopTopWhenBack) {
      this.props.navigation.popToTop(); // 弹出到栈顶。
      return true;// 拦截
    } else if (this.isFromPush && this.isStartUp) {
      Package.exit();
      this.onPause();
      return true;
    } else {
      // this.props.navigation.goBack();
      return false;
    }
  }
}
