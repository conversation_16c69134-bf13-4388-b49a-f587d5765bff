<a name="module_miot/Service"></a>

## miot/Service
Service 模块提供的能力主要包括米家服务端及米家云平台提供的服务能力
能力主要包括：
账号管理(Account.js)
房间管理(room.js)
智能场景(scene.js)
云服务(smarthome.js)
Spec协议(spec.js)
云存储(storage.js)

**Export**: public  
**Doc_name**: 系统服务模块  
**Doc_index**: 1  
**Doc_directory**: service  
**Example**  
```js
import {Service} from 'miot'

Service.getServerName().then(res=>{...})
Service.getUTCTimeFromServer().then(...)

Service.smarthome.reportGPSInfo(...).then(...)

Service.account.ID
Serivce.account.nickName
Service.account.avatar
Service.account.load().then(account=>{})

Service.scene.loadTimerScenes(...).then(scenes=>{})
Service.security.loadSecureKeys(...).then(keys=>{
...
})

Service.storage.getUserConfigs(key).then()
```

* [miot/Service](#module_miot/Service)
    * _static_
        * [.getServiceTokenWithSid(sid)](#module_miot/Service.getServiceTokenWithSid) ⇒ <code>Promise</code>
        * [.revokePrivacyLicense()](#module_miot/Service.revokePrivacyLicense) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
        * [.deleteDevice()](#module_miot/Service.deleteDevice) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
        * [.applyForDeviceIDAndToken(model, mac)](#module_miot/Service.applyForDeviceIDAndToken) ⇒ <code>Promise</code>
    * _inner_
        * [~smarthome](#module_miot/Service..smarthome)
        * [~miotcamera](#module_miot/Service..miotcamera)
        * [~ircontroller](#module_miot/Service..ircontroller)
        * [~account](#module_miot/Service..account) : <code>IAccount</code>
        * ~~[~scene](#module_miot/Service..scene)~~
        * [~sceneV2](#module_miot/Service..sceneV2)
        * [~security](#module_miot/Service..security)
        * [~storage](#module_miot/Service..storage)
        * [~spec](#module_miot/Service..spec)
        * [~callSmartHomeAPI(api, params)](#module_miot/Service..callSmartHomeAPI)
        * [~callSmartHomeCameraAPI(api, subDomain, post, params)](#module_miot/Service..callSmartHomeCameraAPI)
        * [~callSmartHomeCameraAPI(api, subDomain, post, params)](#module_miot/Service..callSmartHomeCameraAPI)
        * [~callSmartHomeCameraAPI(host, path, method, params, needDevice, cookie, contentType)](#module_miot/Service..callSmartHomeCameraAPI) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
        * [~getServerName()](#module_miot/Service..getServerName) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;{countryName:&quot;&quot;, countryCode:&quot;&quot;, serverCode:&quot;&quot;}&gt;</code>
        * [~getTimeZoneOfServer()](#module_miot/Service..getTimeZoneOfServer)
        * [~getUTCFromServer()](#module_miot/Service..getUTCFromServer) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;long&gt;</code>
        * [~callSpecificAPI(url, method, params)](#module_miot/Service..callSpecificAPI) ⇒ <code>Promise</code>


* * *

<a name="module_miot/Service.getServiceTokenWithSid"></a>

### miot/Service.getServiceTokenWithSid(sid) ⇒ <code>Promise</code>
传入域名返回 serverToken 等信息，目前只支持小爱音箱的域名
Android从SDK-10039开始支持该接口

**Kind**: static method of [<code>miot/Service</code>](#module_miot/Service)  

| Param | Type | Description |
| --- | --- | --- |
| sid | <code>string</code> | 域名，类似"xxx.xiaomi.com" |


* * *

<a name="module_miot/Service.revokePrivacyLicense"></a>

### miot/Service.revokePrivacyLicense() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
since 10042
撤销隐私授权,插件调用该接口后需要主动调用退出插件

**Kind**: static method of [<code>miot/Service</code>](#module_miot/Service)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code> - 成功时返回：{code:0,data:true};
                           失败时返回：{code:-1,message:'invalid device'} ,或 {code:-2,message:'xxxxx'}  
**Example**  
```js
Service.revokePrivacyLicense()
       .then(res=>{
         console.log(JSON.stringify(res));
         if( res.code ===0){
           console.log('success');
         }
       }).catch(err=>{
          console.log(JSON.stringify(err));
       });
```

* * *

<a name="module_miot/Service.deleteDevice"></a>

### miot/Service.deleteDevice() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
since 10042
删除设备,插件调用该接口后需要主动调用退出插件

**Kind**: static method of [<code>miot/Service</code>](#module_miot/Service)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code> - 成功时返回：{code:0,data:true};
                           失败时返回：{code:-1,message:'invalid device'} ,或 {code:-2,message:'xxxxx'}  
**Example**  
```js
Service.deleteDevice()
       .then(res=>{
         console.log(JSON.stringify(res));
         if( res.code ===0){
           console.log('success');
         }
       }).catch(err=>{
          console.log(JSON.stringify(err));
       });
```

* * *

<a name="module_miot/Service.applyForDeviceIDAndToken"></a>

### miot/Service.applyForDeviceIDAndToken(model, mac) ⇒ <code>Promise</code>
某设备向服务器申请did和token
Android暂不支持此方法

**Kind**: static method of [<code>miot/Service</code>](#module_miot/Service)  
**Returns**: <code>Promise</code> - resolve({res,did,token})  

| Param | Type | Description |
| --- | --- | --- |
| model | <code>\*</code> | 设备的model |
| mac | <code>\*</code> | 设备的mac地址 |


* * *

<a name="module_miot/Service..smarthome"></a>

### miot/Service~smarthome
设备相关 API

**Kind**: inner property of [<code>miot/Service</code>](#module_miot/Service)  
**See**: [module:miot/service/smarthome](module:miot/service/smarthome)  

* * *

<a name="module_miot/Service..miotcamera"></a>

### miot/Service~miotcamera
摄像机相关 API

**Kind**: inner property of [<code>miot/Service</code>](#module_miot/Service)  
**See**: [module:miot/service/miotcamera](module:miot/service/miotcamera)  

* * *

<a name="module_miot/Service..ircontroller"></a>

### miot/Service~ircontroller
红外 相关 API

**Kind**: inner property of [<code>miot/Service</code>](#module_miot/Service)  
**See**: [module:miot/service/ircontroller](module:miot/service/ircontroller)  

* * *

<a name="module_miot/Service..account"></a>

### miot/Service~account : <code>IAccount</code>
当前的用户信息

**Kind**: inner property of [<code>miot/Service</code>](#module_miot/Service)  
**See**: [module:miot/Account](module:miot/Account)  

* * *

<a name="module_miot/Service..scene"></a>

### ~~miot/Service~scene~~
***Deprecated***

场景1.0 API 的调用

**Kind**: inner property of [<code>miot/Service</code>](#module_miot/Service)  
**See**: [module:miot/service/scene](module:miot/service/scene)  

* * *

<a name="module_miot/Service..sceneV2"></a>

### miot/Service~sceneV2
场景2.0 API 的调用

**Kind**: inner property of [<code>miot/Service</code>](#module_miot/Service)  
**See**: [module:miot/service/sceneV2](module:miot/service/sceneV2)  

* * *

<a name="module_miot/Service..security"></a>

### miot/Service~security
安全相关设置操作

**Kind**: inner property of [<code>miot/Service</code>](#module_miot/Service)  
**See**: [module:miot/service/security](module:miot/service/security)  

* * *

<a name="module_miot/Service..storage"></a>

### miot/Service~storage
用户存储操作, userProfile

**Kind**: inner property of [<code>miot/Service</code>](#module_miot/Service)  
**See**: [module:miot/service/storage](module:miot/service/storage)  

* * *

<a name="module_miot/Service..spec"></a>

### miot/Service~spec
spec 的请求

**Kind**: inner property of [<code>miot/Service</code>](#module_miot/Service)  
**See**: [module:miot/service/spec](module:miot/service/spec)  

* * *

<a name="module_miot/Service..callSmartHomeAPI"></a>

### miot/Service~callSmartHomeAPI(api, params)
通用的请求米家后台接口的方法，与米家服务器交互。
不同设备开放的接口请参照与米家后台对接时提供的文档或说明，以后台给出的信息为准。
米家客户端只封装透传网络请求，无法对接口调用结果解释，有问题请直接联系项目对接后台人员或 PM。

想使用某个接口之前，先检查 SDK 是否已经收录，可在 `miot-sdk/service/apiRepo.js`和`miot-sdk/service/omitApi.js` 文件中查阅。
注:这里的接口路径前缀为https://api.io.mi.com/app，所以请传入的接口中不要带入/app的前缀
如果 SDK 暂时没有收录，可通过 issue 提出申请，提供接口的相关信息。

**Kind**: inner method of [<code>miot/Service</code>](#module_miot/Service)  
**Since**: 10024  

| Param | Type | Description |
| --- | --- | --- |
| api | <code>string</code> | 接口地址，比如'/location/set' |
| params | <code>object</code> | 传入参数，根据和米家后台商议的数据格式来传入，比如{ did: 'xxxx', pid: 'xxxx' } |


* * *

<a name="module_miot/Service..callSmartHomeCameraAPI"></a>

### miot/Service~callSmartHomeCameraAPI(api, subDomain, post, params)
专用摄像头相关接口请求
api in `miot-sdk/service/apiRepo.js`
subDomain in `miot-sdk/service/cameraSubDomain.js`

**Kind**: inner method of [<code>miot/Service</code>](#module_miot/Service)  
**Since**: 10035  

| Param | Type | Description |
| --- | --- | --- |
| api | <code>string</code> | 接口地址 |
| subDomain | <code>string</code> | subDomain |
| post | <code>bool</code> | 是否POST方法 |
| params | <code>object</code> | 传入参数 |


* * *

<a name="module_miot/Service..callSmartHomeCameraAPI"></a>

### miot/Service~callSmartHomeCameraAPI(api, subDomain, post, params)
专用摄像头相关接口请求
api in `miot-sdk/service/apiRepo.js`
subDomain in `miot-sdk/service/cameraSubDomain.js`

**Kind**: inner method of [<code>miot/Service</code>](#module_miot/Service)  
**Since**: 10044  

| Param | Type | Description |
| --- | --- | --- |
| api | <code>string</code> | 接口地址 |
| subDomain | <code>string</code> | subDomain |
| post | <code>bool</code> | 是否POST方法 |
| params | <code>string</code> | BigJSON.strinify(object); |


* * *

<a name="module_miot/Service..callSmartHomeCameraAPI"></a>

### miot/Service~callSmartHomeCameraAPI(host, path, method, params, needDevice, cookie, contentType) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
小爱音箱相关接口请求,注意此请求传的是一个对象，里面部分对象有默认值，可不传

**Kind**: inner method of [<code>miot/Service</code>](#module_miot/Service)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code> - 透传接口，直接返回服务端返回的值  
**Since**: 10041  

| Param | Type | Description |
| --- | --- | --- |
| host | <code>string</code> | 请求的host，取值normal，hd,profile,lbs,skillstore,aifile,ai,aitrain,grayupgrade,homealbum。表示的host分别如下... {    "normal": "https://api2.mina.mi.com",    "hd": "https://hd.mina.mi.com",    "profile": "https://userprofile.mina.mi.com",    "lbs": "https://lbs.mina.mi.com",    "skillstore": "https://skillstore.mina.mi.com",    "aifile": "https://file.ai.xiaomi.com",    "ai": "https://api.ai.xiaomi.com",    "aitrain": "https://i.ai.mi.com/mico",    "grayupgrade": "https://api.miwifi.com/rs/grayupgrade/v2/micoiOS",    "homealbum": "https://display.api.mina.mi.com",    "pusher": "https://pusherapi-iotdcm.ai.xiaomi.com" } |
| path | <code>string</code> | 请求的路径，比如"/device_profile/conversation" |
| method | <code>number</code> | 默认为0（表示get方法），1表示post方法，2表示put方法 |
| params | <code>object</code> | 请求的参数，比如{limit:20} |
| needDevice | <code>bool</code> | cookie中是否需要带上deviceId，默认为true |
| cookie | <code>object</code> | 支持带上自定义的cookie |
| contentType | <code>string</code> | put和post方法默认是以表单方式提交参数，即Content-Type为application/x-www-form-urlencoded，如果想以application/json的方式，请传入'json' |


* * *

<a name="module_miot/Service..getServerName"></a>

### miot/Service~getServerName() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;{countryName:&quot;&quot;, countryCode:&quot;&quot;, serverCode:&quot;&quot;}&gt;</code>
获取 米家 App 设置的地区和服务器信息
Android上返回的countryCode为大写，iOS上为小写，建议使用时在拿到countryCode后调用一下toLowerCase方法，都统一成小写

**Kind**: inner method of [<code>miot/Service</code>](#module_miot/Service)  

* * *

<a name="module_miot/Service..getTimeZoneOfServer"></a>

### miot/Service~getTimeZoneOfServer()
获取服务器所在时区

**Kind**: inner method of [<code>miot/Service</code>](#module_miot/Service)  

* * *

<a name="module_miot/Service..getUTCFromServer"></a>

### miot/Service~getUTCFromServer() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;long&gt;</code>
从米家服务器获取当前UTC时间戳（会发送网络请求）

**Kind**: inner method of [<code>miot/Service</code>](#module_miot/Service)  

* * *

<a name="module_miot/Service..callSpecificAPI"></a>

### miot/Service~callSpecificAPI(url, method, params) ⇒ <code>Promise</code>
调用当前手机设备的网关http服务
只封装透传网络请求，无法对接口调用结果解释，有问题请直接联系项目对接后台人员或 PM。

**Kind**: inner method of [<code>miot/Service</code>](#module_miot/Service)  
**Returns**: <code>Promise</code> - 成功时：返回网络请求的结果对应字符串， 相当于：response.body().string()
失败时：{"code":xxx, "message":"xxx" }  
**Since**: 10031  

| Param | Type | Description |
| --- | --- | --- |
| url | <code>string</code> | url |
| method | <code>string</code> | 如 'get', 'post'等 不区分大小写 暂时只支持 get 和 post |
| params | <code>object</code> | 传入参数，比如{ did: 'xxxx', pid: 'xxxx','allow_private_certificates':true/false };allow_private_certificates是10056新增加的参数(10055及以前的版本该参数不生效)，传true表明该请求使用小米路由器私有证书，默认为false; |


* * *

