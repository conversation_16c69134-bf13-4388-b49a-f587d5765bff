<a name="module_miot/host/ui"></a>

## miot/host/ui
本地原生业务页面访问与处理

**Export**: public  
**Doc_name**: 页面导航模块  
**Doc_index**: 7  
**Doc_directory**: host  
**Example**  
```js
import {Host} from 'miot'
...
//删除设备
Host.ui.openDeleteDevice()
//分享设备
Host.ui.openShareDevicePage
```

* [miot/host/ui](#module_miot/host/ui)
    * [~IUi](#module_miot/host/ui..IUi)
        * [.canOpenStorePage()](#module_miot/host/ui..IUi+canOpenStorePage) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Boolean&gt;</code>
        * ~~[.checkStoreSupporttedOnAndroid()](#module_miot/host/ui..IUi+checkStoreSupporttedOnAndroid) ⇒ <code>boolean</code>~~
        * [.openDeleteDevice([title])](#module_miot/host/ui..IUi+openDeleteDevice)
        * [.openDeleteDeviceWithCallback()](#module_miot/host/ui..IUi+openDeleteDeviceWithCallback)
        * [.openShareDevicePage()](#module_miot/host/ui..IUi+openShareDevicePage)
        * [.keepScreenNotLock(flag)](#module_miot/host/ui..IUi+keepScreenNotLock)
        * [.openRoomManagementPage()](#module_miot/host/ui..IUi+openRoomManagementPage)
        * [.openVoiceCtrlDeviceAuthPage()](#module_miot/host/ui..IUi+openVoiceCtrlDeviceAuthPage)
        * ~~[.openIftttAutoPage()](#module_miot/host/ui..IUi+openIftttAutoPage)~~
        * [.openFeedbackInput()](#module_miot/host/ui..IUi+openFeedbackInput)
        * [.openSecuritySetting()](#module_miot/host/ui..IUi+openSecuritySetting)
        * [.openHelpPage()](#module_miot/host/ui..IUi+openHelpPage)
        * [.openShareListBar(title, description, imagePath, url)](#module_miot/host/ui..IUi+openShareListBar)
        * [.openSystemShareWindow(pathOrUrl)](#module_miot/host/ui..IUi+openSystemShareWindow)
        * [.openSystemFileWindow(pathOrUrl)](#module_miot/host/ui..IUi+openSystemFileWindow)
        * [.openConsumesPageWithParams(params)](#module_miot/host/ui..IUi+openConsumesPageWithParams)
        * [.openConsumesDetailPage(params)](#module_miot/host/ui..IUi+openConsumesDetailPage)
        * [.getDevicesWithModel(model, includeGroupedDevice)](#module_miot/host/ui..IUi+getDevicesWithModel) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;devices&gt;&gt;</code>
        * [.openBtGatewayPage()](#module_miot/host/ui..IUi+openBtGatewayPage)
        * [.openBtGatewayPageWithParams()](#module_miot/host/ui..IUi+openBtGatewayPageWithParams) ⇒ <code>Promise</code>
        * [.alertLegalInformationAuthorization(option)](#module_miot/host/ui..IUi+alertLegalInformationAuthorization) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Boolean&gt;</code>
        * [.previewLegalInformationAuthorization(option)](#module_miot/host/ui..IUi+previewLegalInformationAuthorization) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Boolean&gt;</code>
        * ~~[.privacyAndProtocolReview(licenseTitle, licenseUrl, policyTitle, policyUrl)](#module_miot/host/ui..IUi+privacyAndProtocolReview)~~
        * ~~[.openPrivacyLicense(licenseTitle, licenseUrl, policyTitle, policyUrl)](#module_miot/host/ui..IUi+openPrivacyLicense) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Boolean&gt;</code>~~
        * [.openChangeDeviceName()](#module_miot/host/ui..IUi+openChangeDeviceName)
        * [.openAddToDesktopPage()](#module_miot/host/ui..IUi+openAddToDesktopPage)
        * [.openDeviceUpgradePage(type)](#module_miot/host/ui..IUi+openDeviceUpgradePage)
        * [.openDeviceInfoPage()](#module_miot/host/ui..IUi+openDeviceInfoPage)
        * [.openDeviceUpgradeHistoryPage()](#module_miot/host/ui..IUi+openDeviceUpgradeHistoryPage)
        * [.openBleMeshDeviceUpgradePage()](#module_miot/host/ui..IUi+openBleMeshDeviceUpgradePage)
        * [.openBleCommonDeviceUpgradePage(params)](#module_miot/host/ui..IUi+openBleCommonDeviceUpgradePage)
        * [.openLightGroupUpgradePage()](#module_miot/host/ui..IUi+openLightGroupUpgradePage)
        * [.openBleGroupUpgradePage(type)](#module_miot/host/ui..IUi+openBleGroupUpgradePage)
        * [.openDeviceTimeZoneSettingPage({&quot;sync_device&quot;:)](#module_miot/host/ui..IUi+openDeviceTimeZoneSettingPage)
        * [.openWebPage(url, params)](#module_miot/host/ui..IUi+openWebPage)
        * [.openDeviceServicePage()](#module_miot/host/ui..IUi+openDeviceServicePage)
        * [.openShopPage(gid)](#module_miot/host/ui..IUi+openShopPage)
        * [.openShopSearchPage(keyword)](#module_miot/host/ui..IUi+openShopSearchPage)
        * [.openProductBaikeWebPage(url)](#module_miot/host/ui..IUi+openProductBaikeWebPage)
        * [.openMeshDeviceGroupPage(type, did, version)](#module_miot/host/ui..IUi+openMeshDeviceGroupPage)
        * [.openAddDeviceGroupPage(groupModel)](#module_miot/host/ui..IUi+openAddDeviceGroupPage)
        * [.openEditDeviceGroupPage(dids)](#module_miot/host/ui..IUi+openEditDeviceGroupPage)
        * ~~[.openCountDownPage(isCountDownOn, setting)](#module_miot/host/ui..IUi+openCountDownPage)~~
        * [.openOneTimePassword(did, interval, digits)](#module_miot/host/ui..IUi+openOneTimePassword)
        * ~~[.openTimerSettingPage(onMethod, onParam, offMethod, offParam)](#module_miot/host/ui..IUi+openTimerSettingPage)~~
        * ~~[.openTimerSettingPageWithCustomIdentifier(customTimerIdentifier, onMethod, onParam, offMethod, offParam)](#module_miot/host/ui..IUi+openTimerSettingPageWithCustomIdentifier)~~
        * [.openTimerSettingPageWithVariousTypeParams(onMethod, onParam, offMethod, offParam)](#module_miot/host/ui..IUi+openTimerSettingPageWithVariousTypeParams)
        * ~~[.openTimerSettingPageWithOptions(options)](#module_miot/host/ui..IUi+openTimerSettingPageWithOptions)~~
        * [.openPowerMultikeyPage(did, mac)](#module_miot/host/ui..IUi+openPowerMultikeyPage)
        * [.addOrCopyIR(did, type, models, extra)](#module_miot/host/ui..IUi+addOrCopyIR)
        * [.openDevice(did, model, params)](#module_miot/host/ui..IUi+openDevice) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
        * [.openPluginPage(did, pageName, pageParams)](#module_miot/host/ui..IUi+openPluginPage)
        * [.openPageWithClassName(className)](#module_miot/host/ui..IUi+openPageWithClassName)
        * ~~[.openNewMorePage()](#module_miot/host/ui..IUi+openNewMorePage)~~
        * [.openPhoneBluSettingPage()](#module_miot/host/ui..IUi+openPhoneBluSettingPage)
        * [.openXiaoAiLearnPage(clientId, did, aiMiotClientId, aiClientId, aiVersion, otherParams)](#module_miot/host/ui..IUi+openXiaoAiLearnPage)
        * [.showBLESwitchGuide()](#module_miot/host/ui..IUi+showBLESwitchGuide)
        * [.dismissBLESwitchGuide()](#module_miot/host/ui..IUi+dismissBLESwitchGuide)
        * [.openConnectSucceedPage(model, did)](#module_miot/host/ui..IUi+openConnectSucceedPage)
        * [.openCurtainGroupNamePage(groupDid, leftDid, rightDid)](#module_miot/host/ui..IUi+openCurtainGroupNamePage)
        * [.openZigbeeConnectDeviceList(did)](#module_miot/host/ui..IUi+openZigbeeConnectDeviceList)
        * [.openDeviceNetworkInfoPage()](#module_miot/host/ui..IUi+openDeviceNetworkInfoPage)
        * [.openMiPayPageForAndroid(params)](#module_miot/host/ui..IUi+openMiPayPageForAndroid) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
        * [.openPluginRecommendScene(did, recommendId)](#module_miot/host/ui..IUi+openPluginRecommendScene)
        * [.refreshDeviceList()](#module_miot/host/ui..IUi+refreshDeviceList) ⇒ <code>Promise</code>
        * [.openTerminalDeviceSettingPage(type)](#module_miot/host/ui..IUi+openTerminalDeviceSettingPage)
        * [.openAndroidLocationServerSettingPage()](#module_miot/host/ui..IUi+openAndroidLocationServerSettingPage)
        * [.openResetAndConnectDevicePage()](#module_miot/host/ui..IUi+openResetAndConnectDevicePage)
        * [.openWifiConfigStepPage()](#module_miot/host/ui..IUi+openWifiConfigStepPage)
        * [.openVoiceCtrlDevListPage()](#module_miot/host/ui..IUi+openVoiceCtrlDevListPage)
        * [.openStereoSettingPage()](#module_miot/host/ui..IUi+openStereoSettingPage)
        * [.openXiaoaiContentPage()](#module_miot/host/ui..IUi+openXiaoaiContentPage)
        * [.openXiaoaiPage(params)](#module_miot/host/ui..IUi+openXiaoaiPage)
        * [.openClockMusicSelector(ringtone_tab, ringtone_id, ringtone_name, ringtone_query)](#module_miot/host/ui..IUi+openClockMusicSelector)
        * [.openEarthquakeLocationSelector(name, district, city, latitude, longitude)](#module_miot/host/ui..IUi+openEarthquakeLocationSelector)
        * [.openLocationSelector(name, district, city, latitude, longitude, title)](#module_miot/host/ui..IUi+openLocationSelector)
        * [.openIOSDocumentFileChoosePage()](#module_miot/host/ui..IUi+openIOSDocumentFileChoosePage) ⇒ <code>Promise</code>
        * [.openNFCWriteDeviceInfoPage(extra)](#module_miot/host/ui..IUi+openNFCWriteDeviceInfoPage)
        * [.openNFCWriteDeviceInfoDebugPage(params)](#module_miot/host/ui..IUi+openNFCWriteDeviceInfoDebugPage)
        * [.openCommonDeviceSettingPage(type)](#module_miot/host/ui..IUi+openCommonDeviceSettingPage)
        * [.getFreqCameraNeedShowRedPoint()](#module_miot/host/ui..IUi+getFreqCameraNeedShowRedPoint) ⇒ <code>object</code>
        * [.clearFreqCameraNeedShowRedPoint()](#module_miot/host/ui..IUi+clearFreqCameraNeedShowRedPoint)
        * [.openGenerateCrontabStringPage()](#module_miot/host/ui..IUi+openGenerateCrontabStringPage)
        * [.openFirmWareAutoOTAPage()](#module_miot/host/ui..IUi+openFirmWareAutoOTAPage)
        * [.getNavigationBarHeight()](#module_miot/host/ui..IUi+getNavigationBarHeight)
        * [.openVirtualGroupInitPage(param)](#module_miot/host/ui..IUi+openVirtualGroupInitPage)
        * [.openDeviceOfflineAlert()](#module_miot/host/ui..IUi+openDeviceOfflineAlert)
        * [.openAssociatePage(param)](#module_miot/host/ui..IUi+openAssociatePage)


* * *

<a name="module_miot/host/ui..IUi"></a>

### miot/host/ui~IUi
**Kind**: inner interface of [<code>miot/host/ui</code>](#module_miot/host/ui)  

* [~IUi](#module_miot/host/ui..IUi)
    * [.canOpenStorePage()](#module_miot/host/ui..IUi+canOpenStorePage) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Boolean&gt;</code>
    * ~~[.checkStoreSupporttedOnAndroid()](#module_miot/host/ui..IUi+checkStoreSupporttedOnAndroid) ⇒ <code>boolean</code>~~
    * [.openDeleteDevice([title])](#module_miot/host/ui..IUi+openDeleteDevice)
    * [.openDeleteDeviceWithCallback()](#module_miot/host/ui..IUi+openDeleteDeviceWithCallback)
    * [.openShareDevicePage()](#module_miot/host/ui..IUi+openShareDevicePage)
    * [.keepScreenNotLock(flag)](#module_miot/host/ui..IUi+keepScreenNotLock)
    * [.openRoomManagementPage()](#module_miot/host/ui..IUi+openRoomManagementPage)
    * [.openVoiceCtrlDeviceAuthPage()](#module_miot/host/ui..IUi+openVoiceCtrlDeviceAuthPage)
    * ~~[.openIftttAutoPage()](#module_miot/host/ui..IUi+openIftttAutoPage)~~
    * [.openFeedbackInput()](#module_miot/host/ui..IUi+openFeedbackInput)
    * [.openSecuritySetting()](#module_miot/host/ui..IUi+openSecuritySetting)
    * [.openHelpPage()](#module_miot/host/ui..IUi+openHelpPage)
    * [.openShareListBar(title, description, imagePath, url)](#module_miot/host/ui..IUi+openShareListBar)
    * [.openSystemShareWindow(pathOrUrl)](#module_miot/host/ui..IUi+openSystemShareWindow)
    * [.openSystemFileWindow(pathOrUrl)](#module_miot/host/ui..IUi+openSystemFileWindow)
    * [.openConsumesPageWithParams(params)](#module_miot/host/ui..IUi+openConsumesPageWithParams)
    * [.openConsumesDetailPage(params)](#module_miot/host/ui..IUi+openConsumesDetailPage)
    * [.getDevicesWithModel(model, includeGroupedDevice)](#module_miot/host/ui..IUi+getDevicesWithModel) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;devices&gt;&gt;</code>
    * [.openBtGatewayPage()](#module_miot/host/ui..IUi+openBtGatewayPage)
    * [.openBtGatewayPageWithParams()](#module_miot/host/ui..IUi+openBtGatewayPageWithParams) ⇒ <code>Promise</code>
    * [.alertLegalInformationAuthorization(option)](#module_miot/host/ui..IUi+alertLegalInformationAuthorization) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Boolean&gt;</code>
    * [.previewLegalInformationAuthorization(option)](#module_miot/host/ui..IUi+previewLegalInformationAuthorization) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Boolean&gt;</code>
    * ~~[.privacyAndProtocolReview(licenseTitle, licenseUrl, policyTitle, policyUrl)](#module_miot/host/ui..IUi+privacyAndProtocolReview)~~
    * ~~[.openPrivacyLicense(licenseTitle, licenseUrl, policyTitle, policyUrl)](#module_miot/host/ui..IUi+openPrivacyLicense) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Boolean&gt;</code>~~
    * [.openChangeDeviceName()](#module_miot/host/ui..IUi+openChangeDeviceName)
    * [.openAddToDesktopPage()](#module_miot/host/ui..IUi+openAddToDesktopPage)
    * [.openDeviceUpgradePage(type)](#module_miot/host/ui..IUi+openDeviceUpgradePage)
    * [.openDeviceInfoPage()](#module_miot/host/ui..IUi+openDeviceInfoPage)
    * [.openDeviceUpgradeHistoryPage()](#module_miot/host/ui..IUi+openDeviceUpgradeHistoryPage)
    * [.openBleMeshDeviceUpgradePage()](#module_miot/host/ui..IUi+openBleMeshDeviceUpgradePage)
    * [.openBleCommonDeviceUpgradePage(params)](#module_miot/host/ui..IUi+openBleCommonDeviceUpgradePage)
    * [.openLightGroupUpgradePage()](#module_miot/host/ui..IUi+openLightGroupUpgradePage)
    * [.openBleGroupUpgradePage(type)](#module_miot/host/ui..IUi+openBleGroupUpgradePage)
    * [.openDeviceTimeZoneSettingPage({&quot;sync_device&quot;:)](#module_miot/host/ui..IUi+openDeviceTimeZoneSettingPage)
    * [.openWebPage(url, params)](#module_miot/host/ui..IUi+openWebPage)
    * [.openDeviceServicePage()](#module_miot/host/ui..IUi+openDeviceServicePage)
    * [.openShopPage(gid)](#module_miot/host/ui..IUi+openShopPage)
    * [.openShopSearchPage(keyword)](#module_miot/host/ui..IUi+openShopSearchPage)
    * [.openProductBaikeWebPage(url)](#module_miot/host/ui..IUi+openProductBaikeWebPage)
    * [.openMeshDeviceGroupPage(type, did, version)](#module_miot/host/ui..IUi+openMeshDeviceGroupPage)
    * [.openAddDeviceGroupPage(groupModel)](#module_miot/host/ui..IUi+openAddDeviceGroupPage)
    * [.openEditDeviceGroupPage(dids)](#module_miot/host/ui..IUi+openEditDeviceGroupPage)
    * ~~[.openCountDownPage(isCountDownOn, setting)](#module_miot/host/ui..IUi+openCountDownPage)~~
    * [.openOneTimePassword(did, interval, digits)](#module_miot/host/ui..IUi+openOneTimePassword)
    * ~~[.openTimerSettingPage(onMethod, onParam, offMethod, offParam)](#module_miot/host/ui..IUi+openTimerSettingPage)~~
    * ~~[.openTimerSettingPageWithCustomIdentifier(customTimerIdentifier, onMethod, onParam, offMethod, offParam)](#module_miot/host/ui..IUi+openTimerSettingPageWithCustomIdentifier)~~
    * [.openTimerSettingPageWithVariousTypeParams(onMethod, onParam, offMethod, offParam)](#module_miot/host/ui..IUi+openTimerSettingPageWithVariousTypeParams)
    * ~~[.openTimerSettingPageWithOptions(options)](#module_miot/host/ui..IUi+openTimerSettingPageWithOptions)~~
    * [.openPowerMultikeyPage(did, mac)](#module_miot/host/ui..IUi+openPowerMultikeyPage)
    * [.addOrCopyIR(did, type, models, extra)](#module_miot/host/ui..IUi+addOrCopyIR)
    * [.openDevice(did, model, params)](#module_miot/host/ui..IUi+openDevice) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
    * [.openPluginPage(did, pageName, pageParams)](#module_miot/host/ui..IUi+openPluginPage)
    * [.openPageWithClassName(className)](#module_miot/host/ui..IUi+openPageWithClassName)
    * ~~[.openNewMorePage()](#module_miot/host/ui..IUi+openNewMorePage)~~
    * [.openPhoneBluSettingPage()](#module_miot/host/ui..IUi+openPhoneBluSettingPage)
    * [.openXiaoAiLearnPage(clientId, did, aiMiotClientId, aiClientId, aiVersion, otherParams)](#module_miot/host/ui..IUi+openXiaoAiLearnPage)
    * [.showBLESwitchGuide()](#module_miot/host/ui..IUi+showBLESwitchGuide)
    * [.dismissBLESwitchGuide()](#module_miot/host/ui..IUi+dismissBLESwitchGuide)
    * [.openConnectSucceedPage(model, did)](#module_miot/host/ui..IUi+openConnectSucceedPage)
    * [.openCurtainGroupNamePage(groupDid, leftDid, rightDid)](#module_miot/host/ui..IUi+openCurtainGroupNamePage)
    * [.openZigbeeConnectDeviceList(did)](#module_miot/host/ui..IUi+openZigbeeConnectDeviceList)
    * [.openDeviceNetworkInfoPage()](#module_miot/host/ui..IUi+openDeviceNetworkInfoPage)
    * [.openMiPayPageForAndroid(params)](#module_miot/host/ui..IUi+openMiPayPageForAndroid) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
    * [.openPluginRecommendScene(did, recommendId)](#module_miot/host/ui..IUi+openPluginRecommendScene)
    * [.refreshDeviceList()](#module_miot/host/ui..IUi+refreshDeviceList) ⇒ <code>Promise</code>
    * [.openTerminalDeviceSettingPage(type)](#module_miot/host/ui..IUi+openTerminalDeviceSettingPage)
    * [.openAndroidLocationServerSettingPage()](#module_miot/host/ui..IUi+openAndroidLocationServerSettingPage)
    * [.openResetAndConnectDevicePage()](#module_miot/host/ui..IUi+openResetAndConnectDevicePage)
    * [.openWifiConfigStepPage()](#module_miot/host/ui..IUi+openWifiConfigStepPage)
    * [.openVoiceCtrlDevListPage()](#module_miot/host/ui..IUi+openVoiceCtrlDevListPage)
    * [.openStereoSettingPage()](#module_miot/host/ui..IUi+openStereoSettingPage)
    * [.openXiaoaiContentPage()](#module_miot/host/ui..IUi+openXiaoaiContentPage)
    * [.openXiaoaiPage(params)](#module_miot/host/ui..IUi+openXiaoaiPage)
    * [.openClockMusicSelector(ringtone_tab, ringtone_id, ringtone_name, ringtone_query)](#module_miot/host/ui..IUi+openClockMusicSelector)
    * [.openEarthquakeLocationSelector(name, district, city, latitude, longitude)](#module_miot/host/ui..IUi+openEarthquakeLocationSelector)
    * [.openLocationSelector(name, district, city, latitude, longitude, title)](#module_miot/host/ui..IUi+openLocationSelector)
    * [.openIOSDocumentFileChoosePage()](#module_miot/host/ui..IUi+openIOSDocumentFileChoosePage) ⇒ <code>Promise</code>
    * [.openNFCWriteDeviceInfoPage(extra)](#module_miot/host/ui..IUi+openNFCWriteDeviceInfoPage)
    * [.openNFCWriteDeviceInfoDebugPage(params)](#module_miot/host/ui..IUi+openNFCWriteDeviceInfoDebugPage)
    * [.openCommonDeviceSettingPage(type)](#module_miot/host/ui..IUi+openCommonDeviceSettingPage)
    * [.getFreqCameraNeedShowRedPoint()](#module_miot/host/ui..IUi+getFreqCameraNeedShowRedPoint) ⇒ <code>object</code>
    * [.clearFreqCameraNeedShowRedPoint()](#module_miot/host/ui..IUi+clearFreqCameraNeedShowRedPoint)
    * [.openGenerateCrontabStringPage()](#module_miot/host/ui..IUi+openGenerateCrontabStringPage)
    * [.openFirmWareAutoOTAPage()](#module_miot/host/ui..IUi+openFirmWareAutoOTAPage)
    * [.getNavigationBarHeight()](#module_miot/host/ui..IUi+getNavigationBarHeight)
    * [.openVirtualGroupInitPage(param)](#module_miot/host/ui..IUi+openVirtualGroupInitPage)
    * [.openDeviceOfflineAlert()](#module_miot/host/ui..IUi+openDeviceOfflineAlert)
    * [.openAssociatePage(param)](#module_miot/host/ui..IUi+openAssociatePage)


* * *

<a name="module_miot/host/ui..IUi+canOpenStorePage"></a>

#### iUi.canOpenStorePage() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Boolean&gt;</code>
是否支持商城

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Example**  
```js
Host.ui.canOpenStorePage().then(res => console("can open store = ", res))
```

* * *

<a name="module_miot/host/ui..IUi+checkStoreSupporttedOnAndroid"></a>

#### ~~iUi.checkStoreSupporttedOnAndroid() ⇒ <code>boolean</code>~~
***Deprecated***

检测Android是否支持集成了商城,目前是通过当前的服务来判断的

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

* * *

<a name="module_miot/host/ui..IUi+openDeleteDevice"></a>

#### iUi.openDeleteDevice([title])
弹出删除设备的对话框

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| [title] | <code>string</code> | <code>null</code> | 自定义提示，不设置使用默认提示 |


* * *

<a name="module_miot/host/ui..IUi+openDeleteDeviceWithCallback"></a>

#### iUi.openDeleteDeviceWithCallback()
android 设备暂不支持该方法 会直接reject(false)
删除设备
注意：此方法只做删除设备的活，不会返回上一页。所以在then里面，需要自己调用closeCurrentPage

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

* * *

<a name="module_miot/host/ui..IUi+openShareDevicePage"></a>

#### iUi.openShareDevicePage()
打开分享设备的页面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

* * *

<a name="module_miot/host/ui..IUi+keepScreenNotLock"></a>

#### iUi.keepScreenNotLock(flag)
是否保持屏幕常亮

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| flag | <code>Boolean</code> | <code>false</code> | 默认false |


* * *

<a name="module_miot/host/ui..IUi+openRoomManagementPage"></a>

#### iUi.openRoomManagementPage()
打开房间设备管理的页面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

* * *

<a name="module_miot/host/ui..IUi+openVoiceCtrlDeviceAuthPage"></a>

#### iUi.openVoiceCtrlDeviceAuthPage()
打开语音设备管理的页面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

* * *

<a name="module_miot/host/ui..IUi+openIftttAutoPage"></a>

#### ~~iUi.openIftttAutoPage()~~
***Deprecated***

打开添加智能的页面,注意分享的用户无法打开

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

* * *

<a name="module_miot/host/ui..IUi+openFeedbackInput"></a>

#### iUi.openFeedbackInput()
打开反馈页

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

* * *

<a name="module_miot/host/ui..IUi+openSecuritySetting"></a>

#### iUi.openSecuritySetting()
打开安全管理页

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

* * *

<a name="module_miot/host/ui..IUi+openHelpPage"></a>

#### iUi.openHelpPage()
打开常见问题页，别名「使用帮助」

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

* * *

<a name="module_miot/host/ui..IUi+openShareListBar"></a>

#### iUi.openShareListBar(title, description, imagePath, url)
打开分享列表页面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Description |
| --- | --- | --- |
| title | <code>string</code> | 标题 |
| description | <code>string</code> | 描述 |
| imagePath | <code>string</code> | 和Image source 一样的格式 |
| url | <code>string</code> | 分享链接 |


* * *

<a name="module_miot/host/ui..IUi+openSystemShareWindow"></a>

#### iUi.openSystemShareWindow(pathOrUrl)
打开系统分享文件页面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Description |
| --- | --- | --- |
| pathOrUrl | <code>string</code> | 分享文件的全路径或者链接url。 |


* * *

<a name="module_miot/host/ui..IUi+openSystemFileWindow"></a>

#### iUi.openSystemFileWindow(pathOrUrl)
打开系统文件打开页面 since 10050

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Description |
| --- | --- | --- |
| pathOrUrl | <code>string</code> | 文件的全路径或者链接url。 |


* * *

<a name="module_miot/host/ui..IUi+openConsumesPageWithParams"></a>

#### iUi.openConsumesPageWithParams(params)
该接口已经废弃，外部调用建议使用{@see openConsumesDetailPage }代替

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10068
打开耗材详情页面  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | 耗材传递的参数   params    key1: items 可以参考文档 https://xiaomi.f.mioffice.cn/docs/dock4bLM2Tjps0kZFU9xyd6fKnd# |


* * *

<a name="module_miot/host/ui..IUi+openConsumesDetailPage"></a>

#### iUi.openConsumesDetailPage(params)
**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10078
打开耗材详情页面(自研插件)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | 耗材传递的参数   params   对应的 getConsumableDetails 接口数据的details数组的元素 |


* * *

<a name="module_miot/host/ui..IUi+getDevicesWithModel"></a>

#### iUi.getDevicesWithModel(model, includeGroupedDevice) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;devices&gt;&gt;</code>
获取设备列表中指定model的设备信息(仅白名单设备才允许调用此方法，如需使用，请联系插件框架)

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;devices&gt;&gt;</code> - 对象中有字段 isGrouped 表示是被分组的设备，includeGroupedDevice = true时才有效  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| model |  |  | 指定的model |
| includeGroupedDevice | <code>boolean</code> | <code>false</code> | since 10046 是否包含被组成了一个组的设备（目前仅窗帘设备可用，灯设备不可用），默认不包含 |


* * *

<a name="module_miot/host/ui..IUi+openBtGatewayPage"></a>

#### iUi.openBtGatewayPage()
打开蓝牙网关页

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

* * *

<a name="module_miot/host/ui..IUi+openBtGatewayPageWithParams"></a>

#### iUi.openBtGatewayPageWithParams() ⇒ <code>Promise</code>
使用参数 params 打开蓝牙网关页
会和当前model的企业组进行匹配，只能打开同一企业组下的蓝牙网关页面
标准插件例外，标准插件可以打开任意企业组的蓝牙网关页面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Returns**: <code>Promise</code> - eg:
Host.ui.openBtGatewayPageWithParams({did:xxxx}).then(()=>{}).catch((err)=>{console.log(err)});
可能的返回的信息
 {"code":0, "message": "success"}}
 {"code":-101, "message": "method unallowed on shared device"}}
 {"code":-102, "message": "error input params: check did"}}
 {"code":-201, "message": "account find no specific device with input parmas did"}}
 {"code":-204, "message": "could only open ble gateway page belongs same company, compared with opened plugin model"}}
 {"code":-301, "message": "account find no specific ble gateway device data with input params did"}}  

* * *

<a name="module_miot/host/ui..IUi+alertLegalInformationAuthorization"></a>

#### iUi.alertLegalInformationAuthorization(option) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Boolean&gt;</code>
弹窗请求隐私政策和用户协议授权， 支持显示用户体验计划

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Boolean&gt;</code> - 弹窗授权结果  
**Since**: 10023  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| option | <code>object</code> |  | 配置数据 |
| option.privacyURL | <code>string</code> |  | 隐私协议本地资源 |
| [option.agreementURL] | <code>string</code> |  | 用户协议本地资源，未设置时如果hideAgreement=false，显示为默认的用户协议 |
| [option.experiencePlanURL] | <code>string</code> |  | 用户体验计划本地资源，为空时如果hideUserExperiencePlan=false，则显示米家默认用户体验计划 |
| [option.hideAgreement] | <code>boolean</code> | <code>false</code> | 是否隐藏用户协议，默认显示用户协议 |
| [option.hideUserExperiencePlan] | <code>boolean</code> | <code>false</code> | 是否隐藏用户体验计划，默认显示用户体验计划 |
| [option.privacyURLForChildren] | <code>string</code> |  | since 10060 用于展示儿童信息保护规则 |
| [option.privacyURLForWatch] | <code>string</code> |  | since 10060 用于展示手表隐私政策 |
| [option.privacyChanges] | <code>string</code> |  | since 10060 当隐私有变更时将变更的内容传入以便告知用户 |

**Example**  
```js
可以参考iot文档 或 project/com.xiaomi.demo/MainPage.js部分样例
```

* * *

<a name="module_miot/host/ui..IUi+previewLegalInformationAuthorization"></a>

#### iUi.previewLegalInformationAuthorization(option) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Boolean&gt;</code>
查看隐私政策和用户协议信息， 支持显示用户体验计划

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Boolean&gt;</code> - 授权结果  
**Since**: 10023  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| option | <code>object</code> |  | 配置数据 |
| option.privacyURL | <code>string</code> |  | 隐私协议本地资源 |
| [option.agreementURL] | <code>string</code> |  | 用户协议本地资源，未设置时如果hideAgreement=false，显示为默认的用户协议 |
| [option.experiencePlanURL] | <code>string</code> |  | 用户体验计划本地资源，为空时如果hideUserExperiencePlan=false，则显示米家默认用户体验计划 |
| [option.privacyURLForChildren] | <code>string</code> |  | since 10060儿童信息保护规则本地资源 |
| [option.privacyURLForWatch] | <code>string</code> |  | since 10060手表隐私政策本地资源 |
| [option.hideAgreement] | <code>boolean</code> | <code>false</code> | 是否隐藏用户协议，默认显示用户协议 |
| [option.hideUserExperiencePlan] | <code>boolean</code> | <code>false</code> | 是否隐藏用户体验计划，默认显示用户体验计划 |


* * *

<a name="module_miot/host/ui..IUi+privacyAndProtocolReview"></a>

#### ~~iUi.privacyAndProtocolReview(licenseTitle, licenseUrl, policyTitle, policyUrl)~~
***Deprecated***

查看软件政策和隐私协议

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Description |
| --- | --- | --- |
| licenseTitle | <code>string</code> | optional 可以为空 |
| licenseUrl | <code>string</code> | optional require('资源的相对路径') |
| policyTitle | <code>string</code> | 不可以为空 |
| policyUrl | <code>string</code> | 不可以为空 require('资源的相对路径') |


* * *

<a name="module_miot/host/ui..IUi+openPrivacyLicense"></a>

#### ~~iUi.openPrivacyLicense(licenseTitle, licenseUrl, policyTitle, policyUrl) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Boolean&gt;</code>~~
***Deprecated***

软件政策和隐私协议授权
隐私协议弹框需求：
a. 所有接入米家的设备，绑定成功后第一次进插件，都需要隐私弹框，后续再进不需弹框
b. 取消隐私授权/解绑设备后，重新绑定设备，仍需遵循规则a
插件端可按如下方案实现：
1. 使用batchSetDeviceDatas存储一个标志位，用来记录是否“隐私弹框”过
2. 进入插件时batchGetDeviceDatas获取此标志位，若为NO，弹框，同时设置标志位为YES；若为YES，不弹框
3. 设备取消授权或解绑设备时，此标志位米家后台会自动清除，故遵循了上述需求b
4. 异常处理：进插件时，如果网络异常等原因导致batchGetDeviceDatas失败，就不弹框（此时99%情况是第2+次进插件）

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Description |
| --- | --- | --- |
| licenseTitle | <code>string</code> | optional 可以为空 |
| licenseUrl | <code>string</code> | optional require('资源的相对路径') |
| policyTitle | <code>string</code> | 不可以为空 |
| policyUrl | <code>string</code> | 不可以为空 require('资源的相对路径') |


* * *

<a name="module_miot/host/ui..IUi+openChangeDeviceName"></a>

#### iUi.openChangeDeviceName()
打开重命名对话框

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

* * *

<a name="module_miot/host/ui..IUi+openAddToDesktopPage"></a>

#### iUi.openAddToDesktopPage()
添加桌面快捷方式

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

* * *

<a name="module_miot/host/ui..IUi+openDeviceUpgradePage"></a>

#### iUi.openDeviceUpgradePage(type)
打开设备检查固件升级页（先检查，后可升级）
针对wifi、AP、第三方云等可以联网的设备的统一OTA方案

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Default | Description |
| --- | --- | --- |
| type | <code>0</code> | 默认 0 ，进入最新固件升级页面          type字段 自10049支持              1， 进入旧版（native）固件升级页面    type字段 自10049支持 |


* * *

<a name="module_miot/host/ui..IUi+openDeviceInfoPage"></a>

#### iUi.openDeviceInfoPage()
**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

* * *

<a name="module_miot/host/ui..IUi+openDeviceUpgradeHistoryPage"></a>

#### iUi.openDeviceUpgradeHistoryPage()
打开设备检查固件历史版本信息页面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

* * *

<a name="module_miot/host/ui..IUi+openBleMeshDeviceUpgradePage"></a>

#### iUi.openBleMeshDeviceUpgradePage()
打开Mesh设备固件升级页。分享的设备点击此接口无反应（理论上分享的设备不应该出现调用此接口的菜单）

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10025
后续蓝牙统一OTA接口openBleCommonDeviceUpgradePage接口传参数param.auth_type = 5时也可以实现此功能（两种方式的原生实现一致），但为了向前兼容厂商已调用的此接口，所以此接口不能下掉  

* * *

<a name="module_miot/host/ui..IUi+openBleCommonDeviceUpgradePage"></a>

#### iUi.openBleCommonDeviceUpgradePage(params)
打开通用协议的蓝牙固件OTA页面。分享的设备点击此接口无反应（理论上分享的设备不应该出现调用此接口的菜单）

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10038  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>Object</code> | 请求参数 |
| params.auth_type | <code>number</code> | 指定设备的协议类型 0: 普通小米蓝牙协议设备(新接入设备已废弃该类型)，1: 安全芯片小米蓝牙设备（比如锁类产品） 4: Standard Auth 标准蓝牙认证协议(通常2019.10.1之后上线的新蓝牙设备) 5: mesh 设备 |
| params.fake_dfu_url | <code>string</code> | 指定写入DFU的下载地址，仅在测试环境下有效，指定之后可以强制更新指定DFU固件版本 |

**Example**  
```js
Host.ui.openBleCommonDeviceUpgradePage({auth_type: 5 })
目前ios在进行OTA前，可以先断开与设备的蓝牙连接，然后再从设备广播的信息中拿到设备auth_type的值（无需传参auth_type），但是安卓暂时不好实现所以接口增加了参数auth_type
```

* * *

<a name="module_miot/host/ui..IUi+openLightGroupUpgradePage"></a>

#### iUi.openLightGroupUpgradePage()
打开灯组2.0固件升级页。分享的设备点击此接口无反应（理论上分享的设备不应该出现调用此接口的菜单）

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10031  

* * *

<a name="module_miot/host/ui..IUi+openBleGroupUpgradePage"></a>

#### iUi.openBleGroupUpgradePage(type)
打开Ble 组设备升级页面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10049  

| Param | Type | Description |
| --- | --- | --- |
| type | <code>Number</code> | 蓝牙类型，与蓝牙connect 参数中的type 一致 |


* * *

<a name="module_miot/host/ui..IUi+openDeviceTimeZoneSettingPage"></a>

#### iUi.openDeviceTimeZoneSettingPage({&quot;sync_device&quot;:)
打开设备时区设置页
apiLevel在10025，增加参数的支持，APP修改时区是否需要同步到设备端，前提是设备需要支持miIO.set_timezone 方法
如果sync_device为true，服务端会给设备发送rpc,例如： {'method':'miIO.set_timezone','params':["Asia/Chongqing"]}

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10025  

| Param | Type | Description |
| --- | --- | --- |
| {"sync_device": | <code>Object</code> | false}  true-需要同步给设备 false-不需要同步给设备（默认） |


* * *

<a name="module_miot/host/ui..IUi+openWebPage"></a>

#### iUi.openWebPage(url, params)
打开H5页面
不对外提供

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Description |
| --- | --- | --- |
| url | <code>string</code> | 链接地址 |
| params | <code>object</code> | 可选参数 |
| params.onCurrentPageIfAvailable | <code>boolean</code> | 为 true 时, 如果已经有一个WebView, 则尝试在当前 WebView 加载 since 10053 |
| params.legacy | <code>boolean</code> | 为 true 时，打开支持重定向webview，webview右上角为关闭图标, since 10067 |


* * *

<a name="module_miot/host/ui..IUi+openDeviceServicePage"></a>

#### iUi.openDeviceServicePage()
打开设备服务页面，不对外提供
param{Object}
param.did 设备did

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10075  

* * *

<a name="module_miot/host/ui..IUi+openShopPage"></a>

#### iUi.openShopPage(gid)
打开商城某商品详情页面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Description |
| --- | --- | --- |
| gid | <code>string</code> | 商品ID |


* * *

<a name="module_miot/host/ui..IUi+openShopSearchPage"></a>

#### iUi.openShopSearchPage(keyword)
打开商城搜索结果页面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10024  

| Param | Type | Description |
| --- | --- | --- |
| keyword | <code>string</code> | 搜索关键字 |


* * *

<a name="module_miot/host/ui..IUi+openProductBaikeWebPage"></a>

#### iUi.openProductBaikeWebPage(url)
打开产品百科H5页面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10035  

| Param | Type | Description |
| --- | --- | --- |
| url | <code>string</code> | 链接地址 值得注意的是，米家对该接口能够打开的url做了限制，目前支持的是包含 "*.mi.com",@"*.xiaomi.com",@"*.xiaomiyoupin.com"的域名 |


* * *

<a name="module_miot/host/ui..IUi+openMeshDeviceGroupPage"></a>

#### iUi.openMeshDeviceGroupPage(type, did, version)
打开Mesh灯组 添加/编辑 页,Device.pid为17，则为Mesh设备组

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10021  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| type | <code>String</code> |  | 需要打开创建设备组页面时，type=add，需要打开编辑设备组页面时，type=edit |
| did | <code>String</code> |  | 设备did。如果是创建，则是以当前实际设备的did为基础，进入创建灯组页面。如果是编辑，则是灯组的虚拟设备did。 |
| version | <code>int</code> | <code>1</code> | 灯组版本，目前可选值有1和2，分别代表灯组1.0(旧版灯组)和灯组2.0 ;默认为灯组1.0 |


* * *

<a name="module_miot/host/ui..IUi+openAddDeviceGroupPage"></a>

#### iUi.openAddDeviceGroupPage(groupModel)
打开创建设备组页，如果是支持Mesh的设备，请使用上面的openMeshDeviceGroupPage

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Description |
| --- | --- | --- |
| groupModel | <code>String</code> | 设备组model 打开创建设备组页，只有在设备页内，需要创建设备组时，才能调用此方法。如果是设备组页面内，请使用下面的openEditDeviceGroupPage方法 只有特定设备支持创建设备组统一管理 |


* * *

<a name="module_miot/host/ui..IUi+openEditDeviceGroupPage"></a>

#### iUi.openEditDeviceGroupPage(dids)
打开编辑设备组页，只有在设备组页内，需要修改设备组时，才能调用此方法。如果是设备页面内，请使用上面的openAddDeviceGroupPage方法

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Description |
| --- | --- | --- |
| dids | <code>Array</code> | 包含组设备did的数组 |


* * *

<a name="module_miot/host/ui..IUi+openCountDownPage"></a>

#### ~~iUi.openCountDownPage(isCountDownOn, setting)~~
***Deprecated***

开启倒计时界面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Description |
| --- | --- | --- |
| isCountDownOn | <code>Boolean</code> | 设备的当前状态:YES 为开启，所以我们启动关闭倒计时; NO  为关闭，所以我们启动开启倒计时 |
| setting | <code>object</code> | 设置倒计时页面的属性 |
| setting.onMethod | <code>string</code> | 指硬件端，打开 倒计时应该 执行的方法，请咨询硬件工程师 |
| setting.onParam | <code>string</code> | 指硬件端，打开 倒计时应该 传入的参数，请咨询硬件工程师 |
| setting.offMethod | <code>string</code> | 指硬件端，关闭 倒计时应该 执行的方法，请咨询硬件工程师 |
| setting.offParam | <code>string</code> | 指硬件端，关闭 倒计时应该 传入的参数，请咨询硬件工程师 |
| setting.identify | <code>string</code> | since 10021, 用于设置倒计时的identify |
| options.displayName | <code>string</code> | 配置场景日志显示的名称：注意，不会更改倒计时页面的标题，只会上传到服务端 |

**Example**  
```js
Host.ui.openCountDownPage(true, {onMethod:"power_on", offMethod:'power_off', onParam:'on', offParam:'off',displayName:"新名字"})
```

* * *

<a name="module_miot/host/ui..IUi+openOneTimePassword"></a>

#### iUi.openOneTimePassword(did, interval, digits)
打开一次性密码设置页

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Description |
| --- | --- | --- |
| did | <code>string</code> | 设备did |
| interval | <code>int</code> | 时间间隔，即密码组的刷新时间间隔，单位为分钟，类型为 number，传入 10 到 60 的整数 |
| digits | <code>int</code> | 密码位数，类型为 number，传入 6 到 8 的整数 |


* * *

<a name="module_miot/host/ui..IUi+openTimerSettingPage"></a>

#### ~~iUi.openTimerSettingPage(onMethod, onParam, offMethod, offParam)~~
***Deprecated***

这个api 应该可以废弃了，使用 Service.scene.openTimerSettingPageWithOptions()

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Description |
| --- | --- | --- |
| onMethod | <code>string</code> | 定时到时设备“开”执行的 RPC 指令命令字字符串 |
| onParam | <code>string</code> | 定时到时设备“开”执行的 RPC 指令参数字符串（目前仅支持单参数） |
| offMethod | <code>string</code> | 定时到时设备“关”执行的 RPC 指令命令字字符串 |
| offParam | <code>string</code> | 定时到时设备“关”执行的 RPC 指令参数字符串（目前仅支持单参数） |


* * *

<a name="module_miot/host/ui..IUi+openTimerSettingPageWithCustomIdentifier"></a>

#### ~~iUi.openTimerSettingPageWithCustomIdentifier(customTimerIdentifier, onMethod, onParam, offMethod, offParam)~~
***Deprecated***

这个api 应该可以废弃了，使用 Service.scene.openTimerSettingPageWithOptions()

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Description |
| --- | --- | --- |
| customTimerIdentifier | <code>string</code> | 自定义定时Identifier |
| onMethod | <code>string</code> | 定时到时设备“开”执行的 RPC 指令命令字字符串 |
| onParam | <code>string</code> | 定时到时设备“开”执行的 RPC 指令参数字符串（目前仅支持单参数） |
| offMethod | <code>string</code> | 定时到时设备“关”执行的 RPC 指令命令字字符串 |
| offParam | <code>string</code> | 定时到时设备“关”执行的 RPC 指令参数字符串（目前仅支持单参数） |


* * *

<a name="module_miot/host/ui..IUi+openTimerSettingPageWithVariousTypeParams"></a>

#### iUi.openTimerSettingPageWithVariousTypeParams(onMethod, onParam, offMethod, offParam)
**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Description |
| --- | --- | --- |
| onMethod | <code>string</code> | 定时到时设备“开”执行的 RPC 指令命令字字符串，指硬件端，打开定时应该执行的方法，请咨询硬件工程师,miot-spec下，一般为：set_properties |
| onParam | <code>json</code> | 定时到时设备“开”执行的 RPC 指令参数，可以是字符串、数字、字典、数组，指硬件端，打开定时应该传入的参数，请咨询硬件工程师，iot-spec下，一般为：[{did,siid,piid,value}] |
| offMethod | <code>string</code> | 定时到时设备“关”执行的 RPC 指令命令字字符串,,参数请与嵌入式的同学沟通，指硬件端，关闭定时应该执行的方法，请咨询硬件工程师，miot-spec下，一般为：set_properties |
| offParam | <code>json</code> | 定时到时设备“关”执行的 RPC 指令参数，可以是字符串、数字、字典、数组，指硬件端，关闭定时应该传入的参数，请咨询硬件工程师，miot-spec下，一般为：[{did,siid,piid,value}] |

**Example**  
```js
Host.ui.openTimerSettingPageWithVariousTypeParams("power_on", ["on", "title"], 'off',"title"}),
```

* * *

<a name="module_miot/host/ui..IUi+openTimerSettingPageWithOptions"></a>

#### ~~iUi.openTimerSettingPageWithOptions(options)~~
***Deprecated***

扩展自 openTimerSettingPageWithVariousTypeParams , 新增支持自定义name使用

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10010 ,SDKLevel 10010 开始提供使用  

| Param | Type | Description |
| --- | --- | --- |
| options | <code>object</code> | 配置信息 |
| options.onMethod | <code>string</code> | 配置定时开启的 method 名，同上面openTimerSettingPageWithVariousTypeParams的参数onMethod |
| options.onParam | <code>string</code> | 配置定时开启的 参数，同上面openTimerSettingPageWithVariousTypeParams的参数onParam |
| options.offMethod | <code>string</code> | 配置定时关闭的 method 名，同上面openTimerSettingPageWithVariousTypeParams的参数offMethod |
| options.offParam | <code>string</code> | 配置定时关闭的 参数，同上面openTimerSettingPageWithVariousTypeParams的参数offParam |
| options.displayName | <code>string</code> | 配置场景日志显示的名称 |
| options.identify | <code>string</code> | 自定义定时Identifier |
| options.onTimerTips | <code>string</code> | 定时列表页面、设置时间页面 打开副标题（默认：开启时间） |
| options.offTimerTips | <code>string</code> | 定时列表页面、设置时间页面 关闭时间副标题（默认：关闭时间） |
| options.listTimerTips | <code>string</code> | 定时列表页面 定时时间段副标题（默认：开启时段） |
| options.bothTimerMustBeSet | <code>boolean</code> | 是否强制要求设置时间段？ true: 强制设置时间段(默认：false)如果设置true,忽略下面三个参数 |
| options.showOnTimerType | <code>boolean</code> | 是否可以创建：定时开启？ true: 可以，false:不可以(默认：true) |
| options.showOffTimerType | <code>boolean</code> | 是否可以创建：定时关闭？ true: 可以，false:不可以(默认：true) |
| options.showPeriodTimerType | <code>boolean</code> | 是否可以创建：时间段定时？ true: 可以，false:不可以(默认：true) 注意：showOnTimerType、showOffTimerType、showPeriodTimerType三个参数至少有一个为true，才有效，否则三个中任意都会被忽略掉 |

**Example**  
```js
Host.ui.openTimerSettingPageWithOptions({onMethod:"set_properties", onParam: [{did:Device.deviceID, siid:3, piid:2, value:true}], offMethod: "set_properties", offParam: [{did:Device.deviceID, siid:3, piid:2, value:false}], displayName:"设置xxx定时"，identify:"plug_usb_countdowm"})
```

* * *

<a name="module_miot/host/ui..IUi+openPowerMultikeyPage"></a>

#### iUi.openPowerMultikeyPage(did, mac)
更多设置-多键开关设置页面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10010 ,SDKLevel 10010 开始提供使用  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| did | <code>string</code> |  | 设备did 指定设备ID |
| mac | <code>string</code> | <code>null</code> | 设备mac option, 在不传递时。默认使用当前设备 |

**Example**  
```js
Host.ui.openPowerMultikeyPage(did, mac);
```

* * *

<a name="module_miot/host/ui..IUi+addOrCopyIR"></a>

#### iUi.addOrCopyIR(did, type, models, extra)
添加或者复制一个红外遥控器

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10003  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| did | <code>string</code> |  | 设备did |
| type | <code>number</code> | <code>0</code> | 0：添加遥控器；1：复制遥控器。默认0 |
| models | <code>array</code> |  | 一组红外遥控器model，只传入一个model将直接跳转到相应的品牌列表或者机顶盒列表，支持的models见文档。默认空数组[] |
| extra | <code>object</code> |  | 额外配置，会传入打开的插件页，也有部分特殊功能定义字段如下： |
| [extra.create_device] | <code>boolean</code> | <code>true</code> | 米家首页列表是否展示虚拟遥控器设备。默认true。暂时只有android支持 |
| [extra.dismiss_current_plug] | <code>boolean</code> | <code>true</code> | since 10020 。在推出新的插件页面时，关掉当前页面，返回app首页。iOS Only |
| [extra.open_room_select] | <code>boolean</code> |  | 红外遥控添加完成是否跳转到选择房间页面，默认值 false |


* * *

<a name="module_miot/host/ui..IUi+openDevice"></a>

#### iUi.openDevice(did, model, params) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
打开用户账号下某一设备的插件

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - 打开插件失败，返回错误信息；打开插件成功，无回调信息  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| did | <code>string</code> |  | 设备的did |
| model | <code>string</code> |  | 设备的model |
| params | <code>object</code> |  | 额外参数，打开插件时传入，也有部分特殊功能定义字段如下： |
| [params.dismiss_current_plug] | <code>boolean</code> | <code>true</code> | since 10020 。是否在推出新的插件页面时，关掉当前页面，返回app首页。iOS Only |


* * *

<a name="module_miot/host/ui..IUi+openPluginPage"></a>

#### iUi.openPluginPage(did, pageName, pageParams)
打开用户账号下某一设备的插件,可支持跳转到插件的某一页面
至于跳转到哪个页面，**需要插件方做支持**，示例可以参考com.xiaomi.demo 中 Host.ui.openPluginPage 的使用
整体流程如下：
插件调用此方法openPluginPage
     ⬇  ️
将参数传到native
     ⬇
native调用打开插件的方法，带上此处传递的参数
     ⬇
native打开RN页面，将参数传递到Package.js
     ⬇
支持打开内部页面的插件，通过Package.entrance获取将要跳转到哪个页面，通过Package.pageParams获取此页面需要的页面参数
     ⬇
打开插件对应页面，注意：如果isBackToMainPage为true，则需要在你的插件首页的componentDidMount中，增加跳转逻辑，反之，则应该在index.js中控制入口界面。详细使用请参考Demo中 openPluginPage、Package.entrance、Package.pageParams三个方法的使用

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10026  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| did | <code>string</code> |  | 设备的did |
| pageName | <code>string</code> | <code>&quot;main&quot;</code> | 将打开插件的某一页面, 此参数将会赋值给 Package.entrance, 默认为 Entrance.Main |
| pageParams | <code>object</code> |  | 将打开插件的某一页面的参数，此参数将会赋值给 Package.pageParams |
| [pageParams.isBackToMainPage] | <code>boolean</code> | <code>true</code> | 打开的插件页面按返回，是否需要返回到插件首页 |
| [params.dismiss_current_plug] | <code>boolean</code> |  | since 10040 。是否在推出新的插件页面时，关掉当前页面，返回app首页，默认false。iOS Only |
| [pageParams.open_plugin_source] | <code>int</code> |  | since 10059, 可选参数，标识从哪里打开插件(不传默认为0)。可能的取值有： 0--默认值，如米家首页/其他；1-- 标准插件的更多功能；2--设置页的首页样式 |

**Example**  
```js
let pageParams = {did:Device.deviceID,model:Device.model}
Host.ui.openPluginPage(Device.deviceID, PluginEntrance.Setting, pageParams)
```

* * *

<a name="module_miot/host/ui..IUi+openPageWithClassName"></a>

#### iUi.openPageWithClassName(className)
打开一个原生类 className ，界面类类名 注意 用此方法打开的vc初始化时不需要传参数，
需要传参的viewController暂时还需要手动导出

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Description |
| --- | --- | --- |
| className | <code>string</code> | 类的名字 |


* * *

<a name="module_miot/host/ui..IUi+openNewMorePage"></a>

#### ~~iUi.openNewMorePage()~~
***Deprecated***

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

* * *

<a name="module_miot/host/ui..IUi+openPhoneBluSettingPage"></a>

#### iUi.openPhoneBluSettingPage()
**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10002
android特有页面，ios 不能使用
打开手机蓝牙设置页面  

* * *

<a name="module_miot/host/ui..IUi+openXiaoAiLearnPage"></a>

#### iUi.openXiaoAiLearnPage(clientId, did, aiMiotClientId, aiClientId, aiVersion, otherParams)
打开小爱训练计划

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Description |
| --- | --- | --- |
| clientId | <code>string</code> |  |
| did | <code>string</code> | 设备 ID |
| aiMiotClientId | <code>string</code> | 米家的客户端 ID |
| aiClientId | <code>string</code> | 水滴平台的客户端 |
| aiVersion | <code>string</code> | "" 不隐藏 "thirdpart" 隐藏 “一段录音” “设备控制” 按钮 "audio" 隐藏 “一段录音” 按钮 "device" 隐藏 “设备控制” 按钮 |
| otherParams | <code>object</code> | 想怎么玩都行的参数，会覆盖之前的 |


* * *

<a name="module_miot/host/ui..IUi+showBLESwitchGuide"></a>

#### iUi.showBLESwitchGuide()
显示提示用户打开蓝牙的动画示意图, 仅在iOS下有效，Android下无反应

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10004  

* * *

<a name="module_miot/host/ui..IUi+dismissBLESwitchGuide"></a>

#### iUi.dismissBLESwitchGuide()
隐藏提示用户打开蓝牙的动画示意图, 仅在iOS下有效，Android下无反应

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10004  

* * *

<a name="module_miot/host/ui..IUi+openConnectSucceedPage"></a>

#### iUi.openConnectSucceedPage(model, did)
打开设备快连成功页面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10004  

| Param | Type | Description |
| --- | --- | --- |
| model | <code>string</code> | 设备model |
| did | <code>string</code> | 设备did |


* * *

<a name="module_miot/host/ui..IUi+openCurtainGroupNamePage"></a>

#### iUi.openCurtainGroupNamePage(groupDid, leftDid, rightDid)
打开窗帘组选房间设置名称的页面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10049  

| Param | Type | Description |
| --- | --- | --- |
| groupDid | <code>string</code> | 组设备did |
| leftDid | <code>string</code> | 左侧窗帘did |
| rightDid | <code>string</code> | 右侧窗帘did |


* * *

<a name="module_miot/host/ui..IUi+openZigbeeConnectDeviceList"></a>

#### iUi.openZigbeeConnectDeviceList(did)
打开Zigbee 网关插件开启子设备快连

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10020  

| Param | Type | Description |
| --- | --- | --- |
| did | <code>string</code> | 网关设备did |


* * *

<a name="module_miot/host/ui..IUi+openDeviceNetworkInfoPage"></a>

#### iUi.openDeviceNetworkInfoPage()
打开设备网络信息页面，米家已提供入口：设置 - 更多设置 - 网络信息。此方法只针对wifi设备，combo设备，蓝牙设备请不要调用此方法。

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10026  

* * *

<a name="module_miot/host/ui..IUi+openMiPayPageForAndroid"></a>

#### iUi.openMiPayPageForAndroid(params) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
android 特有， 跳转到小米钱包

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param |
| --- |
| params | 

**Example**  
```js
let params = {action:'issue_mifare',type:'1',product_id:'66666-00211',source_channel:'mijia'};
Host.ui.openMiPayPageForAndroid(params).then((res)=>{console.log(res)}).catch((error)=>{ console.log(error)});
```

* * *

<a name="module_miot/host/ui..IUi+openPluginRecommendScene"></a>

#### iUi.openPluginRecommendScene(did, recommendId)
跳转到设备定向推荐界面,注意：SDK_10024及其之后才可使用

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10024  

| Param | Type |
| --- | --- |
| did | <code>String</code> | 
| recommendId | <code>number</code> | 


* * *

<a name="module_miot/host/ui..IUi+refreshDeviceList"></a>

#### iUi.refreshDeviceList() ⇒ <code>Promise</code>
刷新设备列表，同时刷新设备列表页UI

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10025  

* * *

<a name="module_miot/host/ui..IUi+openTerminalDeviceSettingPage"></a>

#### iUi.openTerminalDeviceSettingPage(type)
跳转到终端设备指定的设置页面 如 iPhone和安卓手机的系统设置页面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10036  

| Param | Type | Description |
| --- | --- | --- |
| type | <code>number</code> | type = 1 打开手机设置页中米家app配置页面      10036及以上 有效      type = 2 WiFi设置页面                      10036及以上 有效      type = 3 WiFi选择页面                      10047及以上 有效(仅Android)，iOS上无任何效果 |


* * *

<a name="module_miot/host/ui..IUi+openAndroidLocationServerSettingPage"></a>

#### iUi.openAndroidLocationServerSettingPage()
打开Android系统位置信息设置页(不同于权限配置页) only Android

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10038  

* * *

<a name="module_miot/host/ui..IUi+openResetAndConnectDevicePage"></a>

#### iUi.openResetAndConnectDevicePage()
打开设备重置页面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10041  

* * *

<a name="module_miot/host/ui..IUi+openWifiConfigStepPage"></a>

#### iUi.openWifiConfigStepPage()
打开配网页面，（仅限猫眼门锁使用）

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10068  

* * *

<a name="module_miot/host/ui..IUi+openVoiceCtrlDevListPage"></a>

#### iUi.openVoiceCtrlDevListPage()
打开语音授权页面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10041  

* * *

<a name="module_miot/host/ui..IUi+openStereoSettingPage"></a>

#### iUi.openStereoSettingPage()
打开立体声设置页面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10041  

* * *

<a name="module_miot/host/ui..IUi+openXiaoaiContentPage"></a>

#### iUi.openXiaoaiContentPage()
跳转到米家播放音乐页面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

* * *

<a name="module_miot/host/ui..IUi+openXiaoaiPage"></a>

#### iUi.openXiaoaiPage(params)
**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10068
跳转到小爱音箱指定的页面  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>jsonObject</code> | 传递的jsonObject对象参数 |

**Example**  
```js
let params={
 xx: xx
}
Host.ui.openXiaoaiPage(params);
```

* * *

<a name="module_miot/host/ui..IUi+openClockMusicSelector"></a>

#### iUi.openClockMusicSelector(ringtone_tab, ringtone_id, ringtone_name, ringtone_query)
跳转到选择闹钟音乐页面，入参是为了告诉native当前选中的是哪个铃声，以高亮当前选中

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Description |
| --- | --- | --- |
| ringtone_tab | <code>string</code> | 铃声tab |
| ringtone_id | <code>string</code> | 铃声的id |
| ringtone_name | <code>string</code> | 铃声的name |
| ringtone_query | <code>string</code> | 铃声的query 返回值:{code:0,data:{ringtone_tab: 'xxx', ringtone_id: 'voice.white_noise', ringtone_name:'xxx',ringtone_query: '森林的声音'}} 错误时：{code:-1,message:'xxxxxx'} |


* * *

<a name="module_miot/host/ui..IUi+openEarthquakeLocationSelector"></a>

#### iUi.openEarthquakeLocationSelector(name, district, city, latitude, longitude)
跳转到地震播报选择地理位置页面，入参是为了告诉native当前选中的是哪个位置

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Description |
| --- | --- | --- |
| name | <code>string</code> | 所选位置的名称 |
| district | <code>string</code> | 所选位置的区 |
| city | <code>string</code> | 所选位置的城市 |
| latitude | <code>number</code> | 所选位置的纬度 |
| longitude | <code>number</code> | 所选位置的经度 返回值:{code:0,data:{name: '武汉市汉阳医院', district: '汉阳区', city:'武汉市', latitude: 30.554658, longitude: 114.252377}} 错误时：{code:-1,message:'xxxxxx'} |


* * *

<a name="module_miot/host/ui..IUi+openLocationSelector"></a>

#### iUi.openLocationSelector(name, district, city, latitude, longitude, title)
跳转到选择地理位置页面，入参是为了告诉native当前选中的是哪个位置

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10059  

| Param | Type | Description |
| --- | --- | --- |
| name | <code>string</code> | 所选位置的名称 |
| district | <code>string</code> | 所选位置的区 |
| city | <code>string</code> | 所选位置的城市 |
| latitude | <code>number</code> | 所选位置的纬度 |
| longitude | <code>number</code> | 所选位置的经度 |
| title | <code>string</code> | 地理位置选择页的标题(默认显示地震播报) 返回值:{code:0,data:{name: '武汉市汉阳医院', district: '汉阳区', city:'武汉市', latitude: 30.554658, longitude: 114.252377}} 错误时：{code:-1,message:'xxxxxx'} |


* * *

<a name="module_miot/host/ui..IUi+openIOSDocumentFileChoosePage"></a>

#### iUi.openIOSDocumentFileChoosePage() ⇒ <code>Promise</code>
打开文件选择页面 only for iOS
 在使用前建议判断平台

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Returns**: <code>Promise</code> - 成功时返回
   { code: 0 , data: [ { path: xxx, fileName: xxx, ext: xxx, fileSize: xxx}, {...}] }
     其中 path 是文件的绝对地址，ext是扩展名，fileName是文件名，byteLen 是文件 size 单位是byte
     需要特别说明的是：
     1：data 返回的数组类型 在 10042 中目前仅返回一个文件信息，不支持多选
     2：如果用户没有选择任何文件，例如点击了左上角的取消按钮，那么 data 中会返回空数组，开发人员需要对此做处理。
 失败时返回
   { code: -1, message: 'file authorized failed'}  // 在ios中 获取icloud需要验证授权，此处错误代表授权失败，如果出现此错误，请联系米家开发人员或提交工单反馈。
   { code: -2, message: 'file read error'}  // 出现此种错误 代表 ios 获取授权文件路径失败，如果出现此错误，请联系米家开发人员或提交工单反馈。
   { code: -3, message: 'method [openIOSDocumentFileChoosePage] can only be invoked on iOS, Android is not supported.' }  
**Since**: 10042  

* * *

<a name="module_miot/host/ui..IUi+openNFCWriteDeviceInfoPage"></a>

#### iUi.openNFCWriteDeviceInfoPage(extra)
**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10049
打开NFC写设备数据的页面，默认会写入设备的基本信息(如did，model等)，如果插件还需要写入其他数据，可以通过参数extra传给App；
在米家首页，手机接触到NFC设备时会读取写入的设备信息，读取成功后会自动打开相应的插件，插件可以通过Package.entryIfno.nfcdata获取
写入NFC设备的extra字段的值  

| Param | Type | Description |
| --- | --- | --- |
| extra | <code>string</code> | 需要写入到nfc设备的额外数据 |

**Example**  
```js
let extra = 'test_data';
Host.ui.openNFCWriteDeviceInfoPage(extra);
```

* * *

<a name="module_miot/host/ui..IUi+openNFCWriteDeviceInfoDebugPage"></a>

#### iUi.openNFCWriteDeviceInfoDebugPage(params)
**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10056
打开NFC写设备数据的调试页面。
注意：该接口仅限于调试使用，只在DB包可用，线上版本不可用。仅特定插件可用。
在米家首页，手机接触到NFC设备时会读取写入的设备信息，读取成功后会自动打开相应的插件，插件可以通过Package.entryIfno.nfcdata获取  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>jsonobject</code> | 需要写入到nfc设备数据; params的格式如下： {    did: string类型，设备的did,必填,且不能为空    model: stringl类型, 对应插件的model,必填,切不能为空    extra: json格式的string类型，需要写入到设备的额外参数，选填 } |

**Example**  
```js
let params={
 did: Device.deviceID,
 model: Device.model,
 extra: JSON.stringify({key:'value'})
}
Host.ui.openNFCWriteDeviceInfoDebugPage(params);
```

* * *

<a name="module_miot/host/ui..IUi+openCommonDeviceSettingPage"></a>

#### iUi.openCommonDeviceSettingPage(type)
**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10052
打开常用设备/常用摄像机设置页面
自SDK10077（含10077）开始，接口变更为打开放大卡片设置页面
***注意***：该接口为SDK内部使用，不建议插件调用  

| Param | Type | Description |
| --- | --- | --- |
| type | <code>string</code> | 在SDK10077以下(不含10077)，0表示常用设备，1表示常用摄像机；SDK10077（含10077）开始，0不再使用，1表示放大卡片。 |

**Example**  
```js
Host.ui.openCommonDeviceSettingPage(1);
```

* * *

<a name="module_miot/host/ui..IUi+getFreqCameraNeedShowRedPoint"></a>

#### iUi.getFreqCameraNeedShowRedPoint() ⇒ <code>object</code>
**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Returns**: <code>object</code> - 成功时：{code:0,data:[true|false]}  
**Since**: 10052
是否需要展示常用摄像机小红点  

* * *

<a name="module_miot/host/ui..IUi+clearFreqCameraNeedShowRedPoint"></a>

#### iUi.clearFreqCameraNeedShowRedPoint()
**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10052
清除常用摄像机小红点  

* * *

<a name="module_miot/host/ui..IUi+openGenerateCrontabStringPage"></a>

#### iUi.openGenerateCrontabStringPage()
**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Param{object}param(optional)。**: param.crontab(string)表示描述定时任务的字符串，当传入的值有效时进入页面会展示对应的定时状态
param.title(string)，要显示的标题
param.hideLegalTime(boolean)，是否隐藏法定节假日,(当服务器为外服时无法显示法定节假日)  
**Return{promise}**: 成功时返回{code:0,data:{crontab:'xxxxxxx'}}
这个方法不会走reject，原生界面崩溃了代表传入的param.crontab不合法，native端解析失败。  
**Since**: 10055
打开设置定时的页面。
这个页面不同于Service.scene.openTimerSettingPageWithOptions，这个页面只负责选择日期然后返回对应的crontab字符串  

* * *

<a name="module_miot/host/ui..IUi+openFirmWareAutoOTAPage"></a>

#### iUi.openFirmWareAutoOTAPage()
**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10056
打开固件自动更新页面（原生页面位置：设置-》检查更新-》固件自动更新）  
**Example**  
```js
Host.ui.openFirmWareAutoOTAPage();
```

* * *

<a name="module_miot/host/ui..IUi+getNavigationBarHeight"></a>

#### iUi.getNavigationBarHeight()
返回Android手机底部导航栏高度

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10069
仅限Android使用  
**Example**  
```js
Host.ui.getNavigationBarHeight()

res : {"data":{"navigationBarHeight":130},"code":0}
```

* * *

<a name="module_miot/host/ui..IUi+openVirtualGroupInitPage"></a>

#### iUi.openVirtualGroupInitPage(param)
打开虚拟组设备的初始化页面，多用在getVirtualDeviceCombineStatus判断成组失败的情况下

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Description |
| --- | --- | --- |
| param | <code>Object</code> | param.groupDid{string},组设备的did param.includeCurtainDevice{boolean}，Android only，页面默认是为灯组设计的，需要兼容窗帘组的时候请传入这个参数 |


* * *

<a name="module_miot/host/ui..IUi+openDeviceOfflineAlert"></a>

#### iUi.openDeviceOfflineAlert()
打开离线弹框页面，用于标准插件页面打开离线弹框

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  
**Since**: 10079  
**Example**  
```js
Host.ui.openDeviceOfflineAlert()
```

* * *

<a name="module_miot/host/ui..IUi+openAssociatePage"></a>

#### iUi.openAssociatePage(param)
打开本地直连控制关联页面

**Kind**: instance method of [<code>IUi</code>](#module_miot/host/ui..IUi)  

| Param | Type | Description |
| --- | --- | --- |
| param | <code>Object</code> | 预留 |

**Example**  
```js
Host.ui.openAssociatePage(param);
```

* * *

