import React from 'react';
import EventGridCard, { CardHeight } from './EventGridCardCar';
import {
  SectionList,
  View,
  Text,
  TouchableOpacity,
  Animated,
  ScrollView,
  Platform,
  TouchableHighlight, Image, RefreshControl, PanResponder
} from "react-native";
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import EventList, { DefFilter, LoadStatus } from "./EventList";
import { BaseStyles } from "../BasePage";
import { Order } from "../framework/EventLoaderInf";
import Separator from 'miot/ui/Separator';
import ImageButton from "miot/ui/ImageButton";
import { DarkMode, Device, Host, Package } from 'miot';
import Util from '../util2/Util';
import { CldDldTypes } from "../framework/CloudEventLoader";
import { MaxSel } from "../car/CarStorage";

const ColCnt = 5;
// const HeaderH = 45;
const HeaderH = 65;
// export const FooterH = 23;
export const FooterH = 22;
const TAG = "EventGridCar";
import dayjs from 'dayjs';
import { DescriptionConstants } from '../Constants';
import ABTest from "../util/ABTest";
import EventGridCardCar from "./EventGridCardCar";
import { carStyles } from "../car/common/Styles";
import { Radius, styles } from "micariot-ui-sdk";
import Toast from "../components/Toast";
import LoadingCarView from "../ui/LoadingCarView";
import imgSource from "../../Resources/Images/car/car_video_unselect.png";
const underlayColor = 'rgba(0,0,0,.05)';

/*
UI data form
[
section0{
  title: XXX,
  data:[[itm1, itm2, itm3],
      [itm1, itm2, itm3],
      [itm1, itm2, itm3]]
}
.....
sectionX{
  title: XXX,
  data:[[itm1, itm2, itm3],
      [itm1, itm2, itm3],
      [itm1, itm2, itm3]]
}
]
*/
const SECTION_SELECT_TYPE = {
  NONE: "NONE",
  PART: "PART",
  ALL: "ALL"
};
export default class EventGridCar extends EventList {
  constructor(aProps) {
    super(aProps);
    this.mSelEv = [];
    this.mCardH = CardHeight;
    this.mSecHeaderBg = this.props.secHeaderBg ? this.props.secHeaderBg : "#ffffff";
    this.offsetY = 0;
    this.innerScrollTop = 0;
    this.state = {
      ...this.state,
      containerTop: new Animated.Value(-80),
      scrollEnabled: true
    };
    this.onMoveShouldSetResponder = this.onMoveShouldSetResponder.bind(this);
    this.onResponderGrant = this.onResponderGrant.bind(this);
    this.onResponderReject = this.onResponderReject.bind(this);
    this.onPanResponderMove = this.onPanResponderMove.bind(this);
    this.onPanResponderRelease = this.onPanResponderRelease.bind(this);
    this.onPanResponderTerminate = this.onPanResponderTerminate.bind(this);
    this.onResponderTerminationRequest = this.onResponderTerminationRequest.bind(this);
    this._panResponder = PanResponder.create({
      onStartShouldSetPanResponder: (event, gestureState) => {
        console.log("+++++++++++++++1onStartShouldSetPanResponder", gestureState.dy, gestureState.dy, this.state.scrollEnabled);
        // return true;
        return !this.state.scrollEnabled;
      }, // 刚开始的时候
      onMoveShouldSetPanResponderCapture: (event, gestureState) => {
        console.log("+++++++++++++++onMoveShouldSetPanResponderCapture", gestureState.dy, this.state.scrollEnabled);
        // return true;
        return !this.state.scrollEnabled;
      },
      onMoveShouldSetPanResponder: (event, gestureState) => {
        console.log("+++++++++++++++1onMoveShouldSetPanResponder",gestureState.dy, this.offsetY, !this.state.scrollEnabled);
        console.log("+++++++++++++++2onMoveShouldSetPanResponder");

        if (this.offsetY == 0 && (Math.abs(gestureState.dy) > 5 || Math.abs(gestureState.dx) > 5)) {
          return true;
        }
        return !this.state.scrollEnabled;
      },
      onPanResponderGrant: this.onResponderGrant,
      onPanResponderReject: this.onResponderReject,
      onPanResponderMove: this.onPanResponderMove,
      onPanResponderRelease: this.onPanResponderRelease,
      onPanResponderTerminationRequest: this.onResponderTerminationRequest,
      onPanResponderTerminate: this.onPanResponderTerminate,
      onShouldBlockNativeResponder: (evt, gestureState) => {
        // Returns whether this component should block native components from becoming the JS
        // responder. Returns true by default. Is currently only supported on android.
        return true;
      },
    });
  }

  onMoveShouldSetResponder(event, gestureState) {
    // if (this.props.refreshing) {
    //   // 正在刷新中，不接受再次下拉
    //   return false;
    // }
    return !this.state.scrollEnabled;
  }
  onResponderGrant(event, gestureState) {
    // console.log(`====== grant`);
  }
  onResponderReject(event, gestureState) {
    // console.log(`====== reject`);
  }
  onPanResponderMove(event, gestureState) {
    // console.log("onPanResponderMove",gestureState.dy, this.offsetY,this.state.scrollEnabled);

    if (gestureState.dy >= 0) {
      this.setState({ scrollEnabled: false });

      // const dy = Math.max(0, gestureState.dy);
      let dy = gestureState.dy - 80;
      if (dy > 0) {
        dy = 0;
      }
      this.state.containerTop.setValue(dy);
    } else {
      this.state.containerTop.setValue(-80);

    }
  }

  onPanResponderRelease(event, gestureState) {
    console.log("++++++onPanResponderRelease+++++",this.state.containerTop.__getValue());
    // 判断是否达到了触发刷新的条件
    const threshold = 100;
    if (this.state.containerTop.__getValue() >= 0) {
      // 触发刷新
      if (this.loadingCarView) {
        this.loadingCarView._startAnim();
      }
      this.props.refreshCallback && this.props.refreshCallback();
      this.mRefresh();
    } else {
      // 没到刷新的位置，回退到顶部
      this._resetContainerPosition();
    }
    this.setState({ scrollEnabled: true });
    // this.checkScroll();
  }
  onResponderTerminationRequest(event) {
    console.log(`====== terminate request`);
    return false;
  }

  onPanResponderTerminate(event, gestureState) {
    console.log(`====== terminate`, this.innerScrollTop, gestureState.dy, gestureState.dy);
    this._resetContainerPosition();
    this.checkScroll();
  }
  innerScrollCallback = (event) => {
    this.innerScrollTop = event.nativeEvent.contentOffset.y;
    this.checkScroll();
  };

  checkScroll = () => {
    // console.log("checkScroll",this.innerScrollTop,this.state.scrollEnabled);
    if (this.innerScrollTop <= 0) {
      if (this.state.scrollEnabled) {
        this.setState({
          scrollEnabled: false,
        });
      }
    } else {
      if (!this.state.scrollEnabled) {
        this.setState({
          scrollEnabled: true
        });
      }
    }
  };

  _resetContainerPosition() {
    Animated.timing(this.state.containerTop, {
      toValue: -80,
      duration: 250
    }).start();
  }

  delayToScroll() {
    let delay = Platform.OS == 'ios' ? 100 : 300;
    setTimeout(() => {
      let scroll = this.getInitScroll();
      console.log(TAG, "scroll", scroll, this.mActive);
      if (this.mActive && scroll != null) {
        this.scrollTo(scroll);
      }
    }, delay);
  }

  getInitScroll() {
    let ev = this.state.events;
    if (this.props.playingId) {
      for (let sectionIndex = 0; sectionIndex < ev.length; ++sectionIndex) {
        let section = ev[sectionIndex].data;
        for (let itemIndex = 0; itemIndex < section.length; ++itemIndex) {
          let tuple = section[itemIndex];
          for (let itm of tuple) {
            // let mPId = `${ itm.fileId }`;
            let mPId = itm.offset != null ? `${ itm.fileId }_${ itm.offset }` : `${ itm.fileId }`;

            if (mPId === this.props.playingId) {
              console.log("======================itemIndex", itemIndex, sectionIndex);
              let itemIndexUse = itemIndex + 1;// 加上顶部的header
              // return { animated: true, itemIndex, sectionIndex, viewOffset: 0, viewPosition: 1 };
              // viewPosition 为 0 时将这个列表项滚动到可视区顶部 (可能会被顶部粘接的 header 覆盖), 为 1 时将它滚动到可视区底部, 为 0.5 时将它滚动到可视区中央。
              // return { animated: false, itemIndex, sectionIndex, viewOffset: HeaderH * sectionIndex, viewPosition: itemIndex == 0 ? 0 : 1 };
              return { animated: false, sectionIndex, itemIndex: itemIndexUse, viewOffset: HeaderH * sectionIndex };
            }
          }
        }
      }
    }
    return null;
  }


  scrollTo(aLoc) {
    if (this.mLst && this.state.events?.length > 0) {
      this.mLst.scrollToLocation(aLoc);
    }
  }

  async getData(date, event, isMore = false, aOrder = Order.Desc, type = this.props.type) {
    console.log("landing1", 'isSltDayMore', this.mSltDayMore);
    if (this.sltDay && !this.mSltDayMore) {
      console.log('getOneDayAllEvent is called');
      let data = await this.mLoader.getOneDayAllEvent(date, event, isMore, aOrder, type);
      return data;
    } else {
      // console.log(this.mLoader, 'getAllEvent is called');
      // let data = await this.mLoader.getAllEvent(date, event, isMore, aOrder, type);
      let data;
      if (this.mLoader.constructor.name == 'SdcardEventLoader' && Package.entryInfo.mobileType == "car") {
        data = await this.mLoader.getAllEventCar(date, event, isMore, aOrder, type);
      } else {
        data = await this.mLoader.getAllEvent(date, event, isMore, aOrder, type);
      }
      return data;
    }
  }

  appendEvents(aOldGrps, aItms, aOrder = Order.Desc) {
    if (Order.Desc == aOrder) {
      let dic = {};
      let dicSel = {};
      if (aOldGrps.length > 0) {
        // use last for fill
        let grp = aOldGrps.pop();
        dic[grp.title] = grp.data;
        dicSel[grp.title] = grp.selected;
      }

      for (let i = 0; i < aItms.length; i++) {
        let item = aItms[i];
        this.buildSection(dic, item);
      }
      for (let key in dic) {
        let groupItem = {
          title: key,
          data: dic[key],
          // selected: dicSel[key] ? dicSel[key] : false,
          selected: dicSel[key] ? dicSel[key] : SECTION_SELECT_TYPE.NONE,
          date: this.getDateInfo(dic[key])
        };

        if (this.props.abType != ABTest.Types.A) {
          delete groupItem.title;
        }
        aOldGrps.push(groupItem);
      }
      return aOldGrps;
    } else {
      throw "not support ASC";
    }
  }

  onGetDataDone(events) {
    let dates = [];
    for (let evt in events) {
      let mdt = events[evt]?.date;
      if (mdt && !dates.includes(mdt)) { // 日期已经归类，不会重复的
        dates.push(mdt);
      }
    }
    if (events?.length > 0 && (events[0]?.data[0]?.isNoSVLTips || events[0]?.data[0]?.isCloudTips)) {
      this.props.onGetDataDone(events.length - 1, events, dates);
    } else {
      this.props.onGetDataDone(events.length, events, dates);
    }

    setTimeout(() => {
      if (this.loadingCarView) {
        this.loadingCarView._stopAnim();
      }
      this._resetContainerPosition();
    },300);

  }

  getDateInfo(grp) {
    let tuple = grp[grp.length - 1];
    return tuple[0] ? tuple[0].createTime : tuple.createTime;
  }

  buildSection(aSecDict, aItm) {

    let dStr = dayjs.unix(aItm.createTime / 1000).format(LocalizedStrings["yyyymmdd"]);
    if (Util.isToday(dayjs.unix(aItm.createTime / 1000))) {
      dStr = `${ dStr } | ${ LocalizedStrings['today'] }`;
    } else if (Util.isYestoday(dayjs.unix(aItm.createTime / 1000))) {
      dStr = `${ dStr } | ${ LocalizedStrings['yestoday'] }`;
    }
    let grp = aSecDict[dStr];
    aItm['sectKey'] = dStr;
    if (!grp) {
      aSecDict[dStr] = [[aItm]];
    } else {
      let tuple = grp[grp.length - 1];
      if (ColCnt == tuple.length) {
        grp.push([aItm]); // make new tuple
      } else {
        tuple.push(aItm); // append to existing
      }
    }
  }


  applyFilter(aEvents, aFilter) {
    let curEv = aEvents;
    let newEv = [];
    for (let evGrp of curEv) {
      let nd = [];
      let filtered = null;
      for (let tuple of evGrp.data) {
        let nt = tuple.filter(aFilter);
        if (!filtered) {
          if (ColCnt == nt.length) {
            nd.push(nt);
          } else if (nt.length > 0) {
            filtered = nt;
          }
        } else {
          while (nt.length > 0 && filtered.length < ColCnt) {
            filtered.push(nt.shift());
          }
          if (ColCnt == filtered.length) {
            nd.push(filtered);
            filtered = null;
          }

          if (nt.length > 0) {
            filtered = nt;
          }
        }
      }
      if (filtered != null) {
        nd.push(filtered);
      }

      evGrp.data = nd;

      if (evGrp.data.length > 0) {
        newEv.push(evGrp);
      }
    }
    return newEv;
  }

  filterItems(data) {
    let arr = data;
    if (data instanceof Array && data[0]) {
      if (arr[0].data[0] && typeof (arr[0].data[0].isShowImg) === 'boolean') {// 判断是否有海外云存需求
        arr[0].data = data[0].data.filter((v) => v.isShowImg);
      }
    }
    return arr;
  }

  render() {
    let hvf = this.props.eventHeaderView;

    let paddingBm = this.props.withDailyStoryButton ? Platform.OS == 'ios' ? 106 : 76 : 50;
    return (
      <View style={ [{ flex: 1, zIndex: 0 }, this.props.showTab ? null : { display: 'none' }] }>
        <View style={{flex: 1, backgroundColor: '#00000000'}} {...this._panResponder.panHandlers}>

          <Animated.View style={{ height: 80, marginTop: this.state.containerTop, justifyContent: 'center', alignItems: 'center' }}>
            <LoadingCarView
              ref={ (ref) => {
                this.loadingCarView = ref;
              } }
              style={ { width: 42, height: 42 } }
              startAnimAtBegin={false}
              source={Util.isDark() ? require('../../Resources/Images/car/car_refresh_loading.png') : require('../../Resources/Images/car/car_refresh_loading_light.png')}/>
          </Animated.View>

          <SectionList
            ref={ (ref) => {
              this.mLst = ref;
            } }
            style={ this.props.style }
            sections={ this.filterItems(this.state.events) }
            contentContainerStyle={ [this.props.contentContainerStyle, { paddingBottom: paddingBm, flexGrow: 1 }] }
            scrollIndicatorInsets={ { top: 0, left: 0, bottom: 0, right: 0 } }
            ListEmptyComponent={ this.props.eventEmptyView != null ? this.props.eventEmptyView : this.mEmptyV() }
            renderItem={ this.mRItem }
            renderSectionHeader={ this.mRSecH }
            // scrollEnabled={this.state.scrollEnabled}
            renderSectionFooter={ this.mRSecF }
            showsVerticalScrollIndicator={ false }
            keyExtractor={ (item, index) => {
              // console.log("keyExtractor", "calc key", index, item, item.fileId, item.offset);
              // return `${ item.fileId }_${ item.offset }_${ index }`;
              if (item.length > 0) {
                return  `${ item[0].fileId }_${ item[0].offset }_${ index }`;
              }
              return `${ index }`;
            } }
            sectionKeyExtractor={(section, index) => {
              console.log("sectionKeyExtractor", "calc key", index, section);

              return section.id || index.toString();
            }}
            onEndReached={ this.mOnEnd }
            onEndReachedThreshold={ 0.1 }
            onRefresh={ this.mRefresh }
            ListHeaderComponent={ null }
            ListFooterComponent={ this.mFooter }
            // refreshing={ LoadStatus.Loading === this.state.loadingStatus }
            refreshing={ false }
            refreshControl={
              <RefreshControl
                refreshing={ false }
                progressBackgroundColor={"#00ff00"}
                progressViewOffset={-1000}
                enabled={false}
                tintColor={"#00ff00"}
                onRefresh = {() => this.mRefresh()}
              />

            }
            getItemLayout={ this.mLayoutGetter }
            stickySectionHeadersEnabled={ true }
            onScroll={ (e) => {
              this.offsetY = e.nativeEvent.contentOffset.y;
              this.innerScrollCallback(e);
              this.props.onScroll && this.props.onScroll(e.nativeEvent.contentOffset.y);
            } }
            scrollEventThrottle={16}
          />
        </View>


        {/*{this.props.isEditing ? this.renderBottomButtons() : null}*/}
      </View>

    );
  }

  removeEvents(aFilter) {
    let newEv = this.applyFilter(this.state.events, aFilter);
    console.log(TAG, 'removeEvents', newEv.length);
    this.setState({ events: newEv });
    // if (0 == newEv.length || this.props.typeTab === 1) {
    //   this.mRefresh();
    // } else {
    //   this.mOnEnd();
    // }
  }

  renderBottomButtons() {

    return <View style={ [{
      height: 88,
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 62,
      marginTop: 42
    }] }>
      <TouchableHighlight
        style={ {
          flex: 1,
          borderRadius: Radius.PanelLevel
        } }
        onPress={ () => {
          console.log("{{{{{{{{{{{{{{{{{{{{{{{{");
        } }
        underlayColor={ underlayColor }>
        <View style={ {
          flex: 1,
          backgroundColor: styles.itemHighlightStyle.backgroundColor,
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: Radius.WidgetLevel
        } }>
          <Text style={ [{
            fontSize: styles.buttonTextStyle.fontSize,
            lineHeight: 35,
            color: styles.buttonTextStyle.color,
            fontFamily: 'D-DINCondensed-Bold'
          }, carStyles.dialogButtonSureStyle] }>
            { LocalizedStrings['action_confirm'] }
          </Text>
        </View>
      </TouchableHighlight>

      <TouchableHighlight
        style={ { flex: 1 } }
        onPress={ () => {
          console.log("{{{{{{{{{{{{{{{{{{{{{{{{");

        } }
        underlayColor={ underlayColor }>
        <View style={ {
          flex: 1,
          backgroundColor: styles.itemSecondaryStyle.backgroundColor,
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: Radius.WidgetLevel,
          marginLeft: 24
        } }>
          <Text style={ [{
            fontSize: styles.buttonTextStyle.fontSize,
            lineHeight: 35,
            color: styles.buttonTextStyle.color,
            fontFamily: 'D-DINCondensed-Bold' // TODO: 英文字体，中文加粗效果

          }, carStyles.dialogButtonCancelStyle] }>
            { LocalizedStrings['action_cancle'] }
          </Text>
        </View>
      </TouchableHighlight>
    </View>;
  }


  componentDidUpdate(aPrevProps) {
    if (this.props.selectAll !== aPrevProps.selectAll) {

      let doSel = this.props.selectAll;
      for (let i = 0; i < this.state.events.length; ++i) {
        let section = this.state.events[i];
        if (this.selSection(section, doSel)) {
          break;
        }
      }
    } else if (this.props.showTab !== aPrevProps.showTab) {
      // for back
      for (let i = 0; i < this.state.events.length; ++i) {
        let section = this.state.events[i];
        // section.selected = false;
        section.selected = SECTION_SELECT_TYPE.NONE;
      }
    }
    super.componentDidUpdate(aPrevProps);
  }

  clearSelect() {
    for (let i = 0; i < this.state.events.length; ++i) {
      let section = this.state.events[i];
      // section.selected = false;
      section.selected = SECTION_SELECT_TYPE.NONE;
    }
  }

  sectSize(aSection) {
    let size = 0;
    for (let j = 0; j < aSection.data.length; ++j) {
      let evTuple = aSection.data[j];
      size += evTuple.length;
    }
    return size;
  }

  isAllSectSelected(aSection) {
    let size = this.sectSize(aSection);
    let cnt = 0;
    for (let j = 0; j < aSection.data.length; ++j) {
      let evTuple = aSection.data[j];
      for (let ev of evTuple) {
        if (ev.selected) {
          cnt += 1;
        }
      }
    }
    if (cnt == size || cnt == MaxSel) {
      return true;
    }
    return false;
  }

  sectionSelectedState(aSection) {
    let size = this.sectSize(aSection);
    let cnt = 0;
    for (let j = 0; j < aSection.data.length; ++j) {
      let evTuple = aSection.data[j];
      for (let ev of evTuple) {
        if (ev.selected) {
          cnt += 1;
        }
      }
    }
    let isAll = false;
    let isPart = false;
    //  || cnt == MaxSel 此条件去掉
    if (cnt == size) {
      isAll = true;
    }

    if (cnt > 0) {
      isPart = true;
    }
    return { isAll, isPart };
  }

  isAllSelected() {
    for (let j = 0; j < this.state.events.length; ++j) {
      let sect = this.state.events[j];
      if (!this.isAllSectSelected(sect)) {
        return false;
      }
    }
    return true;
  }

  selSection(aSection, aDoSel) {
    let stop = false;
    let size = this.sectSize(aSection);
    let cnt = 0;
    for (let j = 0; j < aSection.data.length && !stop; ++j) {
      let evTuple = aSection.data[j];
      for (let ev of evTuple) {
        cnt += 1;
        if (aDoSel) {
          if (!ev.selected) {
            stop = !this.props.onEventPress(ev);
            if (stop) {
              cnt -= 1;
              break;
            }
          }
        } else {
          if (ev.selected) {
            this.props.onEventPress(ev);
          }
        }
      }
      if (stop) {
        break;
      }
    }
    if (cnt == size) {
      // aSection.selected = aDoSel;
      aSection.selected = SECTION_SELECT_TYPE.ALL;
    } else if (cnt > 0 && cnt < size) {
      aSection.selected = SECTION_SELECT_TYPE.PART;
    } else if (cnt > 0) {
      aSection.selected = SECTION_SELECT_TYPE.PART;
    }
    if (!aDoSel) {
      aSection.selected = SECTION_SELECT_TYPE.NONE;
    }
    this.props.onSelectAllCB && this.props.onSelectAllCB(this.isAllSelected());
    return stop;
  }

  getAdjacentEvent(aEv) {
    let curEv = this.state.events;
    for (let i = 0; i < curEv.length; ++i) {
      let evGrp = curEv[i].data;
      for (let j = 0; j < evGrp.length; ++j) {
        let tuple = evGrp[j];
        let idx = tuple.findIndex((aItm) => {
          return aItm.fileId == aEv.fileId;
        });
        if (idx != -1) {
          if (idx + 1 < tuple.length) {
            return tuple[idx + 1];
          } else {
            if (j + 1 < evGrp.length) {
              return evGrp[j + 1][0];
            }
            // check net grp
            else if (i + 1 < curEv.length) {
              return curEv[i + 1].data[0][0];
            }
            // look back
            else if (idx - 1 >= 0) {
              return tuple[idx - 1];
            } else if (j > 0) {
              let preTuple = evGrp[j - 1];
              return preTuple[preTuple.length - 1];
            } else if (i > 0) {
              let preTuple = curEv[i - 1].data[0];
              return preTuple[preTuple.length - 1];
            } else {
              return null;
            }
          }
        }
      }
    }
  }

  mRefresh = () => {
    // if (!this.props.isEditing) {
    //   console.log("landing5", new Date());
    //   this.getEventList(this.state.startDate, 'Default', false);
    //   if (Platform.OS == 'android' && this.sltDay) {
    //     this.props.onScroll && this.props.onScroll(-90);
    //   }
    // }

    console.log("landing5", new Date());
    this.getEventList(this.state.startDate, 'Default', false);
    if (Platform.OS == 'android' && this.sltDay) {
      this.props.onScroll && this.props.onScroll(-90);
    }
  };


  mRItem = ({ section, index }) => {
    // console.log(this.tag, "mRItem", index);
    return (
      <View style={ { height: CardHeight, flexDirection: "row" } }>
        {
          section.data[index]
            .map((itm, idx) => {
              let rightPadding = ColCnt - 1 == idx ? 0 : 28;
              let mkey = itm.offset != null ? `c_${ index }_${ itm.fileId }_${ itm.offset }` : `c_${ index }_${ itm.fileId }`;
              // let mPId = `${ itm.fileId }`;
              let mPId = itm.offset != null ? `${ itm.fileId }_${ itm.offset }` : `${ itm.fileId }`;

              // console.log("key: " + mkey + ", eventtime: " + itm.eventTime + ", ctime: " + itm.createTime + ", offset:"+itm.offset);
              return (
                <EventGridCardCar
                  key={ mkey }
                  style={ { marginRight: rightPadding } }
                  item={ itm }
                  isEditing={ this.props.isEditing }
                  isPlaying={ this.props.playingId === mPId }
                  cardPressed={ (aItm) => {
                    this.mEventPress(aItm);
                  } }
                  cardLongPressed={ (aItm) => {
                    this.props.onEventLongPress && this.props.onEventLongPress(aItm);

                  } }/>
              );
            })
        }
      </View>
    );
  };

  mEventPress = (aItm) => {
    let events = this.state.events;
    let nextDate = this.state.nextDate;
    let success = this.props.onEventPress(aItm, { events, nextDate });
    // if (!aItm.selected) {
    //   if (MaxSel === this.mSelEv.length) {
    //     Toast.show(LocalizedStrings['max_select_noti']);
    //     success = false;
    //   } else {
    //     this.mSelEv.push(aItm);
    //     aItm.selected = true;
    //   }
    // } else {
    //   this.mSelEv = this.mSelEv.filter((arg) => {
    //     return arg.fileId !== aItm.fileId;
    //   });
    //   aItm.selected = false;
    // }

    if (success) {
      let sectionKey = aItm.sectKey;
      if (events && events.length > 0) {
        for (let key in events) {
          let sect = events[key];
          if (sect.title == sectionKey) {
            // if (!aItm.selected && sect.selected) {
            //   sect.selected = false;
            // } else if (!sect.selected && aItm.selected && this.isAllSectSelected(sect)) {
            //   sect.selected = true;
            // } else {
            //   sect.selected = this.isAllSectSelected(sect);
            // }
            let { isAll, isPart } = this.sectionSelectedState(sect);
            if (isAll) {
              sect.selected = SECTION_SELECT_TYPE.ALL;
            } else if (isPart) {
              sect.selected = SECTION_SELECT_TYPE.PART;
            } else {
              sect.selected = SECTION_SELECT_TYPE.NONE;
            }
            this.setState({});
            break;
          }
        }
      }
      this.props.onSelectAllCB && this.props.onSelectAllCB(this.isAllSelected());
    }
  };

  mRSecH = (aDat) => {
    if (!aDat.section?.title || aDat.section.title == "Invalid Date") {
      return null;
    }
    // console.log(TAG, "isSltDay", this.props.isSltDay, "isEditing", this.props.isEditing);
    let mSecStyle = this.props.isSltDay ? {
      fontSize: Util.isLanguageCN() ? 14 : 12,
      fontWeight: 'bold',
      color: "#000000"
    } : { fontSize: Util.isLanguageCN() ? 12 : 10, fontWeight: 'bold', color: "#8C93B0" };
    let mSlctWidth = 90;
    if (Host.locale.language == 'zh' || Host.locale.language == 'zh_hk' || Host.locale.language == 'zh_tw') {
      mSlctWidth = 60;
    }
    let imgSource = require('../../Resources/Images/car/car_video_unselect.png');
    if (aDat.section.selected == SECTION_SELECT_TYPE.ALL) {
      imgSource = require('../../Resources/Images/car/car_video_select.png');
    } else if (aDat.section.selected == SECTION_SELECT_TYPE.PART) {
      imgSource = require('../../Resources/Images/car/car_video_some_select.png');
    }
    return (
      // <View style = {[BaseStyles.row, { backgroundColor: this.mSecHeaderBg, height: HeaderH, paddingHorizontal: 22, paddingBottom: 10 }]}>
      <View style={ [BaseStyles.row, {
        // height: 65,
        height: HeaderH,
        alignItems: "center",
        backgroundColor: styles.smallContainerStyle.backgroundColor
        // backgroundColor: "#0000ff"
      }] }>
        <TouchableOpacity style={{display: "flex", flexDirection: "row"}} onPress={() => {
          console.log("=====================",aDat.section.selected);
          this.selSection(aDat.section, aDat.section.selected == undefined || aDat.section.selected == SECTION_SELECT_TYPE.NONE);
        }}>
          <Image style={{ width: 40, height: 40, marginHorizontal: 12 }} source={imgSource}/>
          <Text style={ [carStyles.storageTitle, { paddingBottom: 20, marginLeft: 17 }] }>{ aDat.section.title }</Text>
        </TouchableOpacity>
        {/*{*/}
        {/*  this.props.isEditing ?*/}
        {/*    (<TouchableOpacity style={ {*/}
        {/*      display: "flex",*/}
        {/*      flexDirection: "column",*/}
        {/*      justifyContent: "center",*/}
        {/*      alignItems: "center",*/}
        {/*      minHeight: 28,*/}
        {/*      paddingBottom: 20*/}
        {/*    } }*/}
        {/*                       onPress={ () => {*/}
        {/*                         console.log(TAG, "ALL cancel or not");*/}
        {/*                         this.selSection(aDat.section, !aDat.section.selected);*/}
        {/*                       } }*/}
        {/*    >*/}
        {/*      <Text*/}
        {/*        style={ [carStyles.selectAllStyle] }>{ aDat.section.selected ? LocalizedStrings["item_unsel_all"] : LocalizedStrings["item_sel_all"] }</Text>*/}
        {/*    </TouchableOpacity>)*/}
        {/*    : null*/}
        {/*}*/}
      </View>);
  };

  mEmptyV = () => {
    let emptyMT = 0;
    if (this.props.eventHeaderHeight) {
      emptyMT = -this.props.eventHeaderHeight / 2;
    }
    return (this.state.loadingStatus === LoadStatus.Finish ?
      <View
        accessibilityLabel={DescriptionConstants.kj_1_17}
        style={{ height: "100%", justifyContent: "center", alignItems:"center" }}>

        <Image
          style={{ alignSelf: "center", width: 200, height: 128 }}
          source={DarkMode.getColorScheme() === 'dark' ? require("../../Resources/Images/car/list_empty.png") : require("../../Resources/Images/car/list_empty_s_light.png")} />

        <Text style={styles.loadingFailedTextStyle}
              numberOfLines={2}
        >
          {
            this.state.isP2pLostEmpty ? LocalizedStrings.device_not_cont_sdcard_page_desc_empty : (this.props.emptyDes ? this.props.emptyDes : LocalizedStrings['no_content'])
          }
        </Text>
      </View>
      : null);
  }

  mRSecF = () => {
    return this.props.type && this.props.type == CldDldTypes.Events ? null :
      <View style={ { height: FooterH, paddingHorizontal: 25, justifyContent: "center" } }>
      </View>
      ;
  };


  mLayoutGetter = (aSections, aIdx) => {
    let pos = -1;
    let hh = this.props.eventHeaderView ? this.props.eventHeaderHeight : 0;
    let length = aIdx < 0 ? hh : HeaderH;
    let offset = aIdx >= 0 ? hh : 0;
    let i = 0;
    for (i = 0; i < aSections.length && pos < aIdx; ++i) {
      let data = aSections[i].data;
      // account for section header and footer
      let nPos = pos + data.length + 2;
      if (nPos < aIdx) {
        pos = nPos;
        offset = offset + data.length * this.mCardH + HeaderH + FooterH;
      } else if (nPos == aIdx) { // hit section footer
        pos = nPos;
        length = FooterH;
        offset = offset + HeaderH + data.length * this.mCardH;
      } else {
        ++pos;// add section header
        if (pos == aIdx) { // hit section header
          length = HeaderH;
        } else {
          offset += HeaderH;
          if (aIdx <= pos + data.length) { // in middle
            offset = offset + (aIdx - pos - 1) * this.mCardH;
            length = this.mCardH;
          } else { // last footer
            offset = offset + (aIdx - pos) * this.mCardH;
            length = FooterH;
          }
          pos = aIdx;
        }
      }
    }
    let ret = { length, offset, index: aIdx };
    // console.log(TAG, "getItemLayout", aIdx, ret, pos, "CardHeight", CardHeight, "HeaderH", HeaderH, "FooterH", FooterH, "mCardH" + this.mCardH);
    return ret;
  };

  getItemsFromEvents() {
    let items = [];
    if (this.state.events && this.state.events.length > 0) {
      for (let key in this.state.events) {
        for (let arr in this.state.events[key].data) {
          items = items.concat(this.state.events[key].data[arr]);
        }
      }
    }
    let nItems = [];
    for (let key in items) { // rever map, ref to map in localeventloader.getEventList()
      let item = {};
      item.url = items[key].localUrl;
      item.modificationDate = items[key].createTime / 1000;
      item.path = items[key].fileId;
      item.mediaType = items[key].mediaType == 'video' ? 2 : 1;
      nItems.push(item);
    }
    return nItems;
  }


}
