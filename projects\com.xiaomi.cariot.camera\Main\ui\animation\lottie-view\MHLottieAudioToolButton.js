import React from "react";
import { Animated, Easing } from 'react-native';
import MHLott<PERSON>BaseButton from "./base/MHLottieBaseButton";

/**
 * 创建自己的行为枚举值
 * @type  { enum } 
 */
export const MHLottieAudioToolBtnDisplayState = {
  NORMAL: "NORMAL",
  MUTED: "MUTED"
};

export default class MHLottieAudioToolButton extends MHLottieBaseButton {

  /**
   * 创建自己的资源文件
   * @type  { object } 
   */
  static JSONFiles = {
    normal: {
      light: require("../lottie-json/btn_audio.json")
    },
    normal_landscape: {
      light: require("../lottie-json/btn_audio_landscape.json")
    }
  };

  /**
   * 创建自己的基于行为枚举值的链式动画
   * @param  { enum } displayState
   * @param  { bool } transition
   */
  switchDisplayState(displayState, transition) {

    if (displayState == MHLottieAudioToolBtnDisplayState.NORMAL) {

      this.setState({
        file: this.props.landscape ? MHLottieAudioToolButton.JSONFiles.normal_landscape : MHLottieAudioToolButton.JSONFiles.normal,
        loop: false
      });

      Animated.timing(
        this.progress,
        {
          from: 1,
          toValue: 0,
          duration: transition ? 800 : 1
        }
      )
        .start();
    } else if (displayState == MHLottieAudioToolBtnDisplayState.MUTED) {

      this.setState({
        file: this.props.landscape ? MHLottieAudioToolButton.JSONFiles.normal_landscape : MHLottieAudioToolButton.JSONFiles.normal,
        loop: false
      });

      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 800 : 1
        }
      )
        .start();
    }
  }

  constructor(props) {
    super(props);
  }

  componentWillUnmount() {
    super.componentWillUnmount();
  }

  componentDidMount() {
    super.componentDidMount();
  }

  componentDidUpdate(prevProps) {
    super.componentDidUpdate(prevProps);
  }
}
