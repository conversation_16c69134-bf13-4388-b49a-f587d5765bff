import React from 'react';
import { ChoiceDialog, AbstractDialog } from "miot/ui/Dialog";
import { getAccessibilityConfig } from 'miot/utils/accessibility-helper';
import { Text, View, Image, TouchableOpacity } from 'react-native';
import { BaseStyles } from "../BasePage";
import { DescriptionConstants } from '../Constants';

export default class ChoiceDlgEx extends ChoiceDialog {
  _onPress(selected, index) {
    this.props.onSelectEx && this.props.onSelectEx([index]);
    super._onPress(selected, index);
  }
}


// TODO add a scroll view is necessary
export class ChoiceDlgWithScroll extends ChoiceDlgEx {
  render() {
    const showButton = this.props.type === ChoiceDialog.TYPE.MULTIPLE || this.props.useNewType;
    return <AbstractDialog 
      animationType={this.props.animationType} 
      visible={this.state.visible} 
      title={this.props.title}
      dialogStyle={this.props.dialogStyle} 
      showButton={showButton} 
      buttons={this.buttons} 
      style={this.props.modalStyle}
      useNewTheme
      hasShade={true}
      onModalShow={this.props.onModalShow}
      onModalHide={this.props.onModalHide} 
      onDismiss={() => this._onDismiss()}
      {...getAccessibilityConfig({
        accessibilityLabel: this.props.accessible
      })}>
      <View style={{
        marginBottom: 16
      }}>
        {
          this.props.options.map((opt, index) =>
            <TouchableOpacity style={[BaseStyles.row, { paddingHorizontal: 27, height: 54, justifyContent: "flex-start" }]} key={(opt.title || '') + index}
              onPress = {() => {
                this._onPress(true, index);
              }}
              accessibilityLabel={opt.title}
            >
              <Image style={{ marginRight: 13, width: 30, height: 30 }} source = {opt.icon}/>
              <Text style={BaseStyles.text16}>{opt.title}</Text>
            </TouchableOpacity>)
        }
      </View>
    </AbstractDialog>;
  }
}




export class ChoiceDlgWithIconSel extends ChoiceDlgEx {

  render() {
    let selectedIndex = this.props.selectedIndexArray[0];
    const showButton = this.props.type === ChoiceDialog.TYPE.MULTIPLE || this.props.useNewType;
    return <AbstractDialog 
      animationType={this.props.animationType} 
      visible={this.state.visible} 
      hasShade={true}
      title={this.props.title}
      dialogStyle={this.props.dialogStyle} 
      showButton={showButton} buttons={this.buttons} 
      style={this.props.modalStyle}
      useNewTheme 
      onModalShow={this.props.onModalShow}
      onModalHide={this.props.onModalHide} 
      onDismiss={() => this._onDismiss()}
      {...getAccessibilityConfig({
        accessibilityLabel: this.props.accessible
      })}>
      <View style={{
        marginBottom: 16
      }}>
        {
          this.props.options.map((opt, index) =>
            <TouchableOpacity 
              style={[BaseStyles.row, { paddingHorizontal: 27, height: 54, width: "100%", justifyContent: "flex-start", backgroundColor: index == selectedIndex ? "#F5F5F5" : "white" }]} key={(opt.title || '') + index}
              onPress = {() => {
                this._onPress(true, index);
              }}
              accessible={true}
              accessibilityLabel={opt.title}
            >
              <Image style={{ marginRight: 18, width: 30, height: 30 }} source = {opt.icon}/>
              <Text style={[BaseStyles.text16, { fontWeight: "bold", flex: 1 }]}>{opt.title}</Text>
            </TouchableOpacity>)
        }
      </View>
    </AbstractDialog>;
  }

}
