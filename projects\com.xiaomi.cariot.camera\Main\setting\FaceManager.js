'use strict';

import React from 'react';
import { ScrollView, Button, BackHandler, View, Text, Image, StyleSheet, FlatList, Dimensions, TouchableOpacity, ActivityIndicator, Platform, PermissionsAndroid } from 'react-native';
import { Device, Service, DarkMode, System, API_LEVEL } from 'miot';
import { MessageDialog } from "mhui-rn";
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import MultiSelectableNavigationBar from '../ui/MultiSelectableNavigationBar';
import { ChoiceDialog, AbstractDialog } from 'miot/ui/Dialog';
import Util from "../util2/Util";
import InputDlgEx from '../widget/InputDlgEx';
import ImagePicker, { launchCamera } from 'react-native-image-picker';
import Toast from '../components/Toast';
import StorageKeys from '../StorageKeys';
import Host from 'miot/Host';
import TrackUtil from '../util/TrackUtil';
import LogUtil from '../util/LogUtil';
import PermissionUtil from '../util/PermissionUtil';
import { NavigationBar } from 'mhui-rn';
import { Permissions } from "miot/system/permission";
import CommonMsgDialog from "../ui/CommonMsgDialog";

const kWindowHeight = Math.max(Dimensions.get("screen").height, Dimensions.get("screen").width);
const kWindowWidth = Math.min(Dimensions.get("screen").height, Dimensions.get("screen").width);
export default class FaceManager extends React.Component {

  state = {
    isSelectMode: false,
    index: 0,
    isEmpty: true,
    isCurrentDayEmpty: false,
    calendarDays: [],
    dialogVisible: false,
    albumFiles: [],
    showLoading: true,
    coverFaceInfosList: [],
    unmarkFacesImgList: [],
    selectedFiguresList: [],
    showMarkView: false,
    showUnMarkView: false,
    figureInfos: [],
    figureInfosLength: 0,
    showPermissionDialog: false,
    isNameError: false
    // figureInfos: []

  };

  constructor(props) {
    super(props);
    this.isCheckingPermission = false;
  }

  setNavigation(isSelectAll, isSelectMode, isDisableSelect, title, shieldLeft) {
    if (Device.isReadonlyShared) {
      isDisableSelect = true;
    }

    this.props.navigation.setParams({

      title: this.state.showNoOpenFaceManager?LocalizedStrings["camera_face"]:LocalizedStrings["lowpower_face_manager"],

      left: [
        {
          key: isSelectMode ? NavigationBar.ICON.CLOSE : NavigationBar.ICON.BACK,
          onPress: () => {
            if (this.state.isSelectMode) {// 如果已经是选择模式，点左边 应该退出选择模式
              this.onSelectAllChanged(false);// 将isSelected重置
              this.setNavigation(false, false, false,);
              this.setState({ isSelectMode: false });
            } else { // 不是选择模式 就退出吧
              this.props.navigation.goBack();
            }
          }
        }
      ],
      right:
        shieldLeft ?
          [] :
          [
            {
              key: !isSelectMode ? NavigationBar.ICON.EDIT : (isSelectAll ? NavigationBar.ICON.SELECTED_ALL : NavigationBar.ICON.SELECT_ALL),
              onPress: () => {
                if (!this.state.isSelectMode) { //进入选择模式
                  this.setNavigation(false, true, false, LocalizedStrings["action_select"]);
                  this.setState({ isSelectMode: true });
                } else if (this.state.isSelectAll) { //退出全选模式
                  this.onSelectAllChanged(false);
                } else { //进入全选模式
                  this.onSelectAllChanged(true);
                }
              }
            }
          ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }

    });
  }
  componentDidMount() { // 第一次进来的时候这样子设计。

    this.setState({ index: 1 });
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.isPageForeGround = true;
        StorageKeys.IS_AI_FACE_OPEN.
          then((result) => {
            console.log('rettt', result);
            // 当前的设备和用户和是否开启
            if (result) {
              this.setState({ showNoOpenFaceManager: false });
              this.setNavigation(false, false, false);
              this._onGetData();
            }
            else {
              this.setState({
                showNoOpenFaceManager: true,
                isSelectMode: false
              });
              this.setNavigation(false, false, false, LocalizedStrings["lowpower_face_manager"], true);
              return;
            }
          })
          .catch((err) => {
            console.log('errr', err)
            this.setState({ showNoOpenFaceManager: true, isSelectMode: false });
            this.setNavigation(false, false, false, LocalizedStrings["lowpower_face_manager"], true);
            return;
          });
      }
    );
    // 读取

    this.didBlurListener = this.props.navigation.addListener(
      'didBlur',
      () => {

        this.isPageForeGround = false;
      }
    );
    // this.setNavigation(false, false, true);
  }
  _onGetData() {
    this.setNavigation(this.state.isSelectAll, this.state.isSelectMode, this.state.isCurrentDayEmpty);
    this.setState({ index: 1 });
    let endTime = new Date().getTime()
    Util.getAllUnmarkFaces(endTime).then((res) => {
      console.log('res', res)
      this.setState({
        unmarkFacesImgList: res.faceList.length > 3 ? res.faceList.slice(0, 3) : res.faceList,
        showLoading: false,
        showUnMarkView: true
      }, () => {
        this.getAllUnFaceurl(this.state.unmarkFacesImgList)
      })
    }).catch((err) => {
      LogUtil.logOnAll("FaceManager", "getAllUnmarkFaces failed" + err);
      Toast.fail("c_get_fail", err);
      this.props.navigation.goBack()
    })
    //就是API文件中的callmethod的用法
    Util.getFaceAllFigure().then((res) => {
      this.setState({
        mAllFigureInf: res,
        figureInfos: [...res, { faceUrl: false, figureName: LocalizedStrings["add"] }],
        showMarkView: true,
        figureInfosLength: res.length,
      }, () => {
        this.getAllFaceurl(this.state.figureInfos);
      })
      this.setNavigation(this.state.isSelectAll, this.state.isSelectMode, false);
      // console.log(this.figureInfos, 'this.figureInfos')
    })
      .catch((err) => {
        LogUtil.logOnAll("FaceManager", "get allFigure failed" + err);
        Toast.fail("c_get_fail", err);
        this.props.navigation.goBack()
        // console.log(this.tag, "getAllFigure failed", aErr);
      });
    // 当前选中的是哪个日期
    // this.updateSdcardFiles();
  }
  getAllFaceurl(figureInfos) {
    let lastNotifyTime = Date.now();
    for (let i = 0; i < figureInfos.length - 1; i++) {
      Util.getFaceImgUrl(figureInfos[i].faceId).then((res) => {
        let path = res;
        figureInfos[i].faceUrl = path;
        this.setState({
          figureInfos: figureInfos
        });

      }).catch((err) => {
        console.log('err', err);
      });
      if (Date.now() - lastNotifyTime < 1000) {//距离上次全局刷新，不超过1s,return
        continue;
      }
      lastNotifyTime = Date.now();
    }
    this.setState({
      figureInfos: figureInfos
    });
  }
  getAllUnFaceurl(unmarkFacesImgList) {
    let lastNotifyTime = Date.now();
    for (let i = 0; i < unmarkFacesImgList.length; i++) {
      Util.getFaceImgUrl(unmarkFacesImgList[i].faceId).then((res) => {
        let path = res;
        unmarkFacesImgList[i].faceUrl = path;
        this.setState({
          unmarkFacesImgList: unmarkFacesImgList
        });

      }).catch((err) => {
        console.log('err', err);
      });
      if (Date.now() - lastNotifyTime < 1000) {//距离上次全局刷新，不超过1s,return
        continue;
      }
      lastNotifyTime = Date.now();
    }
    this.setState({
      unmarkFacesImgList: unmarkFacesImgList
    });
  }
  componentWillUnmount() {
    this.didFocusListener.remove();
    this.didBlurListener.remove();
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  render() {
    // 去使用引导
    let ToUse = <View style={{ position: 'absolute', height: 48, width: '100%', flexDirection: 'row', bottom: 20, justifyContent: 'center', alignItems: 'center' }}
    >
      <TouchableOpacity
        style={{ width: 306, height: 48, backgroundColor: '#32BAC0', alignItems: 'center', borderRadius: 10 }}
        onPress={() => {
        // 点击过 去使用，showNoOpenFaceManager为false，就会获得数据，渲染人脸管理界面
          this.setState({ showNoOpenFaceManager: false }, () => {
            
            // 点击 去使用 之后，要判断有无后台人脸，没有的时候才会跳出InformNoFace界面
            Util.getFaceAllFigure().then((res) => {
              console.log('res是', res);
              if (res.length === 0) {
                this.props.navigation.navigate('InformNoFace');
              }
            });
            this._onGetData();
        
            StorageKeys.IS_AI_FACE_OPEN = true;
          }); 
        }
        }
      >
        <Text style={{ fontSize: 16, textAlign: 'center', color: 'white', lineHeight: 48 }}>
          {LocalizedStrings["face_manager_to_user"]}
          {/* mAllFigureInf:{result} */}
        </Text>
      </TouchableOpacity>
    </View>;

    // 去购买引导
    let ToBuy = (<View style={{ height: 46, width: '100%', flexDirection: 'row', marginBottom: 20, marginTop: 20, justifyContent: 'center', alignItems: 'flex-end' }}
    >

      <Text style={{ fontSize: 13, fontWeight: 'bold', textAlign: 'center', color: '#999999' }}>
        {LocalizedStrings["face_need_open_cloud"]}
        <Text style={{ fontSize: 13, fontWeight: 'bold', textAlign: 'center', color: '#32BAC0', textDecorationLine: 'underline', textDecorationColor: '#32BAC0' }}
          onPress={() => {
            console.log("点击了去购买");
            Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: "face_guide" });
          }}
        >
          {LocalizedStrings["c_cloudvip_buy"]}
        </Text>
      </Text>
    </View>);

    const { navigation } = this.props;
    const isVip = navigation.state.params && navigation.state.params.isVip;

    // 引导页公共部分
    let FaceViewCloseView = <View style={styles.container}>
      <ScrollView >
        <View style={{ paddingBottom: 90 }}>
          <View style={{ flexDirection: 'column', alignItems: "center" }}>
            <Image style={{ width: 174, height: 174, marginTop: 20 }} source={require('../../Resources/Images/icon_face_manager_first_larg.jpg')}>

            </Image>
            <View>
              <Text style={{ fontSize: 12, textAlign: 'center', marginHorizontal: 40, marginTop: 20 }}>
                {LocalizedStrings["face_manager_first_tips"]}
              </Text>
            </View>

          </View>
          <View style={{
            height: 0.5,
            marginHorizontal: 24,
            backgroundColor: "dark" == DarkMode.getColorScheme() ? "xm#ffffff15" : "xm#e5e5e5",
            marginBottom: 10,
            marginTop: 20,
          }}>

          </View>
        
          <View >
            <View style={{ flexDirection: 'row', alignItems: "flex-start", marginLeft: 20, marginVertical: 10 }} >
              <Image
                source={require('../../Resources/Images/icon_face_manager_first.png')}
                style={{ width: 25, height: 25, tintColor: "dark" == DarkMode.getColorScheme() ?'#DDDDDDFF':""}}
              >

              </Image>
              <View style={{ width: kWindowWidth - 65 }}>
                <Text style={{ fontSize: 15, textAlign: 'left', marginLeft: 10 }}>
                  {LocalizedStrings["face_manager_first_tips1"]}
                </Text>
              </View>


            </View>
            <View style={{ flexDirection: 'row', alignItems: "center", marginLeft: 20, marginVertical: 10 }} >
              <Image
                source={require('../../Resources/Images/icon_face_manager_first_mark.png')}
                style={{ width: 25, height: 25, tintColor: "dark" == DarkMode.getColorScheme() ? '#DDDDDDFF' : "" }}
              >

              </Image>
              <View style={{ width: kWindowWidth - 65 }}>
                <Text style={{ fontSize: 15, textAlign: 'left', marginLeft: 10 }}>
                  {LocalizedStrings["face_manager_first_tips2"]}
                </Text>
              </View>
            </View>
            <View style={{ flexDirection: 'row', alignItems: "center", marginLeft: 20, marginVertical: 10 }} >
              <Image
                source={require('../../Resources/Images/icon_face_manager_first_share.png')}
                style={{ width: 25, height: 25, tintColor: "dark" == DarkMode.getColorScheme() ? '#DDDDDDFF' : ""}}
              >

              </Image>
              <View style={{ width: kWindowWidth - 65 }}>
                <Text style={{ fontSize: 15, textAlign: 'left', marginLeft: 10 }}>
                  {LocalizedStrings["face_manager_first_tips3"]}
                </Text>
              </View>
            </View>
            <View style={{
              height: 0.5,
              marginHorizontal: 24,
              backgroundColor: "dark" == DarkMode.getColorScheme() ? "xm#ffffff15" : "xm#e5e5e5",
              marginBottom: 10,
              marginTop: 20,
            }}>

            </View>
            <View style={{ marginHorizontal: 24 }}>
              <Text style={{ fontSize: 11, color: '#999999', lineHeight: 18 }}>
                {LocalizedStrings["low_power_agreement"]}
              </Text>
            </View>




          </View>
        </View>
      </ScrollView>
      {isVip ? ToUse : ToBuy}
      
    </View>;

    // 不是vip或者是第一次进入
    if (this.state.showNoOpenFaceManager || !isVip) {
      return (FaceViewCloseView);
    } else {
      return (
      // 这里是getlocalstorage

        <View style={styles.container}>
          {this._renderFaceView()}
          {/* {this._renderHeader()} 相册不需要day view */}
          {/* {this._renderDayFiles()} */}

          {this._renderBottomSelectView()}
          {/* {this._renderLoadingView()} */}
          {this._renderDialog()}
          {this._renderChoiceAddMarkedFaceWayDialog()}
          {this._renderCommentDlg()}
          {this._renderTipsDialogView()}
          {this._renderPermissionDialog()}
        </View>
      );
    }

  }

  _renderFaceView() {
    let figureInfosLength = 0
    if (this.state.figureInfos) {
      if (this.state.figureInfos.length > 0) {
        figureInfosLength = this.state.figureInfos.length - 1
      }
    }
    if (!this.state.showLoading) {
      return (
        <View style={{ width: '100%', flex: 1 }}>
          <View style={{ display: 'flex', flexDirection: "row", justifyContent: 'space-between', alignItems: "center", marginHorizontal: 26, marginBottom: 10 }}>
            <View>
              <Text style={{ fontSize: 12, color: '#8C93B0' }}>
                {LocalizedStrings["face_unmarked"]} 
              </Text>
            </View>
            {this.state.unmarkFacesImgList.length > 0 ?
              <TouchableOpacity
                style={{ flexDirection: 'row', justifyContent: 'flex-end', alignItems: "center" }}
                onPress={() => {
                  this.props.navigation.navigate('FaceUnmarkedList',
                    {
                      callback: (data) => {
                        if (data.data == 'mark') {
                          this._onGetData()
                        }
                      }
                    }
                  )
                }}
              >
                <Text Text style={{ fontSize: 12, color: "dark" == DarkMode.getColorScheme() ? "xm#fff" : '#666666' }}>
                  {LocalizedStrings["face_unmarked_to_more"]}
                </Text>
                <Image
                  style={{ width: 20, height: 40 }}
                  source={require('../../Resources/Images/button_next_nor.png')}
                />
              </TouchableOpacity>
              : null
            }

          </View>
          {/* <ListItem
                        title={LocalizedStrings["face_unmarked"]}
                        value={LocalizedStrings["face_unmarked_to_more"]}
                        showSeparator={false}
                        dotStyle={{ marginTop: 8 }}
                        titleStyle={{ fontSize: 12, paddingTop: 20, marginBottom: 10, color: '#8C93B0', }}
                        valueStyle={{ fontSize: 12, color: '#666666', marginRight: -2 }}
                        onPress={() => {
                            this.props.navigation.navigate('FaceUnmarkedList', { markedFaceInfosList: this.state.coverFaceInfosList })

                        }}
                        useNewType={false}
                    /> */}
          {
            this.state.unmarkFacesImgList.length > 0 ?
              this._renderUnmarkFacesListView()
              :
              <View style={{ marginBottom: 20, alignItems: "center", fontSize: 14, color: "rgba(255,255,255,0.4)" }}>
                <Text>
                  {LocalizedStrings["face_no_unmarked"]}
                </Text>
              </View>

          }
          <Text style={{ fontSize: 12, marginLeft: 25, color: '#8C93B0' }}>
            {LocalizedStrings["face_marked"].replace("%d", figureInfosLength)}

          </Text>
          {this._renderMarkFacesListView()}
        </View>
      )
    }


  }
  _renderUnmarkFacesListView() {
    return (
      <View>
        <FlatList
          contentContainerStyle={{
            display: 'flex',
            flexDirection: "row",
            justifyContent: 'flex-start',
            flexWrap: 'wrap'
          }}
          data={this.state.unmarkFacesImgList}
          renderItem={({ item, index }) => this._renderUnmarkFacesView(item, index)}
          numColumns={3}
          keyExtractor={(item, index) => index}
          ListFooterComponent={<View style={{ height: 20 }}></View>}
        //设置一个尾部组件 就是最下面的那个留个空白
        />
        <View style={styles.whiteblank} />
      </View>
    )
  }
  _renderUnmarkFacesView(item, index) {
    let path = item.faceUrl;
    let containerWidth = Dimensions.get("window").width / 3;

    return (
      <View>
        <TouchableOpacity
          style={{ width: containerWidth, height: containerWidth, paddingBottom: 10, alignItems: "center", justifyContent: "center" }}
          onPress={() => this._onPressUnmarkFacesView(item)}
        >
          {/* 这个小图片是选中的时候*/}
          <Image style={{ width: 64, height: 64, borderRadius: 32, backgroundColor: item.faceUrl == undefined ? DarkMode.getColorScheme() == 'dark' ? 'xm#000' : "#fff" : "#EEEEEE", }}
            source={path}
          />
        </TouchableOpacity>
      </View>
    )


  }
  _renderMarkFacesListView() {
    // console.log(this.state.figureInfos.length,'this.state.figureInfos.length')
    return (
      <View style={{ flex: 1 }}>
        <FlatList
          data={this.state.figureInfos}
          renderItem={({ item, index }) => this._renderMarkFacesView(item, index)}
          numColumns={3}
          keyExtractor={(item, index) => index}
          ListFooterComponent={<View style={{ height: 20 }}></View>}
          //设置一个尾部组件 就是最下面的那个留个空白

        />
      </View>
    )


  }
  _renderMarkFacesView(item, index) {
    let path = null;
    path = item.faceUrl;
    let containerWidth = Dimensions.get("window").width / 3
    //这里弄个列表 最后一个是添加  然后限制长度是11 如果大于11就弹窗
    return (
      <View style={{ marginTop: 15 }}>
        <TouchableOpacity
          style={{ width: containerWidth, paddingBottom: 5, alignItems: "center", justifyContent: "center", marginBottom: 5 }}
          onPress={() => this._onPressFigureInfo(item, index)}
          key={index}
          onLongPress={
            () => {
              this._onPressFigureInfo(item, index, true)
            }
          }
        >
          {/* 这个小图片是选中的时候*/}
          <Image style={{ width: 64, height: 64, borderRadius: 32, backgroundColor: "#EEEEEE" }}
            source={path ? path : this.state.figureInfosLength == index ? require('../../Resources/Images/home_icon_add2_pres.png') : ''}
          />
          <Text
            style={{ fontSize: 12, color: '#000000', marginTop: 10 }}
          >
            {item.name ? item.name : LocalizedStrings["add"]}
          </Text>
          {

            this.state.isSelectMode && index !== this.state.figureInfos.length - 1 ?
              <Image
                style={{ width: 20, height: 20, position: "absolute", top: 44, right: (containerWidth - 64) / 2 }}
                source={item.isSelected ? require("../../Resources/Images/icon_selected.png") : require("../../Resources/Images/icon_unselected.png")}
              /> :
              null
          }
        </TouchableOpacity>
      </View>
    )
  }
  _onPressFigureInfo(item, index, isLongPress) {
    console.log(index, 'index')
    if (isLongPress) {
      this.setState({
        isSelectMode: true
      })
    }
    if (index == 10) {
      Toast.fail("figure_max_tips", '', true);
      this.setNavigation(false, false, false);
      this.setState({ isSelectMode: false, });
      return
    }


    if (!this.state.isSelectMode) {
      if (index == this.state.figureInfos.length - 1) {
        this.setNavigation(false, false, false);
        this.setState({ addMarkedFaceDialogVisible: true });
      }
      //要带着
      else {
        this.props.navigation.navigate("FacesDetailManager",
          {
            figureInfo: this.state.figureInfos[index],
            callback: (data) => {
              if (data.data == 'deleteAll') {
                this._onGetData()
              }

            }
          }
        )
      }
    }
    else {
      if (index == this.state.figureInfos.length - 1) {
        this.onSelectAllChanged(false);// 将选择所有重置
        this.setNavigation(false, false, false,);
        this.setState({
          isSelectMode: false,
          addMarkedFaceDialogVisible: true
        })
        return

      }
      let figureInfos = this.state.figureInfos[index];
      figureInfos.isSelected = !figureInfos.isSelected;
      let selectedCount = 0;
      for (let file of this.state.figureInfos) {
        if (file.isSelected) {
          selectedCount++;
        }
      }
      // 在这里重新设置标题栏 
      this.setNavigation(false, true, false, LocalizedStrings["selected_count"].replace("%1$d", selectedCount))
      if (selectedCount == 0) {
        this.onSelectAllChanged(false);
      } else if (selectedCount == this.state.figureInfos.length-1) {
        this.onSelectAllChanged(true);
      } else {
        this.setState({ figureInfos: this.state.figureInfos });// 刷新页面 状态不要保留在ui控件里
      }
    }


  }
  _onPressUnmarkFacesView(item) {
    if (item.faceUrl !== undefined) {
      this._showCommentdlg(item)
    }

  }
  _showCommentdlg(item) {
    this.setState({
      commentDlg: true,
      unMarkfaceUrl: item.faceUrl.uri,
      unMarkfaceId: item.fInf.faceId
    })

  }
  _renderCommentDlg() {

    let obj = {}
    obj.uri = this.state.unMarkfaceUrl
    return (
      <InputDlgEx
        title={LocalizedStrings["cloud_comment_dlg_title"]}
        visible={this.state.commentDlg}
        icon={obj}
        listData={this.state.mAllFigureInf}
        onPressed={(aDat) => {
          this.setState({ defVal: aDat.name, isNameError: false });
        }}
        onDismiss={(_) => {
          this.renameItem = null;
          this.setState({ commentDlg: false, isRename: false, defVal: '', commentErr: null, isNameError: false });
        }}
        inputWarnText={this.state.commentErr}
        inputs={[{
          onChangeText: (result) => {
            let isEmoji = Util.containsEmoji(result);
            let length = result.length;
            // let isCommon = this.isTextcommon(result);
            if (isEmoji) {
              this.setState({ isNameError: true, commentErr: LocalizedStrings["Special_symbol_input_not_supported_temporarily"] });
            } else if (length > 8) {
              this.setState({ isNameError: true, commentErr: LocalizedStrings["input_name_too_long3"] });
            } else if (length <= 0 || result.trim().length == 0) {
              this.setState({ isNameError: true, commentErr: LocalizedStrings["add_feature_empty_tips"] });
            } else {
              this.setState({ isNameError: false, commentErr: "error" });
            }
          },
          textInputProps: {
            // maxLength: 8,
            // returnKeyType: "done",
            autoFocus: Util.isHeightPt() ? true : false
          },
          defaultValue: this.state.defVal ? this.state.defVal : '',
          type: 'DELETE',
          isCorrect: !this.state.isNameError
        }]}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: (result) => {
              this.renameItem = null;
              this.setState({ commentDlg: false, isRename: false, defVal: '', commentErr: null, isNameError: false });
            }
            // ignore
          },
          {
            text: LocalizedStrings["csps_right"],
            callback: (result) => {
              if (this.state.isNameError) {
                console.log("name input is not good!")
                return;
              }
              this.setState({ isRename: false, defVal: '', commentErr: null });
              // console.log(this.state.figureInfos.length, 'this.state.figureInfos')
              let text = result.textInputArray[0].trim();

              if (text.length > 0 && !this.containsEmoji(text)) {
                let cmd = null;
                //这个人有名字？

                cmd = Util.commentFace(text, this.state.unMarkfaceId);

                if (cmd) {
                  cmd.then((aRet) => {

                    this._onGetData();

                    Toast.success('save_success');
                    this.setState({
                      commentDlg: false,
                      defVal: ''
                    });
                  })
                    .catch((aErr) => {
                      LogUtil.logOnAll("FaceManager", "commentFace failed" + JSON.stringify(aErr));

                      this.setState({ commentDlg: false });
                      let errCode = aErr.code;
                      // 400302 人物上限
                      let errMap = { 400302: "figure_max_tips", 400305: "face_max_tips" };
                      let err = errMap[errCode] || "action_fail";
                      Toast.fail(err, err, true);
                      console.log(this.tag, "comment failed", aErr);
                    });
                } else {
                  console.log(this.tag, "nothing changed");
                }

              } else {
                if (this.containsEmoji(text)) {
                  this.setState({ commentErr: LocalizedStrings["no_emoij_tips"] });
                }
                else {
                  this.setState({ commentErr: LocalizedStrings["cloud_comment_null"] });
                }
              }
            }
          }
        ]}
        noInputDisButton={true}
      />);
  }
  //添加
  _renderChoiceAddMarkedFaceWayDialog() {
    return (
      <ChoiceDialog
        style={{ width: 100 }}
        dialogStyle={{ itemTitleNumberOfLines: 3 }}
        useNewType={false}
        visible={this.state.addMarkedFaceDialogVisible}
        title={LocalizedStrings["select_dialog_title"]}
        options={[
          { title: LocalizedStrings["select_dialog_camera"] },
          { title: LocalizedStrings["select_dialog_album"] },
          { title: LocalizedStrings["action_cancle"] }
        ]}
        selectedIndexArray={[0]}
        onDismiss={(_) => this.setState({ addMarkedFaceDialogVisible: false })}
        onSelect={(result) => {
          this.selectedIndexArray = result;
          this._choiceAddMarkedFaceWay(result);
          this.setState({ addMarkedFaceDialogVisible: false });
        }}
        buttons={[
        ]}
      />
    );
  }
  _choiceAddMarkedFaceWay(result) {
    if (result ==0) {
      TrackUtil.reportClickEvent("Face_CameraInput_ClickNum");
    } else if (result == 1) {
      TrackUtil.reportClickEvent("Face_GalleryInput_ClickNum");
    }
    if (result == 0) {
      // this._startSelectPhoto(result);
      PermissionUtil.checkCameraPermission().then((res) => {
        console.log(res);
        this.props.navigation.navigate('FaceCamera'
          , {
            callback: (data) => {
              if (data) {
                this.setState({
                  commentDlg: true,
                  unMarkfaceUrl: data.faceUrl.uri,
                  unMarkfaceId: data.faceId
                });
              }

            }
          }
        ); 
      }).catch((err) => {
        // 没有相机存储权限
        this.isCheckingPermission = false;
        if (err == PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
          this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
        }
      });
    }
    else if (result == 1) {
      StorageKeys.IS_AI_FACE_OPEN_TOAST.
        then((res) => {
          this.setState({
            isAIFrame: res
          });
          console.log('不是为第一次进入res', res);
          if (res) {
            this.setState({
              showTips: false
            });
            PermissionUtil.checkCameraPermission().then((res) => {
              PermissionUtil.checkStoragePermission().then((res) => {
                console.log(res);
                this.selectPhotoTapped();
              }).catch((err) => {
              // 没有读写手机存储权限
                if (err == PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
                  this.setState({ showPermissionDialog: true, permissionRequestState: 1 });
                }
              });
            }
            ).catch((err) => {
            // 没有相机权限
              if (err == PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
                this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
              }
            })
          }
          else {
            this.setState({
              showTips: true
            })
          }
        })
        .catch((err) => {
          console.log('errr', err)
          this.setState({
            showTips: true
          })
        });
    }
    else {
      this.setState({
        addMarkedFaceDialogVisible: false
      });
    }
    // 0 是拍照录入 1 是从手机相册选择  2 是取消 取消这个应该不用弄
  }

  selectPhotoTapped() {
    const options = {
      quality: 1.0,
      maxWidth: 500,
      maxHeight: 500,
      storageOptions: {
        skipBackup: true
      }
    };
    setTimeout(() => {
      ImagePicker.launchImageLibrary(options, (response) => {
        console.log('response',response)
        // this.setState({
        //     showLoading: true
        // })
        if (response.didCancel) {
          console.log('User cancelled photo picker');
          // Toast.fail("bind_error");
          return;
        } else if (response.error) {
          console.log('ImagePicker Error: ', response.error);
          Toast.fail("bind_error");
          return;
        } else if (response.customButton) {
          console.log('User tapped custom button: ', response.customButton);
          Toast.fail("bind_error");
          return;
        } else {
          Toast.loading('c_setting');
          let path = response.uri.slice(7)
          console.log(path,"path");
          Service.miotcamera.uploadImageToCameraServer(path).then((result) => {
            LogUtil.logOnAll("FaceManager", "uploadImageToCameraServer success" + JSON.stringify(result));

            let res = JSON.parse(result)
            let data = res.data;
            if (res.result == "ok" && data != null && data.faceInfoMetas[0] != null) {

              this.setState(
                {
                  commentDlg: true,
                  unMarkfaceId: data.faceInfoMetas[0].faceId,
                  unMarkfaceUrl: response.uri
                }
              );
            } else {
              Toast.fail("face_recognition_fail_tips")
              //"face_recognition_fail_tips": "识别失败\n请选择清晰的人脸照片",

            }
          }).catch((err) => {
            LogUtil.logOnAll("FaceManager", "uploadImageToCameraServer failed" + JSON.stringify(err));

            let errCode = err.code;
            // 501103 系统错误
            let errMap = { 501103: "cloud_figure_limit", 400305: "cloud_face_limit" };
            // let err = errMap[errCode] || "action_fail";
            Toast.fail('face_recognition_fail_tips', err);

          });
          // You can also display the image using data:
          // let source = { uri: 'data:image/jpeg;base64,' + response.data };
        }

      })
    }, 100)


  }

  // 删除人脸时显示的对话框
  _renderDialog() {
    let title = LocalizedStrings["face_delete_dialog_title"]
    let message = LocalizedStrings["face_delete_dialog_message"]
    let btn = LocalizedStrings["csps_right"]
    return (
      <MessageDialog
        cancelable={true}
        visible={this.state.dialogVisible} 
        title={title}
        message={message}
        messageStyle={{
          fontSize: 14,
          textAlign: "center"
        }}
        onDismiss={() => this.setState({ dialogVisible: false })}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ dialogVisible: false });
            }
          },
          {
            text: btn,
            callback: () => {
              this.onConfirmDelete();
              this.setState({ dialogVisible: false });
            }
          }
        ]}/>

    );
  }

  _renderLoadingView() {
    let isDark = DarkMode.getColorScheme() == "dark";
    if (this.state.showLoading) {
      return (
        <View

          style={{ position: "absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center", }}
        >
          <ActivityIndicator
            style={{ width: 54, height: 54 }}
            color={isDark ? "xm#ffffff" : "#000000"}
            size={"large"}

          />
          <Text
            style={{ marginTop: 10, fontSize: 12, color: isDark ? "xm#ffffff" : "#000000" }}>
            {LocalizedStrings["camera_loading"]}
          </Text>
        </View>
      );
    }


  }
  //这里是在相册页删除的时候
  _renderBottomSelectView() {
    //这里是没有那个选择按钮的
    if (!this.state.isSelectMode) {

      return;
    }

    return (
      <View style={{ width: "100%", height: 69, bottom: 10, borderBottomColor: '#ffffff', borderTopColor: "#e5e5e5", borderTopWidth: 1, borderBottomWidth: 1, display: "flex", flexDirection: "row", justifyContent: "center", alignItems: "center", backgroundColor: DarkMode.getColorScheme() == 'dark' ? 'xm#000' : '#fff' }}>

        <TouchableOpacity
          style={{ width: 50, display: "flex", flex: 1, alignItems: "center", }}
          onPress={() => { this.onPressDelete(); }}
        >
          <Image
            style={{ width: 35, height: 35 }}
            source={Util.isDark() ? require("../../resources2/images/icon_videorecord_delete_w.png") : require("../../resources2/images/icon_videorecord_delete_b.png")}
          />
          <Text
            style={{ color: "#000000", fontSize: 11 }}
          >
            {LocalizedStrings["delete_files"]}
          </Text>
        </TouchableOpacity>
      </View>
    );
  }
  _renderTipsDialogView() {

    return (
      <AbstractDialog
        visible={this.state.showTips}
        useNewTheme
        onDismiss={() => { this.setState({ showTips: false }) }}
        buttons={[
          {
            text: LocalizedStrings["csps_right"],
            style: { color: '#f0ac3d' },
            callback: (_) => {
              // this.setState({ showTips: false, addMarkedFaceDialogVisible:true });
              this.setState({
                showTips: false,
              }, () => {
                this.selectPhotoTapped()

              });
              StorageKeys.IS_AI_FACE_OPEN_TOAST = true

            }
          }
        ]}
      >
        <View
          style={{
            flex: 1,
            flexDirection: "column",
            // height: 200,
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <View>
            <Image style={{ width: 280, height: 200 }} source={require('../../Resources/Images/photo_placeholder.webp')}>
            </Image>
          </View>
          <View style={{ marginVertical: 5, textAlign: 'center', marginHorizontal: (Dimensions.get("window").width - 280) / 2 }}>
            <Text style={{ fontSize: 12, color: '#000000' }}>
              {LocalizedStrings["pick_album_tips"]}
            </Text>
          </View>

        </View>
      </AbstractDialog>
    )
  }
  // android返回键处理
  onBackHandler = () => {
    if (!this.isPageForeGround) {
      return false;
    }
    if (this.state.isSelectMode) {
      this.onSelectAllChanged(false);
      this.setNavigation(false, false, false);
      this.setState({ isSelectMode: false });
      return true;
    } else {
      return false;// 不接管
    }
  }

  //选择了所有
  onSelectAllChanged(isSelectAll) {
    this.setNavigation(isSelectAll, this.state.isSelectMode, this.state.isCurrentDayEmpty);
    this.setState({ index: isSelectAll ? 0 : 1 });
    if (this.state.figureInfos == null || this.state.figureInfos.length <= 0) return;
    for (let timeHourItem of this.state.figureInfos) {
      timeHourItem.faceUrl ? timeHourItem.isSelected = isSelectAll ? true : false : timeHourItem.isSelected = false

    }
    this.setNavigation(isSelectAll, this.state.isSelectMode, this.state.isCurrentDayEmpty);
    this.setState({ figureInfos: this.state.figureInfos, isSelectAll: isSelectAll });
  }


  onPressDelete() {
    //这里获取name根据name获取figureid 然后传给下面

    this.isallDelete = false
    let ids = [];
    for (let figureInfo of this.state.figureInfos) { // 遍历所有的正在展示的内容
      if (figureInfo.isSelected) {
        Util.getExistFigure(figureInfo.name).then((res) => {

          // console.log(res, 'resssss')
          figureInfo.figureId = res;//好奇怪的这里

        }).catch((err) => {
          LogUtil.logOnAll("FaceManager", "getExistFigure failed" + JSON.stringify(err));
          Toast.fail("delete_failed", err);
        })
        ids.push(figureInfo.figureId);
      }
    }
    if (ids.length == 0) {
      Toast._showToast(LocalizedStrings["bottom_action_tip"]);
      return;
    }
    this.setState({ dialogVisible: true });
  }

  onConfirmDelete() {
    //这里判断是删除一张还是所有 所有的话还要把这个人物删除
    let ids = [];
    for (let figureInfo of this.state.figureInfos) { // 遍历所有的正在展示的内容
      if (figureInfo.isSelected) {
        ids.push(figureInfo.figureId)
      }
    }

    Util.delFigures(ids)
      .then((res) => {
        // console.log('删除了之后的结果', res)
        if ("ok" == res.result.toLowerCase()) {
          this.setState((state) => {
            return {
              isSelectMode: false,
              isSelectAll: false
            };
          }, () => {
            //删除了所有之后返回

            this._onGetData();
          });
        }

        Toast.success("delete_success");
      })
      .catch((err) => {
        LogUtil.logOnAll("FaceManager", "delFigures failed" + JSON.stringify(err));

        Toast.fail("delete_failed", err);
      });

  }
  isEmojiCharacterV2(codePoint) {
    return !((codePoint == 0x0) ||
      (codePoint == 0x9) ||
      (codePoint == 0xA) ||
      (codePoint == 0xD) ||
      ((codePoint >= 0x20) && (codePoint <= 0xD7FF)) ||
      ((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) ||
      ((codePoint >= 0x10000))) ||
      (codePoint == 0xa9 || codePoint == 0xae || codePoint == 0x2122 ||
        codePoint == 0x3030 || (codePoint >= 0x25b6 && codePoint <= 0x27bf) ||
        codePoint == 0x2328 || (codePoint >= 0x23e9 && codePoint <= 0x23fa))
      || ((codePoint >= 0x1F000 && codePoint <= 0x1FFFF))
      || ((codePoint >= 0x2702) && (codePoint <= 0x27B0))
      || ((codePoint >= 0x1F601) && (codePoint <= 0x1F64F));
  }
  containsEmoji(str) {
    let length = str.length;
    for (let i = 0; i < length; ++i) {
      let c = str.charCodeAt(i);
      if (this.isEmojiCharacterV2(c)) {
        return true;
      }
    }
    return false;
  }
  //权限框
  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    let message = null;
    if (this.state.permissionRequestState == 1) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);
    } else {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_camera"]);
    }
    return (
    // <AbstractDialog

      <MessageDialog
        title={LocalizedStrings["tips"]}
        message={message}
        messageStyle={{
          fontSize: 14,
          textAlign:'center'
        }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ showPermissionDialog: false });
            }
          },
          {
            text: LocalizedStrings["setting"],
            callback: () => {
              Host.ui.openTerminalDeviceSettingPage(1);
              this.setState({ showPermissionDialog: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog} />
    );

  }


}

export const styles = StyleSheet.create({
  container: {
    backgroundColor: DarkMode.getColorScheme() == 'dark' ? 'xm#000' : "#fff",
    width: '100%',
    height: '100%',
    display: "flex",
    flexWrap: "nowrap"
  },
  whiteblank: {
    height: 0.5,
    marginHorizontal: 24,
    backgroundColor: DarkMode.getColorScheme() == 'dark' ? "xm#ffffff15" : "#e5e5e5",
    marginBottom: 30,
    marginTop: 20,
  }
});
