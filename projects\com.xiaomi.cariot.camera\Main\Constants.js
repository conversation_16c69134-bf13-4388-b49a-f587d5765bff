
import LocalizedStrings from '../CommonModules/LocalizedStrings';
import IntlMessageFormat from 'intl-messageformat';
import 'intl';
import 'intl/locale-data/jsonp/en.js';
import 'intl/locale-data/jsonp/zh-Hans.js';
import 'intl/locale-data/jsonp/zh-Hant.js';
import 'intl/locale-data/jsonp/ko-KR.js';
import { Device } from 'miot';
import StorageKeys from './StorageKeys';
const strings = {
  'en': {
    zb_1: "rnLabelBtnSleep",
    zb_1_1: "rnLabelBtnSleepClosed",
    zb_2: "rnLabelBtnAudio",
    zb_2_1: "rnLabelBtnAudioClosed",

    zb_3_4: "rnLabelBtnResolution",
    zb_4: "rnLabelBtnFull",
    zb_4_1: "rnLabelBtnFullClosed",
    zb_5: "rnLabelBtnCall",
    zb_5_1: "rnLabelBtnCallClosed",
    zb_6: "rnLabelBtnSnapshot",
    zb_6_1: "rnLabelBtnSnapshotClosed",
    zb_7: "rnLabelBtnRecord",
    zb_7_1: "rnLabelBtnRecordClosed",
    zb_8: "rnLabelBtnSmallDirection",
    zb_10: "rnLabelBtnLSBack",
    zb_11: "rnLabelBtnAudio",
    zb_12: "rnLabelBtnDirection",
    zb_12_1: "rnLabelBtnDirectionLeft",
    zb_12_2: "rnLabelBtnDirectionRight",
    zb_12_3: "rnLabelBtnDirectionUp",
    zb_12_4: "rnLabelBtnDirectionBottom",
    zb_14: "rnLabelBtnSnapshot",
    zb_15: "rnLabelBtnRecord",
    zb_16: "rnLabelBtnCall",
    zb_17: "rnLabelBtnThumbnail",
    zb_18: "rnLabelBtnThumbnailFullscreen",
    zb_19: "rnLabelBtnDirection",
    zb_20: "rnLabelBtnPanoramaEdit",
    zb_21: "rnLabelBtnPanoramaBack",
    zb_23: "rnLabelBtnPanoramaRefresh",
    zb_22: "rnLabelBtnPanoramaDisplay",
    zb_27: "rnLabelBtnTitle",
    zb_28: "rnLabelBtnRate",
    zb_29: "rnLabelBtnReturn",
    zb_30: "rnLabelBtnSetting",
    zb_31: "rnLabelBtnPlayBack",
    zb_32: "rnLabelBtnCloud",
    zb_34: "rnLabelBtnScene1",
    zb_35: "rnLabelBtnScene2",
    zb_36: "rnLabelBtnScene3",



    // cloud
    yc_1: "rnLabelBtnYcBack",
    yc_2: "rnLabelBtnYcDelete",
    yc_3: "rnLabelBtnYcDownload",
    yc_4: "rnLabelBtnYcScreenShot",
    yc_5: "rnLabelBtnYcPause",
    yc_6: "rnLabelBtnYcStartTime",
    yc_7: "rnLabelBtnYcProgressBar",
    yc_8: "rnLabelBtnYcEndTime",
    yc_9: "rnLabelBtnYcMute",
    yc_10: "rnLabelBtnYcSpeed",
    yc_11: "rnLabelBtnYcFull",
    yc_12: "rnLabelBtnYcFullBack",
    yc_13: "rnLabelBtnYcFullDelete",
    yc_14: "rnLabelBtnYcFullDownLoad",
    yc_15: "rnLabelBtnYcFullScreenShot",
    yc_16: "rnLabelBtnYcFullPause",
    yc_18: "rnLabelBtnYcFullProgressBar",
    yc_20: "rnLabelBtnYcFullMute",
    yc_21: "rnLabelBtnYcFullSpeed",
    yc_22: "rnLabelBtnYcFullFull",

    yc_23: "rnLabelBtnReturn",
    yc_25: "rnLabelTextCloud",
    yc_26: "rnLabelBtnByCloud",



    // sd卡
    sd_1: "rnLabelBtnSdBack",
    sd_3: "rnLabelBtnSdEdit",
    sd_6: "rnLabelBtnSdProgressBar",
    sd_9: "rnLabelBtnSdScreenShot",
    sd_10: "rnLabelBtnSdSpeed",
    sd_11: "rnLabelBtnSdFull",
    sd_12: "rnLabelBtnSdFullBack",
    sd_13: "rnLabelBtnSdFullTitle",
    sd_16: "rnLabelBtnSdFullProgressBar",
    sd_19: "rnLabelBtnSdFullScreenShot",
    sd_20: "rnLabelBtnSdFullSpeed",
    sd_21: "rnLabelBtnSdFullFull",
    // LOCAL
    lc_1: "rnLabelBtnLcPause",
    lc_2: "rnLabelBtnLcStartTime",
    lc_3: "rnLabelBtnLcProgressBar",
    lc_4: "rnLabelBtnLcEndTime",
    lc_5: "rnLabelBtnLcMute",
    lc_6: "rnLabelBtnLcFull",
    lc_7: "rnLabelBtnLcFullPause",
    lc_9: "rnLabelBtnLcFullProgressBar",
    lc_11: "rnLabelBtnLcFullMute",
    lc_12: "rnLabelBtnLcFullFull",
    lc_13: "rnLabelBtnLcFullShare",
    lc_14: "rnLabelBtnLcFullDelete",

    lc_15: "rnLabelBtnLcSpeed",
    lc_16: "rnLabelBtnLcSpeedFull",

    // other
    yc_date: "rnLabelBtnYcDate",
    yc_edit: "rnLabelBtnYcEdit",
    lc_date: "rnLabelBtnLcDate",
    lc_edit: "rnLabelBtnLcEdit",
    sd_date: "rnLabelBtnLcDate",
    sd_edit: "rnLabelBtnLcEdit",
    // lookback cloud
    rp_1: "rnLabelBtnRpSnapshot",
    rp_1_1: "rnLabelBtnRpSnapshotClosed",
    rp_2: "rnLabelBtnRpSpeed",
    rp_2_1: "rnLabelBtnRpSpeedClosed",
    rp_3: "rnLabelBtnRpAudio",
    rp_4: "rnLabelBtnRpFullscreen",
    rp_5: "rnLabelBtnRpPlay",
    rp_6: "rnLabelBtnRpDateleft",
    rp_7: "rnLabelBtnRpDateright",
    rp_8: "rnLabelBtnRpTimeleft",
    rp_9: "rnLabelBtnRpTimeright",
    rp_22: "rnLabelBtnRpFullBack",
    rp_23: "rnLabelBtnRpFullPlay",
    rp_24: "rnLabelBtnRpFullAudio",
    rp_24_1: "rnLabelBtnRpFullAudioClosed",
    rp_25: "rnLabelBtnRpFullSpeed",
    rp_25_1: "rnLabelBtnRpFullSpeedClosed",
    rp_26: "rnLabelBtnRpFullSnapshot",
    rp_27: "rnLabelBtnRpFullTimeleft",
    rp_29: "rnLabelBtnRpFullTimeright",
    // sd
    rp_11: "rnLabelBtnRsdSnapshot",
    rp_12: "rnLabelBtnRsdRecord",
    rp_12_1: "rnLabelBtnRsdRecordClosed",
    rp_13: "rnLabelBtnRsdSpeed",
    rp_14: "rnLabelBtnRsdPlay",
    rp_15: "rnLabelBtnRsdAudio",
    rp_15_1: "rnLabelBtnRsdAudioClosed",
    rp_16: "rnLabelBtnRsdFullscreen",
    rp_17: "rnLabelBtnRsdDateleft",
    rp_18: "rnLabelBtnRsdDateright",
    rp_19: "rnLabelBtnRsdTimeleft",
    rp_20: "rnLabelBtnRsdTimeroller",
    rp_21: "rnLabelBtnRsdTimeright",
    rp_30: "rnLabelBtnRsdFullBack",
    rp_31: "rnLabelBtnRsdFullPlay",
    rp_32: "rnLabelBtnRsdFullAudio",
    rp_32_1: "rnLabelBtnRsdFullAudioClosed",
    rp_34: "rnLabelBtnRsdFullSnapshot",
    rp_35: "rnLabelBtnRsdFullRecord",
    rp_35_1: "rnLabelBtnRsdFullRecordClosed",
    rp_36: "rnLabelBtnRsdFullTimeleft",
    rp_38: "rnLabelBtnRpFullTimeright",
    rp_40:
      "rnLabelCloudSave",
    rp_42:
      "rnLabelThumbnail",
    rp_44:
      "rnLabelBtnAllVideo",
    rp_46:
      "rnLabelFullThumbnail",

    rp_50:
      "rnLabelTxtSpeed",
    rp_52:
      "rnLabelSpeedx4",
    rp_54:
      "rnLabelTxtSpeed",
    rp_56: "rnLabelSpeedx4",

    "rp_58": "rnLabelBtnFocus",
    "rp_60": "rnLabelWeekView",
    "rp_62": "rnLabelFrameView",
    "rp_64": "rnLabelFullBackTime",
    "rp_59": "rnLabelBtnToday",
    "rp_61": "rnLabelBtnFullFocus",
    "rp_63": "rnLabelBackTime",


    rp_39:
      "rnLabelBtnReturn",

    rp_41:
      "rnLabelBtnSDSave",

    rp_43:
      "rnLabelRecordTime",

    rp_45:
      "rnLabelFullRecordTime",

    rp_47:

      "rnLabelObjectMove",

    rp_48:
      "rnLabelSomeoneMove",
    rp_49:
      "rnLabelBabyCry",




    rp_51:
      "rnLabelSpeedx1",

    rp_53:
      "rnLabelSpeedx16",

    rp_55:
      "rnLabelSpeedx1",

    rp_57:
      "rnLabelSpeedx16",

    zb_37: "rnLabelDirectionTip",
    zb_39: "rnLabelBtnFocus",
    zb_41:
      "rnLabelTxtDormancy",
    zb_43:
      "rnLabelImgAuto",
    zb_38:
      "rnLabelRecordTime",
    zb_42:
      "rnLabelBtnFullFocus",
    zb_44:
      "rnLabelFullDirectionTip",
    zb_46:
      "rnLabelImgQuility",
    zb_54: "rnLabelFrameView",
    zb_55: "rnLabelStorageManager",
    sz_1:
      "rnLabelBtnReturnVertical",
    sz_3:
      "rnLabelDeviceSettings",
    sz_5:
      "rnLabelHOmeMonitoring",
    sz_7:
      "rnLabelManageStorage",
    sz_9:
      "rnLabelCloudSetting",
    sz_11:
      "rnLabelDeviceName",
    sz_13:
      "rnLabelShareDevice",
    sz_15:
      "rnLabelFirmwareUpdate",
    sz_17:
      "rnLabelAddtionalSetting",
    sz_19:
      "rnLabelCameraShortcut",
    sz_11_1:
      "rnLabelCurrentName",
    sz_19_1:
      "rnLabelShortcutStates",
    sz_2:
      "rnLabeTxtlTitle",
    sz_4:
      "rnLabelCameraSetting",
    sz_6:
      "rnLabelAISetting",
    sz_8:
      "rnLabelAlbum",
    sz_10:
      "rnLabelGeneralSetting",
    sz_12:
      "rnLabeManagelLocation",
    sz_14:
      "rnLabelAutomation",
    sz_16:
      "rnLabelHelpFeedBack",
    sz_18:
      "rnLabelFavorites",
    sz_20:
      "rnLabelDeleteDevice",
    sz_18_1:
      "rnLabelFavoritesStates",
    sz_4_1:
      "rnLabelBtnReturn",
    sz_4_3:
      "rnLabelStatusLight",
    sz_4_5:
      "rnLabelDataUsageWarning",
    sz_4_7:
      "rnLabelDataUsageWarningDescribtion",
    sz_4_9:
      "rnLabelPTZGestureRotationSwitch",
    sz_4_11:
      "rnLabelSleepSettings",
    sz_4_13:
      "rnLabelNightvisionSetting",
    sz_4_15:
      "rnLabelMotionTracking",
    sz_4_17:
      "rnLabelMotionTrackingDescribtion",
    sz_4_19:
      "rnLabelRebootDevice",
    sz_4_2:
      "rnLabeTxtlCameraSettings",
    sz_4_4:
      "rnLabelDataUsageWarningSwitch",
    sz_4_6:
      "rnLabelNetSafeSwitch",
    sz_4_8:
      "rnLabelPTZGestureRotation",
    sz_4_10:
      "rnLabelPTZGestureRotationDescribtion",
    sz_4_12:
      "rnLabelImageSettings",
    sz_4_14:
      "rnLabelCameraCalibration",
    sz_4_16:
      "rnLabelMotionTrackingSwitch",
    sz_4_18:
      "rnLabelFloatWindow",
    sz_4_51:
      "rnLabelBtnReturn",
    sz_4_53:
      "rnLabelTimestamp",
    sz_4_55:
      "rnLabelLensDistortionCorrection",
    sz_4_57:
      "rnLabelWideDynamicRangeMode",
    sz_4_59:
      "rnLabelRotateImage",
    sz_4_57_1:
      "rnLabelBtnReturn",
    sz_4_59_1:
      "rnLabelWideDynamicRangeModeSetting",
    sz_4_61:
      "rnLabelWDRDescription",
    sz_4_63:
      "rnLabelImgWDR",
    sz_4_65:
      "rnLabelTxtWDR",
    sz_4_67:
      "rnLabelAttentionDescription",
    sz_4_69:
      "rnLabelTitleRotate",
    sz_4_71:
      "rnLabelImage",
    sz_4_73:
      "rnLabelBtnRatate",
    sz_4_52:
      "rnLabeTxtImageSettings",
    sz_4_54:
      "rnLabelTimestampSwitch",
    sz_4_56:
      "rnLabelLensDistortionCorrectionSwitch",
    sz_4_58:
      "rnLabelWideDynamicRangeModeSates",
    sz_4_58_1:
      "rnLabelTxtWDRMode ",
    sz_4_60:
      "rnLabelWideDynamicRangeModeSwitch",
    sz_4_62:
      "rnLabelImgNormalMode",
    sz_4_64:
      "rnLabelRxtNormalMode",
    sz_4_66:
      "rnLabelAttention",
    sz_4_68:
      "rnLabelBtnReturn",
    sz_4_70:
      "rnLabelBtnSaveChange",
    sz_4_72:
      "rnLabelImgRotate",
    sz_4_20:
      "rnLabelBtnReturn",
    sz_4_22:
      "rnLabelSleep",
    sz_4_24:
      "rnLabeScheduledSleepTime",
    sz_4_26:
      "rnLabelTitleScheduleList",
    sz_4_28:
      "rnLabelTxtEmptySchedule",




    sz_4_30:
      "rnLabelBtnReturn",
    sz_4_32:
      "rnLabelBtnSaveChange",
    sz_4_34:
      "rnLabelBtnRepeatTImes",
    sz_4_36:
      "rnLabelStartTimeDescription",
    sz_4_38:
      "rnLabelEndTimeDescription",
    sz_4_40:
      "rnLabelOnce",
    sz_4_42:
      "rnLabelWorkingDays",
    sz_4_44:
      "rnLabelCustom",
    sz_4_45:
      "rnLabelTiTleStartEndTime",
    sz_4_47:
      "rnLabelHours",
    sz_4_49:
      "rnLabelBtnCancel",
    sz_4_95:
      "rnLabelTitleCustom",
    sz_4_97:
      "rnLabelMonCheckbox",
    sz_4_99:
      "rnLabelTueCheckbox",
    sz_4_101:
      "rnLabelWedCheckbox",
    sz_4_103:
      "rnLabelThurCheckbox",
    sz_4_105:
      "rnLabelFriCheckbox",
    sz_4_107:
      "rnLabelSatCheckbox",
    sz_4_109:
      "rnLabelSunCheckbox",
    sz_4_111:
      "rnLabelBtnOK",
    sz_4_21:
      "rnLabeTitleSleepSetting",
    sz_4_23:
      "rnLabelSleepSwitch",
    sz_4_25:
      "rnLabelBtnReturn",

    sz_4_29:
      "rnLabelBtnAdd",




    sz_4_31:
      "rnLabelTitleSchedulePriod",
    sz_4_33:
      "rnLabelRepeat",
    sz_4_35:
      "rnLabelStartTime",
    sz_4_37:
      "rnLabelEndTime",
    sz_4_39:
      "rnLabelTittleRepeatOptions",
    sz_4_41:
      "rnLabelEveryDay",
    sz_4_43:
      "rnLabelHolidays",


    sz_4_46:
      "rnLabelSelectTime",
    sz_4_48:
      "rnLabelMinutes",
    sz_4_50:
      "rnLabelBtnOK",
    sz_4_96:
      "rnLabelMon",
    sz_4_98:
      "rnLabelTue",
    sz_4_100:
      "rnLabelWed",
    sz_4_102:
      "rnLabelThur",
    sz_4_104:
      "rnLabelFri",
    sz_4_106:
      "rnLabelSat",
    sz_4_108:
      "rnLabelSun",
    sz_4_110:
      "rnLabelBtnCancelv",
      "sz_5_1": "rnLabelBtnReturn",
      "sz_5_3": "rnLabelHomeMonitoringSetting",
      "sz_5_5": "rnLabelMonitoringTime",
      "sz_5_7": "rnLabelSensitivitySettings",
      "sz_5_9": "rnLabelAlarmInterval",
      "sz_5_11": "rnLabelAlarmIntervalStates",
      "sz_5_13": "rnLabelAlarmNotificationsDescription",
      "sz_5_15": "rnLabelEventNotificationType",
      "sz_5_17": "rnLabelTItleHomeSurveillancePeriod",
      "sz_5_19": "rnLabel24HourDescription",
      "sz_5_21": "rnLabelDaytimeDescription",
      "sz_5_23": "rnLabelNightTimeDescription",
      "sz_5_25": "rnLabelCustomizedDescription",
      "sz_5_27": "rnLabelTitleSelectTime",
      "sz_5_29": "rnLabelStartTime",
      "sz_5_31": "rnLabelEndTime",
      "sz_5_33": "rnLabelTiTleStartEndTime",
      "sz_5_35": "rnLabelHours",
      "sz_5_37": "rnLabelBtnCancel",
      "sz_5_39": "rnLabelTitleAlertSensitivity",
      "sz_5_41": "rnLabelHighDescription",
      "sz_5_43": "rnLabelLowDescription",
      "sz_5_45": "rnLabelAlarmTime",
      "sz_5_47": "rnLabelBtnOK",
      "sz_5_49": "rnLabelTitleEventNotificationType",
      "sz_5_51": "rnLabelMotionDetectedSwitch",
      "sz_5_53": "rnLabelPersonDetectedSwitch",
      "sz_5_55": "rnLabenSmartScenceSwitch",
      "sz_5_2": "rnLabeTitleHomeMonitoring",
      "sz_5_4": "rnLabelHomeMonitoringSwitch",
      "sz_5_6": "rnLabelMonitoringTimeStates",
      "sz_5_8": "rnLabelSensitivityStatus",
      "sz_5_10": "rnLabelAlarmIntervalDescription",
      "sz_5_12": "rnLabelAlarmNotifications",
      "sz_5_14": "rnLabelAlarmNotificationsSwitch",
      "sz_5_16": "rnLabelBtnReturn",
      "sz_5_18": "rnLabel24Hour",
      "sz_5_20": "rnLabelDaytime",
      "sz_5_22": "rnLabelNightTime",
      "sz_5_24": "rnLabelCustomized",
      "sz_5_26": "rnLabelBtnClose",
      "sz_5_28": "rnLabelBtnSaveChange",
      "sz_5_30": "rnLabelStartTimeDesctiption",
      "sz_5_32": "rnLabelEndTimeDescription",
      "sz_5_34": "rnLabelSelectTime",
      "sz_5_36": "rnLabelMinutes",
      "sz_5_38": "rnLabelBtnOK",
      "sz_5_40": "rnLabelHigh",
      "sz_5_42": "rnLabelLow",
      "sz_5_44": "rnLabelTitleAlarmInterval",
      "sz_5_46": "rnLabelBtnCancel",
      "sz_5_48": "rnLabelBtnReturn",
      "sz_5_50": "rnLabelMotionDetected",
      "sz_5_52": "rnLabelPersonDetected",
      "sz_5_54": "rnLabenSmartScence",
      "sz_6_1": "rnLabelBtnReturn",
      "sz_6_3": "rnLabelFaceManager",
      "sz_6_5": "rnLabelFaceIdentification",
      "sz_6_7": "rnLabelFaceIdentTips",
      "sz_6_9": "rnLabelFaceIdentSign",
      "sz_6_11": "rnLabelFaceIdentFuncTips",
      "sz_6_2": "rnLabelTitleAiSetting",
      "sz_6_4": "rnLabelBtnReturn",
      "sz_6_6": "rnLabelFaceIdentImg",
      "sz_6_8": "rnLabelFaceIdentInfo",
      "sz_6_10": "rnLabelFaceIdentShare",
      "sz_6_12": "rnLabelFaceIdentCloudBuy",
      "sz_7_1": "rnLabelBtnReturn",
      "sz_7_3": "rnLabelSDCardFeatures",
      "sz_7_5": "rnLabelSDCardStatusValue",
      "sz_7_7": "rnLabelTiTleManageStorage",
      "sz_7_9": "rnLabelRemaining",
      "sz_7_11": "rnLabelStorageOnOff",
      "sz_7_13": "rnLabelRecordingMode",
      "sz_7_15": "rnLabelFormatSDCard",
      "sz_7_17": "rnLabelUnmountDesciption",
      "sz_7_19": "rnLabelTitleRecordingMode",
      "sz_7_21": "rnLabelOnlyMotionDetectedDescription",
      "sz_7_23": "rnLabelOnlyMotionDetectedDescription",
      "sz_7_25": "rnLabelFormatSDCardDescription",
      "sz_7_27": "rnLabelBtnOK",
      "sz_7_29": "rnLabelUnmountDescription",
      "sz_7_31": "rnLabelBtnOK",
      "sz_7_2": "rnLabeTitleManageStorage",
      "sz_7_4": "rnLabelSDCardStatus",
      "sz_7_6": "rnLabelBtnReturn",
      "sz_7_8": "rnLabelTxtStatus",
      "sz_7_10": "rnLabelCapacity",
      "sz_7_12": "rnLabelStorageOnOffSwitch",
      "sz_7_14": "rnLabelRecordingStates",
      "sz_7_16": "rnLabelUnmountSDCard",
      "sz_7_18": "rnLabelImgStatus",
      "sz_7_20": "rnLabelOnlyMotionDetected",
      "sz_7_22": "rnLabelAlwaysRecord",
      "sz_7_24": "rnLabelTitleFormatSDCard",
      "sz_7_26": "rnLabelBtnCancel",
      "sz_7_28": "rnLabelTitleUnmountSDCard",
      "sz_7_30": "rnLabelBtnCancel",
      "sz_8_1": "rnLabelBtnReturn",
      "sz_8_3": "rnLabelBtnEdit",
      "sz_8_5": "rnLabelBtnSelectAll",
      "sz_8_7": "rnLabelAlbumThumbnail",
      "sz_8_9": "rnLabelVideoTime",
      "sz_8_11": "rnLabelImgNoFiles",
      "sz_8_13": "rnLabelAlertDelete",
      "sz_8_15": "rnLabelBtnOK",
      "sz_8_17": "rnLabeTitleImg",
      "sz_8_19": "rnLabelPicture",
      "sz_8_21": "rnLabelBtnDelete",
      "sz_8_23": "rnLabelAlertDelete",
      "sz_8_25": "rnLabelBtnOK",
      "sz_8_27": "rnLabeTitleImg",
      "sz_8_29": "rnLabelBtnPlay",
      "sz_8_29_1": "rnLabelBtnLcPause",
      "sz_8_31": "rnLabelBtnTimeProgressBar",
      "sz_8_31_1": "rnLabelBtnLcProgressBar",
      "sz_8_33": "rnLabelBtnMute",
      "sz_8_33_1": "rnLabelBtnLcMute",
      "sz_8_35": "rnLabelBtnFullscreen",
      "sz_8_35_1": "rnLabelBtnLcFull",
      "sz_8_37": "rnLabelBtnDelete",
      "sz_8_2": "rnLabeTitleAlbum",
      "sz_8_4": "rnLabelBtnCancelEdit",
      "sz_8_6": "rnLabelBtnDelete",
      "sz_8_8": "rnLabelAlbumVideoTag",
      "sz_8_10": "rnLabelVideoSelect",
      "sz_8_12": "rnLabelTxtNoFiles",
      "sz_8_14": "rnLabelBtnCancel",
      "sz_8_16": "rnLabelReturn",
      "sz_8_18": "rnLabelAlbumNum",
      "sz_8_20": "rnLabelBtnShare",
      "sz_8_22": "rnLabelBtnFloatPlay",
      "sz_8_24": "rnLabelBtnCancel",
      "sz_8_26": "rnLabelBtnCancel",
      "sz_8_28": "rnLabelBtnReturn",
      "sz_8_30": "rnLabelBtnStartTime",
      "sz_8_30_1": "rnLabelBtnLcStartTime",
      "sz_8_32": "rnLabelBtnEndTime",
      "sz_8_32_1": "rnLabelBtnLcEndTime",
      "sz_8_34": "rnLabelBtnSpeed",
      "sz_8_34_1": "rnLabelBtnLcSpeed",
      "sz_8_36": "rnLabelBtnShare",
      "sz_8_38": "rnLabelBtnFullPlay",
      "sz_8_38_1": "rnLabelBtnLcFullPause",
      "sz_8_40": "rnLabelBtnFullTimeProgressBar",
      "sz_8_40_1": "rnLabelBtnLcFullProgressBar",
      "sz_8_42": "rnLabelBtnFullMute",
      "sz_8_42_1": "rnLabelBtnLcFullMute",
      "sz_8_44": "rnLabelBtnSwitchVertical",
      "sz_8_44_1": "rnLabelBtnLcFullFull",
      "sz_8_46": "rnLabelBtnDelete",
      "sz_8_46_1": "rnLabelBtnLcFullDelete",
      "sz_8_48": "rnLabelBtnCancel",
      "sz_8_39": "rnLabelBtnFullStartTime",
      "sz_8_39_1": "rnLabelBtnLcFullStartTime",
      "sz_8_41": "rnLabelBtnFullEndTime",
      "sz_8_41_1": "rnLabelBtnLcFullEndTime",
      "sz_8_43": "rnLabelBtnFullSpeed",
      "sz_8_43_1": "rnLabelBtnLcSpeedFull",
      "sz_8_45": "rnLabelBtnFullShare",
      "sz_8_45_1": "rnLabelBtnLcFullShare",
      "sz_8_47": "rnLabelAlertDelete",
      "sz_8_49": "rnLabelBtnOK",


    hk_2_3:
      "rnLabelBtnEdit",
    hk_2_5:
      "rnLabelBtnSelectAll",
    hk_2_7:
      "rnLabelVideoSelect",
    hk_2_9:
      "rnLabelBtnSave",
    hk_2_15:
      "rnLabelBtnSomeoneMove",
    hk_2_17:
      "rnLabelAlertDelete",
    hk_2_19:
      "rnLabelBtnOK",
    hk_2_4:
      "rnLabelBtnCancelEdit",
    hk_2_6:
      "rnLabelVideoThumbnail",
    hk_2_10:
      "rnLabelBtnDelete",
    hk_2_12:
      "rnLabelTxtNoFiles",
    hk_2_14:
      "rnLabelBtnAllEvents",
    hk_2_16:
      "rnLabelPictureChange",
    hk_2_18:
      "rnLabelBtnCancel",
    hk_2_20:
      "rnLabelDownloading",

    hk_3_1:
      "rnLabelBtnReturn",
    hk_3_3:
      "rnLabelBtnEdit",
    hk_3_5:
      "rnLabelBtnFloatPlay",
    hk_3_7:
      "rnLabelBtnStartTime",
    hk_3_9:
      "rnLabelBtnEndTime",
    hk_3_11:
      "rnLabelBtnSnapshot",
    hk_3_13:
      "rnLabelBtnFullscreen",
    hk_3_15:
      "rnLabelBtnSelectAll",
    hk_3_19:
      "rnLabelBtnSave",
    hk_3_27:
      "rnLabelDownloading",
    hk_3_29:
      "rnLabelBtnCancel",
    hk_3_2:
      "rnLabeTxtlTitle",
    hk_3_4:
      "rnLabelBtnThumbnail",
    hk_3_6:
      "rnLabelBtnPlay",
    hk_3_8:
      "rnLabelBtnTimeProgressBar",
    hk_3_10:
      "rnLabelBtnMute",
    hk_3_10_1: "rnLabelBtnMute",
    hk_3_12:
      "rnLabelBtnSpeed",
    hk_3_14:
      "rnLabelBtnCancelEdit",
    hk_3_20:
      "rnLabelBtnDelete",
    hk_3_28:
      "rnLabelAlertDelete",
    hk_3_30:
      "rnLabelBtnOK",


    //看家
    kj_1_1:
      "rnLabelBtnReturn",

    kj_1_5:
      "rnLabelDateWeek",

    kj_1_9:
      "rnLabelEventTypeRing",
    kj_1_13:
      "rnLabelEventTypeName",
    kj_1_15:
      "rnLabelEventTypeName",
    kj_1_17:
      "rnLabelEventNoData",
    kj_1_2:
      "rnLabeTxtlTitle",

    kj_1_8:
      "rnLabelMoreRecord",
    kj_1_12:
      "rnLabelEventThumbnail",
    kj_1_14:
      "rnLabelEventTypeName",
    kj_1_16:
      "rnLabelBtnCancel",
    sz_4_74:
      "rnLabelBtnReturn",
    sz_4_76:
      "rnLabelFullColorLowLight",

    sz_4_80:
      "rnLabelTxtBeforeActivation",
    sz_4_82:
      "rnLabelTxtAfterActivation",
    sz_4_84:
      "rnLabelAlwaysOn",
    sz_4_86:
      "rnLabelInfraredDescription",
    sz_4_88:
      "rnLabelTxtNormalMode",
    sz_4_90:
      "rnLabelTxtInfraredImaging",
    sz_4_75:
      "rnLabeTitleNightSettings",
    sz_4_77:
      "rnLabelFullColorLowLightSwitch",
    sz_4_79:
      "rnLabelImgBeforeActivation",
    sz_4_81:
      "rnLabelImgAfterActivation",
    sz_4_83:
      "rnLabelAutoSwitch",
    sz_4_85:
      "rnLabelAlwaysOff",
    sz_4_87:
      "rnLabelImgNormalMode",
    sz_4_89:
      "rnLabelImgInfraredImaging",
    hk_1_1:
      "rnLabelBtnReturn",
    hk_1_3:
      "rnLabelBtnEdit",
    hk_1_5:
      "rnLabelBtnSelectAll",


    hk_1_9:
      "rnLabelVideoSelect",
    hk_1_11:
      "rnLabelVideoEvent",
    hk_1_13:
      "rnLabelBtnDelete",
    hk_1_15:
      "rnLabelBtnCancel",
    hk_1_17:
      "rnLabelDownloading",
    hk_1_19:
      "rnLabelTxtNoFiles",
    hk_1_2:
      "rnLabeTxtlTitle",
    hk_1_4:
      "rnLabelBtnCancelEdit",
    hk_1_8:
      "rnLabelVideoThumbnail",
    hk_1_10:
      "rnLabelVideoTime",
    hk_1_12:
      "rnLabelBtnSave",
    hk_1_14:
      "rnLabelAlertDelete",
    hk_1_16:
      "rnLabelBtnOK",
    hk_1_18:
      "rnLabelImgNoFiles",

    kj_2_1:"rnLabelBtnReturn",
    kj_2_3:"rnLabelBtnShare",
    kj_2_5:"rnLabelCloudStorageHint",
    kj_2_17:
      "rnLabelEventThumbnail",
    kj_2_19:
      "rnLabelBtnThumbnail",
    kj_2_21:
      "rnLabelAlertDelete",
    kj_2_23:
      "rnLabelWXshare",
    kj_2_25:
      "rnLabelBtnFullThumbnail",
    kj_2_27:
      "rnLabelBtnFullShare",
    kj_2_29:
      "rnLabelBtnFullDelete",
    kj_2_33:
      "rnLabelBtnFullTimeProgressBar",

    kj_2_2:
      "rnLabelBtnSnapshot",
    kj_2_4:
      "rnLabelBtnSave",
    kj_2_6:
      "rnLabelBtnDelete",
    kj_2_18:
      "rnLabelTVPlaying",
    kj_2_20:
      "rnLabelAlertDelete",
    kj_2_22:
      "rnLabelBtnOK",
    kj_2_24:
      "rnLabelBtnReturnVertical",
    kj_2_26:
      "rnLabelBtnFullSnapshot",
    kj_2_28:
      "rnLabelBtnFullSave",
    kj_2_30:
      "rnLabelCloudStorageHint",
      "kj_2_37": "rnLabelViewFrame",
      "kj_2_38": "rnLabelFullViewFrame",
      "selected": "selected",
      "unSelected": "unselected",
      "example_image": "exampleImage"

  
  },
  'zh': {
    "zb_1": "休眠摄像机",
    "zb_1_1": "打开摄像机",
    "zb_2": "声音已关闭",
    "zb_2_1": "声音已打开",
    "zb_4": "全屏",
    "zb_5": "对讲通话",
    "zb_6": "截图",
    "zb_7": "录制视频",
    "zb_8": "云台控制按钮",
    "zb_10": "返回",
    zb_11: "静音",
    zb_11_1: "开启声音",
    zb_12: "云台控制",
    zb_12_1: "镜头向左",
    zb_12_2: "镜头向右",
    zb_12_3: "镜头向上",
    zb_12_4: "镜头向下",
    zb_14: "截图",
    zb_15: "录制视频",
    zb_16: "对讲通话",
    "zb_17": "截图",
    "zb_18": "截图",
    "zb_19": "云台控制",
    "zb_20": "编辑",
    "zb_21": "全景图",
    "zb_23": "全景图重新绘制",
    "zb_22": "全景图图片",
    "zb_27": "摄像机",
    "zb_28": "网速",
    "zb_29": "返回",
    "zb_30": "设置",
    "zb_31": "回看",
    "zb_32": "云存储",
    "zb_37": "云台手势转动",
    "zb_38": "录制时长",
    "zb_39": "放大倍数 1倍",
    "zb_41": "摄像机镜头已被物理遮蔽",
    "zb_43": "录制时长",
    "zb_44": "云台手势转动",
    "zb_46": "视频画质选择",
    "zb_54": "直播窗口",
    "zb_55": "存储管理",
    "yc_1": "返回",
    "yc_2": "删除",
    "yc_3": "下载",
    "yc_4": "截图",
    "yc_5": "播放",
    "yc_6": "播放时长",
    "yc_7": "进度条",
    "yc_8": "视频时长",
    "yc_9": "静音",
    "yc_10": "倍速 1倍",
    "yc_11": "全屏",
    "yc_12": "返回",
    "yc_13": "删除",
    "yc_14": "下载",
    "yc_15": "截图",
    "yc_16": "暂停",
    "yc_18": "进度条",
    "yc_20": "打开声音",
    "yc_22": "退出全屏",
    "yc_23": "返回",
    "yc_25": "您尚未购买云存服务",
    "yc_26": "点击去购买",
    "lc_1": "播放",
    "lc_2": "播放时长",
    "lc_3": "进度条",
    "lc_4": "视频时长",
    "lc_5": "静音",
    "lc_6": "全屏",
    "lc_7": "暂停",
    "lc_9": "进度条",
    "lc_11": "打开声音",
    "lc_12": "退出全屏",
    "lc_13": "分享",
    "lc_14": "删除",
      lc_15:"倍速 1倍",
    "lc_15_1": "下载列表",
    "lc_16_1": "视频图标",
    "lc_18": "图片图标",
    "lc_21": "退出",
    "lc_22": "请选择",
    "lc_23": "全选",
    "lc_24": "本日期内文件全选",
    "lc_25": "选择",
    "lc_26": "删除",
    "lc_27": "本日期内文件全不选",
    "lc_28": "确认删除",
    "lc_29": "取消",
    "lc_30": "删除",
    "lc_31": "返回",
    "lc_32": "正在下载",
    "lc_33": "下载列表",
    "lc_34": "全部取消",
    "lc_35": "缩略图",
    "lc_38": "进度条",
    "lc_39": "删除",
    "yc_date": "日期筛选",
    "yc_edit": "编辑",
    "lc_date": "日期筛选",
    "lc_edit": "编辑",
    "sd_date": "日期筛选",
    "sd_edit": "编辑",
    "sd_1": "返回",
    "sd_12": "返回",
    "sd_13": "文件时间",
    "sd_3": "编辑",
    "sd_16": "进度条",
    "sd_6": "进度条",
    "sd_19": "截图",
    "sd_9": "截图",
    "sd_20": "倍速 1倍",
    "sd_10": "倍速 1倍",
    "sd_21": "退出全屏",
    "sd_11": "全屏",
    "sd_22": "视频",
    "sd_24": "视频图标",
    "sd_27": "退出",
    "sd_28": "请选择",
    "sd_29": "全选",
    "sd_30": "本日期内全选",
    "sd_31": "选择",
    "sd_32": "删除",
    "sd_33": "确认删除",
    "sd_34": "取消",
    "sd_35": "删除",
    "sd_36": "退出",
    "sd_37": "请选择",
    "sd_38": "全选",
    "rp_1": "截图按钮",
    "rp_2": "倍速 1倍",
    "rp_3": "静音",
    "rp_4": "全屏",
    "rp_5": "播放",
    "rp_6": "前一天",
    "rp_7": "后一天",
    "rp_8": "上一段",
    "rp_9": "下一段",
    "rp_22": "返回",
    "rp_23": "播放",
    "rp_24": "打开声音",
    "rp_25": "倍速 1倍",
    "rp_26": "截图按钮",
    "rp_27": "上一段",
    "rp_29": "下一段",
    "rp_11": "截图按钮",
    "rp_12": "录制视频",
    "rp_12_1": "录制视频",
    "rp_13": "倍速1倍",
    "rp_14": "播放",
    "rp_15": "静音",
    "rp_16": "全屏",
    "rp_17": "前一天",
    "rp_18": "后一天",
    "rp_19": "上一段",
    "rp_21": "下一段",
    "rp_30": "返回",
    "rp_31": "播放",
    "rp_32": "打开声音",
    "rp_34": "截图按钮",
    "rp_35": "录制视频",
    "rp_36": "上一段",
    "rp_38": "下一段",
    "rp_39": "返回",
    "rp_40": "云存储",
    "rp_41": "SD 卡",
    "rp_42": "截图",
    "rp_43": "录制时长",
    "rp_44": "全部回看视频",
    "rp_45": "录制时长",
    "rp_46": "截图",
    "rp_47": "画面变动",
    "rp_48": "有人移动",
    "rp_49": "宝宝哭声",
    "rp_50": "请选择回放倍数",
    "rp_54": "请选择回放倍数",
    "rp_58": "放大倍数 6倍（读取具体倍数）",
    "rp_59": "日期 今天（读取具体日期）",
    "rp_60": "日期栏",
    "rp_61": "放大倍数 1.2倍（读取具体倍数）",
    "rp_62": "回看播放窗口",
    "rp_63": "播放时间点 ",
    "rp_64": "播放时间点",
    "rp_65": "时间",
    "rp_66": "至",
    "rp_67": "视频时间",
    "hk_2_3": "编辑",
    "hk_2_4": "返回",
    "hk_2_5": "全选",
    "hk_2_6": "缩略图",
    "hk_2_7": "选择",
    "hk_2_9": "保存",
    "hk_2_10": "删除",
    "hk_2_12": "没有文件",
    "hk_2_14": "全部事件",
    "hk_2_15": "有人移动",
    "hk_2_16": "画面变动",
    "hk_2_17": "确认删除",
    "hk_2_18": "取消",
    "hk_2_19": "删除",
    "hk_2_20": "下载中",
    "hk_3_1": "返回",
    "hk_3_2": "文件时间",
    "hk_3_3": "编辑",
    "hk_3_4": "截图",
    "hk_3_5": "播放",
    "hk_3_6": "暂停",
    "hk_3_7": "播放时长",
    "hk_3_8": "进度条",
    "hk_3_9": "视频时长",
    "hk_3_10": "静音",
    "hk_3_10_1": "打开声音",
    "hk_3_11": "截图",
    "hk_3_12": "倍速 1倍",
    "hk_3_13": "全屏",
    "hk_3_14": "退出",
    "hk_3_15": "全选",
    "hk_3_19": "保存",
    "hk_3_20": "删除",
    "kj_1_1": "返回",
    "kj_1_2": "看家助手",
    "kj_1_5": "周",
    "kj_1_8": "选择",
    "kj_1_9": "图标",
    "kj_1_12": "缩略图",
    "kj_1_13": "全部事件",
    "kj_1_14": "画面变动",
    "kj_1_15": "有人移动",
    "kj_1_16": "取消",
    "kj_1_17": "暂无事件",
    "kj_2_1": "返回",
    "kj_2_2": "截图按钮",
    "kj_2_3": "分享",
    "kj_2_4": "下载",
    "kj_2_5": "购买云存储可录制完整视频",
    "kj_2_6": "删除",
    "kj_2_17": "缩略图",
    "kj_2_18": "播放中",
    "kj_2_19": "截图",
    "kj_2_20": "确认删除",
    "kj_2_21": "取消",
    "kj_2_22": "删除",
    "kj_2_23": "微信",
    "kj_2_24": "返回",
    "kj_2_25": "截图",
    "kj_2_26": "截图按钮",
    "kj_2_27": "分享",
    "kj_2_28": "下载",
    "kj_2_29": "删除",
    "kj_2_30": "购买云存储可录制完整视频",
    "kj_2_33": "进度条",
    "kj_2_37": "播放窗口",
    "kj_2_38": "播放窗口",
    "sz_1": "返回",
    "sz_2": "设置",
    "sz_3": "功能设置",
    "sz_4": "摄像机设置",
    "sz_5": "看家助手设置",
    "sz_6": "AI功能设置",
    "sz_7": "存储设置",
    "sz_8": "相册",
    "sz_9": "云存储设置",
    "sz_10": "通用设置",
    "sz_4_1": "返回",
    "sz_4_2": "摄像机设置",
    "sz_4_3": "状态灯",
    "sz_4_4": "状态灯 已开启",
    "sz_4_4_1": "状态灯 已关闭",
    "sz_4_5": "流量保护",
    "sz_4_6": "流量保护 已开启",
    "sz_4_6_1": "流量保护 已关闭",
    "sz_4_7": "在移动网络下不会自动播放",
    "sz_4_8": "云台手势转动",
    "sz_4_9": "云台手势转动 已开启",
    "sz_4_9_1": "云台手势转动 已关闭",
    "sz_4_10": "开启后直播窗口内可手势滑动以转动云台方向",
    "sz_4_11": "休眠设置",
    "sz_4_11_1": "物理遮蔽",
    "sz_4_12": "画面设置",
    "sz_4_13": "夜视功能设置",
    "sz_4_14": "云台校准",
    "sz_4_15": "人形追踪",
    "sz_4_15_1": "移动追踪",
    "sz_4_16": "人形追踪 已开启",
    "sz_4_16_1": "人形追踪 已关闭",
    "sz_4_17": "侦测到人形时镜头跟随拍摄",
    "sz_4_18": "浮窗",
    "sz_4_19": "重启设备",
    "sz_4_51": "返回",
    "sz_4_52": "画面设置",
    "sz_4_53": "时间水印",
    "sz_4_54": "时间水印 已开启",
    "sz_4_54_1": "时间水印 已关闭",
    "sz_4_55": "镜头畸变纠正",
    "sz_4_56": "镜头畸变纠正 已开启",
    "sz_4_56_1": "镜头畸变纠正 已关闭",
    "sz_4_57": "宽动态范围模式 WDR",
    "sz_4_58": "关",
    "sz_4_59": "旋转画面",
    "sz_4_57_1": "返回",
    "sz_4_58_1": "宽动态范围模式 WDR",
    "sz_4_59_1": "宽动态范围模式 WDR",
    "sz_4_61": "宽动态范围模式下，能让画面昏暗的部分和曝光过度的部分，保留更多的细节",
    "sz_4_62": "正常模式示意图",
    "sz_4_63": "宽动态范围模式示意图",
    "sz_4_64": "正常模式",
    "sz_4_65": "宽动态范围模式",
    "sz_4_66": "注意",
    "sz_4_68": "返回",
    "sz_4_69": "旋转",
    "sz_4_70": "保存",
    "sz_4_71": "图像",
    "sz_4_72": "旋转按钮",
    "sz_4_73": "旋转",
    "sz_4_20": "返回",
    "sz_4_21": "休眠设置",
    "sz_4_22": "休眠",
    "sz_4_23": "休眠 已开启",
    "sz_4_23_1": "休眠 已关闭",
    "sz_4_24": "定时自动休眠",
    "sz_4_25": "返回",
    "sz_4_26": "定时",
    "sz_4_28": "定时列表为空，点击添加",
    "sz_4_29": "添加",
    "sz_4_30": "退出",
    "sz_4_31": "时间段定时",
    "sz_4_32": "保存",
    "sz_4_33": "重复",
    "sz_4_34": "执行一次",
    "sz_4_35": "开启",
    "sz_4_36": "未设置",
    "sz_4_37": "关闭",
    "sz_4_38": "未设置",
    "sz_4_39": "重复选项",
    "sz_4_40": "执行一次",
    "sz_4_41": "每天",
    "sz_4_42": "法定工作日 中国大陆",
    "sz_4_43": "法定节假日 中国大陆",
    "sz_4_44": "自定义",
    "sz_4_45": "开启时间",
    "sz_4_49": "取消",
    "sz_4_50": "确定",
    "sz_4_95": "自定义重复",
    "sz_4_96": "周日",
    "sz_4_97": "选择",
    "sz_4_98": "周一",
    "sz_4_99": "选择",
    "sz_4_100": "周二",
    "sz_4_101": "选择",
    "sz_4_102": "周三",
    "sz_4_103": "选择",
    "sz_4_104": "周四",
    "sz_4_105": "选择",
    "sz_4_106": "周五",
    "sz_4_107": "选择",
    "sz_4_108": "周六",
    "sz_4_109": "选择",
    "sz_4_110": "取消",
    "sz_4_111": "确定",
    "sz_4_74": "返回",
    "sz_4_75": "夜视功能设置",
    "sz_4_76": "微光全彩",
    "sz_4_77": "开关按钮 已关闭",
    "sz_4_79": "开启前示意图",
    "sz_4_80": "开启前",
    "sz_4_81": "开启后示意图",
    "sz_4_82": "开启后",
    "sz_4_83": "自动切换",
    "sz_4_84": "一直打开 此时微光全彩功能不起作用",
    "sz_4_85": "一直关闭",
    "sz_4_87": "正常模式示意图",
    "sz_4_88": "正常模式",
    "sz_4_89": "红外成像示意图",
    "sz_4_90": "红外成像",
    "sz_4_91": "重启设备",
    "sz_4_93": "取消",
    "sz_4_94": "确认",
    "sz_5_1": "返回",
    "sz_5_2": "看家助手设置",
    "sz_5_3": "看家助手",
    "sz_5_4": "开关按钮 已开启",
    "sz_5_5": "看家时间段",
    "sz_5_6": "全天",
    "sz_5_7": "分区灵敏度设置",
    "sz_5_8": "高灵敏度",
    "sz_5_9": "报警时间间隔",

    "sz_5_11": "5分钟",
    "sz_5_12": "报警消息推送",

    "sz_5_14": "开关按钮 已开启",
    "sz_5_15": "推送事件类型",
    "sz_5_16": "返回",
    "sz_5_17": "看家时间段",
    "sz_5_18": "全天照看",
    "sz_5_26": "返回",
    "sz_5_27": "选择看护时间",
    "sz_5_28": "保存",

    "sz_5_37": "取消",
    "sz_5_38": "确定",
    "sz_5_48": "返回",
    "sz_5_49": "推送事件类型",
    "sz_5_50": "画面变动推送",
    "sz_5_51": "画面变动推送 已开启",
    "sz_5_51_1": "画面变动推送 已关闭",
    "sz_5_52": "看护区域有人移动推送",
    "sz_5_53": "看护区域有人移动推送 已开启",
    "sz_5_53_1": "看护区域有人移动推送 已关闭",
    "sz_5_54": "智能场景触发推送",
    "sz_5_55": "智能场景触发推送 已开启",
    "sz_5_55_1": "智能场景触发推送 已关闭",
    "sz_6_1": "返回",
    "sz_6_2": "AI功能设置",
    "sz_6_3": "人脸管理",
    "sz_6_4": "返回",
    "sz_6_5": "人脸识别",
  
    "sz_7_1": "返回",
    "sz_7_2": "存储设置",
    "sz_7_3": "存储卡功能",
    "sz_7_4": "存储卡状态",
    "sz_7_5": "良好",
    "sz_7_6": "返回",
    "sz_7_7": "存储管理",
    "sz_7_11": "存储开关",
    "sz_7_12": "开关状态 已开启",
    "sz_7_13": "录制模式",
    "sz_7_14": "始终录制",
    "sz_7_15": "格式化存储卡",
    "sz_7_16": "推出存储卡",
    "sz_7_18": "存储暂停",
    "selected": "已选中",
    "unSelected": "未选中",
    "example_image": "示意图"
  }
};

const dev_strings = {
  'en': {
    zb_1: "rnLabelBtnSleep",
    zb_1_1: "rnLabelBtnSleepClosed",
    zb_2: "rnLabelBtnAudio",
    zb_2_1: "rnLabelBtnAudioClosed",

    zb_3_4: "rnLabelBtnResolution",
    zb_4: "rnLabelBtnFull",
    zb_4_1: "rnLabelBtnFullClosed",
    zb_5: "rnLabelBtnCall",
    zb_5_1: "rnLabelBtnCallClosed",
    zb_6: "rnLabelBtnSnapshot",
    zb_6_1: "rnLabelBtnSnapshotClosed",
    zb_7: "rnLabelBtnRecord",
    zb_7_1: "rnLabelBtnRecordClosed",
    zb_8: "rnLabelBtnSmallDirection",
    zb_10: "rnLabelBtnLSBack",
    zb_11: "rnLabelBtnAudio",
    zb_12: "rnLabelBtnDirection",
    zb_14: "rnLabelBtnSnapshot",
    zb_15: "rnLabelBtnRecord",
    zb_16: "rnLabelBtnCall",
    zb_17: "rnLabelBtnThumbnail",
    zb_18: "rnLabelBtnThumbnailFullscreen",
    zb_19: "rnLabelBtnDirection",
    zb_20: "rnLabelBtnPanoramaEdit",
    zb_21: "rnLabelBtnPanoramaBack",
    zb_22: "rnLabelBtnPanoramaDisplay",
    zb_27: "rnLabelBtnTitle",
    zb_28: "rnLabelBtnRate",
    zb_29: "rnLabelBtnReturn",
    zb_30: "rnLabelBtnSetting",
    zb_31: "rnLabelBtnPlayBack",
    zb_32: "rnLabelBtnCloud",
    zb_34: "rnLabelBtnScene1",
    zb_35: "rnLabelBtnScene2",
    zb_36: "rnLabelBtnScene3",



    // cloud
    yc_1: "rnLabelBtnYcBack",
    yc_2: "rnLabelBtnYcDelete",
    yc_3: "rnLabelBtnYcDownload",
    yc_4: "rnLabelBtnYcScreenShot",
    yc_5: "rnLabelBtnYcPause",
    yc_6: "rnLabelBtnYcStartTime",
    yc_7: "rnLabelBtnYcProgressBar",
    yc_8: "rnLabelBtnYcEndTime",
    yc_9: "rnLabelBtnYcMute",
    yc_10: "rnLabelBtnYcSpeed",
    yc_11: "rnLabelBtnYcFull",
    yc_12: "rnLabelBtnYcFullBack",
    yc_13: "rnLabelBtnYcFullDelete",
    yc_14: "rnLabelBtnYcFullDownLoad",
    yc_15: "rnLabelBtnYcFullScreenShot",
    yc_16: "rnLabelBtnYcFullPause",
    yc_18: "rnLabelBtnYcFullProgressBar",
    yc_20: "rnLabelBtnYcFullMute",
    yc_21: "rnLabelBtnYcFullSpeed",
    yc_22: "rnLabelBtnYcFullFull",

    yc_23: "rnLabelBtnReturn",
    yc_25: "rnLabelTextCloud",
    yc_26: "rnLabelBtnByCloud",



    // sd卡
    sd_1: "rnLabelBtnSdBack",
    sd_3: "rnLabelBtnSdEdit",
    sd_6: "rnLabelBtnSdProgressBar",
    sd_9: "rnLabelBtnSdScreenShot",
    sd_10: "rnLabelBtnSdSpeed",
    sd_11: "rnLabelBtnSdFull",
    sd_12: "rnLabelBtnSdFullBack",
    sd_13: "rnLabelBtnSdFullTitle",
    sd_16: "rnLabelBtnSdFullProgressBar",
    sd_19: "rnLabelBtnSdFullScreenShot",
    sd_20: "rnLabelBtnSdFullSpeed",
    sd_21: "rnLabelBtnSdFullFull",
    // LOCAL
    lc_1: "rnLabelBtnLcPause",
    lc_2: "rnLabelBtnLcStartTime",
    lc_3: "rnLabelBtnLcProgressBar",
    lc_4: "rnLabelBtnLcEndTime",
    lc_5: "rnLabelBtnLcMute",
    lc_6: "rnLabelBtnLcFull",
    lc_7: "rnLabelBtnLcFullPause",
    lc_9: "rnLabelBtnLcFullProgressBar",
    lc_11: "rnLabelBtnLcFullMute",
    lc_12: "rnLabelBtnLcFullFull",
    lc_13: "rnLabelBtnLcFullShare",
    lc_14: "rnLabelBtnLcFullDelete",

    lc_15: "rnLabelBtnLcSpeed",
    lc_16: "rnLabelBtnLcSpeedFull",

    // other
    yc_date: "rnLabelBtnYcDate",
    yc_edit: "rnLabelBtnYcEdit",
    lc_date: "rnLabelBtnLcDate",
    lc_edit: "rnLabelBtnLcEdit",
    sd_date: "rnLabelBtnLcDate",
    sd_edit: "rnLabelBtnLcEdit",
    // lookback cloud
    rp_1: "rnLabelBtnRpSnapshot",
    rp_1_1: "rnLabelBtnRpSnapshotClosed",
    rp_2: "rnLabelBtnRpSpeed",
    rp_2_1: "rnLabelBtnRpSpeedClosed",
    rp_3: "rnLabelBtnRpAudio",
    rp_4: "rnLabelBtnRpFullscreen",
    rp_5: "rnLabelBtnRpPlay",
    rp_6: "rnLabelBtnRpDateleft",
    rp_7: "rnLabelBtnRpDateright",
    rp_8: "rnLabelBtnRpTimeleft",
    rp_9: "rnLabelBtnRpTimeright",
    rp_22: "rnLabelBtnRpFullBack",
    rp_23: "rnLabelBtnRpFullPlay",
    rp_24: "rnLabelBtnRpFullAudio",
    rp_24_1: "rnLabelBtnRpFullAudioClosed",
    rp_25: "rnLabelBtnRpFullSpeed",
    rp_25_1: "rnLabelBtnRpFullSpeedClosed",
    rp_26: "rnLabelBtnRpFullSnapshot",
    rp_27: "rnLabelBtnRpFullTimeleft",
    rp_29: "rnLabelBtnRpFullTimeright",
    // sd
    rp_11: "rnLabelBtnRsdSnapshot",
    rp_12: "rnLabelBtnRsdRecord",
    rp_12_1: "rnLabelBtnRsdRecordClosed",
    rp_13: "rnLabelBtnRsdSpeed",
    rp_14: "rnLabelBtnRsdPlay",
    rp_15: "rnLabelBtnRsdAudio",
    rp_15_1: "rnLabelBtnRsdAudioClosed",
    rp_16: "rnLabelBtnRsdFullscreen",
    rp_17: "rnLabelBtnRsdDateleft",
    rp_18: "rnLabelBtnRsdDateright",
    rp_19: "rnLabelBtnRsdTimeleft",
    rp_20: "rnLabelBtnRsdTimeroller",
    rp_21: "rnLabelBtnRsdTimeright",
    rp_30: "rnLabelBtnRsdFullBack",
    rp_31: "rnLabelBtnRsdFullPlay",
    rp_32: "rnLabelBtnRsdFullAudio",
    rp_32_1: "rnLabelBtnRsdFullAudioClosed",
    rp_34: "rnLabelBtnRsdFullSnapshot",
    rp_35: "rnLabelBtnRsdFullRecord",
    rp_35_1: "rnLabelBtnRsdFullRecordClosed",
    rp_36: "rnLabelBtnRsdFullTimeleft",
    rp_38: "rnLabelBtnRpFullTimeright",
    rp_40:
      "rnLabelCloudSave",
    rp_42:
      "rnLabelThumbnail",
    rp_44:
      "rnLabelBtnAllVideo",
    rp_46:
      "rnLabelFullThumbnail",

    rp_50:
      "rnLabelTxtSpeed",
    rp_52:
      "rnLabelSpeedx4",
    rp_54:
      "rnLabelTxtSpeed",
    rp_56: "rnLabelSpeedx4",

    "rp_58": "rnLabelBtnFocus",
    "rp_60": "rnLabelWeekView",
    "rp_62": "rnLabelFrameView",
    "rp_64": "rnLabelFullBackTime",
    "rp_59": "rnLabelBtnToday",
    "rp_61": "rnLabelBtnFullFocus",
    "rp_63": "rnLabelBackTime",


    rp_39:
      "rnLabelBtnReturn",

    rp_41:
      "rnLabelBtnSDSave",

    rp_43:
      "rnLabelRecordTime",

    rp_45:
      "rnLabelFullRecordTime",

    rp_47:

      "rnLabelObjectMove",

    rp_48:
      "rnLabelSomeoneMove",
    rp_49:
      "rnLabelBabyCry",




    rp_51:
      "rnLabelSpeedx1",

    rp_53:
      "rnLabelSpeedx16",

    rp_55:
      "rnLabelSpeedx1",

    rp_57:
      "rnLabelSpeedx16",

    zb_37: "rnLabelDirectionTip",
    zb_39: "rnLabelBtnFocus",
    zb_41:
      "rnLabelTxtDormancy",
    zb_43:
      "rnLabelImgAuto",
    zb_38:
      "rnLabelRecordTime",
    zb_42:
      "rnLabelBtnFullFocus",
    zb_44:
      "rnLabelFullDirectionTip",
    zb_46:
      "rnLabelImgQuility",
    zb_54: "rnLabelFrameView",
    zb_55: "rnLabelStorageManager",
    sz_1:
      "rnLabelBtnReturnVertical",
    sz_3:
      "rnLabelDeviceSettings",
    sz_5:
      "rnLabelHOmeMonitoring",
    sz_7:
      "rnLabelManageStorage",
    sz_9:
      "rnLabelCloudSetting",
    sz_11:
      "rnLabelDeviceName",
    sz_13:
      "rnLabelShareDevice",
    sz_15:
      "rnLabelFirmwareUpdate",
    sz_17:
      "rnLabelAddtionalSetting",
    sz_19:
      "rnLabelCameraShortcut",
    sz_11_1:
      "rnLabelCurrentName",
    sz_19_1:
      "rnLabelShortcutStates",
    sz_2:
      "rnLabeTxtlTitle",
    sz_4:
      "rnLabelCameraSetting",
    sz_6:
      "rnLabelAISetting",
    sz_8:
      "rnLabelAlbum",
    sz_10:
      "rnLabelGeneralSetting",
    sz_12:
      "rnLabeManagelLocation",
    sz_14:
      "rnLabelAutomation",
    sz_16:
      "rnLabelHelpFeedBack",
    sz_18:
      "rnLabelFavorites",
    sz_20:
      "rnLabelDeleteDevice",
    sz_18_1:
      "rnLabelFavoritesStates",
    sz_4_1:
      "rnLabelBtnReturn",
    sz_4_3:
      "rnLabelStatusLight",
    sz_4_5:
      "rnLabelDataUsageWarning",
    sz_4_7:
      "rnLabelDataUsageWarningDescribtion",
    sz_4_9:
      "rnLabelPTZGestureRotationSwitch",
    sz_4_11:
      "rnLabelSleepSettings",
    sz_4_13:
      "rnLabelNightvisionSetting",
    sz_4_15:
      "rnLabelMotionTracking",
    sz_4_17:
      "rnLabelMotionTrackingDescribtion",
    sz_4_19:
      "rnLabelRebootDevice",
    sz_4_2:
      "rnLabeTxtlCameraSettings",
    sz_4_4:
      "rnLabelDataUsageWarningSwitch",
    sz_4_6:
      "rnLabelNetSafeSwitch",
    sz_4_8:
      "rnLabelPTZGestureRotation",
    sz_4_10:
      "rnLabelPTZGestureRotationDescribtion",
    sz_4_12:
      "rnLabelImageSettings",
    sz_4_14:
      "rnLabelCameraCalibration",
    sz_4_16:
      "rnLabelMotionTrackingSwitch",
    sz_4_18:
      "rnLabelFloatWindow",
    sz_4_51:
      "rnLabelBtnReturn",
    sz_4_53:
      "rnLabelTimestamp",
    sz_4_55:
      "rnLabelLensDistortionCorrection",
    sz_4_57:
      "rnLabelWideDynamicRangeMode",
    sz_4_59:
      "rnLabelRotateImage",
    sz_4_57_1:
      "rnLabelBtnReturn",
    sz_4_59_1:
      "rnLabelWideDynamicRangeModeSetting",
    sz_4_61:
      "rnLabelWDRDescription",
    sz_4_63:
      "rnLabelImgWDR",
    sz_4_65:
      "rnLabelTxtWDR",
    sz_4_67:
      "rnLabelAttentionDescription",
    sz_4_69:
      "rnLabelTitleRotate",
    sz_4_71:
      "rnLabelImage",
    sz_4_73:
      "rnLabelBtnRatate",
    sz_4_52:
      "rnLabeTxtImageSettings",
    sz_4_54:
      "rnLabelTimestampSwitch",
    sz_4_56:
      "rnLabelLensDistortionCorrectionSwitch",
    sz_4_58:
      "rnLabelWideDynamicRangeModeSates",
    sz_4_58_1:
      "rnLabelTxtWDRMode ",
    sz_4_60:
      "rnLabelWideDynamicRangeModeSwitch",
    sz_4_62:
      "rnLabelImgNormalMode",
    sz_4_64:
      "rnLabelRxtNormalMode",
    sz_4_66:
      "rnLabelAttention",
    sz_4_68:
      "rnLabelBtnReturn",
    sz_4_70:
      "rnLabelBtnSaveChange",
    sz_4_72:
      "rnLabelImgRotate",
    sz_4_20:
      "rnLabelBtnReturn",
    sz_4_22:
      "rnLabelSleep",
    sz_4_24:
      "rnLabeScheduledSleepTime",
    sz_4_26:
      "rnLabelTitleScheduleList",
    sz_4_28:
      "rnLabelTxtEmptySchedule",




    sz_4_30:
      "rnLabelBtnReturn",
    sz_4_32:
      "rnLabelBtnSaveChange",
    sz_4_34:
      "rnLabelBtnRepeatTImes",
    sz_4_36:
      "rnLabelStartTimeDescription",
    sz_4_38:
      "rnLabelEndTimeDescription",
    sz_4_40:
      "rnLabelOnce",
    sz_4_42:
      "rnLabelWorkingDays",
    sz_4_44:
      "rnLabelCustom",
    sz_4_45:
      "rnLabelTiTleStartEndTime",
    sz_4_47:
      "rnLabelHours",
    sz_4_49:
      "rnLabelBtnCancel",
    sz_4_95:
      "rnLabelTitleCustom",
    sz_4_97:
      "rnLabelMonCheckbox",
    sz_4_99:
      "rnLabelTueCheckbox",
    sz_4_101:
      "rnLabelWedCheckbox",
    sz_4_103:
      "rnLabelThurCheckbox",
    sz_4_105:
      "rnLabelFriCheckbox",
    sz_4_107:
      "rnLabelSatCheckbox",
    sz_4_109:
      "rnLabelSunCheckbox",
    sz_4_111:
      "rnLabelBtnOK",
    sz_4_21:
      "rnLabeTitleSleepSetting",
    sz_4_23:
      "rnLabelSleepSwitch",
    sz_4_25:
      "rnLabelBtnReturn",

    sz_4_29:
      "rnLabelBtnAdd",




    sz_4_31:
      "rnLabelTitleSchedulePriod",
    sz_4_33:
      "rnLabelRepeat",
    sz_4_35:
      "rnLabelStartTime",
    sz_4_37:
      "rnLabelEndTime",
    sz_4_39:
      "rnLabelTittleRepeatOptions",
    sz_4_41:
      "rnLabelEveryDay",
    sz_4_43:
      "rnLabelHolidays",


    sz_4_46:
      "rnLabelSelectTime",
    sz_4_48:
      "rnLabelMinutes",
    sz_4_50:
      "rnLabelBtnOK",
    sz_4_96:
      "rnLabelMon",
    sz_4_98:
      "rnLabelTue",
    sz_4_100:
      "rnLabelWed",
    sz_4_102:
      "rnLabelThur",
    sz_4_104:
      "rnLabelFri",
    sz_4_106:
      "rnLabelSat",
    sz_4_108:
      "rnLabelSun",
    sz_4_110:
      "rnLabelBtnCancelv",
      "sz_5_1": "rnLabelBtnReturn",
      "sz_5_3": "rnLabelHomeMonitoringSetting",
      "sz_5_5": "rnLabelMonitoringTime",
      "sz_5_7": "rnLabelSensitivitySettings",
      "sz_5_9": "rnLabelAlarmInterval",
      "sz_5_11": "rnLabelAlarmIntervalStates",
      "sz_5_13": "rnLabelAlarmNotificationsDescription",
      "sz_5_15": "rnLabelEventNotificationType",
      "sz_5_17": "rnLabelTItleHomeSurveillancePeriod",
      "sz_5_19": "rnLabel24HourDescription",
      "sz_5_21": "rnLabelDaytimeDescription",
      "sz_5_23": "rnLabelNightTimeDescription",
      "sz_5_25": "rnLabelCustomizedDescription",
      "sz_5_27": "rnLabelTitleSelectTime",
      "sz_5_29": "rnLabelStartTime",
      "sz_5_31": "rnLabelEndTime",
      "sz_5_33": "rnLabelTiTleStartEndTime",
      "sz_5_35": "rnLabelHours",
      "sz_5_37": "rnLabelBtnCancel",
      "sz_5_39": "rnLabelTitleAlertSensitivity",
      "sz_5_41": "rnLabelHighDescription",
      "sz_5_43": "rnLabelLowDescription",
      "sz_5_45": "rnLabelAlarmTime",
      "sz_5_47": "rnLabelBtnOK",
      "sz_5_49": "rnLabelTitleEventNotificationType",
      "sz_5_51": "rnLabelMotionDetectedSwitch",
      "sz_5_53": "rnLabelPersonDetectedSwitch",
      "sz_5_55": "rnLabenSmartScenceSwitch",
      "sz_5_2": "rnLabeTitleHomeMonitoring",
      "sz_5_4": "rnLabelHomeMonitoringSwitch",
      "sz_5_6": "rnLabelMonitoringTimeStates",
      "sz_5_8": "rnLabelSensitivityStatus",
      "sz_5_10": "rnLabelAlarmIntervalDescription",
      "sz_5_12": "rnLabelAlarmNotifications",
      "sz_5_14": "rnLabelAlarmNotificationsSwitch",
      "sz_5_16": "rnLabelBtnReturn",
      "sz_5_18": "rnLabel24Hour",
      "sz_5_20": "rnLabelDaytime",
      "sz_5_22": "rnLabelNightTime",
      "sz_5_24": "rnLabelCustomized",
      "sz_5_26": "rnLabelBtnClose",
      "sz_5_28": "rnLabelBtnSaveChange",
      "sz_5_30": "rnLabelStartTimeDesctiption",
      "sz_5_32": "rnLabelEndTimeDescription",
      "sz_5_34": "rnLabelSelectTime",
      "sz_5_36": "rnLabelMinutes",
      "sz_5_38": "rnLabelBtnOK",
      "sz_5_40": "rnLabelHigh",
      "sz_5_42": "rnLabelLow",
      "sz_5_44": "rnLabelTitleAlarmInterval",
      "sz_5_46": "rnLabelBtnCancel",
      "sz_5_48": "rnLabelBtnReturn",
      "sz_5_50": "rnLabelMotionDetected",
      "sz_5_52": "rnLabelPersonDetected",
      "sz_5_54": "rnLabenSmartScence",
      "sz_6_1": "rnLabelBtnReturn",
      "sz_6_3": "rnLabelFaceManager",
      "sz_6_5": "rnLabelFaceIdentification",
      "sz_6_7": "rnLabelFaceIdentTips",
      "sz_6_9": "rnLabelFaceIdentSign",
      "sz_6_11": "rnLabelFaceIdentFuncTips",
      "sz_6_2": "rnLabelTitleAiSetting",
      "sz_6_4": "rnLabelBtnReturn",
      "sz_6_6": "rnLabelFaceIdentImg",
      "sz_6_8": "rnLabelFaceIdentInfo",
      "sz_6_10": "rnLabelFaceIdentShare",
      "sz_6_12": "rnLabelFaceIdentCloudBuy",
      "sz_7_1": "rnLabelBtnReturn",
      "sz_7_3": "rnLabelSDCardFeatures",
      "sz_7_5": "rnLabelSDCardStatusValue",
      "sz_7_7": "rnLabelTiTleManageStorage",
      "sz_7_9": "rnLabelRemaining",
      "sz_7_11": "rnLabelStorageOnOff",
      "sz_7_13": "rnLabelRecordingMode",
      "sz_7_15": "rnLabelFormatSDCard",
      "sz_7_17": "rnLabelUnmountDesciption",
      "sz_7_19": "rnLabelTitleRecordingMode",
      "sz_7_21": "rnLabelOnlyMotionDetectedDescription",
      "sz_7_23": "rnLabelOnlyMotionDetectedDescription",
      "sz_7_25": "rnLabelFormatSDCardDescription",
      "sz_7_27": "rnLabelBtnOK",
      "sz_7_29": "rnLabelUnmountDescription",
      "sz_7_31": "rnLabelBtnOK",
      "sz_7_2": "rnLabeTitleManageStorage",
      "sz_7_4": "rnLabelSDCardStatus",
      "sz_7_6": "rnLabelBtnReturn",
      "sz_7_8": "rnLabelTxtStatus",
      "sz_7_10": "rnLabelCapacity",
      "sz_7_12": "rnLabelStorageOnOffSwitch",
      "sz_7_14": "rnLabelRecordingStates",
      "sz_7_16": "rnLabelUnmountSDCard",
      "sz_7_18": "rnLabelImgStatus",
      "sz_7_20": "rnLabelOnlyMotionDetected",
      "sz_7_22": "rnLabelAlwaysRecord",
      "sz_7_24": "rnLabelTitleFormatSDCard",
      "sz_7_26": "rnLabelBtnCancel",
      "sz_7_28": "rnLabelTitleUnmountSDCard",
      "sz_7_30": "rnLabelBtnCancel",
      "sz_8_1": "rnLabelBtnReturn",
      "sz_8_3": "rnLabelBtnEdit",
      "sz_8_5": "rnLabelBtnSelectAll",
      "sz_8_7": "rnLabelAlbumThumbnail",
      "sz_8_9": "rnLabelVideoTime",
      "sz_8_11": "rnLabelImgNoFiles",
      "sz_8_13": "rnLabelAlertDelete",
      "sz_8_15": "rnLabelBtnOK",
      "sz_8_17": "rnLabeTitleImg",
      "sz_8_19": "rnLabelPicture",
      "sz_8_21": "rnLabelBtnDelete",
      "sz_8_23": "rnLabelAlertDelete",
      "sz_8_25": "rnLabelBtnOK",
      "sz_8_27": "rnLabeTitleImg",
      "sz_8_29": "rnLabelBtnPlay",
      "sz_8_29_1": "rnLabelBtnLcPause",
      "sz_8_31": "rnLabelBtnTimeProgressBar",
      "sz_8_31_1": "rnLabelBtnLcProgressBar",
      "sz_8_33": "rnLabelBtnMute",
      "sz_8_33_1": "rnLabelBtnLcMute",
      "sz_8_35": "rnLabelBtnFullscreen",
      "sz_8_35_1": "rnLabelBtnLcFull",
      "sz_8_37": "rnLabelBtnDelete",
      "sz_8_2": "rnLabeTitleAlbum",
      "sz_8_4": "rnLabelBtnCancelEdit",
      "sz_8_6": "rnLabelBtnDelete",
      "sz_8_8": "rnLabelAlbumVideoTag",
      "sz_8_10": "rnLabelVideoSelect",
      "sz_8_12": "rnLabelTxtNoFiles",
      "sz_8_14": "rnLabelBtnCancel",
      "sz_8_16": "rnLabelReturn",
      "sz_8_18": "rnLabelAlbumNum",
      "sz_8_20": "rnLabelBtnShare",
      "sz_8_22": "rnLabelBtnFloatPlay",
      "sz_8_24": "rnLabelBtnCancel",
      "sz_8_26": "rnLabelBtnCancel",
      "sz_8_28": "rnLabelBtnReturn",
      "sz_8_30": "rnLabelBtnStartTime",
      "sz_8_30_1": "rnLabelBtnLcStartTime",
      "sz_8_32": "rnLabelBtnEndTime",
      "sz_8_32_1": "rnLabelBtnLcEndTime",
      "sz_8_34": "rnLabelBtnSpeed",
      "sz_8_34_1": "rnLabelBtnLcSpeed",
      "sz_8_36": "rnLabelBtnShare",
      "sz_8_38": "rnLabelBtnFullPlay",
      "sz_8_38_1": "rnLabelBtnLcFullPause",
      "sz_8_40": "rnLabelBtnFullTimeProgressBar",
      "sz_8_40_1": "rnLabelBtnLcFullProgressBar",
      "sz_8_42": "rnLabelBtnFullMute",
      "sz_8_42_1": "rnLabelBtnLcFullMute",
      "sz_8_44": "rnLabelBtnSwitchVertical",
      "sz_8_44_1": "rnLabelBtnLcFullFull",
      "sz_8_46": "rnLabelBtnDelete",
      "sz_8_46_1": "rnLabelBtnLcFullDelete",
      "sz_8_48": "rnLabelBtnCancel",
      "sz_8_39": "rnLabelBtnFullStartTime",
      "sz_8_39_1": "rnLabelBtnLcFullStartTime",
      "sz_8_41": "rnLabelBtnFullEndTime",
      "sz_8_41_1": "rnLabelBtnLcFullEndTime",
      "sz_8_43": "rnLabelBtnFullSpeed",
      "sz_8_43_1": "rnLabelBtnLcSpeedFull",
      "sz_8_45": "rnLabelBtnFullShare",
      "sz_8_45_1": "rnLabelBtnLcFullShare",
      "sz_8_47": "rnLabelAlertDelete",
      "sz_8_49": "rnLabelBtnOK",


    hk_2_3:
      "rnLabelBtnEdit",
    hk_2_5:
      "rnLabelBtnSelectAll",
    hk_2_7:
      "rnLabelVideoSelect",
    hk_2_9:
      "rnLabelBtnSave",
    hk_2_15:
      "rnLabelBtnSomeoneMove",
    hk_2_17:
      "rnLabelAlertDelete",
    hk_2_19:
      "rnLabelBtnOK",
    hk_2_4:
      "rnLabelBtnCancelEdit",
    hk_2_6:
      "rnLabelVideoThumbnail",
    hk_2_10:
      "rnLabelBtnDelete",
    hk_2_12:
      "rnLabelTxtNoFiles",
    hk_2_14:
      "rnLabelBtnAllEvents",
    hk_2_16:
      "rnLabelPictureChange",
    hk_2_18:
      "rnLabelBtnCancel",
    hk_2_20:
      "rnLabelDownloading",

    hk_3_1:
      "rnLabelBtnReturn",
    hk_3_3:
      "rnLabelBtnEdit",
    hk_3_5:
      "rnLabelBtnFloatPlay",
    hk_3_7:
      "rnLabelBtnStartTime",
    hk_3_9:
      "rnLabelBtnEndTime",
    hk_3_11:
      "rnLabelBtnSnapshot",
    hk_3_13:
      "rnLabelBtnFullscreen",
    hk_3_15:
      "rnLabelBtnSelectAll",
    hk_3_19:
      "rnLabelBtnSave",
    hk_3_27:
      "rnLabelDownloading",
    hk_3_29:
      "rnLabelBtnCancel",
    hk_3_2:
      "rnLabeTxtlTitle",
    hk_3_4:
      "rnLabelBtnThumbnail",
    hk_3_6:
      "rnLabelBtnPlay",
    hk_3_8:
      "rnLabelBtnTimeProgressBar",
    hk_3_10:
      "rnLabelBtnMute",
    hk_3_10_1:
      "rnLabelBtnMute",
    
    hk_3_12:
      "rnLabelBtnSpeed",
    hk_3_14:
      "rnLabelBtnCancelEdit",
    hk_3_20:
      "rnLabelBtnDelete",
    hk_3_28:
      "rnLabelAlertDelete",
    hk_3_30:
      "rnLabelBtnOK",


    //看家
    kj_1_1:
      "rnLabelBtnReturn",

    kj_1_5:
      "rnLabelDateWeek",

    kj_1_9:
      "rnLabelEventTypeRing",
    kj_1_13:
      "rnLabelEventTypeName",
    kj_1_15:
      "rnLabelEventTypeName",
    kj_1_17:
      "rnLabelEventNoData",
    kj_1_2:
      "rnLabeTxtlTitle",

    kj_1_8:
      "rnLabelMoreRecord",
    kj_1_12:
      "rnLabelEventThumbnail",
    kj_1_14:
      "rnLabelEventTypeName",
    kj_1_16:
      "rnLabelBtnCancel",
    sz_4_74:
      "rnLabelBtnReturn",
    sz_4_76:
      "rnLabelFullColorLowLight",

    sz_4_80:
      "rnLabelTxtBeforeActivation",
    sz_4_82:
      "rnLabelTxtAfterActivation",
    sz_4_84:
      "rnLabelAlwaysOn",
    sz_4_86:
      "rnLabelInfraredDescription",
    sz_4_88:
      "rnLabelTxtNormalMode",
    sz_4_90:
      "rnLabelTxtInfraredImaging",
    sz_4_75:
      "rnLabeTitleNightSettings",
    sz_4_77:
      "rnLabelFullColorLowLightSwitch",
    sz_4_79:
      "rnLabelImgBeforeActivation",
    sz_4_81:
      "rnLabelImgAfterActivation",
    sz_4_83:
      "rnLabelAutoSwitch",
    sz_4_85:
      "rnLabelAlwaysOff",
    sz_4_87:
      "rnLabelImgNormalMode",
    sz_4_89:
      "rnLabelImgInfraredImaging",
    hk_1_1:
      "rnLabelBtnReturn",
    hk_1_3:
      "rnLabelBtnEdit",
    hk_1_5:
      "rnLabelBtnSelectAll",


    hk_1_9:
      "rnLabelVideoSelect",
    hk_1_11:
      "rnLabelVideoEvent",
    hk_1_13:
      "rnLabelBtnDelete",
    hk_1_15:
      "rnLabelBtnCancel",
    hk_1_17:
      "rnLabelDownloading",
    hk_1_19:
      "rnLabelTxtNoFiles",
    hk_1_2:
      "rnLabeTxtlTitle",
    hk_1_4:
      "rnLabelBtnCancelEdit",
    hk_1_8:
      "rnLabelVideoThumbnail",
    hk_1_10:
      "rnLabelVideoTime",
    hk_1_12:
      "rnLabelBtnSave",
    hk_1_14:
      "rnLabelAlertDelete",
    hk_1_16:
      "rnLabelBtnOK",
    hk_1_18:
      "rnLabelImgNoFiles",

    kj_2_1:"rnLabelBtnReturn",
    kj_2_3:"rnLabelBtnShare",
    kj_2_5:"rnLabelCloudStorageHint",
    kj_2_17:
      "rnLabelEventThumbnail",
    kj_2_19:
      "rnLabelBtnThumbnail",
    kj_2_21:
      "rnLabelAlertDelete",
    kj_2_23:
      "rnLabelWXshare",
    kj_2_25:
      "rnLabelBtnFullThumbnail",
    kj_2_27:
      "rnLabelBtnFullShare",
    kj_2_29:
      "rnLabelBtnFullDelete",
    kj_2_33:
      "rnLabelBtnFullTimeProgressBar",

    kj_2_2:
      "rnLabelBtnSnapshot",
    kj_2_4:
      "rnLabelBtnSave",
    kj_2_6:
      "rnLabelBtnDelete",
    kj_2_18:
      "rnLabelTVPlaying",
    kj_2_20:
      "rnLabelAlertDelete",
    kj_2_22:
      "rnLabelBtnOK",
    kj_2_24:
      "rnLabelBtnReturnVertical",
    kj_2_26:
      "rnLabelBtnFullSnapshot",
    kj_2_28:
      "rnLabelBtnFullSave",
    kj_2_30:
      "rnLabelCloudStorageHint",
      "kj_2_37": "rnLabelViewFrame",
      "kj_2_38": "rnLabelFullViewFrame",
  
  }
};

// export const DescriptionConstants = new LocalizedStrings(strings);

let DescriptionConstants = {};
let devOpen = false;
StorageKeys.AUTOMATIC_MODE
  .then((res) => {
    if (res) {
      devOpen = true;
      DescriptionConstants = new LocalizedStrings(dev_strings);
    } else {
      DescriptionConstants = new LocalizedStrings(strings);
    }
  })
  .catch((err) => {
  });


export { DescriptionConstants, devOpen };


export const CAMERA_CONTROL_SIID = 2;
export const CAMERA_POWER_PIID = 1;
export const NIGHT_VISION_PIID = 3;
export const FULL_COLOR_PIID = 6;
export const RECORD_MODE_PIID = 7;
export const IMAGE_ROLLOVER_PIID = 2;
export const MOTION_TRACK_PIID = 8;
export const TIME_WATERMARK_PIID = 9;

export const STATUS_LIGHT_SIID = 3;
export const STATUS_LIGTH_PIID = 1;

export const CAMERA_SDCARD_SIID = 4;
export const CAMERA_SDCARD_STATUS_PIID = 1;
export const CAMERA_SDCARD_TOTAL_SPACE_PIID = 2;
export const CAMERA_SDCARD_FREE_SPACE_PIID = 3;
export const CAMERA_SDCARD_USED_SPACE_PIID = 4;
export const CAMERA_SDCARD_FORMAT_ACTION = 1;
export const CAMERA_SDCARD_EJECT_ACTION = 2;
// export const ALARM_INTERVAL_PIID = 2;
// export const DETECTION_SENSITIVITY_PIID = 3;
// export const START_TIME_PIID = 4;
// export const END_TIME_PIID = 5;

export const CAMERA_CONTROL_SEPC_PARAMS = [
  { did: Device.deviceID, siid: CAMERA_CONTROL_SIID, piid: NIGHT_VISION_PIID },
  { did: Device.deviceID, siid: CAMERA_CONTROL_SIID, piid: FULL_COLOR_PIID },
  { did: Device.deviceID, siid: CAMERA_CONTROL_SIID, piid: RECORD_MODE_PIID },
  { did: Device.deviceID, siid: CAMERA_CONTROL_SIID, piid: IMAGE_ROLLOVER_PIID },
  { did: Device.deviceID, siid: CAMERA_CONTROL_SIID, piid: MOTION_TRACK_PIID },
  { did: Device.deviceID, siid: CAMERA_CONTROL_SIID, piid: TIME_WATERMARK_PIID },
  { did: Device.deviceID, siid: CAMERA_CONTROL_SIID, piid: CAMERA_POWER_PIID }
];

export const CAMERA_SDCARD_SPEC_PARAMS = [
  { did: Device.deviceID, siid: CAMERA_SDCARD_SIID, piid: CAMERA_SDCARD_STATUS_PIID },
  { did: Device.deviceID, siid: CAMERA_SDCARD_SIID, piid: CAMERA_SDCARD_TOTAL_SPACE_PIID },
  { did: Device.deviceID, siid: CAMERA_SDCARD_SIID, piid: CAMERA_SDCARD_FREE_SPACE_PIID },
  { did: Device.deviceID, siid: CAMERA_SDCARD_SIID, piid: CAMERA_SDCARD_USED_SPACE_PIID },
  { did: Device.deviceID, siid: CAMERA_SDCARD_SIID, aiid: CAMERA_SDCARD_FORMAT_ACTION },
  { did: Device.deviceID, siid: CAMERA_SDCARD_SIID, aiid: CAMERA_SDCARD_EJECT_ACTION }
];

export const CAMERA_ALARM = [
  { did: Device.deviceID, siid: 7, piid: 5 }
];

export const CAMERA_ALARM_051 = [
  { did: Device.deviceID, siid: 7, piid: 2 }
]

export const CAMERA_ALARM_SETTING = [
  { did: Device.deviceID, siid: 5, piid: 3 }
];

Object.freeze(CAMERA_CONTROL_SEPC_PARAMS);

