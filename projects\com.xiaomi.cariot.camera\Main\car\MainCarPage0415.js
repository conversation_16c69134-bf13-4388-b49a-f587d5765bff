import React from 'react';
import {
  Animated,
  FlatList,
  Image,
  NativeModules,
  PixelRatio,
  ScrollView,
  Text,
  TouchableOpacity, TouchableWithoutFeedback,
  TouchableHighlight,
  ToastAndroid,
  View, Platform, SectionList, StatusBar, DeviceEventEmitter, findNodeHandle
} from 'react-native';
import Svg, { Line } from 'react-native-svg';

import { styles, Dividers, HeaderComponent, Constants, Radius, Button, Opacity, ClickableCard } from 'micariot-ui-sdk';
import { CarConstants, carStyles } from "./common/Styles";
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import Toast from "../components/ToastCar";
import Util, { DayInMilli } from "../util2/Util";
import StringUtil from "../util/StringUtil";
import ClickableCardButton from "../widget/ClickableCardButton";
import { DarkMode, Device, Package, PackageEvent, Service } from "miot";
import { DescriptionConstants } from "../Constants";
import Host from "miot/Host";
import { CALL_CAR_STATE, CALL_COMMAND, SCREEN_WIDTH } from "../util2/Const";
import dayjs from "dayjs";
import native from 'miot/native';
import DateFilterViewForCar from "../alarm/components/DateFilterViewForCar";
import NearHandDialog from "./widget/NearHandDialog";
import Video from "react-native-video";
import LogUtil from "../util/LogUtil";
import TrackUtil from "../util/TrackUtil";
import SdcardEventLoader from "../framework/sdcard/SdcardEventLoader";
import SdFileManager from "../sdcard/util/SdFileManager";
import CloudVideoCarUtil, { CLOUD_VIDEO_STATUS } from "../sdcard/util/CloudVideoCarUtil";
import CameraPlayer, { MISSCommand_ECO } from "../util/CameraPlayer";
import Singletons from "../framework/Singletons";
import { MISSCommand, MISSConnectState, MISSError } from "miot/service/miotcamera";
import VersionUtil from '../util/VersionUtil';
import TrackConnectionHelper from "../util/TrackConnectionHelper";
import DirectionView, { DirectionViewConstant } from "../ui/DirectionView";
import CameraConfig from "../util/CameraConfig";
import StorageKeys from "../StorageKeys";
import base64js from "base64-js";
import AlarmUtil from "../util/AlarmUtil";
import ImageButton from "miot/ui/ImageButton";
import CameraRenderView, { MISSAudioChannel, MISSCodec, MISSDataBits } from "miot/ui/CameraRenderView";
import { VideoLineView } from "./widget/VideoLineView";
import { EVENT_TYPE } from "../sdcard/util/EventTypeConfig";
import SpecUtil from "../util/SpecUtil";
import Orientation from "react-native-orientation";
import LoadingCarView from "../ui/LoadingCarView";
import {calculateScaleParamByHumanFeature, calculateIoU} from "./VideoFrameFeature"

import {
  CAMERA_CONTROL_CAR_SIID,
  CAMERA_CONTROL_SLEEP_PIID, COCKPIT_SERVICE_RECORD_VOICE_PIID,
  COCKPIT_SERVICE_SIID,
  COCKPIT_SERVICE_STORAGE_SWITCH_PIID,
  COCKPIT_SERVICE_TEMPERATURE_STATUS,
  COCKPIT_SERVICE_WORK_MODE_PIID, MOTION_DETECTION_MOTION,
  MOTION_DETECTION_SIID
} from "../util/CarSpecConstant";
import AlarmUtilV2 from "../util/AlarmUtilV2";
import RectAngleCarView from "../ui/RectAngleCarView";
import { Dld_States } from "../framework/DldMgr";
import { BlurView } from "@react-native-community/blur";
import AlbumHelper, { SNAPSHOT_CAR_FREEZE } from "../util/AlbumHelper";

const timelinePlaybackEndListenerName = "onTimelinePlaybackEnd";
const kRDTDataReceiveCallBackName = 'rdtDataReceiveCallBack';

const dayOfYear = require('dayjs/plugin/dayOfYear');
const TAG = "MainCarPage";

const WEEKS = [LocalizedStrings["sun"], LocalizedStrings["mon"], LocalizedStrings["tue"], LocalizedStrings["wed"], LocalizedStrings["thu"], LocalizedStrings["fri"], LocalizedStrings["sat"]];
const MODE = {
  LIVE: 0,
  SD: 1,
  CLOUD: 2
};
const PLAYBACK_MODE = {
  SD: 0,
  CLOUD: 1
};
const TEMPERATURE_STATE = {
  NORMAL: 0,
  HIGH: 1,
  LOW: 2,
};
const MAX_ANGLE = 101;
const MIN_ANGLE = 1;
const MAX_ELEVATION = 101;
const MIN_ELEVATION = 1;

const VIDEO_WIDTH_PX = 3840;
const VIDEO_HEIGHT_PX = 2160;

export default class MainCarPage extends React.Component {
  static navigationOptions = (navigation) => {
    return {
      headerTransparent: true,
      header: null
    };
  };
  state = {
    currentMode: MODE.LIVE, // 0直播 1回看 2云存
    currentPlaybackMode: PLAYBACK_MODE.SD, // 回看模式 0 SD卡 1云存
    curDate: new Date(),
    liveSelected: true,
    dateData: [],
    isMoving: false,
    currentTime: 0,
    showDayList: false,
    showMoreDlg: false,
    showMoreSettingDlg: false,
    selectedDayIndex: 0,
    showErrorView: false,
    deviceError: false,
    showPoweroffView: false,
    showLoadingView: true,
    showPauseView: false,
    showSDCardError: false,
    isMute: true,
    // 休眠 车机端变为关机状态
    isSleep: false,
    pstate: -1,
    error: -1,
    cloudListData: [],
    sdListData: [],
    touchMovement: false,
    // eventTypeFlags: (EVENT_TYPE.Default | EVENT_TYPE.Pet | EVENT_TYPE.ChildDetected),
    eventTypeFlags: (EVENT_TYPE.Default | EVENT_TYPE.IgnoreEvent),
    videoPath: null,
    isVideoSaveOpen: true,
    // 存储开关是否开启
    videoSaveSwitch: false,
    // 声音录制开关
    recordVoiceSwitch: false,
    isFormatting: false,
    loginState: 0, // 0 车主登录 1 共驾人 2 代客 3未登录
    limitMode: false,  // 限制工作模式
    temperatureState: TEMPERATURE_STATE.NORMAL,  // 温度状态 0 正常 1高温 2低温(低温先不存在)
    loadingData: true,
    isLockOpen: false,
    hfScale: 1, // human feature params
    hfOffsetX: 0,
    hfOffsetY: 0,
    hfchan: 0,
    angle: 51,
    elevation: 51,
    savedVideoScale: 1,
    currentStopIndex: -1,
    isLoadSdcard: true,
    angleViewShowScale: false,
    showCameraAngleView: false,
    dotOpacity: new Animated.Value(0),
    isLoading: false,
    showBlurView: false
  };

  constructor(props) {
    super(props);
    this.dateTime = new Date();
    this.isPluginForeGround = true;// sdcard页面不存在跳到其他页面的情况
    this.isPageForeGround = true;
    this.isAppForeground = true;
    this.isUserPause = false;
    this.isFirstIn = true;
    this.isFirstReceiveFiles = true;
    this.destroyed = false;
    // 用于存储有哪些天有视频数据
    this.cloudDayList = [];
    this.sdcardDayList = [];
    this.isCloudDayFirstReceive = true;
    this.isSdcardDayFirstReceive = true;
    this.connRetry = 2;
    this.cloudOffset = 0;
    this.lockData = null;
    this.lastGetDataTime = new Date().getTime() / 1000;
    this.chanAnimator = new Animated.Value(0);
    this.keyIpCount = 12;
    this.removeIndexList = [];
    this.cloudDownloadGlobelIndex = 0;

    this.removeSDIndexList = [];
    this.sdDownloadGlobelIndex = 0;
    this.enablePtzRotation = false;
    this.weekPageIndex = 0;
    this.needRestartSDVideoPlay = false;
    this.blurTag = Math.random() * 10000;
    CameraPlayer.getInstance().bindConnectionCallback(this._connectionHandler);// 连接
    CameraPlayer.getInstance().bindP2pCommandCallback(this._p2pCommandHandler);// p2p连接
    CameraPlayer.getInstance().bindNetworkInfoCallback(this._networkChangeHandler);
    CameraPlayer.getInstance().bindPowerOffCallback(this._powerOffHandler);
    CameraPlayer.getInstance().bindWorkModeStateCallback(this._workModeCallback);
    CameraPlayer.getInstance().bindTemperatureModeStateCallback(this._temperatureCallback);
    CameraPlayer.getInstance().bindSDCallback(this._sdCallback);
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来 这里监听的是tabNavigation的事件，不是stackNavigation的事件。
      'didFocus',
      () => {
        console.log(TAG, "will focus");

        this.isPageForeGround = true;
        this.isPluginForeGround = true;
        this.isAppForeground = true;
        this.getWindowLevel();
        this.getSaveVideoSwitchState();
        if (!this.isFirstIn) {
          LogUtil.logOnAll("didFocusListener is not first in");
          this._onResume();
        }
        this.isFirstIn = false;
      }
    );

    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'willBlur',
      () => {
        console.log(TAG, "did blur");

        this.isPageForeGround = false;
        this._onPause();
        this.cameraRenderView && this.cameraRenderView.hidesSurfaceView();
      }
    );

    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      LogUtil.logOnAll(TAG, "packageEvent.didResume: isPluginForeground:" + this.isPluginForeGround + " isPageForeround:" + this.isPageForeGround);
      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }

      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      this.isAppForeground = true;// rnactivity调用了onresume
      if (this.isFirstIn) {
        LogUtil.logOnAll("packageDidResume but in the first in");
        return;
      }
      this._onResume();
    });

    this.willPauseListener = PackageEvent.packageWillPause.addListener(() => {
      LogUtil.logOnAll(TAG, "packageEvent.didPause: isPluginForeground:" + this.isPluginForeGround + " isPageForeround:" + this.isPageForeGround);
      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }
      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      this.isAppForeground = false;// rnactivity调用了onresume
      this._onPause();
    });

    this.timelinePlaybackEndListener = DeviceEventEmitter.addListener(timelinePlaybackEndListenerName, () => {
      // 走到这里说明连续回放结束了
      console.log("时间轴模式下，从点播切换成了直播 要暂停camera");
      this.toSdcardEnd();// 停止播放
    });

    this.sdcardFilesListner = SdcardEventLoader.getInstance().addListener((important) => {
      LogUtil.logOnAll(TAG, "SdcardEventLoader Listener", important);
      this.onGetFiles(important);// 刷新数据。
    });
  }

  registerWindowLevelListener() {
    native.MIOTHost.transCustomInfo('window_level_callback', {}, (code, result) => {
      if (code) {
        console.log(`window_level_callback: ${ JSON.stringify(result) }`);
        if (parseInt(result.data) > 0) {
          console.log(`window_level_callback high`);
          this.highWindowLevel();
        } else {
          // 无高层级
          console.log(`window_level_callback no high`);
          this.lowWindowLevel();
        }
        // callback只能使用一次，需反复调用
        this.registerWindowLevelListener();
      } else {
        console.log(`window_level_callback error ${ JSON.stringify(result) }`);
      }
    });
  }

  getWindowLevel() {
    native.MIOTHost.transCustomInfo('get_window_level', {}, (code, result) => {
      if (code) {
        console.log(`get window_level_callback: ${ JSON.stringify(result) }`);
        if (parseInt(result.data) > 0) {
          console.log(`get window_level_callback high`);
          this.highWindowLevel();
        } else {
          // 无高层级
          console.log(`get window_level_callback no high`);
        }
      } else {
        console.log(`get window_level_callback error ${ JSON.stringify(result) }`);
      }
    });
  }

  highWindowLevel() {
    // 某些情况下，就不处理冻帧逻辑了
    if (!this.isPageForeGround) {
      LogUtil.logOnAll(TAG, "highWindowLevel: not in home page");
      return;
    }
    if (this.state.isSleep) {
      LogUtil.logOnAll(TAG, "highWindowLevel: device is sleep");
      return;
    }
    if (this.state.showErrorView) {
      LogUtil.logOnAll(TAG, "highWindowLevel: show error");
    }
    if (this.state.showPlayErrorView) {
      LogUtil.logOnAll(TAG, "highWindowLevel: show play error");
    }

    if (this.state.limitMode) {
      LogUtil.logOnAll(TAG, "highWindowLevel: limit mode");
      return;
    }

    if (this.state.temperatureState == TEMPERATURE_STATE.HIGH) {
      LogUtil.logOnAll(TAG, "highWindowLevel: high temperature");
      return;
    }

    this._realStartSnapshot();

  }

  lowWindowLevel() {
    if (this.isPageForeGround) {
      this.setState({ showBlurView: false });
      if (this.state.currentMode === MODE.CLOUD) {
        // 如果是删除云存视频后，这里直接播放是无法正常播放云存的
        this._startQueryNetwork();
      } else {
        this.queryNetworkJob();
      }
    } else {
      console.log("ignore low window level")
    }

  }

  componentDidMount() {
    this.registerWindowLevelListener();
    // StorageKeys.CAR_STORAGE = true;
    CameraPlayer.getInstance().bindConnectionCallback(this._connectionHandler);// 连接
    CameraPlayer.getInstance().bindP2pCommandCallback(this._p2pCommandHandler);// p2p连接
    CameraPlayer.getInstance().bindNetworkInfoCallback(this._networkChangeHandler);
    CameraPlayer.getInstance().bindPowerOffCallback(this._powerOffHandler);
    CameraPlayer.getInstance().bindWorkModeStateCallback(this._workModeCallback);
    CameraPlayer.getInstance().bindTemperatureModeStateCallback(this._temperatureCallback);
    CameraPlayer.getInstance().bindSDCallback(this._sdCallback);
    console.log("===========Device.lastVersion",Device.lastVersion)
    Service.miotcamera.setTimelinePlaybackMode(false);
    this.getLocalSetting();
    this.createDayList();
    this.initMethod();
    this.getSaveVideoSwitchState();
    this.timeoutResetTag && clearTimeout(this.timeoutResetTag);
    this.timeoutResetTag = setTimeout(() => {
      // 5s后状态仍为正常请求回来，也没走异常，可能发生了不可预知的错误
      if (this.state.loadingData) {
        this.setState({ loadingData: false });
      }
    }, 5000);
  }

  initMethod() {
    this.getLoginState();
    // 查询设备状态是否处于关机（休眠状态）
    this.getDeviceSpecValue();
    // 获取云存相关数据
    // 获取云存数据不是那么重要，延迟个500ms再执行
    this.delayToRequestCloudData && clearTimeout(this.delayToRequestCloudData);
    this.delayToRequestCloudData = setTimeout(() => {
      this.initCloudData();
    }, 500);

    NativeModules.MHCameraSDK.release(Device.deviceID);
    this.getKeyIp();
    this.getSdcardState();
  }

  /**
   * @Author: byh
   * @Date: 2024/12/17
   * @explanation:
   * 获取登录状态
   * 0：车主
   * 1：共驾人
   * 2：代客
   * 3：访客
   * 非0、1则认为是未登录状态
   *********************************************************/
  getLoginState() {
    try {
      native.MIOTHost.transCustomInfo('user_type', null, (code, result) => {
        if (code) {
          console.log(`transCustomInfo: ${ JSON.stringify(result) }_${code}`);
          this.setState({ loginState: result.data });
          CameraConfig.carLoginState = result.data;
        } else {
          console.log(`transCustomInfo error ${ JSON.stringify(result) }`);
        }
      });
    } catch (e) {
      LogUtil.logOnAll(TAG, "getLoginState not support");
    }
  }

  getLocalSetting() {
    // 标记回看选中状态
    StorageKeys.CAR_TO_SHOW_CLOUD.then((res) => {
      let showCloud = false;
      if (typeof (res) === "string" || res == null) {
        showCloud = false;
      } else {
        showCloud = res;
      }
      this.setState({ currentPlaybackMode: showCloud ? PLAYBACK_MODE.CLOUD : PLAYBACK_MODE.SD });
    }).catch((error) => {
    });

    // StorageKeys.IS_PTZ_ROTATION_ENABLE.then((res) => { // 云台手势转动是否开启
    //   if (res == null || typeof (res) != 'boolean') {
    //     res = true;// 默认启用云台手势转动是否开启
    //     StorageKeys.IS_PTZ_ROTATION_ENABLE = true;
    //   }
    //   this.enablePtzRotation = res;
    // }).catch((error) => {
    //   console.log(error);
    //   this.enablePtzRotation = true;
    // });
  }

  toLogin() {
    try {
      native.MIOTHost.transCustomInfo('open_login_page', null, (code, result) => {
        if (code) {
          console.log(`transCustomInfo: ${ JSON.stringify(result) }`);
        } else {
          console.log(`transCustomInfo error ${ JSON.stringify(result) }`);
        }
      });
    } catch (e) {
      Toast._showToast("暂不支持登录调用");
    }

  }

  getKeyIp() {
    if (!this.state.showLoadingView) {
      this.setState({ showLoadingView: true });
    }
    Service.spec.getPropertiesValue([{did: Device.deviceID, siid: 20, piid: 11}])
    // AlarmUtilV2.getSpecPValue([{ sname: SIID_COCKPIT_SERVICE, pname: PIID_COCKPIT_LIVE_INFO }])
      .then((res) => {
        console.log("=============success", res);
        if (res[0].code === 0) {
          this.keyIpCount = 12;
          let value = JSON.parse(res[0].value);
          let params = {
            "ip": value.ip,
            "secretKey": value.key,
            "p2p_id": value.p2p_id,
            "init_string": value.init_string,
            "did": Device.deviceID
          };
          console.log("参数：", params);

          NativeModules.MHCameraSDK.deliveryIpAndSecretKey(JSON.stringify(params));
          this.queryNetworkJob();
        } else {
          this.errorToRequestAgain();
        }

      }).catch((err) => {
        LogUtil.logOnAll(TAG, "get live info error:",err);
        this.errorToRequestAgain();
        // this.setState({ showErrorView: true, errTextString: LocalizedStrings['device_error'] });
      });
  }

  errorToRequestAgain() {
    // 上电后，立马进来获取可能会失败
    // 重试10次吧
    LogUtil.logOnAll(TAG, "errorToRequestAgain", this.keyIpCount);
    if (this.keyIpCount > 0) {
      this.keyIpCount--;
      this.delayToRequestKeyIp && clearTimeout(this.delayToRequestKeyIp);
      this.delayToRequestKeyIp = setTimeout(() => {
        this.getKeyIp();
      }, 500);
    } else {
      this.setState({ showErrorView: true, errTextString: LocalizedStrings['device_error'] });
    }
  }

  startBlinkAnimation() {
    const { dotOpacity } = this.state;
    console.log("++++++++++++++++",dotOpacity);
    this.state.dotOpacity.stopAnimation();
    if (this.blinkAnimation) {
      this.blinkAnimation.stop();
    }
    // 定义闪烁动画
    this.blinkAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(dotOpacity, {
          toValue: 1,
          duration: 1000
        }),
        Animated.timing(dotOpacity, {
          toValue: 0,
          duration: 1000
        })
      ])
    );
    this.blinkAnimation.start((ss)=>{
      console.log("+++++++++blinkAnimation+++++",ss)
    }); // 开始动画
  }

  getSdcardState() {
    CameraPlayer.getInstance().getSdcardStatusDirect()
      .then(({ sdcardCode }) => {
        this.setState({
          sdcardCode: sdcardCode,
          isFormatting: sdcardCode == 4,
          isLoadSdcard: false
        });
        if (sdcardCode === 4) {
          // 格式化中 需要去轮询SD卡状态，待SD卡正常后
          this._formatSdCardTimes = 0;
          this.getInfoIntervalID && clearInterval(this.getInfoIntervalID);
          this.getInfoIntervalID = setInterval(() => {
            this._formatSdCardTimes++;
            console.log("+++++++++++++++++getSdcardState===========")
            this._getFormatStatus();
          }, 5000);
        }

      })
      .catch(({ sdcardCode, error }) => {
        console.log("request sdcard status error", error);
        if (typeof (sdcardCode) === 'number' && sdcardCode >= 0) {
          this.setState({
            sdcardCode: sdcardCode,
            isLoadSdcard: false
          });
        } else {
          this.setState({
            isLoadSdcard: false
          });
        }
      });
  }

  _getFormatStatus() {
    CameraPlayer.getInstance().getSdcardStatusDirect()
      .then(({ sdcardCode }) => {
        LogUtil.logOnAll("format", "sdcardCode", sdcardCode);
        if (sdcardCode == 0) {
          this.setState({
            sdcardCode: 0,
            isFormatting: false
          });
          clearInterval(this.getInfoIntervalID);
          this.getInfoIntervalID = 0;
          Toast.success('sds_format_success');
          this.sdcardNoExitCodeTimes = 0;
          this.formatingSdcard = false;
          SdFileManager.getInstance().clearSdcardFileList();
          this.isSdcardDayFirstReceive = true;
          // 获取循环录制天数
          this._loadSdfiles();
          // this.getCardRecordDuration();
        } else if (sdcardCode == 9) {
          // 格式化后卡
          this.setState({
            sdcardCode: 9,
            isFormatting: false
          });
          clearInterval(this.getInfoIntervalID);
          this.getInfoIntervalID = 0;
          Toast.success('sds_format_success');
          this.sdcardNoExitCodeTimes = 0;
          this.formatingSdcard = false;
          SdFileManager.getInstance().clearSdcardFileList();
          this.isSdcardDayFirstReceive = true;
          this._loadSdfiles();
          // this.getCardRecordDuration();
        } else if (sdcardCode == 4) {
          this.setState({
            sdcardCode: 4,
          });
          this.sdcardNoExitCodeTimes = 0;
        } else if (sdcardCode == 1 && this.sdcardNoExitCodeTimes <= 10) {//部分平台 格式化后，会有一会的状态1 需要做一下兼容,连续10次都是1才认为格式化失败了。
          this.sdcardNoExitCodeTimes++;
        } else {
          if (this._formatSdCardTimes > 36) {
            this.sdcardNoExitCodeTimes = 0;
            LogUtil.logOnAll("sds_format_fail reason: 5555555");
            Toast.fail('sds_format_fail');
            clearInterval(this.getInfoIntervalID);
            this.getInfoIntervalID = 0;
            this.setState({
              sdcardCode: sdcardCode,
              isFormatting: false
            });
            this.formatingSdcard = false;
          }
        }
      })
      .catch((err) => {
        if (!CameraConfig.supportSDCardV2(Device.model) || this._formatSdCardTimes > 36) {
          LogUtil.logOnAll("sds_format_fail reason: 1111111 err=", JSON.stringify(err));
          Toast.fail('sds_format_fail');
          clearInterval(this.getInfoIntervalID);
          this.getInfoIntervalID = 0;
          this.formatingSdcard = false;
          this.setState( {isFormatting: false });
        }
      });

  }

  componentWillUnmount() {
    LogUtil.logOnAll(TAG, "componentWillUnmount do");
    this.destroyed = true;
    try {
      this._stopAll(false, false);
    } catch (exception) {

    }
    // 单独领出来
    CameraPlayer.getInstance().bindConnectionCallback(null);

    NativeModules.MHCameraSDK.release(Device.deviceID);
    this.getInfoIntervalID && clearInterval(this.getInfoIntervalID);
    this.mHFAnimationInterval && clearInterval(this.mHFAnimationInterval);
    this.delayToRequestKeyIp && clearTimeout(this.delayToRequestKeyIp);
    this.delayToPlaySdVideo && clearTimeout(this.delayToPlaySdVideo);
    this.scrollTimeout1 && clearTimeout(this.scrollTimeout1);
    this.resetShowPtzEndToast && clearTimeout(this.resetShowPtzEndToast);
    this.sdDelayToRequestFile && clearTimeout(this.sdDelayToRequestFile);
    this.delayToRequestEvent && clearTimeout(this.delayToRequestEvent);
    this.delayToHideDialog && clearTimeout(this.delayToHideDialog);
    this.delayToRequestCloudData && clearTimeout(this.delayToRequestCloudData);
    this.delayToRefreshLineView && clearTimeout(this.delayToRefreshLineView);
    this.timeoutResetTag && clearTimeout(this.timeoutResetTag);
    this.didFocusListener && this.didFocusListener.remove();
    this.didBlurListener && this.didBlurListener.remove();
    this.willPauseListener && this.willPauseListener.remove();
    this.didResumeListener && this.didResumeListener.remove();
    this.timelinePlaybackEndListener && this.timelinePlaybackEndListener.remove();
    this.sdcardFilesListner && this.sdcardFilesListner.remove();
    this.blinkAnimation && this.blinkAnimation.stop();
    CameraPlayer.getInstance().bindWorkModeStateCallback(null);
    CameraPlayer.getInstance().bindTemperatureModeStateCallback(null);
    CameraPlayer.getInstance().bindP2pCommandCallback(null);// p2p连接
    CameraPlayer.getInstance().bindSDCallback(null);
    CameraPlayer.getInstance().stopPlaybackTimestampInterval();// 停止抓取
    CameraPlayer.getInstance().bindPlaybackTimestampCallback(null);

  }

  getSaveVideoSwitchState() {
    let timestamp = new Date().getTime();
    if (timestamp - this.lastRequestSaveVideoSwitchTime < 1000) {
      console.log(TAG, "request storage switch to fast return");
      return;
    }
    this.lastRequestSaveVideoSwitchTime = timestamp;
    let params = [{ did: Device.deviceID, siid: COCKPIT_SERVICE_SIID, piid: COCKPIT_SERVICE_STORAGE_SWITCH_PIID }];
    if (CameraConfig.supportDivideStorage()) {
      params.push({ did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: MOTION_DETECTION_MOTION });
    }
    if (CameraConfig.supportVoiceMute()) {
      params.push({ did: Device.deviceID, siid: COCKPIT_SERVICE_SIID, piid: COCKPIT_SERVICE_RECORD_VOICE_PIID });
    }
    Service.spec.getPropertiesValue(params)
      .then((res) => {
        let isCloudOpen = false;
        let isOpen = false;
        let isRecordVoice = false;
        if (CameraConfig.supportDivideStorage()) {
          // 存储开关分开
          if (res[0].code === 0) {
            isOpen = res[0].value;
          }
          if (res[1].code === 0) {
            isCloudOpen = res[1].value;
          }
          if (CameraConfig.supportVoiceMute()) {
            isRecordVoice = res[2].value;
          }
        } else {
          if (res[0].code === 0) {
            isOpen = res[0].value;
          }
          if (CameraConfig.supportVoiceMute()) {
            isRecordVoice = res[1].value;
          }
          // 没这个玩意就默认开启的吧
          isCloudOpen = true;
        }
        let videoStorageOpen = CameraConfig.supportDivideStorage() ? (isOpen || isCloudOpen) : isOpen;
        console.log(TAG, "==============",videoStorageOpen,isOpen,isCloudOpen)
        let stateProps = { videoSaveSwitch: videoStorageOpen, recordVoiceSwitch: isRecordVoice };
        if (videoStorageOpen) {
          // 如果是开的，本地的标记位就记为true
          StorageKeys.CAR_STORAGE = true;
          stateProps.isVideoSaveOpen = true;
        }
        this.setState(stateProps);
        if (!videoStorageOpen) {
          StorageKeys.CAR_STORAGE.then((res) => {
            console.log("CAR_STORAGE:", res);
            if (typeof (res) === "string" || res == null) {
              this.setState({ isVideoSaveOpen: false });
            } else {
              this.setState({ isVideoSaveOpen: res });
            }
          }).catch((error) => {
            console.log("CAR_STORAGE error:", error);
          });
        }

      }).catch((err) => {

      });

  }

  createDayList() {
    let daysArr = this.genDateItem(false, 30);
    this.cloudDayList = JSON.parse(JSON.stringify(daysArr));
    let daysSdcardArr = this.genDateItem(false, 60);
    this.sdcardDayList = JSON.parse(JSON.stringify(daysSdcardArr));
    this.setState(() => {
      return { dateData: daysArr };
    }, () => {
      // this.refreshTopDateView(this.state.currentTime);
    });
  }

  initCloudData() {
    CloudVideoCarUtil.setCloudFilesReceivedCallback(this._bindFilesHandler);// 收到了数据  通知刷新
    // 不区分云存，非云存，都拉取视频数据
    CloudVideoCarUtil.fetchCloudVideoDataSerials(Device.deviceID, Device.model, true);
  }

  genDateItem(aVip = false, aSaveLength = 7) {
    let dateItems = [];
    let today = new Date();
    let todayIndex = 0;
    let len = Math.max(aSaveLength + 1, 7);
    console.log("genDateItem", len, aVip, aSaveLength, DayInMilli);
    // 保证从周日开始，那么结束一定是周六。如果今天不是周六，将today向后移动至周六。
    let mls = today.getTime(); // 修改默认值
    if (today.getDay() !== 6) {
      todayIndex = 6 - today.getDay();
      let tempDay = today.getDate() + (6 - today.getDay());
      mls = today.setDate(tempDay);
    }

    for (let i = 0; i < len; i++) {
      let date = today.getDate() - i;
      let tgt = new Date(mls);
      let timestamp = tgt.setDate(date);

      if (i === len - 1 && tgt.getDay() !== 0) {
        console.log('补充', tgt.getDay());
        len += tgt.getDay();
      }
      let dateStr = tgt.getDate().toString();
      // let dateKey = dayjs.unix(timestamp / 1000).format("d" + LocalizedStrings["yyyymmdd"]);
      let dateKey = dayjs.unix(timestamp / 1000).format("MMDD");

      let item = {
        date: dateStr,
        wkDay: Util.getMoment(tgt.getTime() / 1000).format("dd"),
        ts: timestamp,
        startTimestamp: timestamp,
        key: dateKey,
        // enabled: this.state.callData.length > 0 ? this.state.callData.findIndex(item=> item.title == dateKey) > -1 : false,
        enabled: false,
        selected: (i === todayIndex) ? true : false
      };
      // dateItems.unshift(item);
      dateItems.push(item);
    }
    return dateItems;
  }


  render() {
    dayjs.extend(dayOfYear);

    return (
      <View style={ carStyles.mediumLargeContainerStyle }>
        <HeaderComponent
          switchValue={ !this.state.isSleep }
          onSwitchChange={ (value) => {
            console.log(`HeaderComponent switch value is ${ value }`);
            // 休眠关机
            this._hidePlayToolBarLater();
            if (this.state.isSleep) {
              this._toggleSleep(false);
            } else {
              this._toggleSleep(true);
            }

          } }
          type={ "small" }
          title={ Device.name ? (Device.name.length > 15 ? `${ Device.name.substr(0, 15) }...` : Device.name) : "" }
          // subTitle={"已关机"}
          style={ { height: 160, paddingTop: 8 } }
          disableSwitch={ this.state.temperatureState === TEMPERATURE_STATE.HIGH || this.state.limitMode }
          onClosePress={ () => {
            console.log("HeaderComponent onClosePress");
            Package.exit();
          } }
        />
        <ScrollView style={ [{ paddingBottom: 36 }] } scrollEnabled={ true } showsVerticalScrollIndicator={ true }>

          { this.renderVideoAreaView() }

          { this.renderDayIndicator() }
          { this.renderBottomOperationView() }
          { this.renderBottomErrorView() }
          {/*{ this.renderBottomOperationViewTest() }*/}

          { this.renderProxyCustomerView() }
          { this.renderStorageUotOperateOnceView() }
          { this._renderMoreItemDialog() }
          { this._renderMoreSettingDialog() }

        </ScrollView>

      </View>

    );
  }

  renderVideoAreaView() {
    return (

      <View style={ [carStyles.videoContainerStyle, {
        // backgroundColor: styles.itemSecondaryStyle.backgroundColor,
        borderRadius: Radius.WidgetLevel,
        overflow: 'hidden'
      }] }>
        { this._renderVideoView() }
        { this.renderBlurView() }
        {/*{ this._renderAngleView() }*/}
        { this._renderCloudVideoView() }
        { this.renderFloatButtonsGroup() }
        { this._renderDayList() }
        { this._renderDirectionView() }
        { this._renderLoadingView() }
        { this._renderErrorDeviceOfflineView() }
        { this._renderRecordRedDot() }
        { this._renderPlayErrorView() }

      </View>
    );
  }

  renderBlurView() {
    if (!this.state.showBlurView) {
      return null;
    }
    let source = { uri: `${Host.file.storageBasePath}/${SNAPSHOT_CAR_FREEZE}?v=${this.blurTag}` }
    return(
      <View style={{ position: 'absolute', width: CarConstants.VIDEO_CONTENT_WIDTH, height: CarConstants.VIDEO_CONTENT_HEIGHT, justifyContent: 'center', alignItems: 'center' }}>
        <Image
          ref={(img) => { this.backgroundImage = img; }}
          // source={DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/car/blur_dark.png') : require('../../Resources/Images/car/blur_light.png')}
          source={source}
          // style={[{ width: 1155, height: 660, zIndex: 9 }]}
          style={[{ width: 1155, height: CarConstants.VIDEO_CONTENT_HEIGHT, zIndex: 9 }]}
          onLoadEnd={this.imageLoaded.bind(this)}
        />
        {
          this.state.viewRef ? <BlurView
            style={[{ position: 'absolute', width: CarConstants.VIDEO_CONTENT_WIDTH, height: CarConstants.VIDEO_CONTENT_HEIGHT, zIndex: 9 }]}
            viewRef={this.state.viewRef}
            blurType="light"
            blurAmount={10}
          /> : null
        }

        <Text style={{ position: 'absolute', zIndex: 10, color: 'xm#FFFFFF7A', fontSize: 28 }}>{LocalizedStrings['can_not_view']}</Text>

      </View>

    );
  }

  imageLoaded() {
    // this.setState({ viewRef: findNodeHandle(this.blurBg) });
    console.log("imageLoaded");
    setTimeout(() => {
      this.setState({ viewRef: findNodeHandle(this.backgroundImage) });
    },300)
  }

  _renderVideoView() {
    if (this.state.isSleep) {
      return null;
    }
    if (this.state.currentMode === MODE.CLOUD) {
      return null;
    }
    // console.log("return CameraRenderView: " + ((Platform.OS =="android" && this.evenLockScreen >0) && (!this.state.restoreOriFinished || !this.state.restoreOriFinished2)) +  ", evenLockScreen: " + this.evenLockScreen)
    let useWhiteBackground = this.state.darkMode || this.state.fullScreen ? false : this.state.isWhiteVideoBackground;
    return (
      <CameraRenderView
        ref={ (ref) => {
          this.cameraGLView = ref;
        } }
        maximumZoomScale={ 6.0 }
        style={ [{ width: CarConstants.VIDEO_CONTENT_WIDTH, height: CarConstants.VIDEO_CONTENT_HEIGHT }] }
        videoCodec={ MISSCodec.MISS_CODEC_VIDEO_H265 }
        audioCodec={ MISSCodec.MISS_CODEC_AUDIO_OPUS }
        audioRecordSampleRate={ CameraConfig.getCameraAudioSampleRate(Device.model) }
        audioRecordChannel={ MISSAudioChannel.FLAG_AUDIO_CHANNEL_MONO }
        audioRecordDataBits={ MISSDataBits.FLAG_AUDIO_DATABITS_16 }
        audioRecordCodec={ MISSCodec.MISS_CODEC_AUDIO_OPUS }
        fullscreenState={ this.state.fullScreen }
        // scale={ this.state.savedVideoScale }
        videoRate={ this.rateModel() }
        correctRadius={ CameraConfig.getCameraCorrentParam(Device.model).radius }
        osdx={ 0 }
        osdy={ 0 }
        doubleClickMaximumScale={3}
        useLenCorrent={ false }
        onVideoClick={ this._onVideoClick.bind(this) }
        onScaleChanged={ this._onVideoScaleChanged.bind(this) }
        onPTZDirectionCtr={ this._onVideoPTZDirectionCtr.bind(this) }
        onDoubleClickPosition={ this._onDoubleClickPosition.bind(this) }
        did={ Device.deviceID }
        isFull={ false }
        playRate={ this.state.currentMode === MODE.LIVE ? 24 : this.state.currentMode === MODE.SD ? 15 : 24 }
        whiteBackground={ true }
        recordingVideoParam={ {
          ...CameraConfig.getRecordingVideoParam(Device.model),
          isInTimeRecord: false,
          hasRecordAudio: true,
          iosConstantFps: 1,
          fps: -1
        } }
        // chanPositionParams={this.state.isLockOpen ? { scale: this.state.hfScale, offsetY: this.state.hfOffsetY, offsetX: this.state.hfOffsetX, chan: 0 }  : {scale: this.state.savedVideoScale, offsetX: 0.0, offsetY: 0.0, chan: 0}}
        disablePanGesture={ this.state.isLockOpen }
        disablePinchGesture={ this.state.isLockOpen }
        enableAIFrame={ this.state.enableAIFrame }
        accessible={ true }
        accessibilityLabel={ DescriptionConstants.zb_54 }
        onPositionChanged={(data) => {
          console.log("onPositionChanged",data);
        }}
      >
      </CameraRenderView>
    );
  }

  _renderRecordRedDot() {
    if (!this.state.videoSaveSwitch) {
      return null;
    }

    if (this.state.currentMode !== MODE.LIVE) {
      return null;
    }
    if (this.state.showErrorView) {
      return null;
    }
    if (this.state.showLoadingView) {
      return null;
    }

    if (this.state.isSleep) {
      return null;
    }

    if (this.state.limitMode) {
      return null;
    }

    if (this.state.temperatureState === TEMPERATURE_STATE.HIGH) {
      return null;
    }

    return (
      <View style={{ zIndex: 3, position: "absolute", top: 20, right: 32, flexDirection: 'row', alignItems: 'center' }}>
        <Animated.View style={{ backgroundColor: '#D33224', borderRadius: 6, height: 12, width: 12, opacity: this.state.dotOpacity }}></Animated.View>
        <Image style={{ width: 47, height: 29, marginLeft: 8 }} source={require('../../Resources/Images/car/car_record.png')}></Image>
        <Image style={{ width: 40, height: 40, marginLeft: 4 }} source={this.state.recordVoiceSwitch ? require('../../Resources/Images/car/car_volume_open.png') : require('../../Resources/Images/car/car_volume_close.png')}></Image>
      </View>
    );
  }
  rateModel() {
    if (CameraConfig.deviceFrameRate(Device.model)) {
      return 20;
    }
    // return 15;
    return 20;
  }

  _onVideoClick() {
    LogUtil.logOnAll(TAG, "live page onVideoClick",this.state.currentMode);
    if (!CameraPlayer.getInstance().isConnected()) {
      LogUtil.logOnAll(TAG, "_onVideoClick p2p lost");
    }

    if (this.state.isLockOpen) {
      LogUtil.logOnAll(TAG, "cloudVideo is in lock");
      return;
    }
    this.setState((state) => {
      return {
        showPlayToolBar: !this.state.showPlayToolBar,

        showDayList: false
      };
    }, () => {
      this._hidePlayToolBarLater();
    });
    console.log("click video view");
  }

  _onVideoScaleChanged(params) {
    let scale = params.nativeEvent?.scale;
    // console.log("++++++++++++++++scale:",scale);
    // 当返回有倍数时 清除定时器 并更新倍数 相当于防抖操作 一直触发事件就一直清空定时器
    if (scale) {
      clearTimeout(this.videoScaleTimer);

      this.videoScaleTimer = setTimeout(() => {
        // console.log("tick" + scale);
        this._updateScale(scale); // 更新倍数
      }, 0);
    }

    this._onReceiveVideoRenderedEvent(params);
    // 进行节流操作
    let endTime = Date.now();
    if ((endTime - this.startScaleTime) < 50) {
      // console.log('_onVideoScaleChanged', scale);
      return;
    }
    this.startScaleTime = endTime;

    this._updateScale(scale);
  }

  _updateScale(scale) {
    if (scale) {
      scale = Number(scale);

      if (scale < 1) {
        scale = 1;
      }

      if (this.angleViewTimeout) {// 隔一段时间就需要隐藏
        clearTimeout(this.angleViewTimeout);
        this.angleViewTimeout = null;
      }

      this.angleViewTimeout = setTimeout(() => {
        this.setState({ showCameraAngleView: false, angleViewShowScale: false });
      }, 3000);
      if (!this.state.fullScreen) {
        this.videoPortraitScale = scale;// 保存竖屏下的videoScale
      }
      // this.angleView?.setScale(scale);
      // if (!this.state.showCameraAngleView) {
      //   this.setState(() => {
      //     return { showCameraAngleView: true, angleViewShowScale: true };
      //   }, () => {
      //     this.angleView?.setScale(scale);
      //     if (scale > 1 && this.state.showPlayToolBar) {
      //       this.setState({ showPlayToolBar: false });
      //     } else if (scale == 1 && !this.state.showPlayToolBar) {
      //       this.setState({ showPlayToolBar: true });
      //     }
      //   });
      // }
      this.setState({
        videoScale: scale,
        // showCameraAngleView: true,
        // angleViewShowScale: true,
        showPlayToolBar: scale > 1 ? false : true
      });
      if ((Date.now() - this.startScaleReportTime) > 1000) {
        this.startScaleReportTime = Date.now();
        if (scale == 1) {
          TrackUtil.reportClickEvent('camera_ZoomOutFull_Num');
        } else if (scale > this.tmpScale) {
          TrackUtil.reportClickEvent('camera_ZoomIn_Num');
        } else {
          TrackUtil.reportClickEvent('camera_ZoomOut_Num');
        }
      } else {
        this.tmpScale = scale;
      }
    }
  }

  _onReceiveVideoRenderedEvent(params) {
    if (params && params.nativeEvent && params.nativeEvent.firstVideoFrame && Host.isAndroid) {
      console.log(TAG, "received firstVideoFrame");
      this.isFirstFrameReceived = true;
      this.setState({ showDefaultBgView: false, showLoadingView: false, whiteTitleBg: false, showPlayToolBar: true });

      // important !!!!渲染了第一帧数据，开始缩放, 避免没有收到的时候，就发送这个通知，导致UI出问题了。
      if (!Host.isAndroid) {// 只有android有这个通知。。。。
        return;
      }
      StorageKeys.VIDEO_SCALE.then((result) => {
        if (typeof (result) == "number") {
          if (this.savedVideoScale != result) {
            setTimeout(() => {
              this.setState({ savedVideoScale: result, videoScale: result });
            }, 50);
          }
        }
      })
        .catch((err) => {
          console.log(err);
        });


      TrackConnectionHelper.onFrameRendered();
      TrackConnectionHelper.report();
    }
  }

  _onVideoPTZDirectionCtr(params) {
    console.log("received PTZ direction control from CameraRenderView");
    if (!this.enablePtzRotation) {
      return;
    }
    if (this.state.currentMode !== MODE.LIVE) {
      return;
    }
    if (params && params.nativeEvent && params.nativeEvent.direction) {
      let direction = Number(params.nativeEvent.direction);
      if (this.isHorizontalPTZ && (direction == DirectionViewConstant.DIRECTION_UP || direction == DirectionViewConstant.DIRECTION_BOTTOM)) {
        return;
      }

      if (this.isReadonlyShared) {
        Toast.fail('cloud_share_hint');
        return;
      }
      LogUtil.logOnAll("screen drag causes ptz direction cmd");
      this._sendDirectionCmd(direction);
      this._sendDirectionCmd(DirectionViewConstant.CMD_OFF);
    }
  }

  _onDoubleClickPosition(params) {
    console.log("_onDoubleClickPosition",params.nativeEvent,VIDEO_WIDTH_PX,VIDEO_HEIGHT_PX,this.lockData);
    if (this.state.currentMode !== MODE.LIVE) {
      // 非直播，不处理双击事件
      return;
    }
    // 计算点坐标的千分比
    let xWidth = params.nativeEvent.x / VIDEO_WIDTH_PX * 1000;
    let yHeight = params.nativeEvent.y / VIDEO_HEIGHT_PX * 1000;

    if (this.lockData == null) {
      return;
    }
    // let humanData = this.lockData.filter((n) => n.type == 1 ||  n.type == 5);
    let humanData = this.lockData.filter((n) => n.type == 5);
    // 如果已经处于头像锁定
    // 1、点击区域无头像----解除锁定
    // 2、点击区域的头像ID不一致，更换锁定对象

    // 判断双击的位置，是否在符合条件的box中
    // 筛选出人形、人脸的数据，里面有其他数据比如宠物的过滤掉
    if (!humanData || humanData.length == 0) {
      LogUtil.logOnAll(TAG, "_onDoubleClickPosition no human data");

      if (this.state.isLockOpen) {
        this.releaseLockTimer && clearTimeout(this.releaseLockTimer);
        this.resetLockStatus();
      }
      return;
    }

    LogUtil.logOnAll(TAG, "_onDoubleClickPosition", xWidth, yHeight, humanData);

    let inBox = [];
    for (let i = 0; i < humanData.length; i++) {
      let data = humanData[i];
      let box = JSON.parse(data.box);
      if (xWidth >= box[0] && xWidth <= box[2] && yHeight >= box[1] && yHeight <= box[3]){
        inBox.push(data);
      }
    }

    if (inBox.length === 0) {
      LogUtil.logOnAll(TAG, "_onDoubleClickPosition not in box",inBox);
      if (this.state.isLockOpen) {
        this.releaseLockTimer && clearTimeout(this.releaseLockTimer);

        this.resetLockStatus();
      }
      return;
    }
    // 找score的最大的值
    const maxBox = inBox.reduce((maxObj, currentObj) => {
      return (currentObj.score > maxObj.score) ? currentObj : maxObj;
    }, inBox[0]);
    if (this.state.isLockOpen) {
      if (maxBox.id !== this.lastHumanId) {
        // 需要更换锁定对象
        this.shouldChangeToTheFace = true;
        this.lastHumanId = maxBox.id;
        LogUtil.logOnAll(TAG, `change lock ID：${this.lastHumanId}`);
      }
      return;
    }
    if (new Date().getTime() / 1000 - this.lastGetDataTime > 1) {
      // 1秒内已经没有新的信息过来
      return;
    }
    LogUtil.logOnAll(TAG, "_onDoubleClickPosition", maxBox);
    // 开启头像锁定
    this.lastHumanId = maxBox.id;
    LogUtil.logOnAll(TAG, `start lock ID：${this.lastHumanId}`);

    this.setState({isLockOpen: true});
    let result = calculateScaleParamByHumanFeature(maxBox);
    this.createHumanFeatureAnimation(this.lastResult == null ? { scale: 1, offsetX: 0, offsetY: 0 } : this.lastResult, result);//执行动画。
    this.lastResult = result;
    // 5000ms。启动5s定时
    this.delayResetLock();



  }

  _renderLoadingView() {
    if (!this.state.showLoadingView || this.state.showPoweroffView || this.state.showErrorView) {
      return null;
    }
    let backgroundColor = this.state.currentMode === MODE.LIVE || this.state.currentMode === MODE.CLOUD ? carStyles.videoLoadingStyle.backgroundColor : 'transparent';
    let loadingViewStyle = {
      zIndex: 3,
      position: "absolute",
      width: "100%",
      height: CarConstants.VIDEO_CONTENT_HEIGHT,
      bottom: 0,
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: backgroundColor
    };

    return (
      <View
        style={ loadingViewStyle }
        onPress={ () => {
          const initial = Orientation.getInitialOrientation();
          if (initial === 'PORTRAIT') { // 当为横屏时
            this.setState(() => {
              return {
                showPlayToolBar: !this.state.showPlayToolBar
              };
            }, () => {
              this._hidePlayToolBarLater();
            });
          }
        }
        }
      >
        <LoadingCarView
          type={'white'}
          style={ { width: 54, height: 54 } }
        />
        <Text
          style={ [styles.loadingFailedTextStyle, { marginTop: Constants.DEFAULT_TEXT_MARGIN_BOTTOM }] }>
          { LocalizedStrings["camera_loading2"] }
        </Text>
      </View>
    );
  }

  _renderErrorDeviceOfflineView() {
    if (!this.state.showErrorView && !this.state.showPoweroffView && !this.state.limitMode && this.state.temperatureState !== TEMPERATURE_STATE.HIGH) {
      return null;
    }

    if (!this.state.isSleep && !this.state.showErrorView && !this.state.limitMode && this.state.temperatureState !== TEMPERATURE_STATE.HIGH) {
      return null;
    }
    return (
      <View style={ {
        zIndex: 3,
        position: "absolute",
        bottom: 0,
        backgroundColor: carStyles.homeAbnormalStyle.backgroundColor,
        // backgroundColor: "#00ff00",
        // borderRadius: Radius.WidgetLevel,
        width: "100%",
        height: CarConstants.VIDEO_CONTENT_HEIGHT,
        display: "flex",
        justifyContent: "center",
        alignItems: "center"
      } }>
        <Image
          style={ [carStyles.videoErrorImageStyle, { marginBottom: Constants.DEFAULT_TEXT_MARGIN_BOTTOM }] }
          source={ DarkMode.getColorScheme() === 'dark' ? require('../../Resources/Images/car/car_video_abnormal.png') : require('../../Resources/Images/car/car_video_abnormal_light.png') }/>
        <Text style={ styles.loadingFailedTextStyle }>{ LocalizedStrings['no_content'] }</Text>
      </View>
    );
  }

  _renderPlayErrorView() {
    if (!this.state.showPlayErrorView) {
      return null;
    }

    return (
      <View
        style={{
          zIndex: 7,
          position: "absolute",
          bottom: 0,
          backgroundColor: carStyles.homeAbnormalStyle.backgroundColor,
          width: "100%",
          height: CarConstants.VIDEO_CONTENT_HEIGHT,
          display: "flex",
          justifyContent: "center", alignItems: "center" }}
      >
        <View
          style={{ display: "flex", alignItems: "center" }}
        >
          <Image
            style={carStyles.videoErrorImageStyle}
            source={ DarkMode.getColorScheme() === 'dark' ? require('../../Resources/Images/car/car_video_abnormal.png') : require('../../Resources/Images/car/car_video_abnormal_light.png') }/>
          <Text
            style={styles.loadingFailedTextStyle}>
            {this.currentNetworkState == 0 ? LocalizedStrings['common_net_error'] : LocalizedStrings['video_file_error']}
          </Text>
        </View>
      </View>
    );
  }

  renderFloatButtonsGroup() {
    if (this.state.currentMode === MODE.LIVE) {
      // 直播模式下，没有视频操作
      return null;
    }
    if (!this.state.showPlayToolBar) {
      return null;
    }

    if (this.state.showErrorView) {
      return null;
    }

    if (this.state.showLoadingView) {
      return null;
    }

    let toolBarStyle = {
      position: "absolute",
      top: '50%',
      left: '50%',
      bottom: 0,
      width: 408,
      height: 88,
      marginLeft: -204,
      marginTop: -44,
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: 'transparent'
    };
    return (
      <View style={ toolBarStyle }>
        <View style={ { width: '100%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' } }>
          <ImageButton
            style={ { width: 88, height: 88 } }
            source={ require('../../Resources/Images/car/playback_preview.png') }
            onPress={ () => {
              console.log("=========pre");
              // 播放上一个视频
              // this.timelineView && this.timelineView.onMovePrev();
              let currentItem = this.state.currentMode === MODE.SD ? this.sdCurrentPlayItem : this.cloudCurrentPlayItem;
              let currentItemInfo = this.state.currentMode === MODE.SD ? this.sdItemInfo : this.cloudItemInfo;
              this.videoLineView && this.videoLineView.onMovePrev(currentItem, currentItemInfo);
            } }
          />
          <ImageButton
            style={ { width: 88, height: 88 } }
            source={ this.state.isPlaying ? require('../../Resources/Images/car/playback_play.png') : require('../../Resources/Images/car/playback_stop.png') }
            onPress={ () => {
              console.log("=========start or stop");
              if (this.state.currentMode === MODE.SD) {
                this.isUserPause = this.state.isPlaying;
                this._startSdcardPlay(!this.state.isPlaying);
              } else if (this.state.currentMode === MODE.CLOUD) {
                this.isUserPause = this.state.isPlaying;
                this._startCloudPlay(!this.state.isPlaying);
              }
            } }
          />
          <ImageButton
            style={ { width: 88, height: 88 } }
            source={ require('../../Resources/Images/car/playback_next.png') }
            onPress={ () => {
              console.log("=========next");
              // this.timelineView && this.timelineView.onMoveNext();
              let currentItem = this.state.currentMode === MODE.SD ? this.sdCurrentPlayItem : this.cloudCurrentPlayItem;
              let currentItemInfo = this.state.currentMode === MODE.SD ? this.sdItemInfo : this.cloudItemInfo;
              this.videoLineView && this.videoLineView.onMoveNext(currentItem, currentItemInfo);
            } }
          />
        </View>
      </View>
    );

  }

  _renderDirectionView() {
    // 非直播模式
    if (this.state.currentMode != MODE.LIVE) {
      return null;
    }
    // 展示错误时
    if (this.state.showErrorView) {
      return null;
    }

    if (!this.state.showPlayToolBar) {
      return null;
    }

    if (this.state.isSleep) {
      return null;
    }

    let landscapeButtonGroupStyle = {
      position: "absolute",

      left: 32,
      bottom: 32,
      width: 256,
      height: 256,
      alignItems: "center",
      justifyContent: "center"

    };
    return (
      <View style={ landscapeButtonGroupStyle }
      >
        <TouchableOpacity
          style={ { position: "absolute", width: "100%", height: "100%" } }
        />
        <DirectionView
          isPortrait={ false }
          accessibilityLabel={ DescriptionConstants.zb_12 }
          ref={ (ref) => {
            this.directionView = ref;
          } }
          onActionDown={ () => {
            if (this.showPlayToolBarTimer) {
              clearTimeout(this.showPlayToolBarTimer);
              this.showPlayToolBarTimer = null;
            }
            if (this.state.isSleep) {
              Toast.fail(this.getPowerOffString());
              return;
            }
            if (this.isReadonlyShared) {
              Toast.fail("cloud_share_hint");
              return;
            }
            console.log('onActionDown 按下得时候 走进这里');

          } }
          onActionUp={ (off) => {
            if (this.isReadonlyShared) {
              return;
            }
            if (off) {
              this._sendDirectionCmd(DirectionViewConstant.CMD_OFF);
            }
            this._hidePlayToolBarLater();
            console.log('onActionUp 抬起得时候 走进这里');
          } }

          onClickDirection={ (type) => {
            if (this.isReadonlyShared) {
              return;
            }
            this._sendDirectionCmd(type);
            TrackUtil.reportClickEvent("Camera_direction_ClickNum"); // Camera_direction_ClickNum
          } }
        />
      </View>
    );
  }

  renderDashLineView() {
    let storeColor = DarkMode.getColorScheme() === 'dark' ? 'xm#FFFFFF4C' : 'xm#0000004C';
    return (
      <Svg
        width="100%"
        height="88"
      >
        <Line
          x1="0"
          y1="50%"
          x2="100%"
          y2="50%"
          stroke={ storeColor }
          strokeWidth="1"
          strokeDasharray="1,5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </Svg>
    );
  }

  renderLineView() {
    const { touchMovement } = this.state;
    // console.log("+===============",this.state.eventTypeFlags);
    return (
      <View style={ { position: 'absolute', flex: 1, width: '100%', height: 88 } }>
        {
          this.state.currentPlaybackMode === PLAYBACK_MODE.SD ?
          <VideoLineView
            ref={ (ref) => {
              this.videoLineView = ref;
            } }
            data={ this.state.sdListData }
            type={this.state.currentPlaybackMode}
            onScrollEnd={ this._onCenterValueChanged }
            scaleChange={this._onScaleChange}
            onItemPress={this.onLineVideoClick}
            isPlaying={this.state.currentMode === MODE.SD}
            isLoading={this.state.isLoading}
            highWindowFront={this.state.showBlurView}
          /> :
            <VideoLineView
              ref={ (ref) => {
                this.videoLineView = ref;
              } }
              data={ this.state.cloudListData }
              type={this.state.currentPlaybackMode}
              onScrollEnd={ this._onCenterValueChanged }
              scaleChange={this._onScaleChange}
              onItemPress={this.onLineVideoClick}
              isPlaying={this.state.currentMode === MODE.CLOUD}
              isLoading={this.state.isLoading}
              highWindowFront={this.state.showBlurView}

            />
        }

      </View>
      // <View style={ { position: 'absolute', height: 88 } }>
      //   <TimeScaleViewCar
      //     ref={ (ref) => {
      //       this.timelineView = ref;
      //     } }
      //     // onCenterValueChanged={this._onCenterValueChanged}
      //     onScrolling={ this._onScrolling }
      //     onScrollEnd={ this._onCenterValueChanged }
      //     landscape={ false }
      //     isCloud={ this.state.currentPlaybackMode === PLAYBACK_MODE.CLOUD }
      //     eventTypeFlags={ this.state.eventTypeFlags }
      //     isDisabled={ this.state.isEmpty ? true : false }
      //     touchMovement={ touchMovement }
      //   />
      // </View>
    );
  }

  renderLineViewV2() {
    const DATA = [
      { title: 'D', data: ['Devin', 'DenDi'] },
      { title: 'J', data: ['Jackson', 'James', 'Jillian', 'Jimmy', 'Joey', 'John', 'Joshua'] },
      { title: 'M', data: ['Matthew', 'Michael', 'Maria', 'Mateo', 'Matt', 'Max'] }
    ];
    const renderItem = ({ item }) => (
      <View style={ { padding: 10, marginVertical: 8 } }>
        <Text>{ item.duration + "-" + item.eventType }</Text>
      </View>
    );

    const renderSectionHeader = ({ section: { title } }) => (
      <View style={ {
        backgroundColor: carStyles.lineDayBg.backgroundColor,
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 29,
        width: 78,
        height: 30,
        borderRadius: 39
      } }>
        <Text>{ title }</Text>
      </View>
    );

    const renderFoot = () => (
      <View
        style={ { backgroundColor: '#f0f0f0', padding: 10, marginTop: 29, width: 78, height: 30, borderRadius: 39 } }>
        <Text>{ '000000' }</Text>
      </View>
    );

    return (
      <View style={ { position: 'absolute', height: 88 } }>
        <SectionList
          horizontal={ true } // 设置横向滚动
          inverted={ true }
          showsHorizontalScrollIndicator={ true }
          sections={ this.state.cloudListData }
          keyExtractor={ (item, index) => index }
          renderItem={ renderItem }
          renderSectionFooter={ renderSectionHeader }
          ListFooterComponent={ renderFoot }
        />
      </View>

    );
  }

  _renderCenterLine() {
    if (this.props.isDisabled && this.props.isCloud) {
      return null;
    }
    if (this.state.currentMode === MODE.LIVE) {
      return null;
    }
    let isDark = DarkMode.getColorScheme() == "dark";
    // 来区分是否全屏
    return (
      <View
        style={ {
          width: "100%",
          height: "100%",
          display: "flex",
          flexDirection: "row",
          justifyContent: "center",
          alignItems: "flex-end",
          position: "absolute"
        } }
        // onLayout={(event) => {
        //   let width = event.nativeEvent.layout.width;
        //   console.log("+++onLayout",width);
        // }}

      >
        <View style={ carStyles.centerLineContentStyle }>
          <Image style={{ width: 18, height: 11, marginTop: -3 }} source={ DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/car/car_play_index_top.png') : require('../../Resources/Images/car/car_play_index_top_light.png')}/>
          <Image style={{ width: 3, height: 67, marginTop: 2, marginBottom: 2 }} source={ DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/car/car_play_index_line.png') : require('../../Resources/Images/car/car_play_index_line_light.png')}/>
          {/*<View style={{ width: 3, height: 66, marginTop: 2, marginBottom: 2, borderRadius: 2, backgroundColor: DarkMode.getColorScheme() == "dark" ? "xm#FFFFFF" : "xm#121B2E" }}></View>*/}
          <Image style={{ width: 18, height: 11, marginBottom: -3 }} source={ DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/car/car_play_index_bottom.png') : require('../../Resources/Images/car/car_play_index_bottom_light.png')}/>
          {/*<View style={ carStyles.centerLineStyle }>*/}

          {/*</View>*/}
        </View>
      </View>
    );
  }

  renderBottomLineView() {
    if (this.state.currentPlaybackMode === PLAYBACK_MODE.SD && this.state.sdcardCode != 0) {
      return null;
    }
    return (
      <View style={ {
        flex: 1, justifyContent: 'center',
        borderRadius:  Radius.WidgetLevel,
        // borderTopLeftRadius: Radius.WidgetLevel,
        // borderBottomLeftRadius: Radius.WidgetLevel,
        backgroundColor: styles.itemSecondaryStyle.backgroundColor,
        opacity: this.state.showBlurView ? 0.35 : 1
      } }>

        { this.renderDashLineView() }
        { this.renderLineView() }
        { this._renderCenterLine() }
      </View>
    );
  }
  // SD卡状态异常 异常、格式化
  renderSdcardAbnormalView() {

    if (this.state.currentPlaybackMode !== PLAYBACK_MODE.SD) {
      return null;
    }

    if (this.state.sdcardCode == 0) {
      return null;
    }

    if (this.state.isLoadSdcard) {
      return null;
    }

    return (
      <View style={ {
        flexDirection: 'row',
        flex: 1,
        height: 88,
        alignItems: 'center',
        // backgroundColor: styles.itemSecondaryStyle.backgroundColor,
        borderRadius: Radius.WidgetLevel,
        opacity: this.state.showBlurView ? 0.35 : 1
      } }>
        {
          this.state.sdcardCode == 4 ? <ClickableCardButton
            title={ LocalizedStrings['car_sdcard_formatting'] }
            style={ { width: 848, justifyContent: 'flex-start', height: 88, marginTop: 0, marginBottom: 0, backgroundColor: styles.itemSecondaryStyle.backgroundColor,  } }
            showImage={ false }
            showLoadingImage = {this.state.isFormatting}/>
            : this.sdAbnormalView()

        }
      </View>
    );
  }

  sdAbnormalView() {
    let source = DarkMode.getColorScheme() === 'dark' ? require('../../Resources/Images/car/error_red.png') : require('../../Resources/Images/car/error_red_light.png');
    let textColor;
    let text = LocalizedStrings['sdcard_error'];
    if (this.state.sdcardCode == 1 || this.state.sdcardCode == 5) {
      // source = DarkMode.getColorScheme() === 'dark' ? require('../../Resources/Images/car/error_warning_grey.png') : require('../../Resources/Images/car/error_warning_light.png');
      source = DarkMode.getColorScheme() === 'dark' ? require('../../Resources/Images/car/error_warning.png') : require('../../Resources/Images/car/error_warning_light.png');
      text = LocalizedStrings['no_sdcard'];
      textColor = styles.buttonTextStyle.color;
    } else {
      textColor = carStyles.redTxColorStyle.color;
    }
    return (
      <View style={ {
        flexDirection: 'row',
        flex: 1,
        height: 88,
        alignItems: 'center',
        backgroundColor: styles.itemSecondaryStyle.backgroundColor,
        borderRadius: Radius.WidgetLevel
      } }>

        <Image style={ { width: 40, height: 40, marginLeft: 22, marginRight: 16 } } source={ source }/>
        <Text style={ [styles.buttonTextStyle, { color: textColor }] }>{ text }</Text>
      </View>
    );
  }

  _renderCloudVideoView() {
    if (this.state.videoPath == null) {
      return null;// 没有的时候 就这么滴
    }
    if (this.state.currentMode !== MODE.CLOUD) {
      return null;
    }
    if (this.state.currentPlaybackMode !== PLAYBACK_MODE.CLOUD) {
      return null;
    }
    return (
      <TouchableWithoutFeedback onPress={ () => this._onCloudVideoClick() }>
        <Video
          ref={ (ref) => {
            this.video = ref;
          } }
          style={ { position: 'absolute', top: 0, left: 0, bottom: 0, right: 0 } }
          source={ { uri: this.state.videoPath } }
          // source={ { uri: 'https://devstreaming-cdn.apple.com/videos/streaming/examples/img_bipbop_adv_example_ts/master.m3u8' } }
          // source={ { uri: 'http://kbs-dokdo.gscdn.com/dokdo_300/_definst_/dokdo_300.stream/playlist.m3u8' } }
          muted={ this.state.cloudMute }
          paused={ !this.state.isPlaying }
          resizeMode="contain"
          onEnd={ this.onEnd }
          onLoad={ this.onLoad }
          onError={ this.onError }
          onBuffer={ this.onBuffer }
          playInBackground={ false }
          playWhenInactive={ false }
          repeat={ false }
          onProgress={ this.onProgress }
          onSeek={ this.onSeek }
          controls={ false }
          onPress={ this._onCloudVideoClick }
          rate={ 1 }
          ignoreSilentSwitch={ "ignore" }
        />
      </TouchableWithoutFeedback>
    );
  }

  _renderAngleView() {
    if (!this.state.showCameraAngleView) {
      return (null);
    }

    let sPadding = 20;
    // let bottom = this.state.fullScreen ? (kWindowHeight > 600 ? 250 : 180) : (kWindowHeight > 600 ? (this._getVideoAreaHeight() - 28 - sPadding) : 80);// 28 is angle view's height
    let left = 60;
    let angleStyle = {
      position: "absolute",
      left: left,
      top: 60
    };

    return (
      <View style={angleStyle}>
        <RectAngleCarView
          ref={(ref) => { this.angleView = ref; }}
          angle={this.state.angle}
          elevation={this.state.elevation}
          scale={this.state.videoScale}
          showScale={this.state.angleViewShowScale}
          accessible={true}
          accessibilityLabel={DescriptionConstants.zb_39.replace('1',this.state.videoScale)}
        />
      </View>
    );
  }

  _renderMoreItemDialog() {
    let modalStyle = {
      position: 'absolute',
      width: 320,
      height: 240,
      bottom: 230,
      left: 513,
      alignSelf: 'center',
      borderRadius: Radius.PanelLevel
    };
    // #00000066
    let modalShadowStyle = {
      position: 'absolute',
      width: 480,
      height: 400,
      // bottom: 229,
      bottom: 143,
      // left: 512,
      left: 433,
      alignSelf: 'center',
      borderRadius: Radius.PanelLevel,
      // backgroundColor: carStyles.shadowStyle.backgroundColor
    };
    return (
      <NearHandDialog
        style={ modalStyle }
        shadowStyle={ modalShadowStyle }
        showTitle={ false }
        visible={ this.state.showMoreDlg }
        showButton={ false }
        onDismiss={ () => {
          this.setState({ showMoreDlg: false });
        } }
        canDismiss={ true }
        useNewTheme={ true }
      >
        <View>
          <TouchableHighlight
            style={ [{
              display: "flex",
              flexDirection: 'column',
              justifyContent: 'center',
              minHeight: 120,
              borderTopLeftRadius: Radius.PanelLevel,
              borderTopRightRadius: Radius.PanelLevel,
              // backgroundColor: carStyles.nearHandStyle.backgroundColor
              backgroundColor: this.state.currentPlaybackMode === PLAYBACK_MODE.SD ? carStyles.nearHandSelectedItemStyle.backgroundColor : carStyles.nearHandStyle.backgroundColor

            }] }
            underlayColor={ carStyles.nearHandItemStyle.backgroundColor }
            onPress={ () => {
              this.delayToHideDialog && clearTimeout(this.delayToHideDialog);
              this.delayToHideDialog = setTimeout(() => {
                this.setState({ showMoreDlg: false, showDayList: false });
              }, 50);
              if (this.state.currentPlaybackMode === PLAYBACK_MODE.SD) {
                return;
              }
              this.changePlayback(PLAYBACK_MODE.SD);
            } }>
            {/*<Text style={ [styles.titleTextStyle, {*/}
            {/*  paddingLeft: 40,*/}
            {/*  paddingRight: 40*/}
            {/*}] }>{ LocalizedStrings['car_sd_title'] }</Text>*/}
            <View style={{
              display: "flex",
              flexDirection: 'row',
              alignItems: 'center'
            }}>
              <Text style={ [styles.titleTextStyle, {
                paddingLeft: 40,
                paddingRight: 32,
                flex: 1,
                color: this.state.currentPlaybackMode === PLAYBACK_MODE.SD ? carStyles.nearHandSelectedItemStyle.color : styles.titleTextStyle.color
              }] }>{ LocalizedStrings['car_sd_title'] }</Text>
              {this.state.currentPlaybackMode === PLAYBACK_MODE.SD ? <Image style={{ width: 40, height: 40, marginRight: 40 }} source={DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/car/near_hand_check.png') : require('../../Resources/Images/car/near_hand_check_light.png')}/> : null}

            </View>
          </TouchableHighlight>

          <TouchableHighlight
            style={ [{
              display: "flex",
              flexDirection: 'column',
              justifyContent: 'center',
              minHeight: 120,
              borderBottomLeftRadius: Radius.PanelLevel,
              borderBottomRightRadius: Radius.PanelLevel,
              // backgroundColor: carStyles.nearHandStyle.backgroundColor
              backgroundColor: this.state.currentPlaybackMode === PLAYBACK_MODE.CLOUD ? carStyles.nearHandSelectedItemStyle.backgroundColor : carStyles.nearHandStyle.backgroundColor

            }] }
            // activeOpacity={0.6}
            underlayColor={ carStyles.nearHandItemStyle.backgroundColor }

            onPress={ () => {
              this.delayToHideDialog && clearTimeout(this.delayToHideDialog);
              this.delayToHideDialog = setTimeout(() => {
                this.setState({ showMoreDlg: false, showDayList: false });
              }, 50);
              if (this.state.currentPlaybackMode === PLAYBACK_MODE.CLOUD) {
                return;
              }
              this.changePlayback(PLAYBACK_MODE.CLOUD);

            } }>
            {/*<Text style={ [styles.titleTextStyle, {*/}
            {/*  paddingLeft: 40,*/}
            {/*  paddingRight: 40*/}
            {/*}] }>{ LocalizedStrings['s_cloud_setting'] }</Text>*/}
            <View style={{
              display: "flex",
              flexDirection: 'row',
              alignItems: 'center'
            }}>
              <Text style={ [styles.titleTextStyle, {
                paddingLeft: 40,
                paddingRight: 32,
                flex: 1,
                color: this.state.currentPlaybackMode === PLAYBACK_MODE.CLOUD ? carStyles.nearHandSelectedItemStyle.color : styles.titleTextStyle.color
              }] }>{ LocalizedStrings['s_cloud_setting'] }</Text>
              {this.state.currentPlaybackMode === PLAYBACK_MODE.CLOUD ? <Image style={{ width: 40, height: 40, marginRight: 40 }} source={require('../../Resources/Images/car/near_hand_check.png')}/> : null}

            </View>
          </TouchableHighlight>
        </View>

      </NearHandDialog>
    );
  }

  _renderMoreSettingDialog() {
    let modalStyle = {
      position: 'absolute',
      width: 320,
      bottom: 230,
      right: 513,
      alignSelf: 'center',
      borderRadius: Radius.PanelLevel
    };

    let modalShadowStyle = {
      position: 'absolute',
      // width: 322,
      // height: 242,
      width: 480,
      height: 400,
      bottom: 143,
      right: 433,
      alignSelf: 'center',
      borderRadius: Radius.PanelLevel,
      // backgroundColor: carStyles.shadowStyle.backgroundColor
    };

    return (
      <NearHandDialog
        style={ modalStyle }
        shadowStyle={ modalShadowStyle }
        showTitle={ false }
        visible={ this.state.showMoreSettingDlg }
        showButton={ false }
        onDismiss={ () => {
          this.setState({ showMoreSettingDlg: false });
        } }
        canDismiss={ true }
        useNewTheme={ true }
      >
        <View>
          <TouchableHighlight
            style={ [{
              display: "flex",
              flexDirection: 'column',
              justifyContent: 'center',
              minHeight: 120,
              borderTopLeftRadius: Radius.PanelLevel,
              borderTopRightRadius: Radius.PanelLevel,
              backgroundColor: carStyles.nearHandStyle.backgroundColor
            }] }
            underlayColor={ carStyles.nearHandItemStyle.backgroundColor }
            onPress={ () => {
              this.delayToHideDialog && clearTimeout(this.delayToHideDialog);
              this.delayToHideDialog = setTimeout(() => {
                this.setState({ showMoreSettingDlg: false });
              }, 50);

              // this.props.navigation.navigate('CarSetting');
              this.setState({ showLoadingView: true });
              this.props.navigation.push('CarSetting');
            } }>
            <Text style={ [styles.titleTextStyle, {
              paddingLeft: 40,
              paddingRight: 40
            }] }>{ LocalizedStrings['setting'] }</Text>
          </TouchableHighlight>

          <TouchableHighlight
            style={ [{
              display: "flex",
              flexDirection: 'column',
              justifyContent: 'center',
              minHeight: 120,
              borderBottomLeftRadius: Radius.PanelLevel,
              borderBottomRightRadius: Radius.PanelLevel,
              backgroundColor: carStyles.nearHandStyle.backgroundColor
            }] }
            underlayColor={ carStyles.nearHandItemStyle.backgroundColor }

            onPress={ () => {
              this.delayToHideDialog && clearTimeout(this.delayToHideDialog);
              this.delayToHideDialog = setTimeout(() => {
                this.setState({ showMoreSettingDlg: false, showLoadingView: true });
              }, 50);
              if (this.state.loginState != 0 && this.state.loginState != 1) {
                // 判断为未登录状态, 跳转到登录页面
                this.toLogin();
              } else {
                this.props.navigation.push('CarStorage');
              }

            } }>
            <Text style={ [styles.titleTextStyle, {
              paddingLeft: 40,
              paddingRight: 40
            }] }>{ LocalizedStrings['video_manager'] }</Text>
          </TouchableHighlight>
        </View>

      </NearHandDialog>
    );
  }

  // 这里是星期几
  _renderDayList() {
    if (this.state.showErrorView) {
      return null;
    }

    if (this.state.showLoadingView) {
      return null;
    }

    if (!this.state.showDayList) {
      return null;
    }

    let lastIndex = Math.floor(this.state.dateData.length / 7) - 1;
    let preWeekSource = this.weekPageIndex == lastIndex ? Util.isDark() ? require("../../Resources/Images/car/pre_week_disable.png") : require("../../Resources/Images/car/pre_week_disable_light.png") :
      Util.isDark() ? require("../../Resources/Images/car/pre_week.png") : require("../../Resources/Images/car/pre_week_light.png");
    let nextWeekSource =  this.weekPageIndex == 0 ? Util.isDark() ? require("../../Resources/Images/car/next_week_disable.png") : require("../../Resources/Images/car/next_week_disable_light.png") :
      Util.isDark() ? require("../../Resources/Images/car/next_week.png") : require("../../Resources/Images/car/next_week_light.png");
    console.log("+++++++++++++++++++++++++++++++++++++",lastIndex, this.weekPageIndex);
    return (
      <View
        style={ [{
          position: 'absolute',
          alignItems: "center",
          bottom: 0,
          marginHorizontal: 208,
          height: 120,
          display: "flex",
          flexDirection: "row",
          zIndex: 9
        }, carStyles.innerVideoContainerStyle] }
      >
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center', marginHorizontal: 24 }}>
          <TouchableOpacity
            style={ { width: 56, height: 56, justifyContent: 'center' } }
            onPress={ () => {
              // if (this.weekPageIndex == 0) {
              //   Toast.success('car_no_video_in_day');
              //   return;
              // }
              if (this.weekPageIndex == lastIndex) {
                Toast.success('car_no_video_in_day');
                return;
              }
              this.mDfv && this.mDfv.previewWeek();
              this._hideDayWeekLater();
            } }
          >
            <Image
              style={ { width: 40, height: 40 } }
              source={ preWeekSource }
              accessibilityLabel={ DescriptionConstants.rp_6 }/>
          </TouchableOpacity>

          <DateFilterViewForCar
            ref={ (aDfv) => {
              this.mDfv = aDfv;
            } }
            totalWidth={ 576 }
            dataSource={ this.state.dateData }
            dayButtonPressed={ (item) => {
              // this.selectDate(item);
              this._onPressDayItem(item);
            } }
            extraHeader={ null }
            // 加一个scrollModel属性，为1时按周切换
            scrollModel={ 1 }
            currentStopIndex={ this.state.currentStopIndex }
            type={this.state.currentPlaybackMode}
            onScrolling={() => {
              this.showDayWeekTimer && clearTimeout(this.showDayWeekTimer);
            }}
            onScrollEnd={(index) => {
              this.weekPageIndex = index;
              this._hideDayWeekLater();
              this.setState({});
            }}
          />

          <TouchableOpacity
            style={ { width: 56, height: 56, justifyContent: 'center', alignItems: 'flex-end' } }
            onPress={ () => {
              if (this.weekPageIndex == 0) {
                Toast.success('car_no_video_in_day');
                return;
              }

              this.mDfv && this.mDfv.nextWeek();
              this._hideDayWeekLater();
            } }
          >
            <Image
              style={ { width: 40, height: 40 } }
              source={ nextWeekSource }
              accessibilityLabel={ DescriptionConstants.rp_6 }/>
          </TouchableOpacity>
        </View>
      </View>

    );
  }

  // 具体的星期几
  _renderDayItem(item, index) {
    let startTime = item.startTimestamp;
    this.dateTime.setTime(startTime);
    let month = this.dateTime.getMonth() + 1;
    let day = this.dateTime.getDate();
    let str = day > 9 ? `${ day }` : `0${ day }`;
    let week = WEEKS[item.week];
    return (
      <View
        style={ item.isSelected ? {
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: 72,
          width: 72,
          marginHorizontal: 6,
          borderRadius: 36,
          backgroundColor: "#0000ff"
        } : {
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: 72,
          width: 72,
          marginHorizontal: 6
        } }
        key={ index }
      >
        <TouchableOpacity
          style={ { display: "flex", flexDirection: "column", alignItems: "center", paddingTop: 2 } }
          onPress={ () => this._onPressDayItem(item) }
        >
          <Text
            style={ item.hasVideo ? styles.dayItemTextWeek : { color: "#CCCCCC", fontSize: 11 } }
          >
            { week }
          </Text>

          <Text
            style={ [styles.dayItemTextDay, { fontWeight: "bold", color: item.hasVideo ? "black" : "#CCCCCC" }] }
          >
            { str }
          </Text>

        </TouchableOpacity>

      </View>
    );
  }

  renderDayIndicator() {
    let hideLeftRightButton = false;
    if (this.state.showErrorView
      || this.state.showSDCardError
      || !this.state.isVideoSaveOpen
      || this.state.isSleep
      || this.state.limitMode
      || this.state.temperatureState == TEMPERATURE_STATE.HIGH
      || this.state.loginState == 2
      || this.state.loginState == 3) {
      hideLeftRightButton = true;
    }

    if (this.state.currentPlaybackMode === PLAYBACK_MODE.SD && this.state.sdcardCode != 0) {
      hideLeftRightButton = true;
    }

    let index = this._getSelectedDayIndex();
    let prevIndex = this._getPrevDayIndex(index);
    let nextIndex = this._getNextDayIndex(index);

    let preSource = prevIndex < 0 || this.state.showBlurView ? Util.isDark() ? require("../../Resources/Images/car/pre_day_disable.png") : require("../../Resources/Images/car/pre_day_disable_light.png") :
      Util.isDark() ? require("../../Resources/Images/car/pre_day.png") : require("../../Resources/Images/car/pre_day_light.png");

    let nextSource = nextIndex < 0 || this.state.showBlurView ? Util.isDark() ? require("../../Resources/Images/car/next_day_disable.png") : require("../../Resources/Images/car/next_day_disable_light.png") :
      Util.isDark() ? require("../../Resources/Images/car/next_day.png") : require("../../Resources/Images/car/next_day_light.png");

    return (
      <View style={ {
        height: 50,
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: 'center',
        marginTop: 18
      } }>
        {
          hideLeftRightButton ? null :
            <TouchableOpacity
              style={ { width: 50, height: 50 } }
              onPress={ () => {

                // if (this.props.isRecording) {
                //   Toast.success('camera_recording_block');
                //   return;
                // }
                // // 跳到左边的日期 或者跳到右边的日期
                //console.log("跳到左边一天");
                if (this.state.showBlurView) {
                  return;
                }
                let index = this._getSelectedDayIndex();
                let prevIndex = this._getPrevDayIndex(index);
                if (prevIndex < 0) {
                  Toast.success('car_no_video_in_day');
                  return;
                }
                let prevItem = this.state.dateData[prevIndex];
                this._onPressDayItem(prevItem);
              } }
            >

              <Image
                style={ { width: 50, height: 50 } }
                source={ preSource }
                accessibilityLabel={ DescriptionConstants.rp_6 }/>
            </TouchableOpacity>
        }

        <TouchableOpacity
          style={ { display: "flex", alignItems: "center", marginHorizontal: 55 } }
          disabled={ this.state.showErrorView || this.state.loginState == 2 || this.state.loginState == 3 || this.state.limitMode || this.state.temperatureState == TEMPERATURE_STATE.HIGH || this.state.showBlurView}
          onPress={ () => {
            if (this.state.showErrorView) {
              return;
            }
            if (this.state.showSDCardError) {
              return;
            }
            if (this.state.showBlurView) {
              return;
            }
            // 计算当前日期所在的index
            let index = this._getSelectedDayIndex();
            this.weekPageIndex = Math.floor(index / 7);
            this.setState((state) => {
              return {
                showDayList: !state.showDayList,
                currentStopIndex: index

              };
            }, () => {
              if (this.state.showDayList) {
                // 需要刷新日历

                // this.refreshTopDateView(this.state.currentTime);
              } else {
                this.selectedDayIndex = -1;
              }
            });
            // 5s后，隐藏周日历
            this._hideDayWeekLater();
          } }
        >
          <Text style={ [styles.itemTextStyle, { fontSize: 28, minWidth: 188, textAlign: 'center', opacity: this.state.showBlurView ? 0.6 : 1 }] }>
            { this.state.showErrorView || this.state.isSleep ? "" : StringUtil.dayLineString(this.state.curDate) }
          </Text>
        </TouchableOpacity>

        {
          hideLeftRightButton ? null :
            <TouchableOpacity
              style={ { width: 50, height: 50 } }
              onPress={ () => {
                // if (this.props.isRecording) {
                //   Toast.success('camera_recording_block');
                //   return;
                // }
                if (this.state.showBlurView) {
                  return;
                }
                // 跳到左边的日期 或者跳到右边的日期
                //console.log("跳到右边一天");
                let index = this._getSelectedDayIndex();
                let nextIndex = this._getNextDayIndex(index);
                if (nextIndex < 0) {
                  Toast.success('car_no_video_in_day');
                  return;
                }
                let nextItem = this.state.dateData[nextIndex];
                this._onPressDayItem(nextItem);
              } }
            >
              <Image
                style={ { width: 50, height: 50 } }
                source={ nextSource }
                accessibilityLabel={ DescriptionConstants.rp_7 }
              >
              </Image>
            </TouchableOpacity>
        }


      </View>
    );
  }


  renderBottomOperationView() {
    if (this.state.showErrorView) {
      return null;
    }
    // if (this.state.showErrorView && this.state.isSleep) {
    //   return null;
    // }
    if (this.state.limitMode) {
      // 限制工作模式
      return null;
    }
    if (this.state.temperatureState == TEMPERATURE_STATE.HIGH) {
      // 高温工作模式
      return null;
    }

    if (this.state.isSleep) {
      return null;
    }

    if (!this.state.isVideoSaveOpen) {
      return null;
    }
    // 代客模式不展示
    if (this.state.loginState == 2 || this.state.loginState == 3) {
      // 代客模式下，有单独的UI展示
      return null;
    }

    if (this.state.loadingData) {
      return null;
    }
    const opacity = this.props.disabled ? Opacity.Disabled : Opacity.Normal;
    let sdOpacity = Opacity.Normal;
    if (this.state.sdcardCode !== 0 && this.state.currentPlaybackMode === PLAYBACK_MODE.SD) {
      // SD卡异常
      sdOpacity = Opacity.Disabled;
    }
    return (
      <View style={ { flexDirection: 'row' } }>

        <ClickableCardButton
          title={ this.state.currentPlaybackMode === PLAYBACK_MODE.SD ? LocalizedStrings['car_sd_title'] : LocalizedStrings['s_cloud_setting'] }
          style={{ width: 168, marginTop: CarConstants.MARGIN_18 }}
          disabled={ (this.state.loginState != 0 && this.state.loginState != 1) || this.state.showBlurView }
          titleStyle={{ opacity: sdOpacity, marginRight: 10 }}
          paddingLeft={ 24 }
          paddingRight={ 5 }
          onPress={ () => {
            console.log("ClickableCard1 onPress");
            // ToastAndroid.show(`我是一个toast`, ToastAndroid.SHORT);
            this.setState({ showMoreDlg: true });
          } }/>
        <View style={ {
          flexDirection: 'row',
          flex: 1,
          height: 88,
          // marginTop: Constants.DEFAULT_TEXT_MARGIN_BOTTOM,
          marginTop:  CarConstants.MARGIN_18,
          marginHorizontal: 24,
          // backgroundColor: styles.itemSecondaryStyle.backgroundColor,
          borderRadius: Radius.WidgetLevel
        } }>
          { this.renderBottomLineView() }

          {this.renderSdcardAbnormalView()}

          {
            this.state.currentPlaybackMode === PLAYBACK_MODE.SD && this.state.sdcardCode != 0 ? null :
              // <ClickableCardButton
              //   paddingLeft={ 1 }
              //   paddingRight={ 1 }
              //   style={{ width: 88, marginTop: 0, marginLeft: 24 }}
              //   title={LocalizedStrings['nas_interval_real']}
              //   titleStyle={{ marginRight: 0, flex: 1, fontSize: 26, textAlign: 'center' }}
              //   showImage={false}
              //   onPress={ () => {
              //     this.setState({ showMoreSettingDlg: true });
              //
              //   } }
              // />
              <View style={{
                height: 88,
                width: 88,
                borderRadius: Radius.WidgetLevel,
                // borderTopRightRadius: Radius.WidgetLevel,
                // borderBottomRightRadius: Radius.WidgetLevel,
                // backgroundColor: styles.itemSecondaryStyle.backgroundColor,
                marginLeft: Constants.DEFAULT_TEXT_MARGIN_BOTTOM
              }}>
                <TouchableHighlight
                  disabled={this.state.showBlurView}
                  style={ [styles.buttonBaseStyle, {
                    height: 88,
                    width: 88,
                    backgroundColor: this.state.currentMode === MODE.LIVE ?
                      styles.itemHighlightStyle.backgroundColor :
                      styles.itemSecondaryStyle.backgroundColor,
                    opacity: this.state.showBlurView ? 0.35 : 1
                  }] }
                  // activeOpacity={0.7}
                  underlayColor={this.state.currentMode === MODE.LIVE ? carStyles.livePressStyle.backgroundColor : carStyles.liveNormalPressStyle.backgroundColor}
                  // underlayColor={'#0A6CFF'}
                  onPress={ () => {
                    this.setState({ liveSelected: !this.state.liveSelected});
                    console.log(`Button onPress`, this.state.currentMode, this.state.currentPlaybackMode);
                    this.isUserPause = false;
                    if (this.state.currentMode === MODE.LIVE) {
                      // @20250303 按最新交互规则，此按钮只可从回看切换实时查看，不能实时切回看
                    } else {
                      let tempCurrentMode = this.state.currentMode;
                      // 先释放播放器资源
                      if (tempCurrentMode === MODE.CLOUD) {
                        this._startCloudPlay(false);
                      } else {
                        this._startSdcardPlay(false);
                      }
                      this.setState({ currentMode: MODE.LIVE, showPlayErrorView: false }, () => {
                        console.log("{{{{{{{{{}}}}}}", this.state.currentMode, this.state.currentPlaybackMode);
                        Service.miotcamera.setTimelinePlaybackMode(false);
                        // 恢复播放器视图尺寸
                        NativeModules.MHCameraSDK.setPositionParam(Device.deviceID, { scale: 1, offsetY: 0, offsetX: 0, chan: 0 });

                        this._startConnect();
                        // 滚动列表到最右侧，即顶部 需要重置下日期时间及选择的播放item
                        if (this.videoLineView) {
                          setTimeout(() => {
                            // this.videoLineView.scrollToOffset({ animated: true, offset: 0 });
                            this.videoLineView.scrollToLocation({ animated: true, sectionIndex: 0, itemIndex: 0, viewPosition: 0, viewOffset: 20 });
                          }, 0);
                          // this.videoLineView.scrollToLocation({ animated: true, sectionIndex: 0, itemIndex: 0, viewPosition: 0, viewOffset: 20 });

                          // this.videoLineView.scrollToPosition({ animated: true, offset: 0 });
                          let { item, itemInfo } = this.videoLineView.getLastVideoInfo();
                          // console.log("getLastVideoInfo",item,itemInfo);
                          if (item) {
                            if (tempCurrentMode === MODE.CLOUD) {
                              this.cloudCurrentPlayItem = item;
                              this.cloudItemInfo = itemInfo;
                              this.toCloudStartTime = item.startTime;
                            } else {
                              this.sdCurrentPlayItem = item;
                              this.sdItemInfo = itemInfo;
                              this.toSDStartTime = item.startTime;
                            }
                            console.log(TAG, "change to live", item.startTime);
                            this.setSelectedDay(item.startTime);
                          }
                        }
                      });
                    }
                  } }
                  pointerEvents={ this.props.disabled ? "none" : "auto" }
                >
                  <Text
                    style={ [this.state.currentMode === MODE.LIVE ? styles.buttonHightLightTextStyle : styles.buttonTextStyle] }>{ LocalizedStrings['nas_interval_real'] }</Text>
                </TouchableHighlight>
              </View>
          }

        </View>
        <ClickableCardButton
          paddingLeft={ 24 }
          paddingRight={ 24 }
          source={ DarkMode.getColorScheme() === 'dark' ? require('../../Resources/Images/car/ic_action_more.png') : require('../../Resources/Images/car/ic_action_more_light.png') }
          style={{ marginTop: CarConstants.MARGIN_18 }}
          onPress={ () => {
            this.setState({ showMoreSettingDlg: true });

          } }
        />
      </View>
    );
  }

  _realStartSnapshot() {
    let videoView = this.state.currentMode === MODE.CLOUD ? this.video : this.cameraGLView;
    AlbumHelper.snapshotForCarFreeze(videoView, this.state.currentMode === MODE.CLOUD)
      .then((path) => {
        // 到这里可以展示高斯模糊图片了，并且停掉相关的播放器播放
        this.blurTag = Math.random() * 10000;
        this.setState({ showBlurView: true });
        if (this.state.currentMode === MODE.LIVE) {
          if (this.cameraRenderView != null) {
            this.cameraRenderView.stopRender();
          }
          CameraPlayer.getInstance().stopVideoPlay();
        } else if (this.state.currentMode === MODE.SD) {
          this._startSdcardPlay(false);
        } else {
          this._startCloudPlay(false);
        }
      })
      .catch((error) => {
        // TODO 要么显示兜底图，要么不做处理，失败的原因可能有播放器View不存在或其他

      });
  }


  renderStorageUotOperateOnceView() {
    if (this.state.isSleep) {
      return null;
    }

    if (this.state.isVideoSaveOpen && this.state.loginState != 3) {
      return null;
    }

    if (this.state.showErrorView) {
      return null;
    }

    if (this.state.limitMode) {
      return null;
    }

    if (this.state.temperatureState == TEMPERATURE_STATE.HIGH) {
      return null;
    }

    if (this.state.loginState == 2) {
      // 代客模式下，有单独的UI展示
      return null;
    }
    // 未登录与此UI展示一样，差别只在文案

    let text = LocalizedStrings['storage_not_open_once'];
    if (this.state.loginState == 3) {
      text = LocalizedStrings['user_not_login'];
    }
    return (
      <View style={ { flexDirection: 'row' } }>
        <TouchableOpacity
          style={ {
            flexDirection: 'row',
            flex: 1,
            height: 88,
            // marginTop: Constants.DEFAULT_TEXT_MARGIN_BOTTOM,
            marginTop:  CarConstants.MARGIN_18,
            marginRight: 24,
            alignItems: 'center',
            backgroundColor: styles.itemSecondaryStyle.backgroundColor,
            borderRadius: Radius.WidgetLevel
          } }
          onPress={() => {
            if (this.state.loginState != 0 && this.state.loginState != 1) {
              // 非0、1未登录
              this.toLogin();
            } else {
              this.setState({ showLoadingView: true });
              this.props.navigation.navigate('CarSetting');
            }
          }}
        >

          <View style={ { flex: 1, justifyContent: 'center', paddingLeft: 32 } }>
            <Text style={ styles.buttonTextStyle }>{ text }</Text>

          </View>
          <Image style={ { width: 40, height: 40, marginRight: 32 } }
                 source={ DarkMode.getColorScheme() == 'dark' ? require('../../Resources/Images/car/right_arrow.png') : require('../../Resources/Images/car/right_arrow_light.png') }/>

        </TouchableOpacity>

        <ClickableCardButton
          paddingLeft={ 24 }
          paddingRight={ 24 }
          style={{ marginTop: CarConstants.MARGIN_18 }}
          source={ DarkMode.getColorScheme() === 'dark' ? require('../../Resources/Images/car/ic_action_more.png') : require('../../Resources/Images/car/ic_action_more_light.png') }
          onPress={ () => {
            this.setState({ showMoreSettingDlg: true });
          } }
        />
      </View>

    );
  }

  // 代客模式
  renderProxyCustomerView() {
    if (this.state.loginState != 2) {
      return null;
    }

    if (this.state.showErrorView) {
      return null;
    }

    if (this.state.isSleep) {
      return null;
    }

    if (this.state.limitMode) {
      return null;
    }

    if (this.state.temperatureState == TEMPERATURE_STATE.HIGH) {
      return null;
    }

    return (
      <View style={ {
        flexDirection: 'row',
        flex: 1,
        height: 88,
        alignItems: 'center',
        // marginTop: Constants.DEFAULT_TEXT_MARGIN_BOTTOM,
        marginTop: CarConstants.MARGIN_18,
        // backgroundColor: styles.itemSecondaryStyle.backgroundColor,
        backgroundColor: carStyles.homeBarStyle.backgroundColor,
        borderRadius: Radius.WidgetLevel
      } }>
        <Image style={ { width: 40, height: 40, marginHorizontal: 22 } } source={ DarkMode.getColorScheme() === 'dark' ? require('../../Resources/Images/car/error_warning.png') : require('../../Resources/Images/car/error_warning_light.png') }/>
        <Text style={ styles.buttonTextStyle }>{ LocalizedStrings['proxy_customer_mode'] }</Text>
      </View>
    );
  }

  renderBottomErrorView() {

    if (!this.state.isSleep && !this.state.showErrorView && !this.state.limitMode && this.state.temperatureState !== TEMPERATURE_STATE.HIGH) {
      return null;
    }

    let errorSource = DarkMode.getColorScheme() === 'dark' ? require('../../Resources/Images/car/error_red.png') : require('../../Resources/Images/car/error_red_light.png');
    let textStr;
    if (this.state.temperatureState === TEMPERATURE_STATE.HIGH) {
      textStr = LocalizedStrings['high_temperature_mode'];
    } else if (this.state.limitMode) {
      textStr = LocalizedStrings['limited_mode'];
    } else if (this.state.isSleep) {
      textStr = LocalizedStrings['device_not_open_warning'];
    } else {
      textStr = this.state.errTextString;
    }
    // 非断电离线，就是设备异常
    if (this.state.isSleep) {
      errorSource = DarkMode.getColorScheme() === 'dark' ? require('../../Resources/Images/car/error_warning.png') : require('../../Resources/Images/car/error_warning_light.png');
    }
    let textStyle = styles.buttonTextStyle;
    if (!this.state.isSleep) {
      textStyle = [styles.buttonTextStyle, { color: carStyles.redTxColorStyle.color }];
    }
    if (this.state.limitMode) {
      errorSource = DarkMode.getColorScheme() === 'dark' ? require('../../Resources/Images/car/error_red.png') : require('../../Resources/Images/car/error_red_light.png');
      textStyle = [styles.buttonTextStyle, { color: carStyles.redTxColorStyle.color }];
    }
    if (this.state.temperatureState === TEMPERATURE_STATE.HIGH) {
      errorSource = DarkMode.getColorScheme() === 'dark' ? require('../../Resources/Images/car/error_red.png') : require('../../Resources/Images/car/error_red_light.png');
      textStyle = [styles.buttonTextStyle, { color: carStyles.redTxColorStyle.color }];
    }
    return (
      <View style={ {
        flexDirection: 'row',
        flex: 1,
        height: 88,
        alignItems: 'center',
        // marginTop: Constants.DEFAULT_TEXT_MARGIN_BOTTOM,
        marginTop:  CarConstants.MARGIN_18,
        // backgroundColor: styles.itemSecondaryStyle.backgroundColor,
        backgroundColor: carStyles.homeBarStyle.backgroundColor,
        borderRadius: Radius.WidgetLevel,
      } }>
        <Image style={ { width: 40, height: 40, marginLeft: 22, marginRight: 16 } } source={ errorSource }/>
        <Text style={ textStyle }>{ textStr }</Text>
      </View>
    );
  }

  // ================================== 云存/SD卡 ===========================================
  // 云存SD卡切换
  changePlayback(playbackMode = PLAYBACK_MODE.SD) {

    let dayItem =  playbackMode === PLAYBACK_MODE.CLOUD ? this.cloudDayList.find((item) => item.selected) : this.sdcardDayList.find((item) => item.selected);
    let showDate = new Date();
    if (dayItem) {
      showDate = new Date(dayItem.startTimestamp);
    }
    // 切换回看播放模式  SD卡  云存
    if (this.state.currentMode === MODE.LIVE) {
      // 处于实时直播 只切底部UI展示
      StorageKeys.CAR_TO_SHOW_CLOUD = playbackMode === PLAYBACK_MODE.CLOUD;
      this.setState({
        currentMode: MODE.LIVE,
        currentPlaybackMode: playbackMode,
        dateData: playbackMode === PLAYBACK_MODE.CLOUD ? this.cloudDayList : this.sdcardDayList,
        curDate: showDate
      },() => {
        if (this.videoLineView) {
          this.videoLineView.getItemPosition();
          this.videoLineView.scrollToOffset({ animated: true, offset: 0 });
        }
      });
    } else {
      // 如果正在播放云存，无SD卡
      this.isUserPause = false;
      let currentMode = MODE.LIVE;
      if (playbackMode === PLAYBACK_MODE.SD) {
        let timeItems = SdFileManager.getInstance().getTimeItems();
        if(this.state.sdcardCode != 0 || timeItems == null || timeItems.length == 0) {
          // 直接播放直播
          currentMode = MODE.LIVE;
        } else {
          currentMode = MODE.SD;
        }
      } else {
        let video = CloudVideoCarUtil.getLastestVideo();
        if (video == null) {
          currentMode = MODE.LIVE;
        } else {
          currentMode = MODE.CLOUD;
        }
      }
      StorageKeys.CAR_TO_SHOW_CLOUD = playbackMode === PLAYBACK_MODE.CLOUD;
      // 处于播放云存|SD卡回看，切换到另一个模式下
      // 切换到该模式，并开始播放最新的一条视频
      let needStopVideoBefore = false;
      let oldCurrentMode = this.state.currentMode;
      if (this.state.currentMode === MODE.SD && currentMode === MODE.LIVE) {
        needStopVideoBefore = true;
      }
      this.setState({
        currentMode: currentMode,
        currentPlaybackMode: playbackMode,
        dateData: playbackMode === PLAYBACK_MODE.CLOUD ? this.cloudDayList : this.sdcardDayList,
        curDate: showDate,
        showPlayErrorView: false,
        isLoading: false
      },() => {
        // 1、滚动到最右侧
        // 2、取到对应的item，开始从头开始播放
        if (needStopVideoBefore) {
          // 需要停掉另一路的推流 互斥 直播切回看-停直播  回看切直播-停回看
          this.doVideoStop();
        }
        this.delayToPlay && clearTimeout(this.delayToPlay);
        this.delayToPlay = setTimeout(() => {
          let data = playbackMode === PLAYBACK_MODE.SD ? this.state.sdListData : this.state.cloudListData;
          let item = null;
          let itemInfo = null;
          if (this.videoLineView) {
            console.log("change playback mode");
            this.videoLineView.getItemPosition();
            let data = this.videoLineView.getLastVideoInfo();
            item = data.item;
            itemInfo = data.itemInfo;

            // 需要把对应的item滚动到回看的那个item
            this.videoLineView.scrollToItemByItemInfo(itemInfo);
            this.videoLineView.setCurrentPlayItem(item);
          }

          LogUtil.logOnAll(TAG, "getLastVideoInfo", item, itemInfo);

          if (this.state.currentMode === MODE.SD) {

            this.sdCurrentPlayItem = item;
            this.sdItemInfo = itemInfo;
            if (item != undefined) {
              this.toSDStartTime = item.startTime;
            }
            if (this.state.sdcardCode == 0) {
              // 正常的时候才去切换播放
              this._startCloudPlay(false,true);
              this._startSdcardPlay(true);
            }
          } else if (this.state.currentMode === MODE.CLOUD) {
            // 需要停止SD卡视频播放

            this.cloudCurrentPlayItem = item;
            this.cloudItemInfo = itemInfo;
            if (item != undefined) {
              this.toCloudStartTime = item.startTime;
            }
            this._startSdcardPlay(false,true);
            this._startCloudPlay(true);
            // 需要取最新的一条视频出来播放
          } else {
            // 播放直播
            if (oldCurrentMode === MODE.SD) {
              this._startSdcardPlay(false, true);
            } else if (oldCurrentMode === MODE.CLOUD) {
              this._startCloudPlay(false, true);
            }
            Service.miotcamera.setTimelinePlaybackMode(false);
            // 恢复播放器视图尺寸
            NativeModules.MHCameraSDK.setPositionParam(Device.deviceID, { scale: 1, offsetY: 0, offsetX: 0, chan: 0 });
            this.queryNetworkJob();
          }
        },500);
      });
    }
  }

  // 切换当前播放模式-实时播放、云存播放、SD卡播放
  changeToCurrentMode(mode = MODE.LIVE) {
    this.setState({ currentMode: mode });
  }

  // ================================== 请求方法 ===========================================
  //
  _onResume() {
    // if (!this.isAppForeground || !this.isPluginForeGround || !this.isPageForeGround) {
    //   return;
    // }
    console.log("==================onResume==========");
    CameraPlayer.getInstance().bindConnectionCallback(this._connectionHandler);
    CameraPlayer.getInstance().bindP2pCommandCallback(this._p2pCommandHandler);
    CameraPlayer.getInstance().bindNetworkInfoCallback(this._networkChangeHandler);
    CameraPlayer.getInstance().bindPauseAllCallback(() => {
      this._stopAll(false, false);
    });
    DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');
    CameraPlayer.getInstance().bindPowerOffCallback(this._powerOffHandler);
    CameraPlayer.getInstance().bindWorkModeStateCallback(this._workModeCallback);
    CameraPlayer.getInstance().bindTemperatureModeStateCallback(this._temperatureCallback);
    CameraPlayer.getInstance().bindSDCallback(this._sdCallback);

    this.getLocalSetting();
    // // 如果已经播放完成了 或者是播放error  或者是用户手动暂停的
    // if (this.state.showErrorView) {
    //   return;
    // }
    if (CameraConfig.needRefreshCloudVideo) {
      if (this.state.currentMode !== MODE.CLOUD) {
        // 如果不是云存，即切回来后，不需要播放云存
        CameraConfig.needRefreshCloudVideo = false;
      }
      this.isFirstReceiveFiles = true;
      // 需要重新拉云存数据
      this.initCloudData();
    }

    if (this.state.showPoweroffView) {
      return;
    }

    this.getSdcardState();
    // 重新进来 要绑定一遍这些事件。
    this.onGetFiles();// 从其他页面回来 要刷新一遍数据，避免出现其他页面删了  这个页面还没有同步数据的情况。

    Service.miotcamera.bindTimelinePlaybackEndListener(timelinePlaybackEndListenerName);
    // SD卡存在推出的情况，此时需要不播放直接，而切换到实时播放
    let timeItems = SdFileManager.getInstance().getTimeItems();
    this.refreshLineView();
    if (this.state.currentMode === MODE.SD && (CameraPlayer.getInstance().sdcardCode == 5 ||
      (CameraPlayer.getInstance().sdcardCode == 0 && (timeItems == null || timeItems.length ==0)))) {
      // 1、退出SD卡回到首页 2、一般场景为格式化SD卡后。 直接切换到直播吧
      this.setState({ currentMode: MODE.LIVE }, () => {
        this.queryNetworkJob();
      });
      return;
    }
    if (this.isUserPause) {
      console.log("++++++++++",this.isUserPause);
      return;
    }
    LogUtil.logOnAll("in resume method to connect");
    if (this.state.currentMode === MODE.CLOUD) {
      // 如果是删除云存视频后，这里直接播放是无法正常播放云存的
      if (!CameraConfig.needRefreshCloudVideo) {
        this._startQueryNetwork();
      }
    } else {
      this.queryNetworkJob();
    }

  }

  refreshLineView() {
    this.delayToRefreshLineView && clearTimeout(this.delayToRefreshLineView);
    this.delayToRefreshLineView = setTimeout(() => {
      if (this.videoLineView) {
        console.log("========this is do ");
        this.videoLineView && this.videoLineView.getItemPosition();
      }
    },500);


  }
  _onPause(ignoreState = false) {
    if (this.cameraRenderView != null) {
      this.cameraRenderView.stopRender();// stopRender
    }
    // CameraPlayer.getInstance().bindConnectionCallback(null);
    // CameraPlayer.getInstance().bindP2pCommandCallback(null);
    CameraPlayer.getInstance().bindPowerOffCallback(null);
    CameraPlayer.getInstance().bindPauseAllCallback(null);

    CameraPlayer.getInstance().bindNetworkInfoCallback(null);

    // 不移除
    // this.sdcardFilesListner && this.sdcardFilesListner.remove();
    // 不再去轮询查询SD卡状态
    this.getInfoIntervalID && clearInterval(this.getInfoIntervalID);
    if (this.state.showErrorView) {
      return;
    }
    if (this.state.showPoweroffView) {
      return;
    }
    console.log("MainCarPage", "onPause");
    if (this.state.currentMode === MODE.LIVE) {
      CameraPlayer.getInstance().stopVideoPlay();
    } else if (this.state.currentMode === MODE.SD) {
      this._startSdcardPlay(false);
    } else {
      this._startCloudPlay(false);
    }
  }

  _sendDirectionCmd(type) {
    if (this.state.isSleep) {
      return;
    }
    if (type == DirectionViewConstant.DIRECTION_lEFT || type == DirectionViewConstant.DIRECTION_RIGHT ||
      type == DirectionViewConstant.DIRECTION_UP || type == DirectionViewConstant.DIRECTION_BOTTOM) {
      clearTimeout(this.angleViewTimeout);
      this.setState({ showCameraAngleView: true, angleViewShowScale: false });
      this.angleViewTimeout = setTimeout(() => {
        this.setState({ showCameraAngleView: false, angleViewShowScale: false });
      }, 3000);
    }

    if ((type == DirectionViewConstant.DIRECTION_lEFT && this.state.angle == MAX_ANGLE) ||
      (type == DirectionViewConstant.DIRECTION_RIGHT && this.state.angle == MIN_ANGLE) ||
      (type == DirectionViewConstant.DIRECTION_UP && this.state.elevation == MAX_ELEVATION) ||
      (type == DirectionViewConstant.DIRECTION_BOTTOM && this.state.elevation == MIN_ELEVATION)) {

    }
    CameraPlayer.getInstance().sendDirectionCmd(type);
  }

  _connectionHandler = (connectionState) => {
    console.log("======_connectionHandler====", connectionState, new Date().getTime());
    this.loadingRetryTimer = 0;
    if (connectionState.state == MISSConnectState.MISS_Connection_ReceivedFirstFrame) {
      // if (this.hasFirstFrame || Platform.OS === "ios") {
      //   this.setState({ showDefaultBgView: false, whiteTitleBg: false, showLoadingView: false, showPlayToolBar: true });
      // }
      this.setState({ showDefaultBgView: false, whiteTitleBg: false, showLoadingView: false, showPlayToolBar: true });

    }
    LogUtil.logOnAll(TAG, "connect state", this.state.pstate, connectionState.state, this.state.error, connectionState.error,this.state.showPauseView);

    if (this.state.pstate == connectionState.state && this.state.error == connectionState.error) {
      return;// 状态一样 没有必要通知
    }

    if (connectionState.state == MISSConnectState.MISS_Connection_Disconnected) {
      this.isConnecting = false;
      // if (this.state.showPauseView) { // 如果显示暂停，就不显示error
      //   return;
      // }
      TrackConnectionHelper.onDisconnected();
      if (this.state.isSleep) {
        // 休眠状态下断开了连接，也不显示errorView
        return;
      }

      if (this.connRetry > 0 && this.state.pstate != connectionState.state) {
        this.connRetry = this.connRetry - 1;
        setTimeout(() => {
          Service.smarthome.reportLog(Device.model, `error retry connect: ${ this.connRetry }`);
          console.log("connection retry");
          this.queryNetworkJob();
        }, 300);
        return;
      }
      this.handleDisconnected(connectionState.error, true);
    }

    if (connectionState.state == MISSConnectState.MISS_Connection_Connected) { // onconnected 发送video-start
      this.loadingRetryTimer = new Date().getTime();
      console.log("++++++++++++++++2222222",this.isConnecting);
      if (!this.isConnecting) {
        return;
      }
      // this._sendDirectionCmd(DirectionViewConstant.CMD_GET);
      this.isConnecting = false;
      this.startVideoRetry = false;
      console.log("start send video start");
      if (this.state.currentMode === MODE.SD) {
        // sd卡，并且在播放中，开始播放，暂停时不播放
        // SD卡断连，一段时间后重连回来 MIECOCMCZ-212 会播放直播问题
        if (this.state.isPlaying || this.needRestartSDVideoPlay) {
          this._startSdcardPlay(true);
          this.needRestartSDVideoPlay = false;
        }
      } else {
        this._realStartVideo();

        TrackConnectionHelper.onConnected();
        this._hidePlayToolBarLater();
        // 获取SD卡视频数据
        this._loadSdfiles();
      }

    }
    if (connectionState.state == MISSConnectState.MISS_Connection_Connecting) {
      this.isConnecting = true;
    }
    if (connectionState.state >= MISSConnectState.MISS_Connection_Connected) {
      this.connRetry = 2;
      Service.miotcamera.bindRDTDataReceiveCallback(kRDTDataReceiveCallBackName);
      this.setState({ showErrorView: false });
      // Service.miotcamera.bindBPSReceiveCallback(kBpsDataReceiveCallbackName);
    }

    if (connectionState.state >= MISSConnectState.MISS_Connection_ReceivedFirstFrame) {

      this.connRetry = 2;
      // 获取下电机位置信息 暂时车机端不请求，暂时用不到
      // this._getRotateAngle();
      this.hasFirstFrame = true;
      if (this.showPlayToolBarTimer) {
        clearTimeout(this.showPlayToolBarTimer);
        this.showPlayToolBarTimer = null;
      }

      // if (!this.state.fullScreen) {
      //   this._hidePlayToolBarLater();
      // }
      this._hidePlayToolBarLater();

    }
    this.setState({
      pstate: connectionState.state,
      error: connectionState.error
    });
  };

  _getRotateAngle() {
    let param = { operation: 6 };
    Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_MOTOR_REQ, param)
      .then(() => {
        this.isSendingRdtCmd = true;
      })
      .catch((error) => {
        LogUtil.logOnAll(TAG, "查询角度命令发送失败:" + JSON.stringify(error));
        this.isSendingRdtCmd = false;
      });
  }

  _p2pCommandHandler = ({ command, data }) => {
    // 扩展程序注册命令回复回调，command为返回的命令号值，data 为P2P命令的返回数据。
    if (command == MISSCommand.MISS_CMD_SPEAKER_START_RESP) {
      if (this.forceSleep || this.state.showErrorView) {
        return;// 已经休眠或者显示了错误文案，就不打开麦克风
      }
      this.isClickCall = false;
      this.startSpeakerTime = new Date().getTime();
      console.log(' receive start speaker');
      let ba = base64js.toByteArray(data);

      if (ba.length > 0) {
        console.log('receive start speaker 0');
        console.log(ba[0]);
        if (Platform.OS === 'android') {
          if (ba[0] == 48) {
            console.log("start call in android");
            // this.isAudioMuteTmp = this.state.isMute;
            if (this.cameraGLView != null && !this.destroyed) {
              this.cameraGLView.startAudioRecord();
            }
            console.log("this.iscalling = true");
            this._toggleAudio(false);
            this.setState({ isCalling: true, showOneKeyCallDialog: false });
            AlarmUtil.putOneKeyCallStatus(2).then((res) => {
              LogUtil.logOnAll("putOneKeyCallStatus(2)", JSON.stringify(res));
            }).catch((err) => {
              LogUtil.logOnAll("putOneKeyCallStatus(2) err", JSON.stringify(err));
            });
            return;
          }
        } else {
          if (ba[0] == 0) {
            // this.isAudioMuteTmp = this.state.isMute;
            this._toggleAudio(false);
            this.callTimeout = setTimeout(() => {
              if (this.cameraGLView != null && !this.destroyed) {
                this.cameraGLView.startAudioRecord();
              }
            }, 800);// temp solution for bug MIIO-42838
            this.setState({ isCalling: true, showOneKeyCallDialog: false });
            AlarmUtil.putOneKeyCallStatus(2).then((res) => {
              LogUtil.logOnAll("putOneKeyCallStatus(2)", JSON.stringify(res));
            }).catch((err) => {
              LogUtil.logOnAll("putOneKeyCallStatus(2) err", JSON.stringify(err));
            });
            console.log("this.iscalling = true");
            return;
          }
        }
      }
      LogUtil.logOnAll("speak_failed because =", data);
      Toast.fail("speak_failed");
    } else if (command == MISSCommand.MISS_CMD_MOTOR_RESP) {
      console.log("received p2p angle resp");
      console.log(data); // {"ret":0,"angle":12,"elevation":1}
      if (this.isSendingRdtCmd) {
        LogUtil.logOnAll(TAG, "查询电机角度命令返回了：" + JSON.stringify(data));
      }
      try {
        if (typeof (data) == 'string') {
          data = JSON.parse(data);
        }
        this.angleData = data;

        let angleValue = Number(data.angle);
        let elevationValue = Number(data.elevation);
        let result = Number(data.ret);

        if (typeof (angleValue) == 'number' && typeof (elevationValue) == 'number') {
          if (angleValue < 0 || angleValue > 101 || elevationValue < 0 || elevationValue > 101) {
            Service.smarthome.reportLog(Device.model, "illegal angle or elevation value:" + angleValue + " " + elevationValue);
            return;
          }
          if (this.isSendingRdtCmd) {// c01全景绘制后，获取电机方向改到这里了
            this.isSendingRdtCmd = false;
            this.setState({ angle: angleValue, elevation: elevationValue });
            if (this.showPanoAfterReceivedRotateAngle) {
              this.setState({ panoViewStatus: 3 });
              this.showPanoAfterReceivedRotateAngle = false;
              if (this.showPanoToastAfterReceivedRotateAngle) {
                Toast.success("pano_view_success");
                this.showPanoToastAfterReceivedRotateAngle = false;
              }
            }
            // if(this.state.showPanoView){
            //   this.setState({panoViewStatus:3, angle: positionX, elevation: positionY})
            // }
            this.isSendingRdtCmd = false;// end
            return;
          }
          if (Date.now() - this.lastLogTime > 1000) {
            LogUtil.logOnAll("receive ptz direction log: angleValue:" + angleValue + " elevationValue:" + elevationValue);
          }
          this.lastLogTime = Date.now();

          this.setState({ angleViewShowScale: false, angle: angleValue, elevation: elevationValue });
          if (result < 0) {
            if ((result == DirectionViewConstant.CMD_CHECK_END) && !this.state.showPoweroffView) {
              Toast.success("camera_celibrating");
            } else if (result <= -1 && result >= -4 && !this.state.showPoweroffView) {
              if (!this.isShowPtzEnd) {
                this.isShowPtzEnd = true;
                Toast.fail("car_camera_direction_end");
                this.resetShowPtzEndToast && clearTimeout(this.resetShowPtzEndToast);
                this.resetShowPtzEndToast = setTimeout(() => {
                  this.isShowPtzEnd = false;
                }, 2000);
              }
            }
          } else {
          }
        }

      } catch (exception) {
        console.log(`parse angle data error: ${ exception }`);
      }

    } else if (command == MISSCommand.MISS_CMD_CRUISE_STATE_RESP) {
      LogUtil.logOnAll("received cruise state resp", data);
      if (typeof (data) == 'string') {
        data = JSON.parse(data);
      }
      if (data.value == 1) {
        this.setState({ isCruising: true });
      } else {
        this.setState({ isCruising: false });
      }
    } else if (command == MISSCommand.MISS_CMD_CALL_STATUS_RESP) {
      LogUtil.logOnAll("received CALL_STATUS_RESP", data);
      if (typeof (data) == 'string') {
        data = JSON.parse(data);
      }
      if (data.type == "hang_up") {
        LogUtil.logOnAll("received CALL_STATUS_RESP do stopCall");
        this._stopCall();
      } else {
        LogUtil.logOnAll("received CALL_STATUS_RESP type=", data.type);
      }
    } else if (command == MISSCommand_ECO.MISS_CMD_DEVICE_SEND) {
      // 设备端像插件发送数据
      let ba = typeof (data) === 'string' ? JSON.parse(data) : data;
      LogUtil.logOnAll(`receive MISS_CMD_DEVICE_SEND:${ command } data:${ ba }`);
    } else if (command == MISSCommand_ECO.MISS_CMD_NETWORK_STATUS) {
      let ba = base64js.toByteArray(data);
      LogUtil.logOnAll(`receive MISS_CMD_NETWORK_STATUS:${ command } data:${ ba }`);

    } else if (command == MISSCommand.MISS_CMD_PLAYBACK_RESP) {
      // 回看播放的回调
      console.log(data);
      let dataJSON = Host.isAndroid ? JSON.parse(data) : data;
      let id = dataJSON["id"];
      if (id == null) {
        return;
      }
      if (id != this.sessionId) {
        return;
      }
      let status = dataJSON["status"];
      if (status == null) {
        return;
      }
      let startTime = dataJSON.starttime;
      let duration = dataJSON.duration;
      switch (status) {
        case "filefound":

          console.log(dataJSON);
          LogUtil.logOnAll("SdcardTimeline", "filefound:" + JSON.stringify(dataJSON));//收到的视频帧信息
          // what todo ?
          // this.setState({ showLoadingView: false });
          // 开始请求timestamp
          this.endTime = startTime + duration;
          this.toSDStartTime = startTime * 1000;
          //文件找到后才开始拉取时间戳；
          if (this.firstPullVideoFrameTime != 0) {
            let firstVideoFrameShowed = Date.now() - this.firstPullVideoFrameTime;
            LogUtil.logOnAll(TAG, "sdcard connectTime:" + this.connectionTime + " pullSdcardFileList:" + this.sdcardListRequestTime + " firstVideoFrameShowd:" + firstVideoFrameShowed);
            this.firstPullVideoFrameTime = 0;
          }
          if (this.videoLineView) {
            // 播放下一段视频了，需要更新位置信息
            let { item, itemInfo } = this.videoLineView.findItemInfoByTimestamp(startTime * 1000);
            this.sdItemInfo = itemInfo;
            this.sdCurrentPlayItem = item;
            console.log(TAG, "fileFound:",this.sdItemInfo,this.sdCurrentPlayItem,this.toSDStartTime)
            if (this.toSDStartTime > 0) {
              this.videoLineView && this.videoLineView.scrollToPositionByTimestamp(this.sdCurrentPlayItem, this.sdItemInfo, this.toSDStartTime);
            }
          }
          CameraPlayer.getInstance().getPlaybackTimetampInterval();
          CameraPlayer.getInstance().bindPlaybackTimestampCallback(this.timestampCallback);


          break;
        case "filenotfound":
          this.setState({
            progress: 0,
            showErrorView: false,
            showPlayErrorView: true,
            showLoadingView: false,
            showPlayToolBar: false,
            showPauseView: false,
            errTextString: LocalizedStrings["camera_play_error_file"]
          });

          this._startSdcardPlay(false);
          break;
        case "endoffile":
          // this.isUserPause = true;
          // this.setState({ showLoadingView: false, progress: 0 });
          // this._startPlay(false);
          // this.setState({ showPauseView: true });
          // 取消timestamp的定时请求
          if (this.islastestFilePlayFailed && (this.penultimateStartTime + 60000 - this.endTime * 1000 < 1500)) { // 上一次播放最后一个文件失败了，这一次播放到倒数第二个文件就不播放了。
            this.toSdcardEnd();
          }
          // CameraPlayer.getInstance().stopPlaybackTimestampInterval();

          LogUtil.logOnAll("SdcardTimeline", "endoffile:" + JSON.stringify(dataJSON) + " last endTime:" + (this.endTime * 1000) + " lastest endTime:" + this.sdcardLastTimeItemEndTime);//收到的视频帧信息
          if (this.sdcardLastTimeItemEndTime != 0 && this.sdcardLastTimeItemEndTime - this.endTime * 1000 < 1500) {
            this.toSdcardEnd();
          }
          break;
        case "readerror":

          this.setState({
            progress: 0,
            showLoadingView: false,
            showErrorView: false,
            showPlayErrorView: true,
            showPauseView: false,
            showPlayToolBar: false,
            errTextString: LocalizedStrings["camera_play_error_file"]
          });
          this._startSdcardPlay(false);
          // 取消timestamp的定时请求
          CameraPlayer.getInstance().stopPlaybackTimestampInterval();
          break;
      }
    } else if (command == MISSCommand_ECO.MISS_CMD_HUMAN_POSITION) {
      // console.log("=========================",data);

      if (typeof (data) == 'string') {
        try {
          data = JSON.parse(data);
        } catch (err) {
          return;
        }
      }

      let params = data.param;
      this.lastGetDataTime = Math.round(new Date().getTime() / 1000);
      if (params == null || !(params instanceof Array || params.length == 0)) {
        return;
      }
      // 数据是否需要丢弃，前后两个数据很接近时，新来的数据不做处理防止人脸基本静止时，画面仍会晃动缩放
      if (this.state.isLockOpen && this.ignoreLockData(params)) {
        // 最新的固件返回的数据，这里不赋值，影响解除锁定时数据的判断
        // this.lockData = params;
        this.delayResetLock();
        // 会打印很多丢掉的信息
        // LogUtil.logOnAll(TAG, "丢给掉了", params);
        return;
      }
      // 最新的固件返回的数据
      this.lockData = params;

      // 是否需要头像锁定，需要有个开启的标记位
      if (!this.state.isLockOpen) {
        return;
      }
      // let param = params[0];
      let param = this.findLockData(params);
      if (param == null) {
        return;
      }
      // 进行了一次缩放。
      let coordination = param.box;
      let humanId = param.id;
      if (param.type != 1 && param.type != 5) {
        // 非人形  非人脸不做处理
        return;
      }
      this.lastHfStartTime = this.hfStartTime;
      this.hfStartTime = new Date().getTime();
      this.hfhId = humanId;
      // console.log("+++++++++++++", humanId, this.lastHumanId, param);
      if (humanId == -1) {
        LogUtil.logOnAll(TAG, "lock release", `锁定ID：${this.lastHumanId}  新收到的ID:${humanId}  最新收到的人脸参数:${JSON.stringify(param)}`);
        // return;
      }
      if (this.lastHumanId != humanId) {
        //人形特征不一样了，重置一下, 为
        LogUtil.logOnAll(TAG, "lock release", `锁定ID：${this.lastHumanId}  新收到的ID:${humanId}  最新收到的人脸参数:${JSON.stringify(param)}`);
        // this.startResetLock();
        // this.tempHumanId = humanId;
        // this.releaseLockTimer && clearTimeout(this.releaseLockTimer);
        // this.resetLockStatus();
        // this.lastHumanId = humanId;
        // 重置上次坐标。
        this.lastCoordination = null;
        return;
      }
      this.startLockTimer && clearTimeout(this.startLockTimer);
      this.lastHumanId = humanId;
      // let currentCoordination = null;
      // try {
      //   currentCoordination = JSON.parse(`[${coordination}]`);
      //   this.hfCoord = currentCoordination;
      //   this.iou = calculateIoU(currentCoordination, this.lastCoordination);
      //   this.CoordAndIOU = {coord: this.hfCoord, lastCoord: this.lastCoordination, iou: this.iou};
      //   if (this.iou > 0.35 && (this.state.enablePip && !this.stopRenderSubCameraView)) { // pip显示中 并且相似度高  就过滤。
      //     // 相似度太高
      //     this.delayResetLock();
      //     return;
      //   }
      // } catch (err) {
      //
      // }

      let result = calculateScaleParamByHumanFeature(param); // 1.0的比例也要设置进去。 例如人形特别大 就会显示成1.0的比例。 原来是把1.0过滤丢掉了。
      // NativeModules.MHCameraSDK.setPositionParam(Device.deviceID, { scale: result.scale, offsetY: result.offsetY, offsetX: result.offsetX, chan: 0 });
      this.createHumanFeatureAnimation(this.lastResult == null ? { scale: 1, offsetX: 0, offsetY: 0 } : this.lastResult, result);//执行动画。
      this.lastResult = result;
      this.delayResetLock();
    } else {
      console.log(`receive other command:${ command } data:${ JSON.stringify(data) }`);
    }
  };

  // ===================================== 头像锁定start ===================================================
  ignoreLockData(data) {
    // 判断双击的位置，是否在符合条件的box中
    // 筛选出人形、人脸的数据，里面有其他数据比如宠物的过滤掉
    if (this.lockData == null) {
      return false;
    }

    let oldHumanData = this.lockData.filter((n) => n.type == 5);
    let humanData = data.filter((n) => n.type == 5);
    if (!oldHumanData || oldHumanData.length == 0 || !humanData || humanData.length == 0) {
      return false;
    }
    // 找上一个id的
    let oldLock = oldHumanData.find((item) => item.id == this.lastHumanId);
    let newLock = humanData.find((item) => item.id == this.lastHumanId);
    // 只考虑id相同的情况
    if (oldLock == null || newLock == null) {
      return false;
    }
    let oldBox = JSON.parse(oldLock.box);
    let newBox = JSON.parse(newLock.box);
    let iou = calculateIoU(newBox, oldBox);
    if (iou < 0.2) {
      LogUtil.logOnAll(TAG, "IOU < 0.35", `new box: ${newBox}`, `old box: ${oldBox}`, `lock ID: ${this.lastHumanId}`);
    }
    if (iou > 0.80 && !this.shouldChangeToTheFace) {
      this.shouldChangeToTheFace = false;
      return true;
    }
    return false;
  }

  findLockData(data) {
    // 判断双击的位置，是否在符合条件的box中
    // 筛选出人形、人脸的数据，里面有其他数据比如宠物的过滤掉
    // let humanData = data.filter((n) => n.type == 1 || n.type == 5);
    let humanData = data.filter((n) => n.type == 5);

    if (!humanData || humanData.length == 0) {
      return null;
    }
    // 找上一个id的
    let lastLock = humanData.find((item) => item.id == this.lastHumanId);
    if (lastLock != null) {
      return lastLock;
    }
    // 上一个追踪对象不见了
    // 找score的最大的值
    const maxBox = humanData.reduce((maxObj, currentObj) => {
      return (currentObj.score > maxObj.score) ? currentObj : maxObj;
    }, humanData[0]);
    return maxBox;
  }

  // 头像锁定
  // 5s后无新的事件，则解除锁定
  delayResetLock = () => {
    this.releaseLockTimer && clearTimeout(this.releaseLockTimer);
    this.releaseLockTimer = setTimeout(() => {
      LogUtil.logOnAll(TAG, "5s no data receive");
      this.resetLockStatus();
    }, 5000);
  }

  startResetLock = () => {
    if (this.isStartResetLock) {
      return;
    }
    this.isStartResetLock = true;
    this.startLockTimer && clearTimeout(this.startLockTimer);
    this.startLockTimer = setTimeout(() => {
      LogUtil.logOnAll(TAG, "5s no data receive");
      if (this.tempHumanId) {
        this.lastHumanId = this.tempHumanId;
      }
      this.resetLockStatus();
    }, 5000);
  }


  resetLockStatus() {
    this.setState({isLockOpen: false});
    LogUtil.logOnAll(TAG, "resetLockStatus", this.lastResult,this.state.videoScale);
    // NativeModules.MHCameraSDK.setPositionParam(Device.deviceID, { scale: 1, offsetY: 0, offsetX: 0, chan: 0 });

    this.createHumanFeatureAnimation(this.lastResult == null ? {scale: 1, offsetX: 0.0, offsetY: 0.0, chan: 0} : this.lastResult, {scale: 1, offsetX: 0.0, offsetY: 0.0, chan: 0}, () => {
      this.lastResult = {scale: this.state.videoScale, offsetX: 0.0, offsetY: 0.0, chan: 0};
    });
  }

  createHumanFeatureAnimation(from, to, callback) {
    // from obj == {scale: xxx, offsetX:xxx, offsetY:xxx}
    // from 0 - 1
    NativeModules.MHCameraSDK.setPositionParam(Device.deviceID, { scale: from.scale, offsetY: from.offsetY, offsetX: from.offsetX, chan: 0 });

    this.mHFAnimationInterval && clearInterval(this.mHFAnimationInterval);
    this.runningAnimation?.stop();
    this.chanAnimator?.stopAnimation();
    // this.setState({ hfScale: from.scale, hfOffsetX: from.offsetX, hfOffsetY: from.offsetY });

    this.localStoreFrom = from;
    this.localStoreTo = to;
    this.chanAnimator.setValue(0)

    // LogUtil.logOnAll(`HF::::, animation begin, from: ${ JSON.stringify(from) } to: ${ JSON.stringify(to) }`);

    this.hfStartAimateTime = new Date().getTime();
    this.hfStart2AnimateStart = this.hfStartAimateTime - this.hfStartTime;
    this.runningAnimation = Animated.timing(this.chanAnimator, {
        toValue: 1,
        duration: 300
      })
      .start(() => {
        this.hfAnimatDuration = new Date().getTime() - this.hfStartAimateTime;
        // LogUtil.logOnAll(`HF::::, animation end, from: ${ JSON.stringify(from) } to: ${ JSON.stringify(to) }`);
        // animation ended
        callback?.();
        NativeModules.MHCameraSDK.setPositionParam(Device.deviceID, { scale: to.scale, offsetY: to.offsetY, offsetX: to.offsetX, chan: 0 });
        this.mHFAnimationInterval && clearInterval(this.mHFAnimationInterval);
      });

    this.mHFAnimationInterval = setInterval(() => {
      // if (this.chanAnimator)
      {
        let hfScale = this.chanAnimator.interpolate({ inputRange: [0, 1], outputRange: [this.localStoreFrom.scale, this.localStoreTo.scale] }).__getValue();
        let hfOffsetX = this.chanAnimator.interpolate({ inputRange: [0, 1], outputRange: [this.localStoreFrom.offsetX, this.localStoreTo.offsetX] }).__getValue();
        let hfOffsetY = this.chanAnimator.interpolate({ inputRange: [0, 1], outputRange: [this.localStoreFrom.offsetY, this.localStoreTo.offsetY] }).__getValue();
        // console.log('anmiation...', hfScale, hfOffsetX, hfOffsetY);
        // this.lastResult = { scale: hfScale, offsetX: hfOffsetX, offsetY: hfOffsetY };
        NativeModules.MHCameraSDK.setPositionParam(Device.deviceID, { scale: hfScale, offsetY: hfOffsetY, offsetX: hfOffsetX, chan: 0 });
        // this.setState({ hfScale, hfOffsetX, hfOffsetY });
      }
    }, 30);
  }
  // ===================================== 头像锁定end ===================================================

  // 隐藏
  _hidePlayToolBarLater() {
    let tTimer = 5000;
    clearTimeout(this.showPlayToolBarTimer);
    this.showPlayToolBarTimer = setTimeout(() => {
      this.setState({ showPlayToolBar: false });
    }, tTimer);
  }

  // 隐藏日期
  _hideDayWeekLater() {
    console.log("++++++++++++=this is 1111do");
    let tTimer = 5000;
    this.showDayWeekTimer && clearTimeout(this.showDayWeekTimer);
    this.showDayWeekTimer = setTimeout(() => {
      console.log("++++++++++++=this is do");
      this.setState({ showDayList: false });
    }, tTimer);
  }

  _realStartVideo() {
    if (this.state.showPauseView) {
      this.setState({ showPauseView: false });
    }
    Service.smarthome.reportLog(Device.model, "send videoStart cmd");
    this.cameraGLView && this.cameraGLView.stopRender();
    this.cameraGLView && this.cameraGLView.startRender();// startBindVideo
    Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_VIDEO_START, {})
      .then((retCode) => {
        LogUtil.logOnAll("startVideo success ", retCode, "mute", this.state.isMute);
        // 加延迟是因为立马执行动画无效
        this.delayStartDotAnim && clearTimeout(this.delayStartDotAnim);
        this.delayStartDotAnim = setTimeout(() => {
          this.startBlinkAnimation();
        }, 500);
        if (this.cameraGLView == null || this.destroyed) {
          return;
        }
        if (this.state.pstate == 3) {
          this.setState({ showLoadingView: false });// 已经渲染过  直接跳过去
        }
        if (!this.state.isMute || this.state.isRecording) {
          // need renew AudioQueueRef for sound play
          console.log("=====MISS_CMD_VIDEO_START===");
          if (!this.state.isRecording) {
            this.cameraGLView.stopAudioPlay();
          }

          // TODO 暂时注释掉音频
          // if (!this.state.isMute) {
          //   Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {}).then((retCode) => {
          //     console.log("resume audioplay ", retCode);
          //   });
          // }

          // if (!this.state.isRecording) {
          //   this.cameraGLView && this.cameraGLView.startAudioPlay();
          // }

        }
        // 每次video-start都重新发送一次quality change吧
        // if (this.videoQualityFetched || this.state.isRecording) {
        //   this.sendResolutionCmd(this.state.isRecording ? 3 : this.state.resolution, this.state.isRecording ? true : false);// 每次收到firstFrame都需要刷新resolution，避免去往其他页面再回来，分辨率变低了却不知道
        // }

        this.sendResolutionCmd(3);
        // this.sendResolutionCmd(1);
      })
      .catch((err) => {
        console.log("++++++++++发送开始视频失败", err);
        this.cameraGLView && this.cameraGLView.stopRender();
        if (err == -1 || err == -8888) { // - 8888重置本地连接，然后开始重连。
          CameraPlayer.getInstance().resetConnectionState();
          Service.smarthome.reportLog(Device.model, "video-start的时候出错了:" + err);
          console.log("video-start error");
          this.queryNetworkJob();
          return;
        }

        this.setState({
          pstate: 0,
          showLoadingView: false,
          showErrorView: true,
          errTextString: `${ LocalizedStrings["camera_connect_error"] } ${ err } ${ LocalizedStrings["camera_connect_retry"] }`
        });// 已经渲染过  直接跳过去
      });

  }

  sendResolutionCmd(index, ignoreState = false) {
    Service.miotcamera.sendP2PCommandToDevice(
      MISSCommand.MISS_CMD_STREAM_CTRL_REQ, { "videoquality": index })
      .then(() => {
        console.log("============分辨率");
        // if (!ignoreState) {
        //   this.setState({ resolution: index });
        // }
        // StorageKeys.LIVE_VIDEO_RESOLUTION = index;
      })
      .catch((err) => {
        console.log(err);
      });
  }

  getDeviceSpecValue() {
    let params = [
      { did: Device.deviceID, siid: CAMERA_CONTROL_CAR_SIID, piid: CAMERA_CONTROL_SLEEP_PIID },
      { did: Device.deviceID, siid: COCKPIT_SERVICE_SIID, piid: COCKPIT_SERVICE_WORK_MODE_PIID },
      { did: Device.deviceID, siid: COCKPIT_SERVICE_SIID, piid: COCKPIT_SERVICE_TEMPERATURE_STATUS }
    ];
    Service.spec.getPropertiesValue(params, 2)
    // AlarmUtilV2.getSpecPValue([{ sname: SIID_CAMERA_CONTROL, pname: PIID_ON }])
      .then((result) => {
        LogUtil.logOnAll(TAG, "getDeviceSpecValue", result);
        let isOk = result[0].code == 0;
        this.isFirstEnter = false;
        if (isOk) {
          TrackConnectionHelper.onPowerEndChecked();
          let isPowerOn = result[0].value;
          this.isPowerOn = isPowerOn;
          this._powerOffHandler(isPowerOn, false, false);//powerOff notify ui only

        } else {
          this._powerOffHandler(true, false, false);
        }
        let stateProps = {};
        if (result[1].code == 0) {
          // 1为限制工作模式
          stateProps.limitMode = result[1].value == 1;
        }

        if (result[2].code == 0) {
          stateProps.temperatureState = result[2].value;
        }
        stateProps.loadingData = false;
        this.setState(stateProps);
      })
      .catch((error) => {
        TrackConnectionHelper.onPowerEndChecked();
        this.isFirstEnter = false;
        StorageKeys.IS_POWER_ON
          .then((res) => {
            if (typeof (res) == "string" || res === null) {
              res = true;// 没有设置过，默认当做是非休眠状态吧。
            }
            this._powerOffHandler(res, false, false);
          })
          .catch((err) => { // 查询本地属性都失败了。
            this._powerOffHandler(true, false, false);
          });
        this.setState({ loadingData: false });
      });
  }

  queryNetworkJob() {
    if (this.isUserPause && this.state.currentMode === MODE.SD) {
      LogUtil.logOnAll(TAG, "queryNetworkJob userPause", this.isUserPause);
      // 更新下日期
      this.setSelectedDay(this.toSDStartTime);
      return;// 用户主动暂停的
    }
    if (!this.props.navigation.isFocused()) {
      LogUtil.logOnAll(TAG, "queryNetworkJob focus", this.props.navigation.isFocused());
      return;
    }
    console.log(TAG, "start connect", new Date().getTime());
    // 需要考虑是4G还是无网络，及时无网络热点走局域网，仍然能看直播，连接依然需要走下去
    this._startConnect();
    // CameraPlayer.getInstance().queryShouldPauseOn4G()
    //   .then(({ state, pauseOnCellular }) => {
    //     this.networkType = state;
    //     if (state === "NONE" || state === "UNKNOWN") {
    //       this._networkChangeHandler(0);
    //       return;
    //     }
    //     // this._networkChangeHandler(0);
    //     // 其他网络条件 走连接的步骤吧
    //     this._startConnect();// 开始连接
    //   })
    //   .catch(() => { // 获取网络状态失败 也直接走开始连接的流程
    //     this._startConnect();// 开始连接
    //   });
  }

  _networkChangeHandler = (networkState) => {
    console.log("处理网络变化", networkState);
    LogUtil.logOnAll("_networkChangeHandler",networkState);
    this.currentNetworkState = networkState;
    // 网络监听的状态保留，不再展示设备异常提示，局域网直播
    return;
    // this.showNetworkDisconnectTimeout && clearTimeout(this.showNetworkDisconnectTimeout);
    // if (networkState == 0 || networkState == -1) { // 网络断开了连接 showError?
    //   Service.smarthome.reportLog(Device.model, "网络异常" + networkState);
    //   // CameraPlayer.getInstance().disconnectToDevice();// 去往其他注册了网络监听的页面，就不会走到这里了，如果走到这里，这里必须先执行断开的操作
    //   this.showNetworkDisconnectTimeout = setTimeout(() => {
    //     this.handleDisconnected(MISSError.MISS_ERR_CLOSE_BY_LOCAL);// 刷新UI，避免出现异常。
    //   }, 1300);
    //   return;
    // }
    // if (this.isPageForeGround) { // 有网络来了  发起重连吧
    //   this.setState({ showErrorView: false });
    //   clearTimeout(this.reConnectTimeout);
    //   this.reConnectTimeout = setTimeout(() => {
    //     this.queryNetworkJob();
    //   }, 500);// 过一会再查询 ，那个查询网络类型的api有问题。
    // }
  };

  _powerOffHandler = (isPowerOn, popSleepDialog, shouldStartVideo = false) => {
    // 远端的下发的状态与本地的状态不一致 就回走到这里
    if (!isPowerOn) { // 变成了休眠
      this.isClickSleep = false;
      this.isPowerOn = false;
      this.needRestartSDVideoPlay = true;
      CameraPlayer.getInstance().setPowerState(false);

      // this.cameraGLView.stopAudioPlay();//释放资源
      // Service.miotcamera.disconnectToDevice();//断开连接
      this._stopAll(this.state.showPauseView, false, false);
      if (this.state.isPlaying) {
        // 正在播放中，不管是播放云存还是播放SD卡，都需要暂停
        if (this.state.currentMode === MODE.SD) {
          this._startSdcardPlay(false);
        } else if (this.state.currentMode === MODE.CLOUD) {
          this._startCloudPlay(false);

        }
      }
      this.setState(() => {
        return {
          isSleep: true,
          showPlayToolBar: false,
          showPoweroffView: true,
          showPanoView: false,
          // showLoadingView: false,
          showPauseView: false,
          showErrorView: false,
          errTextString: LocalizedStrings['device_not_open_warning'],
          showTargetPushView: false
        };
      }, () => {
        this.setState({
          panoViewStatus: 2
        });// 切换休眠唤醒的时候置为初始状态
      });
      this.cameraGLView?.stopRender();
      CameraPlayer.getInstance().stopVideoPlay();
      this._toggleAudio(true, false);
      console.log("++++++++stop anim")
      // this.state.dotOpacity.setValue(0);
      // this.blinkAnimation && this.blinkAnimation.stop();

    } else { // 唤醒了
      this.isClickSleep = false;
      this.setState({ isSleep: false, showPlayToolBar: true, showPoweroffView: false });
      Service.smarthome.reportLog(Device.model, "on wake up");
      this.isPowerOn = true;
      CameraPlayer.getInstance().connectionState = { state: 0, error: MISSError.MISS_ERR_CLOSE_BY_LOCAL };
      CameraPlayer.getInstance().setPowerState(true);
      if (shouldStartVideo) {
        // 不需要获取SD卡相关状态
        // this._toggleAudio(CameraConfig.getUnitMute());
        if (this.state.currentMode === MODE.LIVE || this.state.currentMode === MODE.SD) {
          this.getKeyIp();
        } else {
          this._startQueryNetwork();
        }

        if (this.state.currentPlaybackMode === PLAYBACK_MODE.SD) {
          let timeItems = SdFileManager.getInstance().getTimeItems();
          if (timeItems == null || timeItems.length <= 0) {
            return;
          }
          this.timelineView && this.timelineView.initData(timeItems);
        } else {
          this.timelineView && this.timelineView.onReceiveCloudDatas();

        }
      }
    }
  };

  _workModeCallback = (value) => {
    console.log("=========work mode is change==========");
    if (value == 0) {
      // 正常工作模式
      this.setState({ limitMode: false }, () => {
        CameraConfig.needRefreshCloudVideo = true;
        this._onResume();
      });
    } else if (value == 1) {
      // 限制工作模式
      this.setState({ limitMode: true }, () => {
        // 需要停掉 播放器的播放
        if (this.state.currentMode === MODE.LIVE) {
          this._stopAll();
        } else if (this.state.currentMode === MODE.CLOUD) {
          this._startCloudPlay(false);
        } else {
          this._startSdcardPlay(false);
        }
      });
    }
  }

  _temperatureCallback = (value) => {
    console.log("=========temperatureStateCallback change==========");
    if (value == 0) {
      // 正常
      this.setState({ temperatureState: 0 }, () => {
        CameraConfig.needRefreshCloudVideo = true;
        // 查询下设备状态
        this.setState({ showLoadingView: true });
        this.keyIpCount = 12;
        this.getLocalSetting();
        this.initMethod();
      });
    } else if (value == 1) {
      // 高温
      this.setState({ temperatureState: 1 }, () => {
        // 需要停掉 播放器的播放
        if (this.state.currentMode === MODE.LIVE) {
          this._stopAll();
        } else if (this.state.currentMode === MODE.CLOUD) {
          this._startCloudPlay(false);
        } else {
          this._startSdcardPlay(false);
        }
      });
    }
  }

  _sdCallback = (value) => {
    if (value == 0) {
      // 正常
      if (this.state.sdcardCode != 0) {
        CameraPlayer.getInstance().sdcardCode = 0;
        this.setState({ sdcardCode: 0 });
        SdFileManager.getInstance().clearSdcardFileList();
        this.isSdcardDayFirstReceive = true;
        // 加延迟的原因为刚插入SD卡，无法正常获取到SD卡数据
        this.sdDelayToRequestFile && clearTimeout(this.sdDelayToRequestFile);
        this.sdDelayToRequestFile = setTimeout(() => {
          this._loadSdfiles();
        }, 5000);
      }
    } else if (value == 4) {
      if (!this.isPageForeGround) {
        // 不在前台就不去处理格式化中的逻辑了
        return;
      }
      // 格式化中 需要去轮询SD卡状态，待SD卡正常后
      this.setState({ sdcardCode: 4 });
      this._formatSdCardTimes = 0;
      this.getInfoIntervalID && clearInterval(this.getInfoIntervalID);
      this.getInfoIntervalID = setInterval(() => {
        this._formatSdCardTimes++;
        console.log("+++++++++++++++++_sdCallback===========")
        this._getFormatStatus();
      }, 5000);
    } else {
      if (!this.isPageForeGround) {
        // 不在前台就不去处理格式化中的逻辑了
        return;
      }
      if (value == 1 && this.state.currentMode == MODE.SD) {
        this._startSdcardPlay(false);
        // 如果当前正处于播放SD卡回看，检测到SD卡被拔出，则切换到实时查看
        this.setState({ currentMode: MODE.LIVE, showPlayErrorView: false }, () => {
          Service.miotcamera.setTimelinePlaybackMode(false);
          // 恢复播放器视图尺寸
          NativeModules.MHCameraSDK.setPositionParam(Device.deviceID, { scale: 1, offsetY: 0, offsetX: 0, chan: 0 });
          this._startConnect();
        });
      }

      // 异常了，需要清空数据列表
      this.setState({ sdcardCode: value, sdListData: [], isLoading: false });
    }
  }


  _stopAll(showPauseView = false, setUnitMute = true, needSetPlayToolBar = true) {

    this._toggleAudio(true, setUnitMute);
    CameraPlayer.getInstance().stopVideoPlay();
    if (this.cameraGLView != null && !this.destroyed) {
      this.cameraGLView.stopRender();// stopRender
    }
    this.hideBgBpsCount = 0;
    if (needSetPlayToolBar) {
      this.setState({ showPauseView: showPauseView, showLoadingView: false, showPlayToolBar: false });
    } else {
      this.setState({ showPauseView: showPauseView, showLoadingView: false });
    }
  }

  _toggleSleep(isSleep) {
    this.isAllViewRpc = false;


    if (this.isClickSleep) {
      return;
    }

    if (isSleep) {
      this.forceSleep = true;
    } else {
      this.forceSleep = false;
    }
    this.isClickSleep = true;

    this.lastClickSleepTime = new Date().getTime();

    SpecUtil.toggleSleep(isSleep)
      .then(() => {
        this._powerOffHandler(!isSleep, true, true);

      })
      .catch((err) => {
        this.isClickSleep = false;
        Toast.fail("action_failed");
      });
  }

  _toggleAudio(isMute, changeUnitMute = true) {

    if (this.cameraGLView == null || this.destroyed) {
      return;
    }
    console.log(TAG, "isMute:" + isMute + " changeUnitMUte:" + changeUnitMute);
    if (isMute) {
      if (this.state.isMute) { // 已经静音
        return;
      }
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_STOP, {}).then((retCode) => {
        console.log("audio stop get send callback");
        console.log(retCode);
      });

      console.log(TAG, "stopAudioPlay called");
      this.cameraGLView.stopAudioPlay();
      this.setState({ isMute: true });
      if (changeUnitMute) {
        CameraConfig.setUnitMute(true);
      }
      return;
    }
    Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {}).then((retCode) => {
      console.log("audio start get send callback");
      console.log(retCode);
    });
    this.cameraGLView && this.cameraGLView.startAudioPlay();
    this.setState({ isMute: false });
    CameraConfig.setUnitMute(false);

  }


  // 网络异常
  handleDisconnected(errorCode, isP2pConnect = false) {
    this.cameraRenderView && this.cameraRenderView.stopAudioPlay();
    this.cameraRenderView && this.cameraRenderView.stopRender();
    // sync ui state
    // this.setState({ isMute: CameraConfig.getUnitMute() });

    if (!Device.isOnline) {
      return;
    }
    if (this.state.currentMode === MODE.CLOUD && isP2pConnect) {
      return;
    }
    // let errorStr = ((errorCode == 36 || errorCode == MISSError.MISS_ERR_MAX_SESSION) && VersionUtil.judgeIsMiss(Device)) ? LocalizedStrings["max_client_exceed"] : (errorCode == -6 && !VersionUtil.judgeIsMiss(Device) ? LocalizedStrings["max_client_exceed"] : `${ LocalizedStrings["camera_connect_error"] } ${ errorCode }, ${ LocalizedStrings["camera_connect_retry"] }`);
    let errorStr = ((errorCode == 36 || errorCode == MISSError.MISS_ERR_MAX_SESSION) && VersionUtil.judgeIsMiss(Device)) ? LocalizedStrings["max_client_exceed"] : (errorCode == -6 && !VersionUtil.judgeIsMiss(Device) ? LocalizedStrings["max_client_exceed"] :  LocalizedStrings["device_error"]);

    this.setState({
      showPlayToolBar: false,
      showErrorView: true,
      isPlaying: false,
      showLoadingView: false,
      errTextString: errorStr
    });
  }

  _startConnect() {
    if (this.state.limitMode || this.state.temperatureState === TEMPERATURE_STATE.HIGH) {
      //高温 限制工作模式，不执行后续操作
      LogUtil.logOnAll(TAG,"in limited mode");
      this.setState({ showLoadingView: false });
      return;
    }
    if (!this.props.navigation.isFocused()) { // 当前页面已经不在前台了
      this.setState({ showLoadingView: false });
      return;
    }
    if (!this.state.showLoadingView && this.state.currentMode !== MODE.CLOUD) { // 如果没有loading
      // 播放云存视频的时候，重新连接p2p不要显示loading
      this.setState({ showLoadingView: true });
    }
    if (this.state.showPauseView) {
      this.setState({ showPauseView: false });
    }
    if (this.state.showPlayErrorView) {
      this.setState({ showPlayErrorView: false });
    }
    console.log("3333333333333")

    // 开始连接
    if (CameraPlayer.getInstance().isConnected()) {
      // 如果已经连接成功 直接发送video_start
      this.setState({ pstate: 2, error: 0 });
      if (this.state.currentMode === MODE.LIVE) {
        this._realStartVideo();
      } else if (this.state.currentMode === MODE.SD && this.state.currentPlaybackMode === PLAYBACK_MODE.SD) {
        console.log("*****************")

        this._startSdcardPlay(true);
      }
      return;
    }
    // this.setState({ pstate: 0, error: 1 });
    this.setState({ pstate: -1, error: 1 });
    this.isConnecting = true;
    console.log("start connecting");
    CameraPlayer.getInstance().startConnect();
    console.log(TAG, "device onLine:", Device.isOnline);
    // if (!Device.isOnline) {
    //   this.setState({ showErrorView: true, errTextString: LocalizedStrings['device_not_open_warning'] });
    //   OfflineHelper.getLastOnlineTime()
    //     .then((result) => {
    //       this.setState({ lastOfflineTime: `${ LocalizedStrings['offline_time_str'] }: ${ result }` });
    //     })
    //     .catch((rr) => {
    //     });
    // }
  }

  doVideoStop() {
    Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_VIDEO_STOP, {}).then((retCode) => {
      LogUtil.logOnAll(TAG, "MISS_CMD_VIDEO_STOP SUCCESS");
      console.log(retCode);
    }).catch((err) => {
      LogUtil.logOnAll(TAG, "MISS_CMD_VIDEO_STOP error");
    });
  }
  // ============================= 云存播放器回调方法 start ==============================
  _startQueryNetwork() {
    if (this.isUserPause && this.state.currentMode === MODE.CLOUD) {
      // 更新下日期
      this.setSelectedDay(this.toCloudStartTime);
      return;// 用户主动暂停的
    }
    CameraPlayer.getInstance().queryShouldPauseOn4G()
      .then(({ state, pauseOnCellular }) => {
        if (state === "NONE" || state === "UNKNOWN") {
          this._networkChangeHandler(0);
          return;
        }
        // 其他网络条件 走连接的步骤吧
        if (this.state.currentMode === MODE.CLOUD) {
          this._startCloudPlay(true);// 开始连接
        } else {
          this._startSdcardPlay(true);// 开始连接
        }
      })
      .catch(() => { // 获取网络状态失败 也直接走开始连接的流程
        if (this.state.currentMode === MODE.CLOUD) {
          this._startCloudPlay(true);// 开始连接
        } else {
          this._startSdcardPlay(true);// 开始连接
        }
      });
  }

  _onScrolling = (timestamp) => {
    console.log("滑动中");
    this.dateTime.setTime(timestamp);
    console.log(`${ this.dateTime.getHours() } ${ this.dateTime.getMinutes() } ${ this.dateTime.getSeconds() }`);
    if (this.timeIndicatorView == null) {
      return;
    }
    this.timeIndicatorView.setState({ centerTimestamp: timestamp });
  };

  onLineVideoClick = (item, itemInfo) => {

    if (this.state.currentMode === MODE.LIVE) {
      // 结束头像锁定
      if (this.state.isLockOpen) {
        this.resetLockStatus();
      } else {
        NativeModules.MHCameraSDK.setPositionParam(Device.deviceID, { scale: 1, offsetY: 0, offsetX: 0, chan: 0 });
      }
      // 处于直播状态下，点击切换到currentPlaybackMode下
      if (this.state.currentPlaybackMode === PLAYBACK_MODE.SD) {
        this.sdCurrentPlayItem = item;
        this.sdItemInfo = itemInfo;
        // 切换到SD卡回看播放
        this.toSDStartTime = item.startTime;
        // 需要调Video stop，否则会导致Mike崩溃
        this.doVideoStop();
        this.cameraGLView && this.cameraGLView.stopRender();

        this.setState({ currentMode: MODE.SD }, () => {
          this._startSdcardPlay(true);
          if (this.videoLineView) {
            this.videoLineView.getItemPosition();
            // 重新计算下布局item位置信息，更新sdItemInfo
            this.sdItemInfo = this.videoLineView.getItemPositionInfo(itemInfo.sectionIndex, itemInfo.itemIndex);
            setTimeout(() => {
              this.videoLineView.scrollToItemByItemInfo(this.sdItemInfo);
            }, 0);
          }
        });
      } else {
        this.cloudCurrentPlayItem = item;
        this.cloudItemInfo = itemInfo;
        // 切换到云存回看播放
        this.toCloudStartTime = item.startTime;
        this.doVideoStop();
        this.cameraGLView && this.cameraGLView.stopRender();

        this.setState({ currentMode: MODE.CLOUD }, () => {
          this._startCloudPlay(true);
          if (this.videoLineView) {
            this.videoLineView.getItemPosition();
            // 重新计算下布局item位置信息，更新sdItemInfo
            this.cloudItemInfo = this.videoLineView.getItemPositionInfo(itemInfo.sectionIndex, itemInfo.itemIndex);
            console.log("++++++++++++scroll1", this.cloudItemInfo, itemInfo);
            setTimeout(() => {
              this.videoLineView.scrollToItemByItemInfo(this.cloudItemInfo);
            }, 0);
          }
        });
      }

    } else {
      // 已经处于回看模式下，点击播放切换视频源
      if (this.state.currentMode === MODE.SD) {
        console.log("00000000000000000000")
        this.isSetPlayTime = true;
        this.sdCurrentPlayItem = item;
        this.sdItemInfo = itemInfo;
        this.isUserPause = false;
        this._sdcardCenterValueChange(item,0);
      } else {
        this.blockOnProgress = true;// 如果开始播放的过程中，滚动了时间轴，要暂时屏蔽onProgress的回调。
        // 1.5s后再处理progress的回调。避免拖拽失败。
        if (this.blockOnProgressTimeout) {
          clearTimeout(this.blockOnProgressTimeout);
        }
        this.blockOnProgressTimeout = setTimeout(() => {
          console.log("=========centerValueChanged is do==========");
          this.blockOnProgress = false;
        }, 1500);
        this.cloudCurrentPlayItem = item;
        this.cloudItemInfo = itemInfo;
        this.isUserPause = false;
        this._cloudCenterValueChange(item.startTime);
      }
    }
  }

  // 这里代表时间轴滚动了
  _onCenterValueChanged = (item, itemInfo, offset) => {
    if (this.state.isPlaying) {
      if (this.state.currentMode === MODE.CLOUD) {
        this.blockOnProgress = true;// 如果开始播放的过程中，滚动了时间轴，要暂时屏蔽onProgress的回调。
        // 1.5s后再处理progress的回调。避免拖拽失败。
        if (this.blockOnProgressTimeout) {
          clearTimeout(this.blockOnProgressTimeout);
        }
        this.blockOnProgressTimeout = setTimeout(() => {
          console.log("=========centerValueChanged is do==========");
          this.blockOnProgress = false;
        }, 1500);
      } else if (this.state.currentMode === MODE.SD) {
        this.isSetPlayTime = true;
      }

    }
    let timestamp;
    console.log("_onCenterValueChanged",item.startTime, offset);
    if (this.state.currentPlaybackMode === PLAYBACK_MODE.SD) {
      this.sdCurrentPlayItem = item;
      this.sdItemInfo = itemInfo;
      timestamp = item.startTime + offset;
    } else {
      this.cloudCurrentPlayItem = item;
      this.cloudItemInfo = itemInfo;
      // 云存offset的单位是s
      timestamp = item.startTime + offset * 1000;

    }

    if (this.state.currentMode === MODE.LIVE) {
      // 直播时，滚动时间轴 不做播放处理
      if (this.state.currentPlaybackMode === PLAYBACK_MODE.SD) {
        this.toSDStartTime = timestamp;
      } else {
        this.toCloudStartTime = timestamp;
      }
      // 需要判断时间，更新日期显示
      this.setSelectedDay(timestamp);

    } else if (this.state.currentMode === MODE.SD) {
      // SD卡回看
      this._sdcardCenterValueChange(item, offset);
    } else {
      // 云存回看
      this._cloudCenterValueChange(timestamp);
    }
  };

  // 放大、缩小时间轴 从新获取一下位置、长度等信息
  _onScaleChange = (item, positionInfo) => {
    if (this.state.currentPlaybackMode === PLAYBACK_MODE.SD) {

    }
    let itemPositionInfo = this.sdItemInfo;
    if (this.state.currentPlaybackMode === PLAYBACK_MODE.CLOUD) {
      itemPositionInfo = this.cloudItemInfo;
    }
    LogUtil.logOnAll(TAG, "_onScaleChange", itemPositionInfo);
    if (itemPositionInfo == null) {
      return;
    }
    let info = this.videoLineView && this.videoLineView.getItemPositionInfo(itemPositionInfo.sectionIndex, itemPositionInfo.itemIndex);
    LogUtil.logOnAll(TAG, "_onScaleChange position info:", info);
    if (info != null) {
      if (this.state.currentPlaybackMode === PLAYBACK_MODE.CLOUD) {
        this.cloudItemInfo = info;
        // 滚动到双击后的位置信息
        this.videoLineView && this.videoLineView.scrollToPositionByTimestamp(this.cloudCurrentPlayItem, this.cloudItemInfo, this.toCloudStartTime);
      } else {
        this.sdItemInfo = info;
        console.log(TAG, "scale scroll", this.sdItemInfo);
        this.videoLineView && this.videoLineView.scrollToPositionByTimestamp(this.sdCurrentPlayItem, this.sdItemInfo, this.toSDStartTime);

      }
    }
  }

  _cloudCenterValueChange(timestamp) {
    console.log("滑动结束");
    this.toCloudStartTime = timestamp;
    if (this.state.isPlaying) {
      this._startQueryNetwork();
    } else {
      if (this.state.currentMode === MODE.CLOUD) {
        this._startQueryNetwork();
      }
    }
  }

  _sdcardCenterValueChange(item, offset) {
    console.log("滑动结束",item.startTime , offset, this.state.isPlaying);

    this.toSDStartTime = item.startTime + offset;
    if (this.state.isPlaying) {
      this.queryNetworkJob();// 检测网络
    } else {
      if (this.state.currentMode === MODE.SD) {
        console.log("11111111111111")

        this.queryNetworkJob();
      }
    }
  }

  _sdcardCenterValueChangeBak(timestamp) {
    console.log("滑动结束");
    this.setState({ touchMovement: false });
    this.dateTime.setTime(timestamp);
    // console.log(`timestamp:${timestamp}`);
    console.log(`${ this.dateTime.getHours() } ${ this.dateTime.getMinutes() } ${ this.dateTime.getSeconds() }`);

    // if (this.timeIndicatorView == null) {
    //   return;
    // }
    // this.timeIndicatorView.setState({ centerTimestamp: 0 });

    this.toSDStartTime = timestamp;
    if (this.state.isPlaying) {
      this.queryNetworkJob();// 检测网络
    }
  }

  _startCloudPlay(isPlay, ignoreState = false, checkEndToPlayStart = false) {
    // handle流量保护。

    if (isPlay) {
      let selectedItem = null;
      let offset = 0;
      let lastestItem = CloudVideoCarUtil.getLastestVideo(this.toCloudStartTime);
      if (lastestItem == null) { // 压根都没有最后一条数据；
        return;
      }

      LogUtil.logOnAll(TAG,"start play cloud",this.toCloudStartTime,lastestItem.endTime);
      if (lastestItem.endTime < this.toCloudStartTime) {
        selectedItem = lastestItem;
        if (lastestItem.endTime - lastestItem.startTime > 20000) {
          offset = (lastestItem.endTime - lastestItem.startTime - 20000) / 1000;
        } else {
          offset = 0;
        }
      } else {
        selectedItem = CloudVideoCarUtil.searchNeareastVideoItem(this.toCloudStartTime, checkEndToPlayStart);
        if (selectedItem == null) {
          return;
        }
        if (selectedItem.startTime > this.toCloudStartTime) {
          offset = 0;
        } else {
          offset = (this.toCloudStartTime - selectedItem.startTime) / 1000;
        }
      }
      LogUtil.logOnAll(TAG,"start play cloud offset",offset, selectedItem);
      // 需要检查下视频是否存在
      Util.checkExist(selectedItem).then((result) => {
        LogUtil.logOnAll(`MainCarPage checkExist enter 10, result: ${ JSON.stringify(result) }`);
        if (!result?.data?.deleteStatus) {
          this.doCloudPlay(selectedItem, offset);
        } else {
          selectedItem = CloudVideoCarUtil.getLastestVideo();
          this.doCloudPlay(selectedItem, 0);
        }
      }).catch((err) => {
        selectedItem = CloudVideoCarUtil.getLastestVideo();
        this.doCloudPlay(selectedItem, 0);
      });

    } else {
      if (this.state.showErrorView) {
        return;
      }
      this.setState({ cloudMute: true });

      // stop播放
      if (!ignoreState) {
        this.setState({ isPlaying: false, showPauseView: true, showPlayToolBar: true, showLoadingView: false });
      }
      this.showPlayToolBarTimer && clearTimeout(this.showPlayToolBarTimer);
    }
  }

  doCloudPlay(selectedItem, offset) {
    if (!this.state.showLoadingView) {
      this.setState({ showLoadingView: true, showPlayErrorView: false });
    }
    if (this.videoItem != null && this.videoItem.fileId == selectedItem.fileId) {
      // console.log(`state:${ JSON.stringify(this.state) } state.isPlaying:${ this.state.isPlaying } isMute:${ CameraConfig.getUnitMute() }`);
      this.setState({ cloudMute: false });
      if (this.state.isPlaying) {
        if (isNaN(offset)) {
          LogUtil.logOnAll(TAG, "_startCloudPlay isPlaying offset isNaN", offset);
          offset = 0;
        }
        this.cloudOffset = offset;
        this.cloudPlayingSeekTime = new Date().getTime();
        !this.destroyed && this.video && this.video.seek(offset);
        return;
      } else {
        this.setState({ isPlaying: true, showPlayToolBar: true, showPauseView: true, showLoadingView: false });
        if (isNaN(offset)) {
          LogUtil.logOnAll(TAG, "_startCloudPlay not isPlaying offset isNaN", offset);
          offset = 0;
        }
        this.cloudOffset = offset;
        !this.destroyed && this.video && this.video.seek(offset);
        return;
      }
    }
    this.videoItem = selectedItem;
    this.cloudOffset = offset;
    if (this.videoLineView) {
      // 云存视频播放新视频
      let { item, itemInfo } = this.videoLineView.findItemInfoByTimestamp(selectedItem.startTime);
      console.log("findItemInfoByTimestamp",item,itemInfo);
      this.cloudItemInfo = itemInfo;
      this.cloudCurrentPlayItem = item;
      // 先滚动指定位置
      this.videoLineView && this.videoLineView.scrollToPositionByTimestamp(this.cloudCurrentPlayItem, this.cloudItemInfo, selectedItem.startTime + offset);
    }
    let fileId = selectedItem.fileId;
    Service.miotcamera.getVideoFileUrl(fileId, false)
      .then((url) => {
        this.setState(() => {
          return {
            showErrorView: false,
            videoPath: url,
            isPlaying: true,
            showPlayToolBar: true,
            showPauseView: false,
            showPlayErrorView: false
          };
        }, () => {
          this.setState({ cloudMute: false });
        });
        this._hidePlayToolBarLater();

      })
      .catch((error) => {
        !this.destroyed && Toast.fail("action_failed", error);
      });
  }

  onEnd = () => {
    // this._startPlay(false);//播放结束了
    this.mCurTime = 0;
    LogUtil.logOnAll("CloudTimeline", "onEnd");
    this.reachCurrentFileEnd();
  };
  onLoad = (info) => {
    // 获取到duration后，需要刷新UI，避免动态增长的云存视频，由于本地没有刷新导致的问题
    if (this.mNewPlay) {
      this.mNewPlay = false;
    }
    let duration = info.duration;// seconds
    this.duration = duration;


    // this.updateTimeStr();
    // this.setState({ showLoadingView: false, showPauseView: true });// 移除loading
    // if (this.offset != null && this.offset >= 0) {
    //   !this.destroyed && this.video && this.video.seek(this.offset);
    // }

    LogUtil.logOnAll("video onLoad", info, this.cloudOffset, isNaN(this.cloudOffset));

    // android端需要先暂停播放，再开始播放
    if (Platform.OS == "android") {
      this.setState(() => {
        return { isPlaying: false };
      }, () => {
        this.setState(() => { return { isPlaying: true }; }, () => {
          if (this.cloudOffset == null || this.cloudOffset == 0 || isNaN(this.cloudOffset)) {
            this.setState({ showLoadingView: false, showPauseView: this.state.showPlayToolBar });// 移除loading
            // 不需要seek
          } else {
            LogUtil.logOnAll("CloudTimeline", "seek:" + this.cloudOffset);
            !this.destroyed && this.video && this.video.seek(this.cloudOffset);
          }
        });
      });
    }
  };

  onError = (error) => { // 修改进度条
    LogUtil.logOnAll("play onError", error);
    // if (!this.state.displayCloudList) {
    //   return;
    // }
    if (this.networkState == 0) {
      this._networkChangeHandler(0);
      return;// 断网了
    }
    this._startCloudPlay(false);
    this.setState({ showErrorView: false, showPlayErrorView: true,  showPlayToolBar: false });
    // 设置错误文案 以及retry button。
  };

  onBuffer = (info) => {
    console.log(info);
  };

  onProgress = (data) => {
    // if (!this.isPageForeGround) {
    //   return;
    // }
    // console.log("=================cloud/**/ video progress",data,this.blockOnProgress, this.offset,this.cloudOffset);
    if (this.blockOnProgress) { // 拖动了时间轴，1.5s内不响应onProgress
      return;
    }

    if (this.state.currentMode !== MODE.CLOUD) {
      return;
    }

    if (this.videoItem == null) {
      return;
    }
    this.progress = data.currentTime;
    if (data.currentTime > this.cloudOffset) {
      let currentTime = data.currentTime * 1000;
      let startTime = this.videoItem.startTime ? this.videoItem.startTime : this.videoItem.createTime;
      let isReachCurrentFileEnd = startTime + currentTime >= this.videoItem.endTime; //特定视频播放器返回的进度条信息不太对，导致直接跳文件末尾了，这里不这么弄；
      if (isReachCurrentFileEnd) {
        // 特定视频不太对，在这里直接跳过去。
        LogUtil.logOnAll("CloudTimeline", "onProgress endReached", this.videoItem, currentTime);
        this.reachCurrentFileEnd();
      } else {
        // 更新播放进度
        // console.log("onProgress:", currentTime);
        this.toCloudStartTime = this.videoItem.startTime + currentTime;
        // console.log("++++++++++++toCloudStartTime",this.toCloudStartTime)
        // 日期时间更新
        this.setSelectedDay(this.toCloudStartTime);

        this.videoLineView && this.videoLineView.scrollToPositionByTimestamp(this.cloudCurrentPlayItem, this.cloudItemInfo, this.toCloudStartTime);
        // !this.destroyed && this.timelineView && this.timelineView.scrollToTimestamp(this.toStartTime);// 受到进度提示了。
      }

    } else {
      // console.log("on seeking");
    }

  };
  //云存视频播放到本个文件末尾
  reachCurrentFileEnd() {
    if (this.blockOnProgress) {
      if (this.cloudPlayingSeekTime && new Date().getTime() - this.cloudPlayingSeekTime < 1500) {
        // 不阻塞后续流程
        this.cloudPlayingSeekTime = null;
      } else {
        return;
      }

    }
    if (this.videoItem != null) {
      console.log(`Video onEnd:${this.videoItem.endTime}`);
      this.toCloudStartTime = this.videoItem.endTime + 1000; // 播放完成后，+1000 让下一次寻找的时候找到下一个。
      let video = CloudVideoCarUtil.getLastestVideo();
      if (video == null) {
        return;
      }
      if (this.toCloudStartTime > video.endTime) {
        this.toCloudEnd();
        return;
      }
      // 多次收到播放结束，播放结束时后播放的回调仍在执行
      this.blockOnProgress = true;
      // 1.5s后再处理progress的回调。避免开启播放后仍收到回调导致定位到其他item播放。
      if (this.blockOnProgressTimeout) {
        clearTimeout(this.blockOnProgressTimeout);
      }
      this.blockOnProgressTimeout = setTimeout(() => {
        this.blockOnProgress = false;
      }, 1500);
      this._startQueryNetwork();// 播放下一个。
    }
  }

  toCloudEnd() {

    // todo jump to sdcard file end
    // todo  pauseCamera
    // 播放到最后一个云存视频
    // !this.destroyed && this.timelineView && this.timelineView.scrollToTimestamp(this.lastTimeItemEndTime);
    !this.destroyed && this.videoLineView && this.videoLineView.scrollToEnd();
    this.videoItem = null;
    this._startCloudPlay(false);
  }

  onSeek = (info) => {
    console.log("video onSeek", info);
    this.mSeeking = false;
    this.mCurTime = info.currentTime;
    this.setState({
      showLoadingView: false, showPauseView: this.state.showPlayToolBar
    });
    this.mNewEvent = false;
  };

  _onCloudVideoClick() {
    console.log("click video view");
    // if (!this.state.displayCloudList) {
    //   return;// 不是vip 直接返回，不应该显示这些页面。
    // }
    this.setState((state) => {
      return {
        showPlayToolBar: !this.state.showPlayToolBar,
        showPauseView: !this.state.showPlayToolBar,
        showDayList: false
      };
    }, () => {
      this._hidePlayToolBarLater();
    });
  }

  // ============================= 云存播放器回调方法 end ==============================
  // =============================   云存数据处理 start  ==============================
  /**
   * @Author: byh
   * @Date: 2024/11/19
   * @explanation:
   * 云存数据请求回调方法
   *********************************************************/
  _bindFilesHandler = (status, isAll = false) => { // 收到文件列表的回调
    if (CLOUD_VIDEO_STATUS.FAIL == status || CLOUD_VIDEO_STATUS.EMPTY == status) {
      this.isFileReceived = false;
      console.log("++++++++++++++++_bindFilesHandler", status);
      // 渲染 common_net_error；
      CameraPlayer.getInstance().queryShouldPauseOn4G()
        .then(({ state, pauseOnCellular }) => {
          if (state === "NONE" || state === "UNKNOWN") {
            this.networkState = 0;
            this.isMobileNetwork = false;

            this.setState({
              showErrorView: false,
              showLoadingView: false,
              showEmptyHint: false
            });
            if (this.state.currentPlaybackMode === PLAYBACK_MODE.CLOUD) {
              this.setState({ isLoading: false });
            }
            this.setState({ cloudListData: [] });
            // 云存被删除完了，又无法播放
            if (CameraConfig.needRefreshCloudVideo && this.state.currentMode === MODE.CLOUD) {
              CameraConfig.needRefreshCloudVideo = false;
              // 直接切换到实时播放吧
              this.emptyToPlayLive();
              if (this.videoLineView) {
                this.videoLineView.scrollToEnd();
              }
            }
          } else {
            this.setState({
              showErrorView: false,
              showEmptyHint: status == CLOUD_VIDEO_STATUS.EMPTY
            });
            if (this.state.currentPlaybackMode === PLAYBACK_MODE.CLOUD) {
              this.setState({ isLoading: false });
            }
            this.setState({ cloudListData: [] });
            if (CameraConfig.needRefreshCloudVideo && this.state.currentMode === MODE.CLOUD) {
              CameraConfig.needRefreshCloudVideo = false;
              // 直接切换到实时播放吧
              this.emptyToPlayLive();
              if (this.videoLineView) {
                this.videoLineView.scrollToEnd();
              }
            }
          }
        });

      return;
    }

    this.isFileReceived = true;
    this.onCloudGetFiles();
    if (this.state.currentPlaybackMode === PLAYBACK_MODE.CLOUD) {
      this.setState({ isLoading: false });
    }
  };

  emptyToPlayLive() {
    this.setState({ currentMode: MODE.LIVE, showPlayErrorView: false }, () => {
      console.log("{{{{{{{{{}}}}}}", this.state.currentMode, this.state.currentPlaybackMode);
      Service.miotcamera.setTimelinePlaybackMode(false);
      // 恢复播放器视图尺寸
      NativeModules.MHCameraSDK.setPositionParam(Device.deviceID, { scale: 1, offsetY: 0, offsetX: 0, chan: 0 });
      this._startCloudPlay(false);
      this._startConnect();
      // 滚动列表到最右侧，即顶部 需要重置下日期时间及选择的播放item
      if (this.videoLineView) {
        this.videoLineView.scrollToOffset({ animated: true, offset: 0 });
      }
    });
  }
  /**
   * @Author: byh
   * @Date: 2024/11/20
   * @explanation:
   * 收到云存视频数据
   * 1、整理数据
   * 2、下载视频缩略图
   * 3、把视频事件塞到数据里面
   *********************************************************/
  onCloudGetFiles() {
    this.testCloudVideo = CloudVideoCarUtil.getLastestVideo();
    // if (this.testCloudVideo) {
    //   this.downloadFileThump([this.testCloudVideo]);
    // }
    this.onCloudGetFilesV2();
    if (this.isCloudDayFirstReceive) {
      // console.log("==============哪些天有视频的数据",CloudVideoCarUtil.cloudDayList);
      this._setCloudDayHasVideo();
      this.isCloudDayFirstReceive = false;
    }

  }

  // 滚动后的时间计算，中间指针指向的时间所在的天
  setSelectedDay(centerTimestamp) {
    // console.log("++++++++setSelectedDay",centerTimestamp);
    // 先看看是否是同一天，如果是就不更新
    this.selectedDayTime = centerTimestamp;
    this.dateTime.setTime(centerTimestamp);
    // let year = this.dateTime.getFullYear();
    // let month = this.dateTime.getMonth() + 1;
    // let day = this.dateTime.getDate();
    // let str = (month > 9 ? `${month}` : `0${month}`) + (day > 9 ? `${day}` : `0${day}`);
    let str = dayjs.unix(this.dateTime.getTime() / 1000).format("MMDD");

    // //console.log("refreshTopdate", "选中日期:", str);
    let selectedDayIndex = -1;
    let dayArray = this.state.dateData;
    let oldSelectDay = dayArray.find((item) => item.selected);
    if (oldSelectDay && oldSelectDay.key == str) {
      return;
    }
    for (let i = 0; i < dayArray.length; i++) {

      let day = dayArray[i];
      if (day.key === str) {
        dayArray[i].selected = true;
        selectedDayIndex = i;
      } else {
        dayArray[i].selected = false;
      }
    }
    let date = new Date(centerTimestamp);
    this.weekPageIndex = Math.floor(selectedDayIndex / 7);
    this.setState({ curDate: date, dateData: dayArray, currentStopIndex: selectedDayIndex });
    this.selectedDayIndex = selectedDayIndex;

    return this.selectedDayIndex;
  }

  _setCloudDayHasVideo() {
    let days = CloudVideoCarUtil.cloudDayList;
    if (!days || days.length <= 0) {
      return;
    }
    let firstHasVideo = days.find((item) => item.hasVideo);
    // console.log("++++++++++firstHasVideo：", days,firstHasVideo,this.cloudDayList);
    // console.log("_setCloudDayHasVideo",days);

    for (let i = 0; i < this.cloudDayList.length; i++) {
      let dItem = this.cloudDayList[i];
      let sameDayItem = days.find((item) => dItem.key === item.dateStr);
      dItem.enabled = days.findIndex((item) => item.hasVideo && dItem.key === item.dateStr) > -1;
      if (sameDayItem != null && sameDayItem != undefined) {
        dItem.startTimestamp = sameDayItem.startTimestamp;
      }
      dItem.selected = dItem.key === firstHasVideo.dateStr;
    }

    if (this.state.currentPlaybackMode === PLAYBACK_MODE.CLOUD) {
      let item =  this.cloudDayList.find((item) => item.selected);
      let showDate = new Date();
      if (item) {
        showDate = new Date(item.startTimestamp);
      }
      this.setState({ curDate: showDate, dateData: this.cloudDayList });
    }
  }

  _setSdcardDayHasVideo() {
    console.log("createDayList", AlarmUtilV2.SD_EARLIEST_TIME);
    if (AlarmUtilV2.SD_EARLIEST_TIME !== 0) {
      // 有时间戳
      let diffMls = new Date().getTime() / 1000 - AlarmUtilV2.SD_EARLIEST_TIME;
      let diffDays = diffMls / (60 * 60 * 24);
      let days = Math.ceil(diffDays / 30) * 30;
      LogUtil.logOnAll(TAG, `diffDays:${ diffDays }, days:${ days }`);
      let daysSdcardArr = this.genDateItem(false, days);
      this.sdcardDayList = JSON.parse(JSON.stringify(daysSdcardArr));
    }
    let dayMillis = 24 * 60 * 60 * 1000;
    // let firstHasVideo = days.find((item) => item.hasVideo);
    // console.log("++++++++++firstHasVideo：", days,firstHasVideo,this.cloudDayList);
    let data = SdFileManager.getInstance().getTimeItems();
    if (!data || data.length <= 0) {
      return;
    }
    let dayItems = [];
    let lastTime = 0;
    for (let i = 0; i < data.length; i++) {
      let item = data[i];
      if (item.startTime >= lastTime) {
        let dayItem = {};
        dayItem.startTimestamp = item.startTime;
        this.dateTime.setTime(item.startTime);
        this.dateTime.setSeconds(0, 0);
        this.dateTime.setMinutes(0);
        this.dateTime.setHours(0);
        let month = this.dateTime.getMonth() + 1;
        let date = this.dateTime.getDate();
        let dateKey = dayjs.unix(this.dateTime.getTime() / 1000).format("MMDD");
        // dayItem.dateStr = (month > 9 ? `${month}` : `0${month}`) + (date > 9 ? `${date}` : `0${date}`);
        dayItem.dateStr = dateKey;
        dayItems.push(dayItem);
        lastTime = this.dateTime.getTime() + dayMillis;
      }

    }

    dayItems.sort((a, b) => b.startTimestamp - a.startTimestamp);
    console.log("_setSdcardDayHasVideo",dayItems);
    for (let i = 0; i < this.sdcardDayList.length; i++) {
      let dItem = this.sdcardDayList[i];
      let sameDayIndex = dayItems.findIndex((item) => dItem.key === item.dateStr);
      dItem.enabled = sameDayIndex > -1;
      if (sameDayIndex > -1) {
        dItem.startTimestamp = dayItems[sameDayIndex].startTimestamp;
      }
      dItem.selected = dayItems.findIndex((item, index) => index === 0 && dItem.key === item.dateStr) > -1;
    }
  }

  _clearSdcardDayHasVideo() {
    for (let i = 0; i < this.sdcardDayList.length; i++) {
      let dItem = this.sdcardDayList[i];
      dItem.enabled = false;
      dItem.selected = false;
    }
  }

  _getSelectedDayIndex() {
    let index = -1;
    for (let i = 0; i < this.state.dateData.length; i++) {
      if (this.state.dateData[i].selected) {
        index = i;
        break;
      }
    }
    return index;
  }

  _getPrevDayIndex(currentSelectedIndex) {
    if (currentSelectedIndex == -1) {
      return -1;
    }
    for (let i = currentSelectedIndex + 1; i < this.state.dateData.length; i++) {
      if (this.state.dateData[i].enabled) {
        return i;
      }
    }
    return -1;
  }

  _getNextDayIndex(currentSelectedIndex) {
    for (let i = currentSelectedIndex - 1; i >= 0; i--) {
      if (this.state.dateData[i].enabled) {
        return i;
      }
    }
    return -1;

  }

  _onPressDayItem(dayItem) {
    let time1 = new Date().getTime();
    //console.log("onpressDayItem:", JSON.stringify(dayItem));
    TrackUtil.reportClickEvent("TimeSlider_Date_ClickNum");

    if (!dayItem.enabled) {
      return;
    }

    let timestamp = dayItem.startTimestamp;
    if (this.state.currentPlaybackMode === PLAYBACK_MODE.CLOUD) {
      // 如果这一天还有没有视频 只考虑云存，不用考虑SD卡视频
      // SD卡视频，一般不会存在这种情况，因为SD卡视频日期是由数据计算出来的
      let date = new Date(timestamp);
      // date.setHours(23, 59, 59, 0);
      date.setHours(0, 0, 0, 0);
      // let hasVideo = CloudVideoCarUtil.videoExistMap.get(date.getTime());// 本地时间匹配。
      let hasVideo = CloudVideoCarUtil.cloudVideoDayMap.get(date.getTime());// 本地时间匹配。
      if (!hasVideo || hasVideo.length == 0) {
        Toast._showToast(LocalizedStrings['data_is_loading']);
        return;
      }
    }
    //console.log(`selected item:${CloudVideoCarUtil.convertTimestampToTimeStr(timestamp)}`);
    let date = new Date(dayItem.ts);
    let selectIndex = 0;
    for (let i = 0; i < this.state.dateData.length; i++) {
      let di = this.state.dateData[i];
      if (di.ts === dayItem.ts) {
        if (di.selected) {
          return;
        }
        selectIndex = i;
        di.selected = true;
      } else {
        di.selected = false;
      }
    }
    this.weekPageIndex = Math.floor(selectIndex / 7);
    this.setState({ curDate: date, dateData: this.state.dateData, showDayList:false, currentStopIndex: selectIndex });
    // 防止在两边的情况 做一下处理
    LogUtil.logOnAll(TAG,"change day:",timestamp);
    // 需要滚动到这天，并且
    // this.timelineView && this.timelineView.scrollToTimestampWithNotify(timestamp, true);
    this.videoLineView && this.videoLineView.scrollToDayStartByTimestamp(timestamp, true);

  }

  onCloudGetFilesV2() {
    // console.log("++++++++++++++++",CloudVideoCarUtil.cloudVideoDayMap);
    this.setState({ isLoading: false });
    // 没有云存视频
    let videoMap = CloudVideoCarUtil.cloudVideoDayMap;
    let eventMap = CloudVideoCarUtil.alarmEventDayMap;
    if (videoMap == null || videoMap.size <= 0) {
      return null;
    }
    let videoKeys = [...videoMap.keys()];
    if (videoKeys == null || videoKeys.length == 0) {
      return null;
    }
    // 倒序，最近的时间在前面
    videoKeys.sort((a1, a2) => {
      return a2 - a1;
    });
    // console.log("++++++++++++++++key",videoKeys);

    //
    let cloudArr = [];
    for (let videoKey of videoKeys) {
      let cloudVideoArrays = videoMap.get(videoKey);
      if (cloudVideoArrays == null || cloudVideoArrays.length == 0) { // 数据是逆序的
        continue;
      }

      if (cloudVideoArrays[0] == null) {
        continue;
      }

      // 视频
      for (let i = 0; i < cloudVideoArrays.length; i++) {
        // 每个视频塞一个对应事件
        let cloudVideo = cloudVideoArrays[i];
        if (eventMap != null && eventMap.size > 0) {
          // 有事件
          // 某一天的事件集合
          let alarmEvents = eventMap.get(videoKey);

          // 因为视频跨天，导致视频不能正常渲染事件颜色，需要把后一天的事件加入进来
          let indexKey = videoKeys.findIndex((item) => videoKey === item);
          if (indexKey < videoKeys.length - 1 && indexKey < eventMap.size - 1) {
            let nextKey = videoKeys[indexKey + 1];
            let alarmEventsNext = eventMap.get(nextKey);
            if (alarmEventsNext != null && alarmEventsNext.length > 0) {
              alarmEvents = alarmEventsNext.concat(alarmEvents);
            }
          }

          if (alarmEvents != null && alarmEvents.length > 0) {
            // 视频从大到小   事件
            for (let j = 0; j < alarmEvents.length; j++) {
              let alarmEvent = alarmEvents[j];
              let startTime = alarmEvent.createTime;
              // +800的原因是存在下一个视频的开始时间位于上一个视频的结束时间中，导致事件绘制异常
              if (startTime >= cloudVideo.startTime && (startTime + 800) < cloudVideo.endTime) {
                //matched = true;
                cloudVideo.eventType = alarmEvent.eventTypes[0];
                break;
              }
            }
          }
        }
      }


      // 组合供sectionList使用的数据
      let section = { title: `${ videoKey }`, data: cloudVideoArrays, key: `${ videoKey }` };
      cloudArr.push(section);
      // this.downloadFileThump(cloudVideoArrays);
    }
    // 下载图片
    this.downloadFileThump(cloudArr);

    // 整理后的数据
    this.setState({ cloudListData: cloudArr });
    let video = CloudVideoCarUtil.getLastestVideo();
    if (video == null) {
      console.log("find cloud items failed");
      return;
    }
    // this.setState({ isEmpty: false });
    console.log("收到了视频数据");
    this.lastTimeItemEndTime = video.endTime;
    if (!this.isFirstReceiveFiles) {
      return;
    }

    this.isFirstReceiveFiles = false;
    // setTimeout(() => {
    //   this.timelineView && this.timelineView.scrollToLocation();
    //
    // },4300);
    // return ;
    setTimeout(() => {
      let video = CloudVideoCarUtil.getLastestVideo();
      if (video == null) {
        console.log("find cloud items failed");
        return;
      }
      this.toCloudStartTime = video.startTime;
      if (this.videoLineView) {
        // this.videoLineView.scrollToOffset({ animated: false, offset: 380 });
        let { item, itemInfo } = this.videoLineView.getLastVideoInfo();
        // console.log("getLastVideoInfo",item,itemInfo);
        if (item) {
          this.cloudCurrentPlayItem = item;
          this.cloudItemInfo = itemInfo;
          this.toCloudStartTime = item.startTime;
          if (this.state.currentMode === MODE.CLOUD && CameraConfig.needRefreshCloudVideo) {
            CameraConfig.needRefreshCloudVideo = false;
            this._startQueryNetwork();
          }
        }
      }
    }, 500);
    // here startToPlay

  }


  async downloadFileThump(sections) {
    this.removeIndexList.push(this.cloudDownloadGlobelIndex);
    this.cloudDownloadGlobelIndex++;
    const cloudDownloadIndex = this.cloudDownloadGlobelIndex;
    console.log("+++++++++++removeIndexList++++++++++++++++",this.removeIndexList);

    try {
      // load from new to old
      //如果图片返回太快，就慢一些刷新，避免频繁刷新导致的UI卡顿问题，如果
      this.lastCloudNotifyTime = Date.now();
      let shouldBreak = false;
      let shouldPBreak = false;
      outerLoop: for (let i = 0; i < sections.length; i++) {
        // if (signal.aborted || shouldPBreak || shouldBreak) {
        //   console.log("========0 signal aborted=========",cloudDownloadIndex);
        //   break;
        // }
        let items = sections[i].data;
        for (let j = 0; j < items.length; ++j) {
          // if (signal.aborted || shouldBreak) {
          //   console.log("========1 signal aborted=========",cloudDownloadIndex);
          //   shouldPBreak = true;
          //   break;
          // }
          let item = items[j];
          for (let k = 0; k < item.images.length; k++) {
            if (this.removeIndexList.findIndex((itm) => itm == cloudDownloadIndex) > -1) {
              console.log("========2 signal aborted=========",cloudDownloadIndex);
              shouldBreak = true;
              break outerLoop;
            }
            try {
              let child = item.images[k];
              // console.log("get thumb", item.createTime, i, item);
              child.imgStoreUrl = await Singletons.CloudEventLoader.getThumb(child);
              // 3 thumb per refresh
              if (this.state.currentPlaybackMode !== PLAYBACK_MODE.CLOUD) {
                // 如果当前回看状态为非云存状态，不刷新，只做图片的下载
                continue;
              }
              if (Date.now() - this.lastCloudNotifyTime < 1000) {//距离上次全局刷新，不超过1s,return
                continue;
              }
              if (!this.props.navigation.isFocused()) {
                return;
              }
              this.lastCloudNotifyTime = Date.now();
              // if (i % 4 == 3 || i == items.length - 1) {
              this.setState({});
            } catch (e) {
              // console.log('downloadCloudFileThump', "getthumb", e);
            }

          }
        }
        this.setState({});
      }

    } catch (error) {
      if (error.name === 'AbortError') {
        console.log('请求被取消');
      } else {
        console.log('请求失败:', error);
      }
    } finally {
      this.removeIndexList = this.removeIndexList.filter((item) => item != cloudDownloadIndex);
    }

  }

  // =============================    云存数据处理 end   ==============================

  // ================================= SD卡 start ====================================
  _prepareStartSdcard() {
    this._startSdcardPlay(true);
    if (this.videoLineView) {
      this.videoLineView.getItemPosition();
      if (this.sdCurrentPlayItem && this.sdItemInfo) {
        this.videoLineView.scrollToPositionByTimestamp(this.sdCurrentPlayItem, this.sdItemInfo, this.toSDStartTime);
      }
    }
  }
  // 播放SD卡视频
  _startSdcardPlay(isPlay, ignoreState = false) {

    if (isPlay) {
      if (this.sdcardLastTimeItemEndTime == 0) { // 没有数据
        LogUtil.logOnAll(TAG, "sdcardLastTimeItemEndTime == 0");
        return;
      }
      console.log("55555555555555",this.toSDStartTime);
      if (!this.toSDStartTime) {
        // 不存在，取最后一个
        this.toSDStartTime = SdFileManager.getInstance().getLastestItemStartTime();
      }
      // 开始寻找合适的item
      let startTime = this.toSDStartTime;
      if (startTime >= this.sdcardLastTimeItemEndTime) {
        // 加个补丁，sdcardLastTimeItemEndTime可能不是最新的情况
        LogUtil.logOnAll(TAG, "start >= last", this.toSDStartTime, this.sdcardLastTimeItemEndTime);
        let endTime = SdFileManager.getInstance().getLastestItemEndTime();
        if (this.sdcardLastTimeItemEndTime < endTime) {
          this.sdcardLastTimeItemEndTime = endTime;
        } else {
          startTime = SdFileManager.getInstance().getLastestItemStartTime();
        }
      }
      if (startTime <= 0) {
        LogUtil.logOnAll(TAG, "_startSdcardPlay <= 0",startTime);
        return;
      }
      console.log("66666666666")

      this.dateTime.setTime(startTime);
      // console.log(`开始播放 format time:${this.dateTime.getHours()}:${this.dateTime.getMinutes()}:${this.dateTime.getSeconds()}`);

      let timeItem;
      if (Device.model === VersionUtil.Model_Camera_V1) {
        timeItem = SdFileManager.getInstance().getTimeItemClosestForV1(startTime);
      } else {
        timeItem = SdFileManager.getInstance().getTimeItemClosest(startTime);
      }
      if (timeItem != null) {
        this.startTime = Number.parseInt(timeItem.startTime / 1000);
        let duration = Number.parseInt(timeItem.duration / 1000);
        if (timeItem.startTime < startTime) {
          this.offset = Number.parseInt((startTime - timeItem.startTime) / 1000);
          if (this.offset >= duration) {
            this.offset = duration - 2;
          }
        } else {
          this.offset = 0;
        }
        this.isSetPlayTime = true;
        this.setPlayTimeMillis = new Date().getTime();
        // show loading

      } else { // 这里只可能是选中的时间比最后一个文件的endTime 还远一些。
        LogUtil.logOnAll(TAG, "timeItem not find");
        this.toSdcardEnd();
        return;
      }

      CameraPlayer.getInstance().stopPlaybackTimestampInterval();// 停止抓取

      console.log(`start play:${ this.startTime }  ${this.offset}  ${this.toSDStartTime}`);
      this.dateTime.setTime(this.startTime * 1000);
      console.log(`start play format time:${ this.dateTime.getHours() }:${ this.dateTime.getMinutes() }:${ this.dateTime.getSeconds() }`);


      this.cameraGLView && this.cameraGLView.stopRender();// 重新开启播放之前 先暂停播放。
      this.cameraGLView && this.cameraGLView.startRender();// startBindVideo
      this._startPlayback();

    } else {
      CameraPlayer.getInstance().stopPlaybackTimestampInterval();// 停止抓取
      CameraPlayer.getInstance().bindPlaybackTimestampCallback(null);
      if (!ignoreState) {
        // stop播放
        this.setState({
          isMute: this.state.speed > 1 ? true : CameraConfig.getUnitMute(),
          isPlaying: false,
          showPauseView: true,
          showPlayToolBar: true,
          showLoadingView: false
        });
      }
      clearTimeout(this.showPlayToolBarTimer);

      // stop audio 需要停掉音频
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_STOP, {}).then((retCode) => {
        console.log("audio stop");
        console.log(retCode);
      }).catch((err) => console.log(err));
      this.cameraGLView && this.cameraGLView.stopAudioPlay();

      // stop video
      if (this.state.currentMode === MODE.LIVE) {
        // 已经处于直播模式了，不能去停止视频
        return;
      }
      this.doVideoStop();

      this.cameraGLView && this.cameraGLView.stopRender();

      Service.miotcamera.setTimelinePlaybackMode(false);
      // stop audio local resources

    }
  }

  _startPlayback() {
    CameraPlayer.getInstance().startPlayBack(this.startTime, this.offset, 0, this.state.speed)
      .then((res) => {
        Service.miotcamera.bindTimelinePlaybackEndListener(timelinePlaybackEndListenerName);
        //this.setState({ showPlayToolBar: true });
        this.setState(() => {
          return { showErrorView: false, showLoadingView: true, isPlaying: true };
        }, () => {
          // let position = (this.state.speed == 1 ? 0 : (this.state.speed == 4 ? 1 : 2));
          // this.updateSpeed(position);
        });
        this.sessionId = this.startTime;
        // 需要开启声音播放
        this.cameraGLView && this.cameraGLView.stopAudioPlay();
        Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {}).then((retCode) => {
          // console.log("audio stop get send callback");
          console.log(retCode);
        });
        this.cameraGLView && this.cameraGLView.startAudioPlay();

        setTimeout(() => {
          CameraPlayer.getInstance().getPlaybackTimetampInterval();
          CameraPlayer.getInstance().bindPlaybackTimestampCallback(this.timestampCallback);
        }, 500);// 1.5s后再拉

        Service.miotcamera.setTimelinePlaybackMode(true);
      })
      .catch((err) => {
        LogUtil.logOnAll(TAG, "startPlayBack error",err);
        this._p2pCommandErrHandler(err, "startPlayback");
      });

  }

  _p2pCommandErrHandler(err, src) {
    Service.smarthome.reportLog(Device.model, `p2p err, src: ${ src }`);

    this.cameraGLView && this.cameraGLView.stopRender();// 重新开启播放之前 先暂停播放。
    if (err == -1 || err == -8888) { // - 8888重置本地连接，然后开始重连。
      CameraPlayer.getInstance().resetConnectionState();
      this.queryNetworkJob();
      return;
    }

    this.setState({ pstate: 0, showLoadingView: false, showErrorView: false, showPlayErrorView: true,  errTextString: `${ LocalizedStrings["camera_connect_error"]} ${ err } ${ LocalizedStrings[""] }` });// 已经渲染过  直接跳过去

    Service.miotcamera.setTimelinePlaybackMode(false);
  }

  // sd卡播放，时间回到
  timestampCallback = (timestamp) => {

    // 这里每隔一秒就会触发一次 返回当前的时间戳
    if (timestamp == this.lastTimestamp) { // 没有发生更新
      LogUtil.logOnAll(TAG, "timestampCallback timestamp is same", this.lastTimestamp, timestamp);
      return;
    }

    if (this.state.currentMode !== MODE.SD) {
      return;
    }

    if (timestamp && timestamp.toString().length < 10) {
      // 回看播放中，切换到直播，此时间会异常
      LogUtil.logOnAll(TAG, "timestampCallback timestamp length error", timestamp);
      // console.log("==============", timestamp);
      return;
    }

    // if (this.lastTimeItemEndTime - timestamp * 1000 < 1500 ) {//接近文件末尾了 直接pause
    //   this.toSdcardEnd();
    //   return;
    // }
    this.toSDStartTime = timestamp * 1000;
    if (this.isSetPlayTime) {

      let diff = timestamp - this.startTime;
      LogUtil.logOnAll(TAG, `timestampCallback offset ${this.offset}  ${diff}`);

      this.setState({ showLoadingView: false, showPauseView: true, showPlayToolBar: true });
      this.isSetPlayTime = false;
      this._hidePlayToolBarLater();
      return;
      // if (Math.abs(this.offset - diff) <= 20 || (new Date().getTime() - this.setPlayTimeMillis) > 6000) { // 刚刚设置了playback指令， 要消除loading
      //   // todo hide loading
      //   this.setState({ showLoadingView: false, showPauseView: true, showPlayToolBar: true });
      //   this.isSetPlayTime = false;
      //   this._hidePlayToolBarLater();
      // } else {
      //   LogUtil.logOnAll(TAG, `timestampCallback setPlayTime ${timestamp} ${this.startTime} ${this.isSetPlayTime} ${this.startTime}`);
      //   return;// 不管，等后面来timestamp
      // }
    }

    this.lastTimestamp = timestamp;
    // console.log(`updated time:${ this.lastTimestamp }`);
    this.dateTime.setTime(timestamp * 1000);
    // console.log(`scroll to time:${ this.dateTime.getHours() }:${ this.dateTime.getMinutes() }:${ this.dateTime.getSeconds() }`, this.lastTimestamp);
    // this.timelineView && this.timelineView.scrollToTimestamp(this.lastTimestamp * 1000);
    // 更新日期时间
    this.setSelectedDay(timestamp * 1000);
    this.videoLineView && this.videoLineView.scrollToPositionByTimestamp(this.sdCurrentPlayItem, this.sdItemInfo, timestamp*1000);

  };


  toSdcardEnd() {
    // todo jump to sdcard file end
    // todo  pauseCamera
    console.log('停止播放了');
    this.toSDStartTime = this.sdcardLastTimeItemEndTime + 1000;// 播放到文件末尾后，强制+ 1000, 下次找的时候 就会找到文件列表的katou 开头
    console.log(`toSdcardEnd:${ this.sdcardLastTimeItemEndTime }`);
    // todo
    // this.timelineView && this.timelineView.scrollToTimestamp(this.lastTimeItemEndTime);
    this._startSdcardPlay(false);

  }


  /**
   * @Author: byh
   * @Date: 2024/11/19
   * @explanation:
   * 获取SD卡数据
   * 1、进入时就获取一次
   * 2、直播连接成功时，也获取一次
   *********************************************************/
  _loadSdfiles() {
    let sdcardListStartTime = Date.now();
    // here to delete all datas;
    SdcardEventLoader.getInstance().getEventList()
      .then(() => {
        this.sdcardListRequestTime = Date.now() - sdcardListStartTime;
        LogUtil.logOnAll(TAG,"SdcardEventLoader getEventList");
        this.onGetFiles();// 数据已经读取完毕了。
        // 加载成功了。
      })
      .catch(() => {
        // 加载失败了
      });
  }

  onGetFiles(important = true) {
    let timeItems = SdFileManager.getInstance().getTimeItems();
    if (timeItems == null || timeItems.length <= 0) {
      if (this.state.sdListData && this.state.sdListData.length > 0) {
        this._clearSdcardDayHasVideo();
        this.isSdcardDayFirstReceive = true;
        this.setState({ sdListData: [], isLoading: false });
      }
      return;
    }

    this.setState({ isEmpty: false });
    // 数据处理
    // 1、时间轴数据 sdListData 存储sd卡回看时间轴数据
    let timeItemsList = SdFileManager.getInstance().getTimeItemContinuous();
    let sdListData = [];
    for (let i = 0; i < timeItemsList.length; i++) {
      let item = {key: `${timeItemsList[i].startTime}`, title: timeItemsList[i].startTime, data: timeItemsList[i].timeItemContinuousList };
      sdListData.push(item);
    }
    // 倒序，大的在前
    sdListData.sort((a, b) => b.title - a.title);
    // console.log("allData:",JSON.stringify(sdListData));

    // 2、周日历所需要的日期
    if (this.isSdcardDayFirstReceive) {
      this._setSdcardDayHasVideo();
      if (this.state.currentPlaybackMode === PLAYBACK_MODE.SD) {
        let item =  this.sdcardDayList.find((item) => item.selected);
        let showDate = new Date();
        if (item) {
          showDate = new Date(item.startTimestamp);
        }
        this.setState({ curDate: showDate, dateData: this.sdcardDayList, isLoading: false });
      }
    }
    // 这里把数据丢给Videolineview
    this.setState({ sdListData: sdListData });

    // 3、下载缩略图
    if (sdListData.length > 0 && important) {
      // 下载缩略图，先全部下载看看
      this.downloadSdcardThumbs(sdListData);
    }

    // this.timelineView && this.timelineView.initData(timeItems);
    let lastTimeItem = timeItems[timeItems.length - 1];
    this.sdcardLastTimeItemEndTime = lastTimeItem.endTime;
    LogUtil.logOnAll("SdcardTimeline", "刷新sdcard列表：最后一个文件的信息： " + JSON.stringify(lastTimeItem) + " lastest endTime:" + this.sdcardLastTimeItemEndTime, timeItems.length);//收到的视频帧信息

    this.lastTimeItemStartTime = lastTimeItem.startTime;
    let penultimateItem = timeItems.length > 1 ? timeItems[timeItems.length - 2] : null;
    if (!this.isSdcardDayFirstReceive) {
      return;
    }

    // 滚动到指定位置
    this.isSdcardDayFirstReceive = false;
    this.firstPullVideoFrameTime = Date.now();
    this.scrollTimeout1 && clearTimeout(this.scrollTimeout1);
    this.scrollTimeout1 = setTimeout(() => {

      if (penultimateItem) {
        this.penultimateStartTime = penultimateItem.startTime;
      } else {
        this.penultimateStartTime = 0;
      }
      // this.timelineView && this.timelineView.scrollToTimestamp(this.lastTimeItemStartTime);// 不通知
      // 这里开始播放了.
      if (this.videoLineView) {
        // 改动listHeader 未播放时不需要滚动了
        // this.videoLineView.scrollToOffset({ animated: false, offset: 380 });
        let { item, itemInfo } = this.videoLineView.getLastVideoInfo();
        // console.log("getLastVideoInfo",item,itemInfo);
        if (item) {
          this.sdCurrentPlayItem = item;
          this.sdItemInfo = itemInfo;
          this.toSDStartTime = item.startTime;
        }
      }

    }, 500);

  }

  /**
   * @Author: byh
   * @Date: 2024/11/19
   * @explanation:
   * 下载SD卡缩略图
   * timestampList数据结构按天分组
   *
   *********************************************************/
  async downloadSdcardThumbs(timestampList) {
    if (timestampList.length > 0) {
      this.removeSDIndexList.push(this.sdDownloadGlobelIndex);
      this.sdDownloadGlobelIndex++;
      const sdDownloadIndex = this.sdDownloadGlobelIndex;
      console.log("+++++++++++removeIndexList sd++++++++++++++++",this.removeIndexList);
      // 不管结果如果，5s开始插件队列，执行事件拉取
      this.delayToRequestEvent && clearTimeout(this.delayToRequestEvent);
      this.delayToRequestEvent = setTimeout(() => {
        if (!this.sdEventIsAlreadyRequest) {
          // 最后拉取事件列表
          this.sdEventIsAlreadyRequest = true;
          SdFileManager.getInstance().startRequestSdcardFileEventTypes();
        }
      }, 5000);
      try {
        // 图片下载异常的时候会阻塞本地已下载图片的展示
        SdFileManager.getInstance().setImageFilePath(timestampList);
        this.setState({});
      } catch (e) {

      }

      let lastNotifyTime = Date.now();
      outerLoop: for (let i = 0; i < timestampList.length; i++) {
        let oneDayList = timestampList[i].data;
        for (let j = 0; j < oneDayList.length; j++) {
          let images = oneDayList[j].images;
          // 图片的顺序按展示：从左往右依次增大，希望展示从右侧开始，所以先从时间最大的开始下载
          let length = images.length;
          for (let k = length - 1; k >= 0; k--) {
            if (this.removeSDIndexList.findIndex((itm) => itm == sdDownloadIndex) > -1) {
              console.log("========2 sd signal aborted=========",sdDownloadIndex);
              break outerLoop;
            }
            try {
              await SdcardEventLoader.getInstance().getThumb({ imgStoreId: images[k].startTime });
              if (Date.now() - lastNotifyTime < 1000) {
                continue;
              }
              if (!this.props.navigation.isFocused()) {
                return;
              }
              console.log("============sd refresh2============", lastNotifyTime, Date.now(), Date.now() - lastNotifyTime);
              lastNotifyTime = Date.now();
              this.onReceiveFile();// 通知刷新咯。
            } catch (err) {
              console.log("download error",err);
            }
          }
        }
      }
      // 最后刷新下
      this.onReceiveFile();
      if (!this.sdEventIsAlreadyRequest) {
        // 最后拉取事件列表
        this.sdEventIsAlreadyRequest = true;
        SdFileManager.getInstance().startRequestSdcardFileEventTypes();
      }
    }


  }

  // 收到下载后的数据
  onReceiveFile = () => {
    // checkTimestampRange
    let files = this.state.sdListData;
    if (files == null || files.length == 0) {
      return;
    }

    if (this.state.currentPlaybackMode !== PLAYBACK_MODE.SD) {
      // 非SD卡 不要刷新
      return;
    }

    // if (files[0].startTime >= timestamp && files[files.length - 1].startTime <= timestamp) { // 只有在这中间的才刷新
    //   this.setState({ index: (this.state.index > 100 ? 0 : this.state.index + 1) });// 刷新页面
    // }
    // console.log("++++++++++++刷新++++++++++");
    // this.setState({});
    console.log("++++++++++++++forceUpdate UI++++++++++++",Date.now());
    // this.forceUpdate();
    this.setState({});
  };

  // ================================= SD卡 end ===========================================


}