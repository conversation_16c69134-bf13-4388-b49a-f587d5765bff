<a name="module_miot/device/bluetooth"></a>

## miot/device/bluetooth
经典蓝牙设备操作类 sdk 10023  仅支持Android。iOS因为苹果的MFI认证（麻烦且收益低，暂时生态链公司及其它iot合作公司使用），暂没有经典蓝牙设备可以和iPhone通讯。
本文件提供了经典蓝牙设备的创建，连接，读写，断连。此处不再提供example。

**Export**: public  
**Doc_name**: 经典蓝牙模块  
**Doc_index**: 4  
**Doc_directory**: bluetooth  
**Since**: 10023  

* [miot/device/bluetooth](#module_miot/device/bluetooth)
    * [module.exports](#exp_module_miot/device/bluetooth--module.exports) ⏏
        * _static_
            * [.create()](#module_miot/device/bluetooth--module.exports.create) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code>
            * [.connectSocket(macAddress, transport)](#module_miot/device/bluetooth--module.exports.connectSocket) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code>
            * [.disconnectSocket()](#module_miot/device/bluetooth--module.exports.disconnectSocket) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code>
            * [.write(data)](#module_miot/device/bluetooth--module.exports.write) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code>
            * [.prepareBluetoothProfile(profile)](#module_miot/device/bluetooth--module.exports.prepareBluetoothProfile) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code>
            * [.connectBluetoothProfile(macAddress, profile)](#module_miot/device/bluetooth--module.exports.connectBluetoothProfile) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code>
            * [.disconnectBluetoothProfile(macAddress, profile)](#module_miot/device/bluetooth--module.exports.disconnectBluetoothProfile) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code>
            * [.getBluetoothProfileState(macAddress, profile)](#module_miot/device/bluetooth--module.exports.getBluetoothProfileState) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code>
            * [.destroy()](#module_miot/device/bluetooth--module.exports.destroy) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code>
        * _inner_
            * [~ClassicBluetoothEvent](#module_miot/device/bluetooth--module.exports..ClassicBluetoothEvent) : <code>object</code>
                * [.classicBlueBondStateChanged](#module_miot/device/bluetooth--module.exports..ClassicBluetoothEvent.classicBlueBondStateChanged)
                * [.classicBlueConnectionStateChanged](#module_miot/device/bluetooth--module.exports..ClassicBluetoothEvent.classicBlueConnectionStateChanged)
                * [.classicBlueReceivedData](#module_miot/device/bluetooth--module.exports..ClassicBluetoothEvent.classicBlueReceivedData)


* * *

<a name="exp_module_miot/device/bluetooth--module.exports"></a>

### module.exports ⏏
经典蓝牙设备操作类

**Kind**: Exported interface  

* * *

<a name="module_miot/device/bluetooth--module.exports.create"></a>

#### module.exports.create() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code>
初始化经典蓝牙,返回的数据没有实际作用, 执行到catch表示初始化失败。

**Kind**: static method of [<code>module.exports</code>](#exp_module_miot/device/bluetooth--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code> - 成功进入then，失败进入catch  
**Since**: 10023  

* * *

<a name="module_miot/device/bluetooth--module.exports.connectSocket"></a>

#### module.exports.connectSocket(macAddress, transport) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code>
根据device 的mac 地址，与中心设备建立socket 链接, 返回的数据没有实际作用, 执行到catch表示连接失败

**Kind**: static method of [<code>module.exports</code>](#exp_module_miot/device/bluetooth--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code> - 成功进入then，失败进入catch  
**Since**: 10023  

| Param | Type | Description |
| --- | --- | --- |
| macAddress | <code>string</code> | 中心设备mac地址。格式类似："AA:BB:CC:DD:EE:FF" |
| transport | <code>string</code> | 连接中心设备的相应服务的UUID,格式类似："1000000-0000-0000-000000000001" |


* * *

<a name="module_miot/device/bluetooth--module.exports.disconnectSocket"></a>

#### module.exports.disconnectSocket() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code>
断开与中心设备的socket连接, 返回的数据没有实际作用, 执行到catch表示断开连接失败

**Kind**: static method of [<code>module.exports</code>](#exp_module_miot/device/bluetooth--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code> - 成功进入then，失败进入catch  
**Since**: 10023  

* * *

<a name="module_miot/device/bluetooth--module.exports.write"></a>

#### module.exports.write(data) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code>
向蓝牙设备写入数据, 返回的数据没有实际作用, 执行到catch表示写失败

**Kind**: static method of [<code>module.exports</code>](#exp_module_miot/device/bluetooth--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code> - 成功进入then，失败进入catch  
**Since**: 10023  

| Param | Type |
| --- | --- |
| data | <code>string</code> | 


* * *

<a name="module_miot/device/bluetooth--module.exports.prepareBluetoothProfile"></a>

#### module.exports.prepareBluetoothProfile(profile) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code>
事先准备要需要的BluetoothProfile, 具体的类型是profile, 具体的数值参考Android Api: BluetoothProfile.HEADSET，BluetoothProfile.A2DP
HEADSET = 1;A2DP = 2;HEALTH = 3;

**Kind**: static method of [<code>module.exports</code>](#exp_module_miot/device/bluetooth--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code> - 成功进入then, 返回对应的profile，失败进入catch  
**Since**: 10023  

| Param | Type |
| --- | --- |
| profile | <code>int</code> | 


* * *

<a name="module_miot/device/bluetooth--module.exports.connectBluetoothProfile"></a>

#### module.exports.connectBluetoothProfile(macAddress, profile) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code>
连接类型为profile（比如BluetoothProfile.HEADSET，BluetoothProfile.A2DP) 的蓝牙服务

**Kind**: static method of [<code>module.exports</code>](#exp_module_miot/device/bluetooth--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code> - 成功进入then, 返回值没有实际作用，失败进入catch  
**Since**: 10023  

| Param | Type | Description |
| --- | --- | --- |
| macAddress | <code>string</code> | 需要查询的设备macAddress |
| profile | <code>int</code> | BluetoothProfile 接口类的类型（ BluetoothProfile.HEADSET，BluetoothProfile.A2DP等） |


* * *

<a name="module_miot/device/bluetooth--module.exports.disconnectBluetoothProfile"></a>

#### module.exports.disconnectBluetoothProfile(macAddress, profile) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code>
断开类型为profile（比如BluetoothProfile.HEADSET，BluetoothProfile.A2DP) 的蓝牙服务

**Kind**: static method of [<code>module.exports</code>](#exp_module_miot/device/bluetooth--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code> - 成功进入then, 返回值没有实际作用，失败进入catch  
**Since**: 10023  

| Param | Type |
| --- | --- |
| macAddress | <code>string</code> | 
| profile | <code>int</code> | 


* * *

<a name="module_miot/device/bluetooth--module.exports.getBluetoothProfileState"></a>

#### module.exports.getBluetoothProfileState(macAddress, profile) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code>
获取类型为profile的BluetoothProfile的当前状态, 返回值有四个选项,参考android api : BluetoothProfile.STATE_DISCONNECTED等
STATE_DISCONNECTED = 0; STATE_CONNECTING = 1;STATE_CONNECTED = 2;TATE_DISCONNECTING = 3;

**Kind**: static method of [<code>module.exports</code>](#exp_module_miot/device/bluetooth--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code> - 成功进入then, 返回值{"state": 0}，失败进入catch  
**Since**: 10023  

| Param | Type |
| --- | --- |
| macAddress | <code>string</code> | 
| profile | <code>int</code> | 


* * *

<a name="module_miot/device/bluetooth--module.exports.destroy"></a>

#### module.exports.destroy() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code>
销毁蓝牙服务

**Kind**: static method of [<code>module.exports</code>](#exp_module_miot/device/bluetooth--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code> - 成功进入then，失败进入catch  
**Since**: 10023  

* * *

<a name="module_miot/device/bluetooth--module.exports..ClassicBluetoothEvent"></a>

#### module.exports~ClassicBluetoothEvent : <code>object</code>
经典蓝牙事件名集合

**Kind**: inner namespace of [<code>module.exports</code>](#exp_module_miot/device/bluetooth--module.exports)  

* [~ClassicBluetoothEvent](#module_miot/device/bluetooth--module.exports..ClassicBluetoothEvent) : <code>object</code>
    * [.classicBlueBondStateChanged](#module_miot/device/bluetooth--module.exports..ClassicBluetoothEvent.classicBlueBondStateChanged)
    * [.classicBlueConnectionStateChanged](#module_miot/device/bluetooth--module.exports..ClassicBluetoothEvent.classicBlueConnectionStateChanged)
    * [.classicBlueReceivedData](#module_miot/device/bluetooth--module.exports..ClassicBluetoothEvent.classicBlueReceivedData)


* * *

<a name="module_miot/device/bluetooth--module.exports..ClassicBluetoothEvent.classicBlueBondStateChanged"></a>

##### ClassicBluetoothEvent.classicBlueBondStateChanged
BondStateChange 状态改变事件
返回的数据格式为：{"macAddress": "xxx", "state":xxx}
state 取值为：BOND_BONDING = 11;BOND_NONE = 10;BOND_BONDED = 12;

**Kind**: static property of [<code>ClassicBluetoothEvent</code>](#module_miot/device/bluetooth--module.exports..ClassicBluetoothEvent)  

* * *

<a name="module_miot/device/bluetooth--module.exports..ClassicBluetoothEvent.classicBlueConnectionStateChanged"></a>

##### ClassicBluetoothEvent.classicBlueConnectionStateChanged
经典蓝牙连接状态改变事件
返回的数据格式为：{"macAddress": "xxx", "state":xxx}
state 取值为：DISCONNECTED = 0;CONNECTING = 1;CONNECTED = 2;DISCONNECTING = 3;NO_STATE = 4;

**Kind**: static property of [<code>ClassicBluetoothEvent</code>](#module_miot/device/bluetooth--module.exports..ClassicBluetoothEvent)  

* * *

<a name="module_miot/device/bluetooth--module.exports..ClassicBluetoothEvent.classicBlueReceivedData"></a>

##### ClassicBluetoothEvent.classicBlueReceivedData
收到数据事件
返回的数据格式为：{"macAddress": "xxx", "data":"xxx"}

**Kind**: static property of [<code>ClassicBluetoothEvent</code>](#module_miot/device/bluetooth--module.exports..ClassicBluetoothEvent)  

* * *

