import React from 'react';
import { Text, Image, StyleSheet, View, TouchableOpacity, TouchableWithoutFeedback } from 'react-native';
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import { SCREEN_WIDTH } from '../util2/Const';
import Util from '../util2/Util';
import { BaseStyles } from "../BasePage";
import LinearGradient from 'react-native-linear-gradient';
import { DescriptionConstants } from '../Constants';
import { Host } from "miot";

const horizontalPadding = 20;
const interSpace = 10;
const columnNum = 3;

export const CardWidth = Math.floor((SCREEN_WIDTH - horizontalPadding * 2 - interSpace * (columnNum - 1)) / columnNum);
export const CardHeight = 110;// Math.floor(CardWidth * 64 / 99) + 34;
const TAG = "EventGridCard";
export default class EventGridCard extends React.Component {

  constructor(props) {
    super(props);
  }

  render() {
    const item = this.props.item;
    const imgStoreUrl = item.imgStoreUrl;
    let imgSource = imgStoreUrl;
    if (imgStoreUrl) {
      if (Util.isLocalThumb(imgStoreUrl, item.mediaType)) {
        imgSource = { uri: imgStoreUrl };
      } else {
        imgSource = { uri: `file://${ imgStoreUrl }` };
      }
    }
    let evTypeIc = Util.getSmallIconFromType(item.type);
    // console.log(TAG, "selected", item.selected);
    // let imgSource = imgStoreUrl == null ? null : { uri: `file://${ imgStoreUrl }` };
    let selectIconSource = require('../../resources2/images/icon_album_no_select.png');
    if (item.selected) {
      selectIconSource = require('../../resources2/images/icon_album_selected.png');
    }
    let durStr = null;
    if (item.duration) {
      durStr = `${ Util.zeroPad(Math.floor(item.duration / 60), 10) }:${ Util.zeroPad(Math.floor(item.duration % 60), 10) }`;
    }
    let typeIcon = (item.mediaType === "image") ? require("../../resources2/images/icon_type_image.png")
      : (item.mediaType === "sdcard" ? require("../../Resources/Images/icon_type_folder.png") : require("../../resources2/images/icon_type_video.png"));
    // console.log("Card", "duration", item.duration);
    return (<TouchableOpacity style={[styles.container, this.props.style]} onPress={() => { this.props.cardPressed(item) }}
      activeOpacity={this.props.isEditing ? 1 : 0.2}
    >

      {/* {this.renderItem(imgSource, durStr, typeIcon, selectIconSource)} */}
      <Image style={[styles.img, { backgroundColor: "#EEEEEE" }]} source={imgSource}
             onError={(err) => {
               console.log("=============image error", err);
             }}
        accessibilityLabel={DescriptionConstants.lc_35}
      />
      {
        this.props.isPlaying ?
          <View style={[styles.img, { position: "absolute", backgroundColor: "rgba(0,0,0,0.55)", alignItems: "center", justifyContent: "center" }]}>
            <Text style={[BaseStyles.text12, { color: "#ffffff" }]}>{LocalizedStrings["storage_item_playing"]}</Text>
          </View>
          :
          <LinearGradient colors={['#00000000', '#00000088']} style={[{ width: CardWidth }, { position: 'absolute', bottom: 38, left: 0, height: '20%', borderBottomLeftRadius: 9, borderBottomRightRadius: 9 }]}>
            <View style={[BaseStyles.row, { justifyContent: "flex-start", paddingLeft: 4, position: 'absolute', bottom: 3, left: 3 }]}>
              <Image style={{ height: 14, width: 14 }} source={typeIcon} />
              { durStr ? <Text style={[BaseStyles.text12, { marginLeft: 3, color: "white" }]} accessibilityLabel={DescriptionConstants.yc_8 + "00:" + durStr}>{durStr}</Text> : null }
            </View>
          </LinearGradient>

      }


      {this.props.isEditing ? <Image style={[styles.icon, {bottom: 43 }]}
        accessibilityLabel={this.props.item.selected ? DescriptionConstants.selected : DescriptionConstants.unSelected}
        source={selectIconSource} /> : null}

      <View style={[BaseStyles.row, { justifyContent: "flex-start", alignItems: "center", marginTop: -2, height: 30 }]}>
        {
          evTypeIc? <Image style={{ marginRight: 2, width: 12, height: 12 }} source = {evTypeIc}/>:<View style={{paddingLeft: 7}}/>
        }
        <Text style={{ color: 'gray' }} accessibilityLabel={DescriptionConstants.rp_65 + item.eventTime}>{item.eventTime}</Text>
      </View>
    </TouchableOpacity>);
  }

  renderItem(imgSource, durStr, typeIcon, selectIconSource) {
    return (
      <View >
        <Image style = {[styles.img, {  resizeMode: "stretch" }]} source = {imgSource}/>
        <Image
            source={require("../../resources2/images/shelter.png")}
            style={[styles.shelter]} />
        {
          this.props.isPlaying ?
            <View style={[styles.img, { position: "absolute", backgroundColor: "rgba(0,0,0,0.55)", alignItems: "center", justifyContent: "center" }]}>
              <Text style={[BaseStyles.text12, { color: "#ffffff" }]}>{LocalizedStrings["storage_item_playing"]}</Text>
            </View>
            :
            durStr ?
              <LinearGradient colors={['#00000000', '#00000055']} style={[BaseStyles.row, { position: 'absolute', bottom: 0, left: 0, height: '70%', borderBottomLeftRadius: 9, borderBottomRightRadius: 9 }]}>
                <View style={[BaseStyles.row, { justifyContent: "flex-start", paddingLeft: 4, position: 'absolute', bottom: 3, left: 3 }]}>

                  <Image style={{ height: 14, width: 14 }} source = {typeIcon}/>
                  <Text style={[BaseStyles.text12, { marginLeft: 3, color: "white" }]}>{durStr}</Text>
                </View>
              </LinearGradient>
              :
              <Image style={styles.typeIcon} source = {typeIcon}/>
        }


        {this.props.isEditing ? <Image style = {styles.icon} source = {selectIconSource}/> : null}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    width: CardWidth,
    height: CardHeight
  },
  shelter: {
    height:40,
    width:'100%',
    position: 'absolute',
    bottom: 0,
    borderBottomLeftRadius: 9,
    borderBottomRightRadius: 9
  },
  imgContainer: {
    width: CardWidth,
    height: CardHeight - 38
  },
  img: {
    width: CardWidth,
    height: CardHeight - 38,
    borderRadius: 9,
  },
  icon: {
    position: 'absolute',
    bottom: 5,
    right: 5,
    width: 22,
    height: 22
  },

  typeIcon: {
    position: 'absolute',
    bottom: 4,
    left: 4,
    height: 14,
    width: 14
  }
});
