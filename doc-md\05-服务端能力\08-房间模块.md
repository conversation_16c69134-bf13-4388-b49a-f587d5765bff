<a name="module_miot/service/Room"></a>

## miot/service/Room
家庭房间管理是指对米家APP中“米家 tab”页面的房间进行管理，主要包括房间信息的获取、增加和修改。
开发者需要注意的是由于该系列API会影响到米家APP的用户体验，因此做了权限的控制，
如果开发者有使用该系列API的需求， 需要与SDK开发人员进行沟通获取权限。
对于家庭房间管理模块，目前我们提供能力主要在于信息的获取、增加和修改，细分具体如下:
1、获取所有房间列表信息  2、创建（新增）房间  3、修改房间名称

**Export**: public  
**Doc_name**: 房间模块  
**Doc_index**: 8  
**Doc_directory**: service  
**Example**  
```js
import { Service } from "miot";
Service.room.loadAllRoom(true).then((rooms)=>{
  console.log(rooms)
}).catch((error)=>{
 console.log(error)
})
```

* [miot/service/Room](#module_miot/service/Room)
    * _static_
        * [.IMHRoom](#module_miot/service/Room.IMHRoom)
            * [.homeID](#module_miot/service/Room.IMHRoom+homeID)
            * [.roomID](#module_miot/service/Room.IMHRoom+roomID)
            * [.name](#module_miot/service/Room.IMHRoom+name)
            * [.shareFlag](#module_miot/service/Room.IMHRoom+shareFlag)
            * [.didList](#module_miot/service/Room.IMHRoom+didList)
            * [.updateName(newName)](#module_miot/service/Room.IMHRoom+updateName) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;unknown&gt;</code>
    * _inner_
        * [~IMiotRoom](#module_miot/service/Room..IMiotRoom)
            * [.loadAllRoom([forceReload])](#module_miot/service/Room..IMiotRoom+loadAllRoom) ⇒ <code>[ &#x27;Rromise&#x27; ].&lt;Array.&lt;IMHRoom&gt;&gt;</code>
            * [.createRoom(name)](#module_miot/service/Room..IMiotRoom+createRoom) ⇒ <code>[ &#x27;Rromise&#x27; ].&lt;IMHRoom&gt;</code>
            * [.getRoomNames(roomIds)](#module_miot/service/Room..IMiotRoom+getRoomNames) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;array&gt;</code>


* * *

<a name="module_miot/service/Room.IMHRoom"></a>

### miot/service/Room.IMHRoom
**Kind**: static interface of [<code>miot/service/Room</code>](#module_miot/service/Room)  

* [.IMHRoom](#module_miot/service/Room.IMHRoom)
    * [.homeID](#module_miot/service/Room.IMHRoom+homeID)
    * [.roomID](#module_miot/service/Room.IMHRoom+roomID)
    * [.name](#module_miot/service/Room.IMHRoom+name)
    * [.shareFlag](#module_miot/service/Room.IMHRoom+shareFlag)
    * [.didList](#module_miot/service/Room.IMHRoom+didList)
    * [.updateName(newName)](#module_miot/service/Room.IMHRoom+updateName) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;unknown&gt;</code>


* * *

<a name="module_miot/service/Room.IMHRoom+homeID"></a>

#### imhRoom.homeID
房间所属的家庭ID

**Kind**: instance property of [<code>IMHRoom</code>](#module_miot/service/Room.IMHRoom)  

* * *

<a name="module_miot/service/Room.IMHRoom+roomID"></a>

#### imhRoom.roomID
房间ID

**Kind**: instance property of [<code>IMHRoom</code>](#module_miot/service/Room.IMHRoom)  

* * *

<a name="module_miot/service/Room.IMHRoom+name"></a>

#### imhRoom.name
房间名称

**Kind**: instance property of [<code>IMHRoom</code>](#module_miot/service/Room.IMHRoom)  

* * *

<a name="module_miot/service/Room.IMHRoom+shareFlag"></a>

#### imhRoom.shareFlag
房间的分享标识

**Kind**: instance property of [<code>IMHRoom</code>](#module_miot/service/Room.IMHRoom)  

* * *

<a name="module_miot/service/Room.IMHRoom+didList"></a>

#### imhRoom.didList
房间支持的设备did列表，目前只有小爱音箱有权限

**Kind**: instance property of [<code>IMHRoom</code>](#module_miot/service/Room.IMHRoom)  

* * *

<a name="module_miot/service/Room.IMHRoom+updateName"></a>

#### imhRoom.updateName(newName) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;unknown&gt;</code>
更新房间名称

**Kind**: instance method of [<code>IMHRoom</code>](#module_miot/service/Room.IMHRoom)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;unknown&gt;</code> - 成功时：{"code":0, "data":'update success'}
失败时：{"code":xxx, "message":"xxx" }  
**Since**: 10020  

| Param | Description |
| --- | --- |
| newName | 新的房间名称 |


* * *

<a name="module_miot/service/Room..IMiotRoom"></a>

### miot/service/Room~IMiotRoom
**Kind**: inner class of [<code>miot/service/Room</code>](#module_miot/service/Room)  
**Export**:   

* [~IMiotRoom](#module_miot/service/Room..IMiotRoom)
    * [.loadAllRoom([forceReload])](#module_miot/service/Room..IMiotRoom+loadAllRoom) ⇒ <code>[ &#x27;Rromise&#x27; ].&lt;Array.&lt;IMHRoom&gt;&gt;</code>
    * [.createRoom(name)](#module_miot/service/Room..IMiotRoom+createRoom) ⇒ <code>[ &#x27;Rromise&#x27; ].&lt;IMHRoom&gt;</code>
    * [.getRoomNames(roomIds)](#module_miot/service/Room..IMiotRoom+getRoomNames) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;array&gt;</code>


* * *

<a name="module_miot/service/Room..IMiotRoom+loadAllRoom"></a>

#### iMiotRoom.loadAllRoom([forceReload]) ⇒ <code>[ &#x27;Rromise&#x27; ].&lt;Array.&lt;IMHRoom&gt;&gt;</code>
获取当前家庭所有房间列表

**Kind**: instance method of [<code>IMiotRoom</code>](#module_miot/service/Room..IMiotRoom)  
**Returns**: <code>[ &#x27;Rromise&#x27; ].&lt;Array.&lt;IMHRoom&gt;&gt;</code> - Promise, 带有房间列表的结果, IMHRoom的数据结构参考IMHRoom类
成功时：[{IMHRoom},...]
失败时：{"code":xxx, "message":"xxx" }  
**Since**: 10020  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| [forceReload] | <code>boolean</code> | <code>false</code> | 是否从强制从网络获取；  false:表示从缓存获取  true：从网络获取； 默认为false |


* * *

<a name="module_miot/service/Room..IMiotRoom+createRoom"></a>

#### iMiotRoom.createRoom(name) ⇒ <code>[ &#x27;Rromise&#x27; ].&lt;IMHRoom&gt;</code>
使用指定名称创建房间

**Kind**: instance method of [<code>IMiotRoom</code>](#module_miot/service/Room..IMiotRoom)  
**Returns**: <code>[ &#x27;Rromise&#x27; ].&lt;IMHRoom&gt;</code> - Promise, 带有房间结果,  IMHRoom的数据结构参考IMHRoom类
成功时：{IMHRoom}
失败时：{"code":xxx, "message":"xxx" }  
**Since**: 10020  

| Param | Type | Description |
| --- | --- | --- |
| name | <code>string</code> | 要创建的房间名 |


* * *

<a name="module_miot/service/Room..IMiotRoom+getRoomNames"></a>

#### iMiotRoom.getRoomNames(roomIds) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;array&gt;</code>
多用于多键开关获取其按键所在的房间名称，通过getMultiSwitchName接口拿到roomId，再通过此接口获取房间名称

**Kind**: instance method of [<code>IMiotRoom</code>](#module_miot/service/Room..IMiotRoom)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;array&gt;</code> - 成功时：返回数组[roomName1, roomName2, ...]
失败时：[]  
**Since**: 10061  

| Param | Type | Description |
| --- | --- | --- |
| roomIds | <code>array</code> | 要获取的房间名称的ids |

**Example**  
```js
Service.smarthome.getMultiSwitchName(Device.deviceID).then(res => {
 if (res) {
   console.log('getMultiSwitchName: ' + JSON.stringify(res));
   let roomIds = Object.keys(res).map(obj => (res[obj].room_id));
   Service.room.getRoomNames(roomIds).then(res => {
     if (res) {
       ...
       console.log('getRoomNames: ' + JSON.stringify(res));
     }
   }).catch(err => {
     console.log('getRoomNames: ' + JSON.stringify(err));
   })
 }
}).catch(err => {
 console.log('getMultiSwitchName: ' + JSON.stringify(err));
})
```

* * *

