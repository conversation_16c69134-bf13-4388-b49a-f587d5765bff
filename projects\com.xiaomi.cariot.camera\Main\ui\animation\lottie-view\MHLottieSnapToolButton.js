import React from "react";
import { Animated, Easing } from 'react-native';
import MHLottieBaseButton from "./base/MHLottieBaseButton";

/**
 * 创建自己的行为枚举值
 * @type  { enum } 
 */
export const MHLottieSnapToolBtnDisplayState = {
  NORMAL: "NORMAL"
};

export default class MHLottieSnapToolButton extends MHLottieBaseButton {

  /**
   * 创建自己的资源文件
   * @type  { object } 
   */
  static JSONFiles = {
    normal: {
      light: require("../lottie-json/btn_snap_play_back.json")
    }
  };

  /**
   * 创建自己的基于行为枚举值的链式动画
   * @param  { enum } displayState
   * @param  { bool } transition
   */
  switchDisplayState(displayState, transition) {

    if (displayState == MHLottieSnapToolBtnDisplayState.NORMAL) {
        
      this.setState({
        file: MHLottieSnapToolButton.JSONFiles.normal,
        loop: false
      });

      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();
    }
  }

  constructor(props) {
    super(props);

    this.replayAnimationWhenPress = true;
    this.replayAnimationWhenFocus = false;
  }

  componentWillUnmount() {
    super.componentWillUnmount();
  }

  componentDidMount() {
    super.componentDidMount();
  }

  componentDidUpdate(prevProps) {
    super.componentDidUpdate(prevProps);
  }
}
