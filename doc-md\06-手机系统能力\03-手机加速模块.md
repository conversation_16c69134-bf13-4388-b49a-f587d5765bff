<a name="module_miot/system"></a>

## miot/system
手机的加速计

**Export**: public  
**Doc_name**: 手机加速模块  
**Doc_index**: 3  
**Doc_directory**: system  
**Example**  
```js
import {System} from "miot"
import {AccelerometerChangeEvent} from "miot"
...
System.accelerometer.startAccelerometer(//interval).then(() => {
    alert(`startAccelerometer: ${ JSON.stringify(res) }`);
   })
...
   System.accelerometer.stopAccelerometer().then(() => {})
...
```

* [miot/system](#module_miot/system)
    * _static_
        * [.AccelerometerChangeEvent](#module_miot/system.AccelerometerChangeEvent) ⇒ <code>object</code>
    * _inner_
        * [~IAccelerometer](#module_miot/system..IAccelerometer)
            * [.startAccelerometer(interval)](#module_miot/system..IAccelerometer+startAccelerometer) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
            * [.stopAccelerometer()](#module_miot/system..IAccelerometer+stopAccelerometer) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>


* * *

<a name="module_miot/system.AccelerometerChangeEvent"></a>

### miot/system.AccelerometerChangeEvent ⇒ <code>object</code>
监听加速度数据事件。需要先调用startAccelerometer开始监听,回调的频率根据 startAccelerometer的 interval 参数

**Kind**: static constant of [<code>miot/system</code>](#module_miot/system)  
**Returns**: <code>object</code> - result 包含X、Y、Z轴的加速度  
**Since**: 10043  
**Example**  
```js
AccelerometerChangeEvent.onAccelerometerChange.addListener((result) => {
      console.log(result);
    });
```

* * *

<a name="module_miot/system..IAccelerometer"></a>

### miot/system~IAccelerometer
**Kind**: inner interface of [<code>miot/system</code>](#module_miot/system)  

* [~IAccelerometer](#module_miot/system..IAccelerometer)
    * [.startAccelerometer(interval)](#module_miot/system..IAccelerometer+startAccelerometer) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
    * [.stopAccelerometer()](#module_miot/system..IAccelerometer+stopAccelerometer) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>


* * *

<a name="module_miot/system..IAccelerometer+startAccelerometer"></a>

#### iAccelerometer.startAccelerometer(interval) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
开始监听加速度数据

**Kind**: instance method of [<code>IAccelerometer</code>](#module_miot/system..IAccelerometer)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - 成功时：{code:0,message:'success'}  
**Since**: 10043  

| Param | Type | Description |
| --- | --- | --- |
| interval | <code>string</code> | 监听加速度数据回调函数的执行频率。其合法值如下： game 适用于更新游戏的回调频率，在 20ms/次 左右； ui 适用于更新 UI 的回调频率，在 60ms/次 左右； normal 普通的回调频率，在 200ms/次 左右。 |

**Example**  
```js
System.accelerometer.startAccelerometer(interval.a).then((res) => {
        alert(`startAccelerometer: ${ JSON.stringify(res) }`);
      }).catch((error) => {
        alert(`startAccelerometer: ${ JSON.stringify(error) }`);
      });
```

* * *

<a name="module_miot/system..IAccelerometer+stopAccelerometer"></a>

#### iAccelerometer.stopAccelerometer() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
停止监听加速度数据

**Kind**: instance method of [<code>IAccelerometer</code>](#module_miot/system..IAccelerometer)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - 成功时：{code:0,message:'success'}  
**Since**: 10043  
**Example**  
```js
System.accelerometer.stopAccelerometer().then((res) => {
          alert(`stopAccelerometer: ${ JSON.stringify(res) }`);
        }).catch((error) => {
          alert(`stopAccelerometer: ${ JSON.stringify(error) }`);
        });
      }
```

* * *

