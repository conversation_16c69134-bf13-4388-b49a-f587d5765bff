import React from 'react';
import { Package } from 'miot';
import TitleBar from 'miot/ui/TitleBar';
import { ScrollView, View, Button, Platform, Text } from 'react-native';
import { Device } from 'miot/device';
import { createStackNavigator } from 'react-navigation';
import RouteProxy from "./RouteProxy";
import StackNavigationInstance from './StackNavigationInstance';
import { NavigationBar } from 'mhui-rn';
let rootStack = null;
export function getStack() {
  return rootStack;
}
function createRootStack(initPage) {
  return createStackNavigator({
    // 设置
    GestureSwitchSetting: new RouteProxy("GestureSwitchSetting"),
    SceneSelectPreposition: new RouteProxy("SceneSelectPreposition"),
    SceneSelectGesture: new RouteProxy("SceneSelectGesture"),
    SceneSelectFigure: new RouteProxy("SceneSelectFigure"),
    SceneSelectContact: new RouteProxy("SceneSelectContact"),
  },
  {
    initialRouteName: initPage,
    navigationOptions: ({ navigation }) => {
      StackNavigationInstance.setStackNavigationInstance(navigation);
      if (navigation.state.params && navigation.state.params.show) {
        return { header: null };
      } else {
        return {
          header:
          // <TitleBar
          //   title={navigation.state.params ? navigation.state.params.title : 'Page Name'}
          //   type={'dark'}
          //   leftText={navigation.state.params ? navigation.state.params.leftText : null}
          //   rightText={navigation.state.params ? navigation.state.params.rightText : null}
          //   onPressLeft={navigation.state.params ? navigation.state.params.onPressLeft : () => { navigation.goBack(); }}
          //   onPressRight={navigation.state.params ? navigation.state.params.onPressRight : null}
          //   leftTextStyle={navigation.state.params ? navigation.state.params.leftTextStyle : null}
          //   rightTextStyle={navigation.state.params ? navigation.state.params.rightTextStyle : null}
          // />
            <NavigationBar
              {...navigation.state.params}
            />
        };
      }
    }
  });
}
export default class SceneMain extends React.Component {
  constructor(props, context) {
    super(props, context);
    console.log("native传过来的参数为：", JSON.stringify(Package.entryInfo), Device.deviceID, Device.model);
    if (Package.entryInfo.payload.plug_id == "chuangmi.ipc.gesture") {
      this.initPage = "SceneSelectGesture";
    } else if (Package.entryInfo.payload.plug_id == "chuangmi.ipc.favindex") {
      this.initPage = "SceneSelectPreposition";
    } else if (Package.entryInfo.payload.plug_id == "chuangmi.ipc.calluser") {
      this.initPage = "SceneSelectContact";
    } else {
      this.initPage = "SceneSelectFigure";
    }
    console.log("initPage=", this.initPage);
  }


  render() {
    let RootStack = createRootStack(this.initPage);
    return (<RootStack
      ref={(ref) => {
        rootStack = ref;// 保留RootStack，获取state.nav.routes 得到当前正在展示页的名称；获取_navigation，得到全局跳转页面的工具类。
      }}/>);
  }

}