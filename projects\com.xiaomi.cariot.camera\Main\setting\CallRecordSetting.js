'use strict';

import { ListItemWithSwitch } from 'miot/ui/ListItem';

import React from 'react';
import { Dimensions, ScrollView, View, Text, SectionList, Image, ActivityIndicator, Platform } from 'react-native';
import { Device, Service } from 'miot';


import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import Toast from '../components/Toast';
import NavigationBar from "miot/ui/NavigationBar";

import { DarkMode } from 'miot';
import Host from "miot/Host";
import dayjs from 'dayjs';

const WindowHeight = Math.max(Dimensions.get("window").height, Dimensions.get("window").width);
const WindowWidth = Math.min(Dimensions.get("window").height, Dimensions.get("window").width);
import LogUtil from '../util/LogUtil';
import NetInfo from "@react-native-community/netinfo";
import { SCREEN_WIDTH } from "../util2/Const";
import DateFilterView from "../alarm/components/DateFilterView";
import Util, { DayInMilli } from "../util2/Util";
import TrackUtil from "../util/TrackUtil";
import DateFilterViewV2 from "../alarm/components/DateFilterViewV2";
import { DescriptionConstants } from "../Constants";
import { CardHeight } from "../widget/EventGridCard";
const HeaderH = 58;
const FooterH = 0;
let mCardH = 72;
const dayOfYear = require('dayjs/plugin/dayOfYear');
const TAG = "CallRecordSetting";

export const LoadStatus = {
  Idle: "Idel",
  Finish: "Finish",
  Failed: "Failed",
  Loading: "Loading"
};
class HeaderDay extends React.PureComponent {

  render() {
    let appearDay = this.props.section.title.slice(1);
    let appearWeek = [LocalizedStrings["sunday1"], LocalizedStrings["monday1"], LocalizedStrings["tuesday1"], LocalizedStrings["wednesday1"], LocalizedStrings["thursday1"], LocalizedStrings["friday1"], LocalizedStrings["saturday1"]];
    let titleStr = `${ appearDay } ${ appearWeek[Number(this.props.section.title.slice(0, 1))] }`
    let titleDate = dayjs.unix(this.props.section.timestamp).toDate();
    if (Util.isToday(titleDate)) {
      titleStr = LocalizedStrings['today'];
    } else if (Util.isYestoday(titleDate)) {
      titleStr = LocalizedStrings['yestoday'];
    }

    return (
      <View style={ {
        backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : '#fff',
        marginTop: 12,
        marginHorizontal: 12,
        borderTopRightRadius: 12,
        borderTopLeftRadius: 12,
        height: 46,
        paddingBottom: 8
      } }>
        {/* <View style={styles.whiteblank}>
          </View> */ }
        <Text
          style={ { fontSize: 12, fontWeight: 'bold', color: "#8C93B0", marginHorizontal: 14, marginVertical: 12 } }>
          { titleStr }
        </Text>
      </View>
    );

  }
}

class CallList extends React.PureComponent {

  render() {
    let isOnlyOneInSection = this.props.section.data && this.props.section.data.length === 1;
    let lastIndexSection = this.props.section.data && this.props.index === this.props.section.data.length - 1;
    let bottomRadius = lastIndexSection ? {
      borderBottomLeftRadius: 12,
      borderBottomRightRadius: 12
    } : {};
    let callDesc = '';
    if (this.props.item.callType == 0) {
      // 设备呼叫
      callDesc = `${LocalizedStrings['cs_device_call_user']}${this.props.item.userName}（${this.props.item.userId}）`;
    } else {
      callDesc = `${this.props.item.userName}（${this.props.item.userId}）${LocalizedStrings['cs_call_device']}`;
    }

    let callState = '';
    if (this.props.item.duration > 0) {
      callState = (this.props.item.type == 0 ? LocalizedStrings['video_call'] : LocalizedStrings['voice_call']) + this.getCallDuration(this.props.item.duration);
    } else if (this.props.item.huangUpType == 3) {
      callState = LocalizedStrings['call_no_answer']
    } else if (this.props.item.huangUpType == 5) {
      callState = LocalizedStrings['call_refuse']
    } else if (this.props.item.huangUpType == 7) {
      callState = LocalizedStrings['call_other_side_busy']
    } else {
      callState = LocalizedStrings['call_cancel']
    }
    return (
      <View style={ [{
        display: 'flex',
        flexDirection: 'row',
        marginHorizontal: 12,
        backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : 'xm#fff',
        height: mCardH,
      }, bottomRadius] }>
        <View style={{width: 24}}></View>

        <View style={ { flexDirection: 'column', alignItems: 'center'} }>
          {
            isOnlyOneInSection ? null : lastIndexSection ?
              <View style={ { width: 1, height: 10, backgroundColor: '#D8D8D8' } }/> :
              <View style={ { width: 1, flex: 1, backgroundColor: '#D8D8D8', top: this.props.index === 0 ? 10 : 0 } }/>
          }
          <Image
            style={ { width: 20, height: 20, position: "absolute" } }
            source={ this.props.item.callType != 0 ? require('../../Resources/Images/icon_call_record_to_device.png') : require('../../Resources/Images/icon_call_record_from_device.png') }/>


        </View>
        <View style={{width: 20}}></View>
        <View style={ { minWidth: 48 } }>
          <Text style={ { color: '#000000', fontSize: 16, paddingRight: 8, lineHeight: 20 } }>
            { this.props.item.appearHour }
          </Text>
        </View>
        <View style={{width: 8}}></View>
        <View style={ { flexDirection: 'column', flex: 1 } }>
          <View style={ { flexDirection: "row", flexWrap: "wrap" } }>
            <Text style={ { color: '#000000', fontSize: 16, fontWeight: '500' } } numberOfLines={5}>
              { callDesc }
            </Text>
          </View>

          <View>
            <Text style={ { color: '#999999', fontSize: 13 } }>
              { callState }
            </Text>
          </View>
          {/*<View style={{height: 30}}></View>*/}
        </View>
      </View>
    );
  }

  getCallDuration(seconds) {
    let second = Number.parseInt(seconds % 60);
    let minute = Number.parseInt(seconds / 60 % 60);
    let hour = Number.parseInt(seconds / 60 / 60);
    let str;

    if (hour > 0) {
      str = this.isCNLang ? (`${hour}${LocalizedStrings['cruiseSetting_cruiseIntervalhour']}${minute}${LocalizedStrings['tip_time_minute']}${second}${LocalizedStrings['tip_time_second']}`):
        (`${hour}${LocalizedStrings['cruiseSetting_cruiseIntervalhour']} ${minute}${LocalizedStrings['tip_time_minute']} ${second}${LocalizedStrings['tip_time_second']}`);
    } else if (minute > 0){
      str = this.isCNLang ? `${minute}${LocalizedStrings['tip_time_minute']}${second}${LocalizedStrings['tip_time_second']}` : `${minute}${LocalizedStrings['tip_time_minute']} ${second}${LocalizedStrings['tip_time_second']}`;
    } else {
      str = second+LocalizedStrings['tip_time_second']
    }
    return str;
  }
}

export default class CallRecordSetting extends React.PureComponent {

  constructor(props, context) {
    super(props, context);
    this.state = {
      showCallRecordList: true,
      loadingStatus: LoadStatus.Idle,
      loading: false,
      moreEndtime: 0,
      starttime: 0,
      showMoreLoading: false,
      UngroupedList: [],
      hasMore: true,
      dateItems: [],
      callData: []
    };
    this.isScrolling = false;
    this.layoutList = [];
    this.currentStopItem = {};
    this.currentStopIndex = -1;
    this.isCNLang = Util.isLanguageCN();
    mCardH = this.isCNLang ? 72 : 92;
  }

  componentDidMount() {

    this.props.navigation.setParams({
      // show:true
      title: LocalizedStrings['cs_call_record'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => {
            this.props.navigation.goBack();
          }
        }
      ],
      backgroundColor: Util.isDark() ? "#xm000000" : '#F6F6F6',
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });

    let dateItems = this.genDateItem(true, 30);
    console.log("=====++++++", dateItems);
    this.setState({ dateItems: dateItems, curDate: new Date(dateItems[dateItems.length - 1].ts) });

    this.startTime = 0;
    let today = new Date();
    let date = today.getDate() - 29;
    let tgt = new Date();
    tgt.setDate(date);
    tgt.setHours(0,0,0,0);
    this.startTime = tgt.getTime()/1000;
    this.endTime = new Date().getTime()/1000;
    this.isFirstRequest = true;
    this.hasMore = true;
    this.isRefresh = true;
    this.getCallInfo();

  }

  genDateItem(aVip = false, aSaveLength = 7) {
    let dateItems = [];
    let today = new Date();
    let todayIndex = 0;
    let len = Math.max(aSaveLength + 1, 7);
    console.log("genDateItem", len, aVip, aSaveLength, DayInMilli);
    // 保证从周日开始，那么结束一定是周六。如果今天不是周六，将today向后移动至周六。注意251行设定初始值时也要改变
    let mls = today.getTime(); // 修改默认值
    if (today.getDay() !== 6) {
      todayIndex = 6 - today.getDay();
      let tempDay = today.getDate() + (6 - today.getDay());
      mls = today.setDate(tempDay);
    }

    for (let i = 0; i < len; i++) {
      let date = today.getDate() - i;
      let tgt = new Date(mls);
      let timestamp = tgt.setDate(date);

      if (i === len - 1 && tgt.getDay() !== 0) {
        console.log('补充', tgt.getDay())
        len += tgt.getDay();
      }
      let dateStr = tgt.getDate().toString();
      let dateKey = dayjs.unix(timestamp/1000).format("d" + LocalizedStrings["yyyymmdd"]);

      let item = {
        date: dateStr,
        wkDay: Util.getMoment(tgt.getTime() / 1000).format("dd"),
        ts: timestamp,
        key: dateKey,
        enabled: this.state.callData.length > 0 ? this.state.callData.findIndex(item=> item.title == dateKey) > -1 : false,
        selected: (i === todayIndex) ? true : false
      };
      dateItems.unshift(item);
    }
    return dateItems;
  }

  getCallInfo() {

    console.log("refresh Call info...");
    this.setState({ loadingStatus: LoadStatus.Loading });
    let params = {
      did: Device.deviceID,
      key: "18.2",
      type: 'event',
      time_start: this.startTime,
      time_end: this.endTime,
      // group: "day",
      limit: 1000
    };
    Service.smarthome.getDeviceDataV2(params).then((response) => {
      // console.log("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$,通话记录：", response);
      if (response.length > 0) {
        // byh@20240806 value 数据结构变更 "[{\"piid\":2,\"value\":[\"1722937431\",\"1\",\"\",\"\",\"0\",\"2\",\"1\"]}]"}
        let valueArr = JSON.parse(response[response.length - 1].value);
        if (typeof (valueArr[0]) != "string") {
          valueArr = valueArr[0].value;
        }
        let valueArrFirst = JSON.parse(response[0].value);
        // console.log("++++++++++++++++++++++",typeof (valueArr), valueArr,typeof (valueArr.value),typeof (valueArr[0]), typeof (valueArrFirst), Array.isArray(valueArrFirst),valueArrFirst,typeof (valueArrFirst[0]));

        let usefulData;
        if (this.isRefresh) {
          this.isRefresh = false;
          usefulData = response;
        } else {
          usefulData = this.state.UngroupedList.concat(response);
        }

        let callArr = this._onPickedDataV2(usefulData);
        this._setItemLayout(callArr);
        this.endTime = valueArr[0];
        if (this.isFirstRequest && callArr.length > 0 && callArr[0].data && callArr[0].data.length > 0) {
          // 滚动到通话记录所在周
          let scrollToDate = callArr[0].data[0].appearDay;
          this.state.dateItems.forEach((item, index) => {
            if (scrollToDate == item.key) {
              this.currentStopIndex = index;
              this.currentStopItem = item;
            }
          });
        }
        this.setState({
          showEmptyList: false,
          callData: callArr,
          UngroupedList: response,
          moreEndtime: valueArr[0]
        },() => {
          // 先把数据展示出来，后台继续拉数据
          this.isFirstRequest = false;
          this.getCallInfo();
        });
      } else {
        console.log("data is over pull");
        this.setState({ loadingStatus: LoadStatus.Finish });
      }

    }).catch((err) => {
      // console.log("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$err,通话记录：", err);
      LogUtil.logOnAll(TAG,"get call info error",err);
      NetInfo.fetch().done((status) => {
        console.log('status', JSON.stringify(status));
        let isWifi = status.toString().toLowerCase() === "wifi" || (status.type && status.type.toLowerCase() === "wifi");
        let isCellular = status.toString().toLowerCase() === "cellular" || (status.type && status.type.toLowerCase() === "cellular");
        let isConnected = status.isConnected;
        if (Platform.OS == 'android' && (isWifi || isCellular)) {
          isConnected = true;
        }
        if (!isConnected) {
          Toast.fail('no_network');
        } else {
          Toast.fail("c_get_fail", err);
        }
      });
      this.setState({ loadingStatus: LoadStatus.Failed });
    });
  }

  _onPickedDataV2(arr) {
    let temp = [];
    temp = arr.reduce((total, currentValue) => {
      let value = currentValue['value'];
      let valueArr = JSON.parse(value);
      if (typeof (valueArr[0]) != "string") {
        valueArr = valueArr[0].value;
      }

      if (valueArr[0] > 0) {

        let appearTime = dayjs.unix(valueArr[0]).format("d" + LocalizedStrings["yyyymmdd"] + ",HH:mm");
        let curDay = appearTime.split(',')[0];
        let curHour = appearTime.split(',')[1];
        currentValue['appearDay'] = curDay;
        currentValue['appearHour'] = curHour;
        currentValue['timestamp'] = valueArr[0];
        currentValue['callType'] = valueArr[1];
        currentValue['userName'] = valueArr[2];
        currentValue['userId'] = valueArr[3];
        currentValue['huangUpType'] = valueArr[4];
        currentValue['duration'] = valueArr[5];
        currentValue['type'] = valueArr.length > 6 ? valueArr[6] : 0;
        let currentVal = currentValue['appearDay'];
        total[currentVal] || (total[currentVal] = []);//当前数组有吗没有是空，有的话就有
        // 需要加个标记判断是否是重复数据
        let oneDayList = total[currentVal];
        if (oneDayList.length > 0) {
          let index = oneDayList.findIndex((item) => item.timestamp == valueArr[0]);
          if (index !== -1) {
            return total;
          }
        }
        total[currentVal].push(currentValue);
        return total;
      } else {
        return total;
      }

    }, {});
    // console.log("通话记录：", temp);

    let groupArr = [];
    for (var days in temp) {
      // console.log(days, arr[days])
      // let data = temp[days].concat(temp[days]);
      // data = data.concat(temp[days]);
      // data = data.concat(temp[days]);
      // data = data.concat(temp[days]);
      let obj = {};
      obj['title'] = days;
      obj['data'] = temp[days];
      obj['timestamp'] = temp[days].length > 0 ? temp[days][0].timestamp : 0;
      groupArr.push(obj);

    }

    this.setState({
      dateItems: this.state.dateItems.map((dItem, _index) => {
        if (this.isFirstRequest) {
          return  {
            ...dItem,
            enabled: groupArr.length > 0 ? groupArr.findIndex(item => dItem.key == item.title) > -1 : false,
            selected: groupArr.length > 0 ? groupArr.findIndex((item,index) => index == 0 && dItem.key == item.title) > -1 : false
          };
        } else {
          return  {
            ...dItem,
            enabled: groupArr.length > 0 ? groupArr.findIndex(item=> dItem.key == item.title) > -1 : false,
          };
        }
      })
    });
    return groupArr;
  }

  //这里是一个对象，key是日期
  _onPickedData(arr) {
    let temp = [];
    temp = arr.reduce((total, currentValue, currentIndex, arr2) => {
      let appearTime = dayjs.unix(currentValue['timestamp'] / 1000).format("d" + LocalizedStrings["yyyymmdd"] + ",HH:mm");
      let curDay = appearTime.split(',')[0];
      let curHour = appearTime.split(',')[1];
      currentValue['appearDay'] = curDay;
      currentValue['appearHour'] = curHour;
      let currentVal = currentValue['appearDay'];
      total[currentVal] || (total[currentVal] = []);//当前数组有吗没有是空，有的话就有
      total[currentVal].push(currentValue);
      return total;
    }, {});
    console.log("日期格式修改", temp);
    return temp;
  }

  render() {
    dayjs.extend(dayOfYear);
    return (
      <View style={ { backgroundColor: Util.isDark() ? "#xm000000" : '#F6F6F6', flex: 1 } }>
        <DateFilterViewV2
          ref={ (aDfv) => {
            this.mDfv = aDfv;
          } }
          totalWidth={ SCREEN_WIDTH }
          dataSource={ this.state.dateItems }
          dayButtonPressed={ (item) => {
            this.selectDate(item);
          }}
          extraHeader={null}
          // 加一个scrollModel属性，为1时按周切换
          scrollModel={1}
          currentStopIndex={this.currentStopIndex}
        />
        <View style={ { height: 1, marginHorizontal: 24, backgroundColor: "#E5E5E5" } }/>
        <Text style={ { marginVertical: 12, marginHorizontal: 24, color: 'rgba(140, 147, 176, 1)' } }>{ LocalizedStrings['cs_call_record_all'] }</Text>
        {/*{ this.state.showEmptyList ? this._renderEmptyView() : this._renderCallRecordListView() }*/}
        { this._renderCallRecordListView() }
      </View>
    );
  }

  selectDate(aItm, needScroll = true, currentStopIndex) {
    if (this.isScrolling && needScroll) {
      console.log("===+=++=+=+=+===))))))00", this.isScrolling)
      return;
    }
    // 只有滑动时传入才能计数，其他情况下不计数
    if (!(typeof currentStopIndex === "number" && currentStopIndex > -1)) {
      this.currentStopIndex = -1;
    }
    let date = new Date(aItm.ts);
    for (let di of this.state.dateItems) {
      if (di.ts === aItm.ts) {
        if (di.selected) {
          return;
        }
        di.selected = true;
      } else {
        di.selected = false;
      }
    }
   
    if (needScroll) {
      // 找到这个时间所在的sectionIndex
      let keyTitle = dayjs.unix(aItm.ts/1000).format("d" + LocalizedStrings["yyyymmdd"]);
      let index = (this.state.callData || []).findIndex((item) => item.title == keyTitle);
      console.log('index:------', index)
      if (index != -1) {
        // let params = { animated: false, sectionIndex: index, itemIndex: index==0 ? 0:0 };
        let params = { animated: false, sectionIndex: index, itemIndex: 0 };
        // console.log("====================", params)
        this.mSectionList && this.mSectionList.scrollToLocation(params);
      }
    }

    this.setState({ curDate: date, dateItems: this.state.dateItems, showExtFilter: false });
    // this.mDfTs = moment(aItm.ts);
    this.mDfTs = dayjs(aItm.ts);

    // this.mEvLst.switchDay(date, true);// remove all exist and refresh
    // TrackUtil.reportClickEvent('Monitoring_Date_ClickNum');

  }

  _renderEmptyView = () => {
    return (this.state.loadingStatus === LoadStatus.Finish ? <View accessibilityLabel={DescriptionConstants.kj_1_17} style={{ height: "100%", flex: 1, alignItems: "center", justifyContent: 'center' }}>
        <View style={{ alignItems: "center" }}>
          <Image
            style={{  width: 92, height: 60 }} source={Util.isDark() ? require("../../Resources/Images/icon_home_empty_d.webp") : require("../../Resources/Images/icon_home_empty.webp")} />
          <Text style={{ color: 'gray', textAlign: "center", paddingHorizontal: 20, marginTop: 10 }}
                numberOfLines={2}
          >{ LocalizedStrings['cs_call_record_empty'] }
          </Text>
        </View>
    </View> : null
    );
  }

  _renderCallRecordListView() {
    return (
      <View style={ { flex: 1 } }>
        <SectionList
          ref={(ref) => { this.mSectionList = ref; }}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 1 }}
          sections={ this.state.callData }
          keyExtractor={ (item, index) => `${item.time}_${index}` }
          renderItem={ ({ item, index, section }) =>
            <CallList item={ item } index={ index } section={ section }/>
          }
          renderSectionHeader={ ({ section }) =>
            // this._renderHeaderDay(section)
            <HeaderDay section={ section }/>
          }
          ListFooterComponent={
            <View style={ { paddingBottom: 30 } }/>
          }
          stickySectionHeadersEnabled={ false }
          refreshing={ LoadStatus.Loading === this.state.loadingStatus }

          onRefresh={ () => {
            // 刷新，重置结束时间
            this.endTime = new Date().getTime() / 1000;
            this.isRefresh = true;
            this.getCallInfo(); //下拉刷新加载数据
          } }
          onEndReachedThreshold={ 0.1 }
          ListEmptyComponent={this._renderEmptyView()}
          getItemLayout={this.mLayoutGetter}
          onScroll={ this.handleScroll }
          onScrollBeginDrag={ this.handleDragScrollBegin }
          onMomentumScrollEnd={ this.handleScrollEnd }
          onViewableItemsChanged={this.viewableChange}
        />
      </View>
    );
  }

  _setItemLayout(list) {

    let [itemHeight, headerHeight] = [mCardH, HeaderH];
    let layoutList = [];
    let layoutIndex = 0;
    let layoutOffset = 0;
    list.forEach(section => {
      layoutList.push({
        index: layoutIndex,
        length: headerHeight,
        offset: layoutOffset,
      });
      layoutIndex += 1;
      layoutOffset += headerHeight;
      section.data.forEach(() => {
        layoutList.push({
          index: layoutIndex,
          length: itemHeight,
          offset: layoutOffset,
        });
        layoutIndex += 1;
        layoutOffset += itemHeight;
      });
      layoutList.push({
        index: layoutIndex,
        length: 0,
        offset: layoutOffset,
      });
      layoutIndex += 1;
    });

    this.layoutList = layoutList;
  }

  mLayoutGetter = (aSections, index) => {
    let layout = this.layoutList.filter(n => n.index == index)[0];
    return layout;
  }

  viewableChange = (event) => {
    // console.log("===+viewableChange====+=",event)
    // console.log("===+viewableChange====+=item",event.changed[0].item)
    // console.log("===+====+=key",event.changed[0].key)
    // console.log("===+====+=section",event.changed[0].section)
    // console.log("===+====+=",event.viewableItems && event.viewableItems[0].key)
    if (!this.isScrolling) {
      return;
    }
    let viewableItems = event.viewableItems;
    if (viewableItems) {
      let { mostFrequent, maxCount } = this.findMostFrequent(viewableItems);
     /*  if (maxCount > 3) {
        this.state.dateItems.forEach((item) => {
          if (mostFrequent == item.key && !item.selected) {
            this.selectDate(item,false);
          }
        });
      } */
      this.state.dateItems.forEach((item, index) => {
        if (mostFrequent == item.key && !item.selected) {
          this.currentStopIndex = index;
          this.currentStopItem = item;
          // this.selectDate(item, true);
          this.selectDate(this.currentStopItem, false, this.currentStopIndex);
        }
      });
    }
  }

  findMostFrequent(array) {
    // 使用 Map 对象来记录每个元素出现的次数
    const frequencyMap = new Map();
    // 遍历数组，更新元素计数
    // console.log("}}]]]]]]]]]]]]]]]]]]]",array,frequencyMap)
    array.forEach(item => {
      if (item.index != null) {
        frequencyMap.set(item.section.title, (frequencyMap.get(item.section.title) || 0) + 1);
      }
    });
    // 寻找出现次数最多的元素
    let maxCount = 0;
    let mostFrequent = null;
    console.log("==+==++======+=+=+===========00000",frequencyMap);
    frequencyMap.forEach((count, item) => {
      if (count > maxCount) {
        maxCount = count;
        mostFrequent = item;
      }
    });
    // 返回出现次数最多的元素
    return { mostFrequent, maxCount };
  }

  handleDragScrollBegin = (evt) => {
    this.isScrolling = true;
    console.log("=============this is scroll begin")
  }

  handleScrollEnd = (evt) => {
    this.isScrolling = false;
    console.log("=============this is scroll end")
    if (Object.keys(this.currentStopItem).length ) {
      // this.selectDate(this.currentStopItem, true, this.currentStopIndex);
      this.selectDate(this.currentStopItem, false, this.currentStopIndex);
    }
    
  }

  handleScroll = (event) => {
    // this.isScrolling = true;
    // console.log("=========",event.nativeEvent);
    if (this.mSectionList.current) {
      const { viewableItems } = this.mSectionList.current;
      console.log('Scrolled to viewableItems:', viewableItems);
      // 遍历viewableItems计算当前可见的section
      viewableItems.forEach(({ section }) => {
        // 这里你可以执行你想要的操作，比如更新状态或调用API
        console.log('Scrolled to section:', section.key);
      });
    }
    // const {scrollTop, scrollHeight, clientHeight} = event.nativeEvent;
    // console.log('scrollTop:', scrollTop);
    // console.log('scrollHeight:', scrollHeight);
    // console.log('clientHeight:', clientHeight);
  };

}
