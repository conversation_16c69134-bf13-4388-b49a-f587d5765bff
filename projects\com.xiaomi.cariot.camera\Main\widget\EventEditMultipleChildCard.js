import React from 'react';
import { BaseStyles } from "../BasePage";
import Util from "../util2/Util";
import { StyleSheet, View, Text, Image, TouchableOpacity, Dimensions } from 'react-native';
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import { DescriptionConstants } from '../Constants';
export const CardHeight = 80;
export const CardMB = 12;
import { SCREEN_WIDTH } from '../util2/Const';
const screenWidth = Dimensions.get("window").width;

const TAG = "EventEditMultipleChildCard";
export default class EventEditMultipleChildCard extends React.Component {

  render() {
    let imgStoreUrl = this.props.item.imgStoreUrl;
    let imgSource = imgStoreUrl != null ? { uri: `file://${imgStoreUrl}` } : null;
    const type = this.props.type;
    // let iconSource = Util.getIconFromType(this.props.item.type, this.props.item.faceInfo ? this.props.item.faceInfo.name : null);
    let iconSource = this.props.item.isSelected?require("../../Resources/Images/icon_selected.png"):require("../../Resources/Images/icon_unselected.png")
    let actColor = Util.getActiveColorFromType(this.props.item.type);
    // nomal 0 read 1 active 2
    let styleIdx =  0;
    let timeStyle = [[BaseStyles.text12, { marginTop: 2, fontWeight: "bold", color: "#7F7F7F" }],
    [BaseStyles.text12, { marginTop: 2, color: "#7F7F7F", fontWeight: "bold" }],
    [BaseStyles.text12, { marginTop: 2, color: actColor, fontWeight: "bold" }]
    ];

    let descStyle = [[BaseStyles.text16, { fontWeight: "bold" }],
    [BaseStyles.text16, { color: "#7F7F7F", fontWeight: "bold" }],
    [BaseStyles.text16, { color: actColor, fontWeight: "bold" }]
    ];
    // console.log(TAG, "render", this.props.item.desc, "evt", this.props.item.eventTime, "type", this.props.item.type);
    let textW = SCREEN_WIDTH - 176 - 30;
    let showImg = true;
    if (typeof(this.props.item.isShowImg) === 'boolean') {
      showImg = this.props.item.isShowImg;
    }


    return (
      <View style={[{
        flexDirection: "row",
        width: "100%",
        flexWrap: 'nowrap',
        alignItems: "center",
        paddingLeft: 15
      },{ height: CardHeight, marginBottom: CardMB}]}>
        <Image
          style={[BaseStyles.icon22, { marginRight: 13 }]}
          accessibilityLabel={DescriptionConstants.kj_1_9}
        >
        </Image>
        <View style={[BaseStyles.column, { flex: 1, alignItems: "flex-start", justifyContent: "space-between", marginRight: 20, }]} accessibilityLabel={ this.props.item.desc.trim() + this.props.item.eventTime }>
          <Text
            style={descStyle[styleIdx]}
            numberOfLines={3}
            ellipsizeMode={"tail"}
          >{this.props.item.desc.trim()}
          </Text>
          <Text
            style={timeStyle[styleIdx]}>
            {this.props.item.eventTime}
          </Text>
        </View>

        {
          showImg ? <View styles={styles.imgView}>
            {
              imgSource?<Image
              accessibilityLabel={!this.props.isPlaying ? DescriptionConstants.kj_2_17 : DescriptionConstants.kj_2_18}
              style={[styles.imgView, {  alignSelf: "center", marginRight: 12, resizeMode: "stretch" }]} source={imgSource} />:<View style={[styles.imgView, {  alignSelf: "center", marginRight: 12, resizeMode: "stretch" ,backgroundColor: "#EEEEEE",borderRadius: 9}]}/>
            }
        </View> : null
        }
      </View>
    );
  }
}

const styles = StyleSheet.create({
  timeView: {
    flexDirection: 'row',
    alignItems: 'flex-start'
  },
  pressedTimeLabel: {
    color: 'gray'
  },
  imgView: {
    width: 106,
    height: 64,
    borderRadius: 9
  }
});
