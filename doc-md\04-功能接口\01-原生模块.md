<a name="module_miot/Host"></a>

## miot/Host
扩展程序运行时的宿主环境
所有由宿主APP直接提供给扩展程序的接口均列在这里. 主要包括原生业务页面、本地数据访问、系统提供的能力等
系统的能力主要包括：
音频(audio.js)
文件存储(file.js)
本地KV存储(storage.js)
编解码(crypto.js)
系统基本信息(locale.js)
米家APP提供的能力主要包括：
米家APP提供的UI能力(ui.js)

**Export**: public  
**Doc_name**: 原生模块  
**Doc_index**: 1  
**Doc_directory**: host  
**Example**  
```js
import {Host} from 'miot'

 Host.type // ios/ android/ tv
 Host.isIOS
 Host.isAndroid

 Host.version
 Host.apiLevel
 Host.isDebug


 Host.ui.openDeviceListPage()
 Host.ui.openShopPage(100)

 Host.locale.language
 Host.locale.timezone
 Host.locale.currentTimeMillis.then(time=>{})
 Host.locale.getCurrentCountry().then(country=>{})
 Host.locale.getPlaceMark().then(place=>{})
 Host.locale.getGPS().then(gps=>{})


 Host.file.readFile(path).then(file=>{})
 Host.file.writeFile(path, file).then(ok=>{})

 Host.storage.get(key)
 Host.storage.set(key, value)
```

* [miot/Host](#module_miot/Host)
    * _static_
        * [.displayCutoutTop](#module_miot/Host.displayCutoutTop) : <code>int</code>
        * [.MiTv](#module_miot/Host.MiTv)
        * [.type](#module_miot/Host.type) : <code>string</code>
        * [.systemInfo](#module_miot/Host.systemInfo) : <code>object</code>
        * [.isAndroid](#module_miot/Host.isAndroid) : <code>boolean</code>
        * [.isIOS](#module_miot/Host.isIOS) : <code>boolean</code>
        * [.isPad](#module_miot/Host.isPad) : <code>boolean</code>
        * [.isIphoneXSeries](#module_miot/Host.isIphoneXSeries) : <code>boolean</code>
        * [.version](#module_miot/Host.version) : <code>string</code>
        * [.apiLevel](#module_miot/Host.apiLevel) : <code>int</code>
        * [.isDebug](#module_miot/Host.isDebug) : <code>boolean</code>
        * ~~[.applicationEdition](#module_miot/Host.applicationEdition) : <code>int</code>~~
        * [.appConfigEnv](#module_miot/Host.appConfigEnv) : <code>int</code>
        * [.ui](#module_miot/Host.ui)
        * [.locale](#module_miot/Host.locale)
        * [.storage](#module_miot/Host.storage)
        * [.file](#module_miot/Host.file)
        * [.audio](#module_miot/Host.audio)
        * [.crypto](#module_miot/Host.crypto)
        * [.getPhysicsDimension()](#module_miot/Host.getPhysicsDimension) : <code>object</code>
        * [.getWifiInfo()](#module_miot/Host.getWifiInfo) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
        * [.getAppName()](#module_miot/Host.getAppName) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;string&gt;</code>
        * [.getPhoneScreenInfo()](#module_miot/Host.getPhoneScreenInfo) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
        * ~~[.getCurrentCountry()](#module_miot/Host.getCurrentCountry) ⇒~~
        * [.getOperatorsInfo()](#module_miot/Host.getOperatorsInfo) ⇒ <code>Promise</code>
        * [.createBackgroundExecutor(jx, initialProps)](#module_miot/Host.createBackgroundExecutor) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;IExecutor&gt;</code>
        * ~~[.phoneHasNfcForAndroid()](#module_miot/Host.phoneHasNfcForAndroid) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>~~
        * [.connectWifiWithSsid(ssid)](#module_miot/Host.connectWifiWithSsid) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;JSON&gt;</code>
        * [.bindProcessToNetwork(type)](#module_miot/Host.bindProcessToNetwork)
        * [.pageShouldAdapterSoftKeyboard(shouldAdapter)](#module_miot/Host.pageShouldAdapterSoftKeyboard) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;boolean&gt;</code>
        * [.checkAndroidLocationServerIsOpen()](#module_miot/Host.checkAndroidLocationServerIsOpen) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
        * [.getIOSLocationAuthorizationStatus()](#module_miot/Host.getIOSLocationAuthorizationStatus) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
        * [.jumpToThirdpartyApplication(scheme, params, passThrough)](#module_miot/Host.jumpToThirdpartyApplication) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
        * [.checkAbilityOfJumpToThirdpartyApplication(param)](#module_miot/Host.checkAbilityOfJumpToThirdpartyApplication) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;bool&gt;</code>
        * [.notifyMultikeyStateChanged()](#module_miot/Host.notifyMultikeyStateChanged)
        * [.setPadScrollDealStrategy(params)](#module_miot/Host.setPadScrollDealStrategy)
    * _inner_
        * [~HostEvent](#module_miot/Host..HostEvent) : <code>object</code>
            * ["cellPhoneNetworkStateChanged"](#module_miot/Host..HostEvent.event_cellPhoneNetworkStateChanged)
        * [~IExecutor](#module_miot/Host..IExecutor)


* * *

<a name="module_miot/Host.displayCutoutTop"></a>

### miot/Host.displayCutoutTop : <code>int</code>
获取Android 官方提供的打孔屏api提供的打孔屏高度。 ios手机一律返回0.

**Kind**: static property of [<code>miot/Host</code>](#module_miot/Host)  
**Read only**: true  
**Since**: 10042  

* * *

<a name="module_miot/Host.MiTv"></a>

### miot/Host.MiTv
获取小米电视模块

**Kind**: static property of [<code>miot/Host</code>](#module_miot/Host)  

* * *

<a name="module_miot/Host.type"></a>

### miot/Host.type : <code>string</code>
返回本地环境的类型, ios|android

**Kind**: static constant of [<code>miot/Host</code>](#module_miot/Host)  

* * *

<a name="module_miot/Host.systemInfo"></a>

### miot/Host.systemInfo : <code>object</code>
系统信息。包含sysVersion，mobileModel，hostApiLevel，isXiaomiPhone(红米手机不算)，以及miuiVersion（非MIUI系统不会有这个值）

**Kind**: static constant of [<code>miot/Host</code>](#module_miot/Host)  

* * *

<a name="module_miot/Host.isAndroid"></a>

### miot/Host.isAndroid : <code>boolean</code>
判断是否是 android

**Kind**: static constant of [<code>miot/Host</code>](#module_miot/Host)  

* * *

<a name="module_miot/Host.isIOS"></a>

### miot/Host.isIOS : <code>boolean</code>
判断是否 iOS，和上面那个方法二选一即可

**Kind**: static constant of [<code>miot/Host</code>](#module_miot/Host)  

* * *

<a name="module_miot/Host.isPad"></a>

### miot/Host.isPad : <code>boolean</code>
判断是否 Pad大屏设备

**Kind**: static constant of [<code>miot/Host</code>](#module_miot/Host)  

* * *

<a name="module_miot/Host.isIphoneXSeries"></a>

### miot/Host.isIphoneXSeries : <code>boolean</code>
判断是否 iOS 刘海屏  Android返回false

**Kind**: static constant of [<code>miot/Host</code>](#module_miot/Host)  
**Since**: 10044  目前已支持iPhone全系列  

* * *

<a name="module_miot/Host.version"></a>

### miot/Host.version : <code>string</code>
APP 的版本, 例如"1.0.0"

**Kind**: static constant of [<code>miot/Host</code>](#module_miot/Host)  

* * *

<a name="module_miot/Host.apiLevel"></a>

### miot/Host.apiLevel : <code>int</code>
APP 的 apiLevel

**Kind**: static constant of [<code>miot/Host</code>](#module_miot/Host)  

* * *

<a name="module_miot/Host.isDebug"></a>

### miot/Host.isDebug : <code>boolean</code>
判断是否是调试版本

**Kind**: static constant of [<code>miot/Host</code>](#module_miot/Host)  
**Read only**: true  

* * *

<a name="module_miot/Host.applicationEdition"></a>

### ~~miot/Host.applicationEdition : <code>int</code>~~
***Deprecated***

是否是国际版APP 国内版1 国际版2 欧洲版3

**Kind**: static constant of [<code>miot/Host</code>](#module_miot/Host)  
**Read only**: true  

* * *

<a name="module_miot/Host.appConfigEnv"></a>

### miot/Host.appConfigEnv : <code>int</code>
获取 米家APP中 我的-->开发者设置-->其他设置，  AppConfig接口拉取preview版数据 是否选中的状态
1:表示选中, preview ； 0：表示未选中, release
如果选中，Service.smarthome.getAppConfig 获取的数据为preview版数据， 反之为release版数据

**Kind**: static constant of [<code>miot/Host</code>](#module_miot/Host)  
**Read only**: true  
**Since**: 10024  

* * *

<a name="module_miot/Host.ui"></a>

### miot/Host.ui
可调起的host业务页面

**Kind**: static constant of [<code>miot/Host</code>](#module_miot/Host)  
**See**: [module:miot/host/ui](module:miot/host/ui)  

* * *

<a name="module_miot/Host.locale"></a>

### miot/Host.locale
host 的本地化设置, 包括语言,地区,城市等等

**Kind**: static constant of [<code>miot/Host</code>](#module_miot/Host)  
**See**: [module:miot/host/locale](module:miot/host/locale)  

* * *

<a name="module_miot/Host.storage"></a>

### miot/Host.storage
本地数据存储服务模块

**Kind**: static constant of [<code>miot/Host</code>](#module_miot/Host)  
**See**: [module:miot/host/storage](module:miot/host/storage)  

* * *

<a name="module_miot/Host.file"></a>

### miot/Host.file
本地文件服务模块

**Kind**: static constant of [<code>miot/Host</code>](#module_miot/Host)  
**See**: [module:miot/host/file](module:miot/host/file)  

* * *

<a name="module_miot/Host.audio"></a>

### miot/Host.audio
音频 播放，录制，转码相关模块

**Kind**: static constant of [<code>miot/Host</code>](#module_miot/Host)  
**See**: [module:miot/host/audio](module:miot/host/audio)  

* * *

<a name="module_miot/Host.crypto"></a>

### miot/Host.crypto
加密解密模块

**Kind**: static constant of [<code>miot/Host</code>](#module_miot/Host)  
**See**: [module:miot/host/crypto](module:miot/host/crypto)  

* * *

<a name="module_miot/Host.getPhysicsDimension"></a>

### miot/Host.getPhysicsDimension() : <code>object</code>
获取设备物理尺寸信息，即物理设备的长宽信息，包含长宽属性 返回值中包含 width 和 height 在设备旋转后需要重新读取

**Kind**: static method of [<code>miot/Host</code>](#module_miot/Host)  
**Since**: 10061  

* * *

<a name="module_miot/Host.getWifiInfo"></a>

### miot/Host.getWifiInfo() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
获取手机wifi信息;
在Android上，从Android 9开始，获取WiFi信息需要申请定位权限，因此插件在调用该接口需要先判断是否有定位权限，没有就提示用户授权；否则就拿不到WiFi信息

**Kind**: static method of [<code>miot/Host</code>](#module_miot/Host)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code> - 成功时：{BSSID:xxx, SSID:xxx}
失败时：返回的是错误信息，字符串格式  
**Example**  
```js
Host.getWifiInfo()
.then(res => console.log("ssid and bssid = ", res.SSID, res.BSSID))
.catch((error)=>{
  console.log(error)
});
```

* * *

<a name="module_miot/Host.getAppName"></a>

### miot/Host.getAppName() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;string&gt;</code>
获取APP名称

**Kind**: static method of [<code>miot/Host</code>](#module_miot/Host)  

* * *

<a name="module_miot/Host.getPhoneScreenInfo"></a>

### miot/Host.getPhoneScreenInfo() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
获取Android手机屏幕相关信息(包括状态栏高度)

**Kind**: static method of [<code>miot/Host</code>](#module_miot/Host)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code> - 手机屏幕相关信息 {'viewWidth':xxx, 'viewHeight':xxx, 'viewWidthPixel':xxx, 'viewHeightPixel':xxx}
viewWidth和viewHeight返回的都是dp值，若想使用px值还得使用PixelRatio.getPixelSizeForLayoutSize方法转化为px值
但是在Pad小窗上时由于修改了scale值，所以转换出来的px值会偏小
所以10056新增两个返回值viewWidthPixel和viewHeightPixel表示当前ReactView的宽高像素值，若想使用px值建议直接使用这两个值，不必再转换dp值  
**Since**: 10012  

* * *

<a name="module_miot/Host.getCurrentCountry"></a>

### ~~miot/Host.getCurrentCountry() ⇒~~
***Deprecated***

获取当前登陆用户的服务器国家

**Kind**: static method of [<code>miot/Host</code>](#module_miot/Host)  
**Returns**: Promise<string> 返回国家编码，如:‘CN’  
**Since**: 10010  

* * *

<a name="module_miot/Host.getOperatorsInfo"></a>

### miot/Host.getOperatorsInfo() ⇒ <code>Promise</code>
获取手机运营商信息
返回值中：
name 运营商名称-与手机语言一致
simOperator 运营商 国家编码(三位)+网络编码 参考 https://en.wikipedia.org/wiki/Mobile_country_code
countryCode 运营商国家码，ISO 3166-1 country code

**Kind**: static method of [<code>miot/Host</code>](#module_miot/Host)  
**Returns**: <code>Promise</code> - 运营商信息 {'1':{name:'',simOperator:'',,countryCode:''},'2':{...}}  
**Since**: 10021  

* * *

<a name="module_miot/Host.createBackgroundExecutor"></a>

### miot/Host.createBackgroundExecutor(jx, initialProps) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;IExecutor&gt;</code>
后台执行文件, 后台最多同时运行三个线程, 超过将销毁最早创建的 executor

**Kind**: static method of [<code>miot/Host</code>](#module_miot/Host)  
**Since**: 10002  

| Param | Type | Description |
| --- | --- | --- |
| jx | <code>\*</code> | 可执行的纯 js 文件, 不使用任何高级语法, 如要使用 es6, 请自行编译通过. |
| initialProps | <code>json</code> | 用于脚本初始化的数据, 在jx文件中为 'initialProps' 对象，使用方法参考样例 或者sampleProject中 ‘com.xiaomi.demo/Main/tutorial/JSExecutor.js’ |

**Example**  
```js
var myexecutor = null;
Host.createBackgroundExecutor(require('./test.jx'), {name1:"testName"})
     .then(executor=>{
         myexecutor = executor;
         executor.execute("myFunc", 1,2,'a')
                 .then(result=>{
                     console.log(result);
                 })
         //支持使用initialProps或者在jx中直接使用
         executor.execute("myFunc2", "initialProps.name1").then(res =>{...})
         //支持使用obj与arr
         executor.execute("SomeObject.myFunc3", {"name":"hello"}, ["a1","a2"]).then(res =>{...})
})
.then(err=>{...})
....
myexecutor&&myexecutor.remove();
```

* * *

<a name="module_miot/Host.phoneHasNfcForAndroid"></a>

### ~~miot/Host.phoneHasNfcForAndroid() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>~~
***Deprecated***

android 手机是否有NFC功能

**Kind**: static method of [<code>miot/Host</code>](#module_miot/Host)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - {hasNfc:true/false}  
**Since**: 10021  
**Example**  
```js
Host.phoneHasNfcForAndroid().then((result)=>{
  console.log(result.hasNfc);
}))
```

* * *

<a name="module_miot/Host.connectWifiWithSsid"></a>

### miot/Host.connectWifiWithSsid(ssid) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;JSON&gt;</code>
android 连接指定ssid得wifi，要求该wifi之前已经连接过 使用此api不需要特别权限

**Kind**: static method of [<code>miot/Host</code>](#module_miot/Host)  
**Since**: 10036  

| Param | Description |
| --- | --- |
| ssid | 需要去掉字串两端的引号。在native层会自己增加"" |

**Example**  
```js
Host.connectWifiWithSsid().then((result)=>{
  console.log(result);
}))
```

* * *

<a name="module_miot/Host.bindProcessToNetwork"></a>

### miot/Host.bindProcessToNetwork(type)
**Kind**: static method of [<code>miot/Host</code>](#module_miot/Host)  
**Since**: 10037  

| Param | Description |
| --- | --- |
| type | 0 for mobile  1 for wifi 2 for null equal to android's bindProcessToNetwork |


* * *

<a name="module_miot/Host.pageShouldAdapterSoftKeyboard"></a>

### miot/Host.pageShouldAdapterSoftKeyboard(shouldAdapter) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;boolean&gt;</code>
页面有输入框，需要打开软键盘，页面适配软键盘

**Kind**: static method of [<code>miot/Host</code>](#module_miot/Host)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;boolean&gt;</code> - 设置成功返回true(iOS没有实现这个接口,直接返回true)  
**Since**: 10027  (10050 后开始支持iOS)  

| Param | Type | Description |
| --- | --- | --- |
| shouldAdapter | <code>boolean</code> | Android: true: 表示进行适配,建议UI用ScrollView包裹起来，当输入框在屏幕的下半部分时，只会触发ScrollView滚动; false： 整个页面滚动, demo可参考SoftKeyboardAdapterTestDemo.js      iOS :  true 表示进行适配，整个页面会跟随滑动，false: 表示不进行适配，整个页面不会跟随键盘滑动，默认true   (10050 后开始支持iOS) |


* * *

<a name="module_miot/Host.checkAndroidLocationServerIsOpen"></a>

### miot/Host.checkAndroidLocationServerIsOpen() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
检测Android系统位置服务(不同于权限)是否打开  only Android

**Kind**: static method of [<code>miot/Host</code>](#module_miot/Host)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code> - 成功时：{"code":0, "data":{locationServerIsOpen: true/false}}
失败时：{"code":-1, "message":"xxx" }  
**Since**: 10038  

* * *

<a name="module_miot/Host.getIOSLocationAuthorizationStatus"></a>

### miot/Host.getIOSLocationAuthorizationStatus() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
获取iOS定位授权的权限状态 only iOS

**Kind**: static method of [<code>miot/Host</code>](#module_miot/Host)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code> - 成功时：{LocationAuthStatus}
失败时：{"message":"xxx" }  
**Since**: 10038  

* * *

<a name="module_miot/Host.jumpToThirdpartyApplication"></a>

### miot/Host.jumpToThirdpartyApplication(scheme, params, passThrough) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
跳转到其他App

**Kind**: static method of [<code>miot/Host</code>](#module_miot/Host)  
**Since**: 10039  

| Param | Type | Description |
| --- | --- | --- |
| scheme | <code>string</code> | 其他App的Scheme 如 mihome://plugin |
| params | <code>Object</code> | 传给其他App的参数 |
| passThrough | <code>Object</code> | 从其他App回来时原封不动带回来的参数（部分App支持） 成功时：{"code":0, "data":{// 第三方app返回的数据}} 失败时：{"code":-1, "message":"xxx" } |


* * *

<a name="module_miot/Host.checkAbilityOfJumpToThirdpartyApplication"></a>

### miot/Host.checkAbilityOfJumpToThirdpartyApplication(param) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;bool&gt;</code>
判断是否可以跳到其他App

**Kind**: static method of [<code>miot/Host</code>](#module_miot/Host)  
**Result**: <code>&quot;code&quot;:0, &quot;data&quot;:true/false</code>  
**Since**: 10039  

| Param | Type | Description |
| --- | --- | --- |
| param | <code>string/Object</code> | 10074扩展了此API，参数param接受两种类型，string或Object 若为string，那么param就是scheme，用法和原来不变 若为object，其格式为:{   android:'xxxxxxx', //packageName   ios:'xxxxxx' //scheme } 这是为了解决在Android上某些APP的scheme与其他app支持的scheme格式相同的从而导致这个方法返回了true，而实际上目标APP却不存在的问题 所以在Android上改为使用应用包名的方式判断APP是否存在，Object中android Key传的是packageName，而iOS依旧传scheme即可 |


* * *

<a name="module_miot/Host.notifyMultikeyStateChanged"></a>

### miot/Host.notifyMultikeyStateChanged()
**Kind**: static method of [<code>miot/Host</code>](#module_miot/Host)  
**Param{object}**: 接收到的数据 {did: xxx, splitFlag: xxx}
             splitFlag可取值如下：
             1 ：设备已拆分
             0 ：设备已合并  
**Since**: 10059
多键开关状态发生变化--设备被拆分或者合并  
**Example**  
```js
Host.notifyMultikeyStateChanged(param);
```

* * *

<a name="module_miot/Host.setPadScrollDealStrategy"></a>

### miot/Host.setPadScrollDealStrategy(params)
**Kind**: static method of [<code>miot/Host</code>](#module_miot/Host)  
**Since**: 10072
设置Pad上的滑动策略（only Android）
这个api是为了解决某些插件使用的组件总是选择消耗滑动事件但是却又不做任何事，导致出现滑动无响应的问题  

| Param | Description |
| --- | --- |
| params | params.strategy [PAD_SCROLL_STRATEGY](PAD_SCROLL_STRATEGY) AUTO：表示默认策略，SDK会根据用户滑动位置做出相应的响应。 当用户滑动的位置不会消耗滑动事件时，该事件会被SDK消耗掉。 ALWAYS_SDK_DEAL：滑动事件总是交给SDK处理，插件将无法接受到任何滑动事件。 ALWAYS_PLUGIN_DEAL：滑动事件全部交给插件处理。（Scroll组件剩余的滑动距离依旧可以被SDK消费掉，因为SDK支持滑动嵌套） 这个方法一经调用所有插件页面都会应用设置的策略，所以如果只是某个页面需要适配滑动策略的话，请记得在退出该页面时将滑动策略设置回进入页面时的样子 |

**Example**  
```js
componentWillUnmount() {
     Host.setPadScrollDealStrategy({ strategy: PAD_SCROLL_STRATEGY.AUTO });
   }

效果可参考com.xiaomi.demo中的PadScrollDemo
```

* * *

<a name="module_miot/Host..HostEvent"></a>

### miot/Host~HostEvent : <code>object</code>
Host事件集合

**Kind**: inner namespace of [<code>miot/Host</code>](#module_miot/Host)  
**Example**  
```js
import { HostEvent } from 'miot/host';
   const subscription = HostEvent.cellPhoneNetworkStateChanged.addListener(
      (event)=>{
         ...
      }
    )
   ...
   subscription.remove()
   ...
```

* * *

<a name="module_miot/Host..HostEvent.event_cellPhoneNetworkStateChanged"></a>

#### "cellPhoneNetworkStateChanged"
手机网络状态变更事件

**Kind**: event emitted by [<code>HostEvent</code>](#module_miot/Host..HostEvent)  
**Param{object}**: 接收到的数据 {networkState: xxx}
             networkState可取值如下：
            -1 ：DefaultState
             0 ：网络不可用
             1 ：蜂窝网络 2G 3G 4G
             2 ：WiFi网络  
**Since**: 10031  
**Example**  
```js
可查看HostEventDemo.js
```

* * *

<a name="module_miot/Host..IExecutor"></a>

### miot/Host~IExecutor
jx执行器

**Kind**: inner typedef of [<code>miot/Host</code>](#module_miot/Host)  
**Since**: 10002  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| isReady | <code>boolean</code> | 是否可用 |
| isRunning | <code>boolean</code> | 是否运行中 |
| execute(method, | <code>\*</code> | ...args) - 执行某个函数 |
| remove() |  | 删除 |


* * *

