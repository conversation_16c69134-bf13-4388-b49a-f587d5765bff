import React from 'react';
import { Image, Text, View } from 'react-native';
import PropTypes from 'prop-types';
import { ConfigContext } from "mhui-rn/dist/components/configProvider";
import { Opacity, Radius, styles } from 'micariot-ui-sdk/common/styles/Styles';
import ScaleableOpacity from '../ScaleableOpacity';
import { DarkMode } from "miot";
import SvgUri from 'react-native-svg-uri';
import MarqueeText from "../MarqueeText";

const DEFAULT_HEIGHT = 132;
const DEFAULT_PADDING = 48;
const DEFAULT_PADDING_END = 24;
const DEFAULT_IMAGE_SIZE = 40;
const DEFAULT_IMAGE_SIZE2 = 48;
const DEFAULT_TEXT_MARGIN_RIGHT = 16;

/**
 * @export public
 * @module ClickableCardNew
 * @description ClickableCardNew for CarIoT
 * @property {object} style - style
 * @property {string} title - 标题文本
 * @property {string} tipText - 提示文本内容
 * @property {object} tipTextStyle - 提示文本style
 * @property {function} onPress - 点击回调方法
 * @property {bool} disabled - 是否禁用，默认值 false
 */
class ClickableCardNew extends React.PureComponent {
    static contextType = ConfigContext;
    static propTypes = {
      style: PropTypes.object,
      icon: PropTypes.object,
      title: PropTypes.string,
      tipText: PropTypes.string,
      tipTextStyle: PropTypes.object,
      onPress: PropTypes.func,
      disabled: PropTypes.bool,
      selected: PropTypes.bool
    };

    constructor(props) {
      super(props);
    }

    componentDidMount() {
    }

    componentWillUnmount() {
    }

    render() {
      const {
        colorScheme
      } = this.context;
      const opacity = this.props.disabled ? Opacity.Disabled : Opacity.Normal;
      return (
        <View
          style={[
            this.props.style,
            { opacity }
          ]}
          pointerEvents={this.props.disabled ? "none" : "auto"}
        >
          <ScaleableOpacity
            style={[{
              alignItems: 'center',
              height: DEFAULT_HEIGHT,
              padding: 6
            }]}
            selected={this.props.selected}
            onPress={this.props.onPress}
            disabled={this.props.disabled}
          >
            <View style={{
              flex: 1,
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: DarkMode.getColorScheme() === 'dark' ? "xm#C1CCE22B" : "xm#F0F5FFCC",
              borderRadius: Radius.WidgetLevel,
              height: 120,
              width: '100%',
              paddingLeft: DEFAULT_PADDING,
              paddingRight: DEFAULT_PADDING_END
            }}>

              {this.props.svgXmlData && <SvgUri width={DEFAULT_IMAGE_SIZE} height={DEFAULT_IMAGE_SIZE} svgXmlData={this.props.svgXmlData} />}
              {this.props.icon && <Image style={{ width: DEFAULT_IMAGE_SIZE, height: DEFAULT_IMAGE_SIZE }} source={this.props.icon} />}

              <View
                style={{
                  flex: 1,
                  flexDirection: "row",
                  marginLeft: 24,
                  justifyContent: 'center',
                  alignItems: 'center'
                }}
              >
                <View
                  style={{
                    flex: 1,
                    flexDirection: "column",
                    justifyContent: 'center',
                    marginEnd: DEFAULT_IMAGE_SIZE + 48
                  }}
                >
                  <Text numberOfLines={1} style={[styles.buttonTextStyle]}>{this.props.title}</Text>
                  <MarqueeText
                    isSelected={this.props.selected}
                    style={[styles.subTitleTextStyle, this.props.tipTextStyle, { marginTop: 8 }]}
                    speed={70}
                    text={ this.props.tipText }/>
                </View>
                <View style={{ position: 'absolute', right: 24 }}>
                  {this.props.selected && <Image
                    style={{ width: DEFAULT_IMAGE_SIZE, height: DEFAULT_IMAGE_SIZE }}
                    source={DarkMode.getColorScheme() === 'dark' ? require('../../../../Resources/Main/card1_icon_dark.png') : require('../../../../Resources/Main/card1_icon.png')}
                  />}
                </View>
              </View>
            </View>

          </ScaleableOpacity>
        </View>
      );
    }
}

export default ClickableCardNew;
export { ClickableCardNew };