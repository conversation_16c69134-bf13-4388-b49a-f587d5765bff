'use strict';

import { Device } from "miot";
import { Styles } from 'miot/resources';
import { ListItem } from 'miot/ui/ListItem';
import Separator from 'miot/ui/Separator';
import React from 'react';
import { ScrollView, View, Text } from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import API from '../API';
import Toast from '../components/Toast';
import AlarmUtil from '../util/AlarmUtil';
import VersionUtil from '../util/VersionUtil';
import { Rect } from "react-native-svg";
import NavigationBar from "miot/ui/NavigationBar";
import { DescriptionConstants } from '../Constants';
import CameraConfig from "../util/CameraConfig";
import Util from "../util2/Util";

export default class SurvelillancePeriodSetting extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
      type: 0,
      // not use
      detectionSwitch: false,
      interval: ''
    };
    if (this.props.navigation.state.params) {
      this.switchStartTime = this.props.navigation.state.params.startTime;
      this.switchEndTime = this.props.navigation.state.params.endTime;
      this.commitCallback = this.props.navigation.state.params.commitCallback;
      this.igRemoteWork = this.commitCallback != null;
    }
  }

  render() {
    return (
      <View style={styles.container}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.featureSetting}
            key={2}
          >
            {
              [
                { name: 'sps_all', type: 1 },
                { name: 'sps_day', type: 2 },
                { name: 'sps_night', type: 3 },
                { name: 'sps_custom', type: 4 }
              ].map((item, i) => {
                let title = LocalizedStrings[item.name];
                let subTitle = LocalizedStrings[`${ item.name }_detail`];
                let subTitleValue = LocalizedStrings[`${ item.name }_detail`];
                if (item.name == 'sps_custom' && this.switchStartTime && this.switchEndTime && (this.state.type == 4)) {
                  subTitle = LocalizedStrings[`${ item.name }_detail2`];
                  subTitle = subTitle.replace("%1$d", this.switchStartTime).replace("%2$d", this.switchEndTime);
                  subTitleValue = subTitle.replace(this.switchEndTime, DescriptionConstants.rp_66 + this.switchEndTime);
                }
                if (item.name == 'sps_day') {
                  subTitleValue = subTitle.replace("-", DescriptionConstants.rp_66);
                }
                let style = this.state.type == item.type
                  ? { color: Styles.common.MHGreen }
                  : { color: 'black' };
                return (
                  <ListItem
                    key={i + 100}
                    showSeparator={false}
                    accessibilityLabel={title + subTitleValue}
                    title={
                      (this.state.type === item.type ? '> ' : '  ') + title
                    }
                    titleStyle={[{ fontSize: 15 }, style, { fontWeight: 'bold', color: this.state.type === item.type ? Styles.common.MHGreen : 'black' }]}
                    subtitle={`   ${ subTitle }`}
                    subtitleStyle={[{ fontSize: 12 }, style]}
                    unlimitedHeightEnable={true}
                    subtitleNumberOfLines={4}
                    hideArrow={true}
                    onPress={() =>
                      this._onSelectedItem(item.type)
                    }
                    
                  />
                );
              })
            }
          </View>
        </ScrollView>
      </View>
    );
  }

  componentDidMount() {

    this.props.navigation.setParams({
      // show:true
      title: LocalizedStrings['sps_title'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });

    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {
        if (this.igRemoteWork) {
          let startTime = AlarmUtil.formatTimeString(this.switchStartTime);
          let endTime = AlarmUtil.formatTimeString(this.switchEndTime);

          let detectionType = this._type(startTime, endTime);
          console.log("xxxx startTime:" + startTime + " endTime:" + endTime + " detectionType:" + detectionType +" xxxxx");
          this.switchStartTime = startTime;
          this.switchEndTime = endTime;
          

          this.setState({ type: detectionType });
          return;
        }
        this._getSetting();
      }
    );
  }

  componentWillUnmount() {
    this.willFocusSubscription.remove();
  }

  formatTimeString(timeStr) {
    if (!timeStr || typeof (timeStr) != 'string') {
      return timeStr;
    }
    let subs = timeStr.split(':');
    if (subs.length < 3) {
      return timeStr;
    }
    let ret = '';
    for (let i = 0; i < subs.length; ++i) {
      let sub = subs[i];
      if (sub.length < 2) {
        sub = `0${ sub }`;
      }
      ret = ret + sub;
      if (i < subs.length - 1) {
        ret = `${ ret }:`;
      }
    }
    return ret;
  }

  _getSetting() {
    if (VersionUtil.isUsingSpec(Device.model)) {
      AlarmUtil.getSpecAlarmConfig(2).then((result) => {
        if (result instanceof Array) {
          let detectionSwitch = result[0].value;
          let alarmInterval = result[1].value;
          let sensitivity = result[2].value;

          let startTime = result[3].value;
          let endTime = result[4].value;

          let detectionType = this._type(startTime, endTime);
          
          startTime = AlarmUtil.formatTimeString(startTime);
          endTime = AlarmUtil.formatTimeString(endTime);

          this.setState({
            detectionSwitch: detectionSwitch,
            interval: alarmInterval
          });
          this.switchStartTime = startTime;
          this.switchEndTime = endTime;

          this.setState({ type: detectionType });
        }
      }).catch((err) => {
        console.log(`getSpecAlarmConfig err=${ JSON.stringify(err) }`);
      });
    } else {
      AlarmUtil.getAlarmConfig().then((res) => {
        if (res.code == 0) {
        } else {
          console.log("getwxSetting:", JSON.stringify(-1));
          return;
        }
        this.setState({
          detectionSwitch: res.data.motionDetectionSwitch.detectionSwitch,
          interval: res.data.motionDetectionSwitch.interval
        });

        let startTime = AlarmUtil.formatTimeString(res.data.motionDetectionSwitch.startTime);
        let endTime = AlarmUtil.formatTimeString(res.data.motionDetectionSwitch.endTime);
        

        let detectionType = this._type(startTime, endTime);
        this.switchStartTime = startTime;
        this.switchEndTime = endTime;
        this.setState({ type: detectionType });
      }).catch((err) => {
        console.log("getSetting:", JSON.stringify(err));
        Toast.fail('c_get_fail', err);
      });
    }
  }

  _type(startTime, endTime) {
    if (startTime == this._startTime(1)
      && endTime == this._endTime(1)) {
      return 1;
    } else if (startTime == "00:00"
      && endTime.includes("23:59")) {
      return 1;
    } else if (startTime == this._startTime(2)
      && endTime == this._endTime(2)) {
      return 2;
    } else if (startTime == this._startTime(3)
      && endTime == this._endTime(3)) {
      return 3;
    } else {
      return 4;
    }
  }

  _startTime(type) {
    switch (type) {
      case 1: return '00:00';
      case 2: return '08:00';
      case 3: return '20:00';
      default: return '12:34';
    }
  }

  _endTime(type) {
    switch (type) {
      case 1: return '23:59';
      case 2: return '20:00';
      case 3: return '08:00';
      default: return '23:45';
    }
  }

  _onSelectedItem(type) {
    if (type == 4) {
      if (this.igRemoteWork) {
        this.props.navigation.navigate('CustomSurvelillancePeriodSetting', { startTime: this.switchStartTime, endTime: this.switchEndTime, commitCallback: (startTime, endTime) => {
          if (this.commitCallback) {
            return new Promise((resolve, reject) => {
              this.commitCallback(startTime, endTime)
                .then((res) => {
                  // 刷新

                  this.switchStartTime = AlarmUtil.formatTimeString(startTime);
                  this.switchEndTime = AlarmUtil.formatTimeString(endTime);

                  let detectionType = this._type(this.switchStartTime, this.switchEndTime);

                  this.switchStartTime = startTime;
                  this.switchEndTime = endTime;

                  this.setState({ type: detectionType });
                  resolve(res);
                })
                .catch((err) => {
                  reject(err);
                });
            });
  
          }
        } });
      } else {
        this.props.navigation.navigate("CustomSurvelillancePeriodSetting");
      }
      
      return;
    }
    Toast.loading('c_setting');

    if (this.igRemoteWork && this.commitCallback) {
      this.commitCallback(`${ this._startTime(type) }:00`, `${ this._endTime(type) }:00`)
        .then((res) => {
          Toast.success('c_set_success');
          this.switchStartTime = this._startTime(type);
          this.switchEndTime = this._endTime(type);
          this.setState({ type: type });
        })
        .catch((error) => {

          Toast.fail('c_set_fail');
        });
      return;
    }


    let startT = this._startTime(type);
    let endT = this._endTime(type);

    if (VersionUtil.isUsingSpec(Device.model)) {
      let param = { startTime: `${ startT }:00`, endTime: `${ endT }:00` };
      AlarmUtil.putSpecMotionDetectionPeriod(param).then((res) => {
        if (res[0].code == 0) {
          Toast.success('c_set_success');
          this.switchStartTime = startT;
          this.switchEndTime = endT;
          this.setState({ type: type });
        } else {
          Toast.fail('c_set_fail');
        }
      }).catch((err) => {
        Toast.fail('c_set_fail', err);
      });
    } else {
      AlarmUtil.putMotionDetection({
        open: this.state.detectionSwitch,
        interval: this.state.interval,
        startTime: `${ startT }:00`,
        endTime: `${ endT }:00`
      }).then((res) => {
        if (res.code == 0) {
          Toast.success('c_set_success');
          this.switchStartTime = startT
          this.switchEndTime = endT;
          this.setState({ type: type });
        } else {
          Toast.fail('c_set_fail');
        }
      }).catch((err) => {
        Toast.fail('c_set_fail', err);
      });
    }
  }



}
