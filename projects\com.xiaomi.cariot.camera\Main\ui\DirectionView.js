import { DarkMode } from 'miot';
import CameraConfig, { IMG_DARKMODE_TINT } from '../util/CameraConfig';

import {
  View, PanResponder, Image
} from 'react-native';
import PropTypes from 'prop-types';
import { DescriptionConstants } from '../Constants';

import React from 'react';
import LogUtil from '../util/LogUtil';



export const DirectionViewConstant = {
  DIRECTION_lEFT: 1,
  DIRECTION_UP: 3,
  DIRECTION_RIGHT: 2,
  DIRECTION_BOTTOM: 4,
  CMD_CHECK: 5,
  CMD_GET: 6,
  CMD_OFF: -1001,
  CMD_CHECK_END: -5
};

export default class DirectionView extends React.Component {

  static propTypes = {
    isPortrait: PropTypes.bool,
    onActionDown: PropTypes.func,
    onActionUp: PropTypes.func,
    onClickDirection: PropTypes.func
  };

  static defaultProps = {
    isPortrait: true
  }

  constructor(props) {
    super(props);


    // let isPortrait = this.props.isPortrait || true;// 默认为竖直的
    let isPortrait = this.props.isPortrait;
    this.bgImg = isPortrait ? require("../../Resources/Images/direction_portrait.png") : require("../../Resources/Images/direction_land.png");
    this.upImg = isPortrait ? require("../../Resources/Images/direction_portrait_up_pres.png") : require("../../Resources/Images/direction_land_up_pres.png");
    this.leftImg = isPortrait ? require("../../Resources/Images/direction_portrait_left_pres.png") : require("../../Resources/Images/direction_land_left_pres.png");
    this.rightImg = isPortrait ? require("../../Resources/Images/direction_portrait_right_pres.png") : require("../../Resources/Images/direction_land_right_pres.png");
    this.bottomImg = isPortrait ? require("../../Resources/Images/direction_portrait_down_pres.png") : require("../../Resources/Images/direction_land_down_pres.png");

    this.bgImgDark = require("../../Resources/Images/direction_portrait_dark.png");
    this.upImgDark = require("../../Resources/Images/direction_portrait_up_pres_dark.png");
    this.leftImgDark = require("../../Resources/Images/direction_portrait_left_pres_dark.png");
    this.rightImgDark = require("../../Resources/Images/direction_portrait_right_pres_dark.png");
    this.bottomImgDark = require("../../Resources/Images/direction_portrait_down_pres_dark.png");


    this.d = isPortrait ? 206 : 140;
    this.r = this.d / 2;


    this.downTime = 0;
    this.isPress = false;
    this.directionTimeout = null;

    this.panResponder = PanResponder.create({
      onStartShouldSetPanResponder: (evt) => { // 刚开始的时候
        let x = evt.nativeEvent.locationX;
        let y = evt.nativeEvent.locationY;

        let dx = x - this.r;
        let dy = y - this.r;
        let distance = Math.sqrt(dx * dx + dy * dy);
        if (distance > this.r) {
          return false;
        }
        return true;
      }, 
      onMoveShouldSetPanResponder: () => true,
      onShouldBlockNativeResponder: () => false,
      onPanResponderTerminationRequest: () => false, // 不允许其他人抢占。
      onPanResponderGrant: (evt) => {
        LogUtil.logOnAll("directionView", "on touch action down");
        //
        clearTimeout(this.directionTimeout);        
        // 判断区域
        this.downTime = new Date().getTime();
        let x = evt.nativeEvent.locationX;
        let y = evt.nativeEvent.locationY;

        let dx = x - this.r;
        let dy = y - this.r;
        let distance = Math.sqrt(dx * dx + dy * dy);
        if (distance > this.r) {
          return;
        }

        if (x < this.r && y > x && y < (this.d - x)) { // 左
          this.isPress = true;// 按压了
          this.setState({ direction: DirectionViewConstant.DIRECTION_lEFT });
        } else if (y < this.r && x < this.r ? (y < x) : (y < (this.d - x))) { // 上
          this.isPress = true;// 按压了
          this.setState({ direction: DirectionViewConstant.DIRECTION_UP });
        } else if (x > this.r && y < this.r ? (y > (this.d - x)) : y < x) { // 右
          this.isPress = true;// 按压了
          this.setState({ direction: DirectionViewConstant.DIRECTION_RIGHT });
        } else if (y >= this.r && x < this.r ? (y > (this.d - x)) : y > x) { // 下
          this.isPress = true;// 按压了
          this.setState({ direction: DirectionViewConstant.DIRECTION_BOTTOM });
        }
        if (this.isPress && this.props.onActionDown != null) {
          this.props.onActionDown();
        }
        if (this.isPress) {
          clearTimeout(this.directionTimeout);
          this.directionTimeout = setTimeout(() => {
            this.onPressDirection();
          }, 0);
        }

      },

      onPanResponderMove: () => {
        // ignore
      },

      onPanResponderRelease: () => {
        // release
        this.cancleDown();
      },
      onPanResponderTerminate: () => {
        //   
        this.cancleDown();
      }
    });
  }

  cancleDown = () => {
    
    LogUtil.logOnAll("directionView", "on touch action up cancel");
    
    this.isPress = false;// 没有按压了
    // 更新@byh 解决即使不点击云台，每次销毁时也会调用问题
    if (this.props.onActionUp != null && this.downTime > 0) {
      let temp = new Date().getTime() - this.downTime;
      this.downTime = 0;
      this.props.onActionUp(temp > 800);
    }
    clearTimeout(this.directionTimeout);
    this.setState({ direction: -1 });
  }

  onPressDirection = () => {
    if (this.isPress) {
      clearTimeout(this.directionTimeout);
      if (this.props.onClickDirection != null) {
        this.props.onClickDirection(this.state.direction);
      }
      this.directionTimeout = setTimeout(() => {
        this.onPressDirection();
      }, 200);
    }
  }

  state = {
    direction: -1
  }

  componentDidMount() {

  }

  componentWillUnmount() {
    // 更新@byh 调用此方法的意义是什么
    this.cancleDown();//控件被移除的时候也要尝试移除一下。
  }

  onLayout = (event) => {
    this.d = event.nativeEvent.layout.width;
    this.r = this.d / 2;
  }

  render() {
    let width = "100%";
    let colorScheme = DarkMode.getColorScheme();
    let isDark = false;
    if (colorScheme == 'dark' && this.props.isPortrait) {
      isDark = true;
    }
    let label = this.props.accessibilityLabel;
    if (!label) {
      label = "rnLabelDirectionView";
    }
    let bgImage = isDark ? this.bgImgDark : this.bgImg;
    let pressView = null;
    if (this.state.direction == DirectionViewConstant.DIRECTION_lEFT) {
      pressView = isDark ? this.leftImgDark : this.leftImg;
      label = DescriptionConstants.zb_12_1;
    } else if (this.state.direction == DirectionViewConstant.DIRECTION_UP) {
      pressView = isDark ? this.upImgDark : this.upImg;
      label = DescriptionConstants.zb_12_3;

    } else if (this.state.direction == DirectionViewConstant.DIRECTION_RIGHT) {
      pressView = isDark ? this.rightImgDark : this.rightImg;
      label = DescriptionConstants.zb_12_2;
    } else if (this.state.direction == DirectionViewConstant.DIRECTION_BOTTOM) {
      pressView = isDark ? this.bottomImgDark : this.bottomImg;
      label = DescriptionConstants.zb_12_4;
    }

    // let colorScheme = DarkMode.getColorScheme();
    // if (colorScheme == 'dark') {
    //   imageBtnStyle.tintColor = IMG_DARKMODE_TINT;
    // }
    // console.log("why!, pressView: " + pressView);
    // pressView = require("../../Resources/Images/direction_portrait_left_pres_dark.png");
    let imgStyle = { width: "100%", height: "100%", position: "absolute" };
    let containterStyle = { 
      width: width, 
      height: width, 
      position: "relative"
    };
    
    if (this.props.isPortrait) {
      if (isDark) {
        containterStyle.backgroundColor = "#EEEEEE";
      } else {
        containterStyle.backgroundColor = "#ffffff";
      }
    }
    

    return (
      <View style={containterStyle}
        onLayout={this.onLayout}
        accessibilityLabel= {label}
        focusable={true}
        accessible={true}
        accessibilityRole={"button"}
        accessibilityTraits={"button"}
        accessibilityLiveRegion={"assertive"}
        {...this.panResponder.panHandlers}
      >
        <Image style={imgStyle}
          source={bgImage}
        />
        <Image style={{ width: "100%", height: "100%", position: "absolute" }}
          source={pressView}
        />

      </View>
    );
  }
}