import React from 'react';
import EventGrid, { FooterH } from "./EventGrid";
import { View } from "react-native";
import EventCard, { CardHeight, CardMB } from "./EventCard";
import { BaseStyles } from "../BasePage";
import { LoadStatus } from "./EventList";
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import { Order } from "../framework/EventLoaderInf";
import Separator from 'miot/ui/Separator';
import Util, { DayInMilli } from "../util2/Util";
import dayjs from 'dayjs';

/*
UI data form
[
section0 = {
  title: XXX,
  data:[itm1, itm2, itm3]
}
.....
sectionX = {
  title: XXX,
  data:[itm1, itm2, itm3]
}
]
*/
const TAG = "EventSectionList";
export const EvSectionFilter = (aEv) => {
  return false;
};

export function evList2SectionList(aEvL) {
  let sectionLst = [];
  let dic = {};
  for (let i = 0; i < aEvL.length; i++) {
    let item = aEvL[i];
    buildSection(dic, item);
  }
  for (let key in dic) {
    let section = {
      title: key,
      data: dic[key],
      selected: false
    };
    sectionLst.push(section);
  }
  return sectionLst;
}

function buildSection(aSecDict, aItm) {

  let dStr = dayjs.unix(aItm.createTime / 1000).format(LocalizedStrings["yyyymmdd"]);
  if (Util.isToday(dayjs.unix(aItm.createTime / 1000))) {
    dStr = `${ dStr } | ${ LocalizedStrings['today'] }`;
  } else if (Util.isYestoday(dayjs.unix(aItm.createTime / 1000))) {
    dStr = `${ dStr } | ${ LocalizedStrings['yestoday'] }`;
  }
  let sec = aSecDict[dStr];
  if (!sec) {
    console.log(TAG, "buildSection create", dStr);
    aSecDict[dStr] = [aItm];
    return dStr;
  } else {
    sec.push(aItm);
    return null;
  }
}

export default class EventSectionList extends EventGrid {
  constructor(aProps) {
    super(aProps);
    this.mCardH = CardHeight + CardMB;
    this.mSecHeaderBg = BaseStyles.mainBg.backgroundColor;
  }

  getInitScroll() {
    let ev = this.state.events;
    if (this.props.playingId) {
      for (let sectionIndex = 0; sectionIndex < ev.length; ++sectionIndex) {
        let section = ev[sectionIndex].data;
        for (let itemIndex = 0; itemIndex < section.length; ++itemIndex) {
          let itm = section[itemIndex];
          // let mPId = `${ itm.fileId }`;
          let mPId = itm.offset != null ? `${ itm.fileId }_${ itm.offset }` : `${ itm.fileId }`;

          if (mPId === this.props.playingId) {
            return { animated: false, itemIndex, sectionIndex, viewOffset: 0, viewPosition: 0 };
          }
        }
      }
    }
    return null;
  }

  async getData(date, event, isMore = false, aOrder = Order.Desc) {
    console.log('landing1002');
    let data = await this.mLoader.getEventList(date, this.props.evType ? this.props.evType : event, isMore, this.props.type);
    return data;
  }

  applyFilter(aEvs, aFilter) {
    let curEv = aEvs;
    for (let sec of curEv) {
      sec.data = sec.data.filter(aFilter);
    }
    return curEv;
  }
  findEvent(aEv) {
    let curEv = this.state.events[0]['data'];
    for (let i = 0; i < curEv.length; ++i) {
      if (aEv.fileId == curEv[i].fileId) {
        return curEv[i];
      }
    }
    return null;
  }
  getAdjacentEvent(aEv) {
    let curEv = this.state.events;
    for (let i = 0; i < curEv.length; ++i) {
      let evGrp = curEv[i].data;
      let idx = evGrp.findIndex((aItm) => {
        return aItm.fileId == aEv.fileId;
      });
      if (idx != -1) {
        if (idx + 1 < evGrp.length) {
          return evGrp[idx + 1];
        } else {
          // check net grp
          if (i + 1 < curEv.length) {
            return curEv[i + 1].data[0];
          }
          // look back
          else if (idx - 1 >= 0) {
            return evGrp[idx - 1];
          }
          // pre section
          else if (i > 0) {
            return curEv[i - 1].data[0];
          } else {
            return null;
          }
        }
      }

    }
  }


  buildSection(aSecDict, aItm, aOrder) {
    return buildSection(aSecDict, aItm);
  }


  mRItem = ({ section, index }) => {
    // console.log(TAG, "mRItem aaa", section, "idx", index);
    let item = section.data[index];
    // let mPId = `${ item.fileId }`;
    let offset = item?.offset != undefined ? item.offset : -1;
    let mPId = offset != -1 ? `${item?.fileId}_${offset}` : `${item?.fileId}`; // grid and list use fileId only
    return (
      <EventCard 
        type={'details'}
        item={item}
        isPlaying = {this.props.playingId === mPId}
        cardPressed={(aItm) => {

          // this.naviTo('AlarmDetail', { item: item, event: this.state.selectedEventKey });
          item.isRead = true;
          let events = this.state.events;
          let nextDate = this.state.nextDate;
          this.props.onEventPress(item, { events, nextDate });
        }}/>);
  }
}
