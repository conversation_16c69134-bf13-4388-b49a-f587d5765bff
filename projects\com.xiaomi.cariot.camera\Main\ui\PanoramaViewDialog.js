import { View, Image, TouchableOpacity } from "react-native";
import PropTypes, { func } from "prop-types";

import React from 'react';
import { StyleSheet, Modal, Text } from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { DescriptionConstants } from '../Constants';
import ImageButton from "miot/ui/ImageButton";
import CheckBox from "miot/ui/Checkbox/Checkbox"
import { AccessibilityRoles } from "mhui-rn/dist/utils/accessibility-helper";
import CustomImageButton from "../widget/CustomImageButton";


export default class PanoramaViewDialog extends React.Component {

  static propTypes = {
    visible: PropTypes.bool,
    initType: PropTypes.number,
    showAngle: PropTypes.bool,
    onCancelPress: PropTypes.func,
    onConfirmPress: PropTypes.func
  }

  static defaultProps = {
    visible: false,
    showAngle: true,
    initType: 0
  };

  state = {
    selectedType: 0
  }

  constructor(props) {
    super(props);
    console.log(`why! constructor PanoramaViewDialog: ${ props.initType }`);

    this.state = {
      selectedType: props.initType
    };
  }

  componentDidMount() {
  }

  componentWillUnmount() {
  }



  render() {

    let installItems = [
      {
        panoType: 0,
        title: LocalizedStrings["pano_view_no_wall"],
        degree: LocalizedStrings["pano_view_360_deg"],
        source: require("../../Resources/Images/camera_panormic_360.png"),

      },
      {
        panoType: 1,
        title: LocalizedStrings["pano_view_one_side_wall"],
        degree: LocalizedStrings["pano_view_270_deg"],
        source: require("../../Resources/Images/camera_panormic_270.png"),
      },
      {
        panoType: 2,
        title: LocalizedStrings["pano_view_two_sides_wall"],
        degree: LocalizedStrings["pano_view_180_deg"],
        source: require("../../Resources/Images/camera_panormic_180.png"),
      }
    ];

    let title = LocalizedStrings["pano_dialog_title"];
    let dialogBgStyle = {
      display: "flex",
      position: "absolute", 
      bottom: 0, 
      width: "100%", 
      height: 350, 
      backgroundColor: "#ffffff",
      borderTopLeftRadius: 20, 
      borderTopRightRadius: 20       
    };

    if (!this.props.showAngle) {
      dialogBgStyle.height = 150;
      title = LocalizedStrings["pano_view_need_again"];
    }

    return (
      <Modal
        style={{ display: "flex", width: "100%", height: "100%" }}
        animationType="none"
        transparent={true}
        visible={this.props.visible}
        onRequestClose={() => {
          this.props.onCancelPress();
        }}
      >

        <View style={{ width: "100%", height: "100%", backgroundColor: "#00000066" }}>
          <View style={dialogBgStyle}>

            <Text style={styles.titleStyle}>
              {title}
            </Text>
          
            {
              this.props.showAngle ?
                <Text style={styles.descStyle}>
                  {LocalizedStrings["pano_dialog_desc"]}
                </Text>
                :
                null
            }

            {
              this.props.showAngle ?
                <View style={styles.installGroupStyle}>
                  {this._renderInstallItem(installItems[0])}
                  {this._renderInstallItem(installItems[1])}
                  {this._renderInstallItem(installItems[2])}
                </View>
                :
                null
            }


            <View style={styles.btnGroupStyle}>
              {this._renderButton(LocalizedStrings["btn_cancel"], "#000000", this.props.onCancelPress)}
              {this._renderButton(LocalizedStrings["btn_confirm"], "#34ADB3", this.props.onConfirmPress)}
            </View>
          </View>
        </View>

      </Modal>
    );
  }

  _renderInstallItem(item) {
    let installGroupStyle = {
      display: "flex",
      flexDirection: "column",
      position: "relative",
      width: "30%",
      height: "100%",
      alignItems: "center",
      justifyContent: "center"
    };

    let imgUnselect = require("../../Resources/Images/icon_camera_panoram_angle_unselect.png");
    let imgSelect = require("../../Resources/Images/icon_camera_panoram_angle_select.png");

    return (
      <View style={installGroupStyle} 
      >
        <Image style={{ position: "relative", width: 60, height: 60 }}
          source={item.source}
        >
        </Image>

        <Text style={{ position: "relative", marginTop: 10, fontSize: 13, textAlign: 'center', textAlignVertical: 'center' }}>
          {item.title}
        </Text>

        <Text style={{ position: "relative", marginTop: 5, fontSize: 12, textAlign: 'center', textAlignVertical: 'center' }}>
          {item.degree}
        </Text>

        <CustomImageButton style={{ padding: 4, marginTop: 15, width: 25, height: 25, borderRadius: 13 }}
          source={this.state.selectedType == item.panoType ? imgSelect : imgUnselect}
          onPress={() => {
            this.setState({ selectedType: item.panoType });
          }}
          disabled={this.state.selectedType == item.panoType}
          accessible={true}
          accessibilityLabel={item.degree}
          accessibilityRole={AccessibilityRoles.radio}
          accessibilityState={{
            selected: this.state.selectedType == item.panoType ? true : false,
            checked: this.state.selectedType == item.panoType ? true : false
          }}
        />
      </View>
    );
  }

  _renderButton(btnText, btnTextColor, onBtnPress) {

    let btnStyle = {
      position: "relative",
      fontSize: 16, 
      fontWeight: "normal",
      color: btnTextColor,
      textAlign: 'center',
      textAlignVertical: 'center'
    };

    return (
      <View style={styles.btnContainerStyle}>
        
        <TouchableOpacity style={styles.btnContainerStyle}
          onPress={() => {
            onBtnPress(this.state.selectedType);
          }}
        >
          <Text style={btnStyle}
            
          >
            {btnText}
          </Text>
        </TouchableOpacity>
      </View>
    );
  }
}

export const styles = StyleSheet.create({
  dialogBgStyle: {
    display: "flex",
    position: "absolute", 
    bottom: 0, 
    width: "100%", 
    height: 350, 
    backgroundColor: "#ffffff",
    borderTopLeftRadius: 20, 
    borderTopRightRadius: 20       
  },

  titleStyle: {
    position: "relative", 
    marginTop: 25, 
    fontSize: 18, 
    fontWeight: "bold", 
    textAlign: 'center', 
    textAlignVertical: 'center'
  },

  descStyle: {
    position: "relative", 
    marginTop: 10, 
    fontSize: 14, 
    textAlign: 'center', 
    textAlignVertical: 'center'
  },

  installGroupStyle: {
    display: "flex",
    flexDirection: "row",
    position: "relative",
    marginTop: 10,
    width: "100%",
    height: 180,
    alignItems: "center",
    justifyContent: "space-around"
  },


  btnGroupStyle: {
    display: "flex",
    position: "absolute",
    flexDirection: "row",
    bottom: 0,
    marginBottom: 20,
    width: "100%",
    height: 46,
    alignItems: "center", 
    justifyContent:"center"
  },

  btnContainerStyle: {
    display: "flex",
    flexDirection: "row",
    position: "relative",
    width: 147,
    height: 50,
    height: "100%",
    backgroundColor: "#EEEEEE",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 30,
    marginLeft: 6,
    marginRight: 6
  }


});