import React from 'react';

import { createStackNavigator } from "react-navigation";
import MainCarPage from "./car/MainCarPage";

import { Device, DeviceEvent, Host, Package } from "miot";
import Service from "miot/Service";

let rootStack = null;

export function getStack() {
  return rootStack;
}

function createRootStack(initPage, routeParam) {
  return createStackNavigator({
    // 主页面
    MainCarPage
  },

  {
    initialRouteName: initPage,
    initialRouteParams: routeParam,
    navigationOptions: ({ navigation }) => {
      return { header: null };
    }
  });
}


export default class CarIndex extends React.Component {
  constructor(props) {
    super(props);
    this.initData();
  }

  initData() {
    this.initPage = "MainCarPage";
  }

  componentDidMount() {
    Service.smarthome.reportLog(Device.model, "car index componentDidMount");
    Package.disableAutoCheckUpgrade = false;
  }


  render() {
    let RootStack = createRootStack(this.initPage, {});
    return (<RootStack
      ref={ (ref) => {
        rootStack = ref; // 保留RootStack，获取state.nav.routes 得到当前正在展示页的名称；获取_navigation，得到全局跳转页面的工具类。
      } }/>);
  }

}