import React from 'react';
import { BaseStyles } from "../BasePage";
import Util from "../util2/Util";
import { StyleSheet, View, Text, Image, TouchableOpacity, FlatList } from 'react-native';
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import { DescriptionConstants } from '../Constants';
export const CardHeight = 88;
export const CardMB = 12;
import EventEditMultipleChildCard from "./EventEditMultipleChildCard";

const TAG = "EventEditMultipleCard";
export default class EventEditMultipleCard extends React.Component {

  render() {
    let iconSource = this.props.item.selected?require("../../Resources/Images/icon_selected.png"):require("../../Resources/Images/icon_unselected.png");
    return (
      <TouchableOpacity style={[BaseStyles.column, { marginBottom: CardMB, borderRadius: 12, backgroundColor: "#ffffff" }]}
                        activeOpacity={1}
        onPress={() => {
          this.props.cardPressed(this.props.item);
          }}>
        <View style={[styles.row,{paddingTop: 22,paddingLeft: 15, paddingRight: 12}]}>
          <Image
            style={[BaseStyles.icon22, { marginRight: 13 }]}
            source={iconSource}
            accessibilityLabel={DescriptionConstants.kj_1_9}
          >
          </Image>
          <Text>{LocalizedStrings["event_count"].replace("%1$d", this.props.item.data.length)}</Text>
        </View>

        <FlatList
          data={this.props.item.data}
          style={{width: "100%"}}
          ref={(ref) => { this.mLst = ref; }}
          contentContainerStyle={[{ marginTop: 12, paddingBottom: 60,flexGrow: 1 }]}
          showsVerticalScrollIndicator={false}
          renderItem={
            ({ item, index }) => {
              let mkey = item.offset != null ? `c_${ index }_${ item.fileId }_${ item.offset }` : `c_${ index }_${ item.fileId }`;
              return (
                  <EventEditMultipleChildCard
                    style={styles.item}
                    item={item}
                    key={mkey}
                    isPlaying={this.props.playingId === `${ item.fileId }-${ item.offset }`}
                    cardPressed={(aItm) => {
                      // this.naviTo('AlarmDetail', { item: item, event: this.state.selectedEventKey });
                      item.isRead = true;
                      let events = this.state.events;
                      let nextDate = this.state.nextDate;
                      this.props.onEventPress(aItm, { events, nextDate });
                    }}
                    cardLongPressed={(aItm)=>{
                      let events = this.state.events;
                      let nextDate = this.state.nextDate;
                      this.props.onEventLongPress(aItm,{events,nextDate})
                    }}/>
                )
            }
          }
          keyExtractor={(item, index) => `e_${index}_${item.fileId}`}

        />


      </TouchableOpacity>
    );
  }
}

const styles = StyleSheet.create({
  row: {
    width: "100%",
    flexDirection: "row",
    flexWrap: 'nowrap',
  },
  timeView: {
    flexDirection: 'row',
    alignItems: 'flex-start'
  },
  pressedTimeLabel: {
    color: 'gray'
  },
  imgView: {
    width: 106,
    height: 64,
    borderRadius: 9
  },

});
