'use strict';

import { ListItemWithSwitch } from 'miot/ui/ListItem';
import Separator from 'miot/ui/Separator';
import React from 'react';
import { ScrollView, View, Text } from 'react-native';
import { Device, Host, Service } from 'miot';
import CameraConfig from '../util/CameraConfig';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import { ListItem } from 'miot/ui/ListItem';
import StorageKeys from '../StorageKeys';
import API from '../API';
import Toast from '../components/Toast';
import AlarmUtil from '../util/AlarmUtil';
import TrackUtil from '../util/TrackUtil';
import VersionUtil from '../util/VersionUtil';
import { CAMERA_ALARM_051 } from '../Constants';
import { CAMERA_ALARM, DescriptionConstants } from '../Constants';
import NavigationBar from "miot/ui/NavigationBar";
import { MessageDialog } from 'miot/ui/Dialog';

export default class AISetting extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
      babyCrySwitch: false,
      faceSwitch: false,
      enableFaceManager: false,
      petSwitch: false,
      babyPush: false,
      isVip: false,
      facePush: false,
      pedestrianDetectionPushSwitch: false,
      showGBFDialog: false
    };
    this.isEuropeServer = CameraConfig.getIsEuropeServer();
    this.isInternational = CameraConfig.getInternationalServerStatus();
    this.is051 = CameraConfig.Model_chuangmi_051a01 == Device.model;
    this.isBabyCrySpec = /**Device.model === CameraConfig.Model_chuangmi_022 || CameraConfig.isCamera039(Device.model)**/ CameraConfig.isXiaomiCamera(Device.model) || this.is051;//022走云端，039、049识别成功率低 改成了云端；目前只有c01a02的宝宝哭声开关走spec，051也走spec
    this.shouldDisableBabyCry = false;
  }

  render() {
    let faceRecognize = (
      <ListItemWithSwitch
        showSeparator={false}
        title={LocalizedStrings['as_facial_recognized']}
        value={this.state.faceSwitch}
        onValueChange={(value) => this._onFaceSwitchValueChange(value)}
        titleStyle={{ fontWeight: 'bold' }}
        onPress={() => {
        }}
        accessibilityLabel={DescriptionConstants.sz_6_5}
        accessibilitySwitch={{
          accessibilityLabel: DescriptionConstants.sz_6_5
        }}

      />
    );

    let faceManage = (
      <ListItem title={LocalizedStrings['camera_face_manage']}
        showSeparator={false}
        onPress={() => {
          if (!Device.isOwner) {
            Toast.fail('face_deny_tips');
            return;
          }
          TrackUtil.reportClickEvent("Camera_Face_ClickNum"); // Camera_Face_ClickNum
          // if (VersionUtil.Model_chuangmi_051a01 == Device.model) { // todo  react native canvas的canvas在米家pad模式下被缩放了，导致绘制出来的内容全部产生了偏移，不同设备不一样，处理起来很麻烦，暂时android pad跳原生。
          //   Service.miotcamera.showFaceRecognize(this.state.isVip || VersionUtil.isAiCameraModel(Device.model));
          //   return;
          // }
          if (VersionUtil.isAiCameraModel(Device.model)) {
            this.props.navigation.navigate('FaceManager2');  
          } else {
            this.state.isVip ? this.props.navigation.navigate('FaceManager', {
              isVip: this.state.isVip }) : this.props.navigation.navigate('NoVipFaceManager');
          }
          
          // Service.miotcamera.showFaceRecognize(this.state.isVip);

        }
        }
        titleStyle={{ fontWeight: 'bold' }}

      />
    );
    if (this.isInternational) {
      faceManage = null;// 海外隐藏人脸管理入口
      faceRecognize = null; // 只要是海外 就没有人脸开关
    }

    let babyCry = (
      <ListItemWithSwitch
        showSeparator={false}
        title={LocalizedStrings['as_baby_cry']}
        value={this.state.babyCrySwitch}
        onValueChange={(value) => this._onBabyCrySwitchValueChange(value)}
        titleStyle={{ fontWeight: 'bold' }}
        onPress={() => {
        }}
        accessibilityLabel={DescriptionConstants.rp_49}
        accessibilitySwitch={{
          accessibilityLabel: DescriptionConstants.rp_49
        }}

      />
    );
    let petSwitchItem = (
      <ListItemWithSwitch
        showSeparator={false}
        title={LocalizedStrings['pet_detection']}
        value={this.state.petSwitch}
        onValueChange={(value) => this._onPetSwitchChange(value)}
        titleStyle={{ fontWeight: 'bold' }}
      />
    );

    let smartMonitorItem = (<ListItem
      title={LocalizedStrings['setting_smart_monitoring']}
      showSeparator={false}
      onPress={()=> this.props.navigation.navigate("SmartMonitorSetting")}
      titleStyle={{ fontWeight: 'bold' }}
    />);
   
    let gesture_switch = <ListItem
      title={LocalizedStrings['gesture_switch_txt']}
      showSeparator={false}
      onPress={() => this.props.navigation.navigate("GestureSwitchSetting")}
      titleStyle={{ fontWeight: 'bold' }}
    />;
   
    let showFace = this.props.navigation.state.params.showFace;
    let showBabyCry = this.props.navigation.state.params.showBabyCry;
    let showPet = this.props.navigation.state.params.showPet;

    return (
      <View style={styles.container}>
        {/* <Separator/> */}
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* <View style={[styles.blank, { borderTopWidth: 0 }]}/> */}
          <View style={styles.featureSetting}>
            {showFace ? faceRecognize : null}
            {faceManage}
            {showBabyCry && CameraConfig.CloudBabyCry()? babyCry : null}
            {CameraConfig.isSupportPet(Device.model) && showPet ? petSwitchItem : null}

            {VersionUtil.isSupportSmartMonitor(Device.model) ? smartMonitorItem : null}
            {VersionUtil.Model_chuangmi_051a01 == Device.model ? gesture_switch : null}
          </View>

          {/* <View style={styles.whiteblank}
          /> */}
        </ScrollView>
        {this._renderGBFDialog()}
      </View>
    );
  }

  _onPetSwitchChange(value) {
    AlarmUtil.putPetSwitch022(value).then(() => {
      Toast.success('c_set_success');
      this.setState({ petSwitch: value });
    }).catch(() => {
      Toast.fail('c_set_fail');
      this.setState({ petSwitch: !value });
    });
  }

  componentDidMount() {
    this.props.navigation.setParams({
      // show:true
      title: LocalizedStrings['s_ai_setting'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });

    this.forceBabyCry = CameraConfig.isSupportNonVipBabyCry(Device.model);


    // 宝宝哭声部分设备的
    // API.get('/miot/camera/app/v1/get/alarmSwitch', 'business.smartcamera').then((res) => {
    if (this.isBabyCrySpec) {
      Service.spec.getPropertiesValue([this.is051? CAMERA_ALARM_051[0] :CAMERA_ALARM[0]], 2)
        .then((result) => {
          console.log(result);
          if (result instanceof Array && result.length >= 1) {
            let htValue = result[0].value;
            console.log(`why!, htValue=${htValue}`);
            this.setState({ babyCrySwitch: htValue });
          }
        })
        .catch((error) => {
          Toast.fail('c_get_fail', error);
        });
    }

    if (VersionUtil.isAiCameraModel(Device.model)) {
      AlarmUtil.getAiSwitch022(2).then((res) => {
        console.log("getAiSwitch022 res=", JSON.stringify(res));
        this.setState({
          faceSwitch: res[0].value,
          petSwitch: res[1].value
        });
      }).catch((err) => {
        console.log("getAiSwitch022 err=", JSON.stringify(err));
      });
    }

    AlarmUtil.getAlarmConfig().then((res) => {
      if (res.code == 0) {
      } else {
        Toast.fail('c_get_fail');
        return;
      }
      if (!this.isBabyCrySpec) {
        this.setState({
          babyCrySwitch: res.data.babyCrySwitch,
        });
      }
      if (!this.is051) {
        this.setState({
          faceSwitch: res.data.faceSwitch
        });
      }
    }).catch((err) => {
      Toast.fail('c_get_fail', err);
    });

    StorageKeys.IS_VIP_STATUS.then((vipStatus) => {
      StorageKeys.IN_CLOSE_WINDOW.then((windowStatus) => {
        if (vipStatus || windowStatus) {
          this.setState({ enableFaceManager: true, isVip: vipStatus });
        }
      })
    }).catch((err) => {
    });
  }

  _onFaceSwitchValueChange(value) {
    Toast.loading('c_setting');
    if (VersionUtil.isAiCameraModel(Device.model)) {
      if (value === false) {
        AlarmUtil.setFacePrivacyConfirmation(AlarmUtil.FACE_CANCEL).then(() => {
          this.setState({ faceSwitch: false });
        }).catch(() => {
          this.setState({ faceSwitch: true });
        });
      } else {
        this.setState({ showGBFDialog: true });
      }
      // AlarmUtil.putFaceSwitch022(value).then(() => {
      //   Toast.success('c_set_success');
      // }).catch((err) => {
      //   this.setState({ faceSwitch: !value });
      //   console.log("putFaceSwitch022 err=", JSON.stringify(err));
      //   Toast.fail('c_set_fail');
      // })
    } else {
      API.post('/miot/camera/app/v1/put/faceSwitch', 'business.smartcamera', {
        open: value
      }).then((res) => {
        this.setState({ faceSwitch: res.code == 0 ? value : !value });
        if (res.code == 0) {
          Toast.success('c_set_success');
        } else {
          Toast.fail('c_set_fail');
        }
      }).catch((err) => {
        this.setState({ faceSwitch: !value });
        Toast.fail('c_set_fail', err);
      });
    }
  }

  _renderGBFDialog() {
    if (VersionUtil.isAiCameraModel(Device.model) && !this.isInternational) { // 海外人脸全部不支持
      return (
        <MessageDialog
          visible={this.state.showGBFDialog}
          title={LocalizedStrings['face_service_tips']}
          message={this.isEuropeServer ? LocalizedStrings['eu_face_service_tips_message'] : LocalizedStrings['face_service_tips_message']}
          canDismiss={false}
          buttons={[
            {
              text: LocalizedStrings["license_negative_btn_face"],
              // style: { color: 'lightpink' },
              callback: (_) => {
                AlarmUtil.setFacePrivacyConfirmation(AlarmUtil.FACE_CANCEL);
                this.setState({ showGBFDialog: false, faceSwitch: false });
              }
            },
            {
              text: LocalizedStrings["license_positive_btn_face"],
              // style: { color: 'lightblue' },
              callback: (_) => {
                AlarmUtil.setFacePrivacyConfirmation(AlarmUtil.FACE_ACCEPT).then(() => {
                  this.setState({ faceSwitch: true });
                });
                this.setState({ showGBFDialog: false });
              }
            }
          ]}
          onDismiss={(_) => {
          }}
        />
      );
    }
  }

  _onBabyCrySwitchValueChange(value) {

    this.shouldDisableBabyCry = false;

    value ?
      TrackUtil.reportResultEvent("AIsettings_BabyCrying_Status", "type", 1)
      :
      TrackUtil.reportResultEvent("AIsettings_BabyCrying_Status", "type", 2);

    Toast.loading('c_setting');
    if (this.isBabyCrySpec) {
      Service.spec.setPropertiesValue([this.is051? { ...CAMERA_ALARM_051[0], value: value } :{ ...CAMERA_ALARM[0], value: value }])
        .then((result) => {
          let success = result[0].code == 0;
          if (success) {
            this.setState({
              babyCrySwitch: value
            });
            Toast.success('c_set_success');
          } else {
            this.setState({
              babyCrySwitch: this.state.babyCrySwitch
            });
            Toast.fail('c_set_fail');
          }
        })
        .catch((error) => {
          this.setState({
            babyCrySwitch: this.state.babyCrySwitch
          });
          Toast.fail('c_set_fail');
        });
    } else {
      API.post('/miot/camera/app/v1/put/babyCrySwitch', 'business.smartcamera', {
        open: value
      }).then((res) => {
        this.setState({ babyCrySwitch: res.code == 0 ? value : !value });
        if (res.code == 0) {
          Toast.success('c_set_success');
          if ((CameraConfig.Model_chuangmi_026c02 == Device.model || Device.model === CameraConfig.Model_chuangmi_046a01) && !this.state.isVip && !value) { // 026c02 海外 主动关闭了宝宝哭声开关，就要刷新  是否要主动退出??
            this.shouldDisableBabyCry = true;
          }
        } else {
          Toast.fail('c_set_fail');
        }
      }).catch((err) => {
        this.setState({ babyCrySwitch: !value });
        Toast.fail('c_set_fail', err);
      });
    }
  }

  componentWillUnmount() {
    if (this.shouldDisableBabyCry) {
      CameraConfig.force026c02BabyCryEnable(false);
    }
  }
}
