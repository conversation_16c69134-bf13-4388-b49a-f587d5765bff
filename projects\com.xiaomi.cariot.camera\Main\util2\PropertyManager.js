import { Host, Device, Service } from "miot";
const TAG = "PropertyManager";
export const EnablePropCache = false;
export const SetRet = {
  EffectNow: 0,
  EffectLater: 1
};
export default class PropertyManager {
  static myInstance = null;

  static instance() {
    if (this.myInstance == null) {
      this.myInstance = new PropertyManager();
      return this.myInstance;
    }

    return this.myInstance;
  }

  constructor() {
    this.mCache = new Map();
    this.mStatusHelper = null;
    // this.preload();
  }

  setStatusHelper(aHelper) {
    this.mStatusHelper = aHelper;
  }
  
  async preloadWithSpecMap(aSpecMap) {
    if (EnablePropCache) {
      let allProps = Object.values(aSpecMap);
      let specs = allProps.filter((aVal) => {
        return aVal != "hide" && null == aVal.type;
      });
      let profs = allProps.filter((aVal) => {
        return aVal != "hide" && 'prof' == aVal.type;
      });
      await this.getSpecValue(specs, true);
      await this.getSpecValue(profs, true, false);
    }
  }

  preload(aMethod = 1) {
    if (EnablePropCache) {
      if (1 == aMethod) {
        let allProps = Object.keys(PropertyManager.PropertySheet);
        // console.log(TAG, "preload", allProps);
        this.fetchV2(allProps, aMethod);
      } else {
        // TODO prof stuff
      }
    }
  }

  // 旧版属性对应spec的siid与piid的对应表，
  // 请参照 https://iot.mi.com/new/index.html 内产品功能定义
  static PropertySheet = {
    "prop.s_night_vision": { siid: 2, piid: 3 },
    "prop.s_fw_autoupgrade": { siid: 10, piid: 19 },
    "prop.settingsync_time": { siid: 10, piid: 40 },
    "prop.startuptimes": { siid: 10, piid: 43 },
    "prop.s_bell_select": { siid: 2, piid: 7 },
    "prop.s_volume": { siid: 10, piid: 2 },
    "prop.s_videodelay": { siid: 10, piid: 11 },
    "prop.s_videointerval": { siid: 4, piid: 2 },
    "prop.s_videolength": { siid: 10, piid: 12 },
    "prop.s_motiondetection": { siid: 10, piid: 55 },
    "prop.s_motionsensibility": { siid: 4, piid: 3 },
    "prop.s_motion_time": { siid: 10, piid: 38 },
    "prop.s_visitpush": { siid: 10, piid: 53 },
    "prop.s_pushpattern": { siid: 10, piid: 4 },
    "prop.s_motionpush": { siid: 10, piid: 54 },
    "prop.s_pushtype": { siid: 10, piid: 7 },
    "prop.electricity": { siid: 8, piid: 1 },

    "prop.s_ringer_music": { siid: 10, piid: 52 }, // 接收器音效 ringer-music spec
    "prop.s_ringer_time": { siid: 10, piid: 18 }, // 勿扰模式时间段 ringer-time spec
    "prop.eco": { siid: 2, piid: 6 }, // 原版是 keeplive，spec更改为eco，与keeplive相反
    "prop.s_keeplive_code": { siid: 10, piid: 36 }, // 误触发情况 eco-code spec
    "prop.s_router_mode": { siid: 10, piid: 1 }, // 路由器模式 router-mode spec
    "prop.s_disalarm": { siid: 7, piid: 1 }, // 强拆警报 alarm spec
    "prop.s_autoreply": { siid: 10, piid: 13 }, // 自动回复开关 autoreply spec
    "prop.s_autoreply_week": { siid: 10, piid: 14 }, // 自动回复每周生效时间 autoreply-week spec
    "prop.s_autoreply_time": { siid: 10, piid: 15 }, // 自动回复每天生效时间 autoreply-time spec
    "prop.s_autoreply_item": { siid: 10, piid: 16 }, // 自动回复内容 autoreply-item spec
    "prop.s_videomsg": { siid: 10, piid: 8 }, // 视频留言开关 videomsg spec
    "prop.s_videomsg_time": { siid: 10, piid: 9 }, // 视频留言时间段 videomsg-time spec
    "prop.online": { siid: 2, piid: 2 }, // 门铃在线状态 status spec
    "prop.domain": { siid: 10, piid: 56 } // 门铃语言 domain
  }

  static Properties = {
    "prop.s_powersave": "prop.s_powersave"// 门铃当前保活状态 不支持 spec新格式，使用时必须参数2
  }
  get _generalParams() {
    return {
      did: Device.deviceID,
      region: Host.locale.language.includes("en") ? "US" : "CN"
    };
  }

  _spProp(prop) {
    let sp = PropertyManager.PropertySheet[prop];
    return `prop.${ sp.siid }.${ sp.piid }`;
  }

  response2Value(property, response) {
    let deviceJson = response[Device.deviceID];

    if (deviceJson[property]) {
      return deviceJson[property];
    } else if (deviceJson[this._spProp(property)]) {
      return deviceJson[this._spProp(property)];
    }

    return null;
  }

  checkPropsCache(aProps) {
    let st = Date.now();
    let caches = [];
    for (let i = 0; i < aProps.length; ++i) {
      let cache = this.mCache.get(aProps[i]);
      if (cache != null) {
        caches.push(cache);
      } else {
        break;
      }
    }

    let ret = null;
    if (caches.length === aProps.length) {
      let ct = {};
      aProps.forEach((aProp, i) => ct[aProp] = caches[i]);
      ret = {};
      ret[Device.deviceID] = ct;
      // console.log(TAG, "catch hit", ret, "cost", (Date.now() - st));
    }
    return ret;
  }


  extract(aResp, aSpec, aUseSpec = true) {
    let key = aUseSpec ? `prop.${ aSpec.siid }.${ aSpec.piid }` : `prop.${ aSpec.siid }`;
    console.log(TAG, "extract", key, "useSpec", aUseSpec);
    let dat = aResp[Device.deviceID];

    if (dat) {
      return dat[key];
    } else {
      return null;
    }
  }

  // aSpec array for aspec defs
  getSpecValue(aSpec, aIgnoreCache, aUseSpec = true) {
    let spec = aSpec.map((aItm) => aUseSpec ? `prop.${ aItm.siid }.${ aItm.piid }` : `prop.${ aItm.siid }`);
    console.log(TAG, "getSpecValue", aSpec, spec);
    let cache = aIgnoreCache ? null : this.checkPropsCache(spec);
    if (cache != null) {
      console.log(TAG, "cache hit");
      cache.fromCache = true;
      return Promise.resolve(cache);
    } else {
      let params = {
        props: spec
      };

      let combinedParams = Object.assign({}, params, this._generalParams);

      // 提交的参数是一个json数组
      let obj = [combinedParams];
      return new Promise((aResolve, aReject) => {
        (aUseSpec ? Service.smarthome.batchGetDeviceDatas(obj) : Service.smarthome.batchGetDeviceProps(obj))
          .then((aRet) => {
            if (EnablePropCache) {
              let dat = aRet[Device.deviceID];
              Object.keys(dat).forEach((aKey) => {
                // console.log(TAG, "add to cache", aKey, "=>", dat[aKey]);
                this.mCache.set(aKey, dat[aKey]);
              });
            }
            aResolve(aRet);
          })
          .catch((aErr) => {
            aReject(aErr);
          });
      });
    }
  }
  // a single spec and a value
  setSpecValue(aSpec, aVal, aUseSpec = true) {
    let spec = aUseSpec ? `prop.${ aSpec.siid }.${ aSpec.piid }` : `prop.${ aSpec.siid }`;
    let params = {
      props: { [spec]: aVal }
    };
    let combinedParams = Object.assign({}, params, this._generalParams);
    // 提交的参数是一个json数组
    let obj = aUseSpec ? [combinedParams] : combinedParams;
    console.log(TAG, "setSpecValue USING", obj);
    return new Promise((aResolve, aReject) => {
      (aUseSpec ? Service.smarthome.batchSetDeviceDatas(obj) : Service.smarthome.setDeviceProp(obj))
        .then((aRet) => {
          console.log(TAG, "setSpecValue", aRet, params);
          if (EnablePropCache) {
            this.mCache.set(spec, aVal);
          }

          this.mStatusHelper.syncSettings([aSpec], aUseSpec)
            .then((aRet) => {
              aResolve("c_set_success");
            })
            .catch((aErr) => {
              console.log(TAG, "syncSettings err", aErr);
              aResolve("madv_diff_set_ffective_next_time");
            });
        })
        .catch((aErr) => {
          aReject(aErr);
        });
    });
  }

  /**
   * @param  {Array.<string>} 属性名数组，PropertySheet的key值
   * @param  {int} method=1   1代表使用“混合型Spec“进行操作，2代表旧版纯Profile形式进行操作，默认为1
   * PS：对于属性获取/更新，目前已知的有三种方式，这是由服务器端给出的意见书写的代码：
   * 1 - 混合型spec（method = 1）； => [{ did:0000001 props:[ prop.2.3, prop.4.2 ] }] 这样的形式
   * 2 - 纯Profile（method = 2）； => [{ did:0000001 props:[ prop.s_night_vision, prop.s_motion_time ] }] 这样的形式
   * 3 - 纯Spec（未添加，如需要，请参照spec.js中的getPropertiesValue与setPropertiesValue方法自行添加功能）.
   * @returns {Promise}
   */
  fetchV2(properties, aIgnoreCache = false, method = 1) {

    if (method == 1) {
      let specProperties = [];
      properties.forEach((element) => {
        let sp = PropertyManager.PropertySheet[element];
        specProperties.push(this._spProp(element));
      });

      let cache = aIgnoreCache ? null : this.checkPropsCache(specProperties);
      if (cache != null) {
        cache.fromCache = true;
        return Promise.resolve(cache);
      } else {
        let params = {
          props: specProperties
        };

        let combinedParams = Object.assign({}, params, this._generalParams);

        // 提交的参数是一个json数组
        let obj = [combinedParams];
        return new Promise((aResolve, aReject) => {
          Service.smarthome.batchGetDeviceDatas(obj)
            .then((aRet) => {
              if (EnablePropCache) {
                let dat = aRet[Device.deviceID];
                Object.keys(dat).forEach((aKey) => {
                  // console.log(TAG, "add to cache", aKey, "=>", dat[aKey]);
                  this.mCache.set(aKey, dat[aKey]);
                });
              }
              aResolve(aRet);
            })
            .catch((aErr) => {
              aReject(aErr);
            });
        });
      }

    } else if (method == 2) {
      let norProperties = [];
      properties.forEach((element) => {
        norProperties.push(PropertyManager.Properties[element]);
      });

      let params = {
        props: norProperties
      };

      let combinedParams = Object.assign({}, params, this._generalParams);

      // 提交的参数是一个json数组
      let obj = [combinedParams];

      return Service.smarthome.batchGetDeviceProps(obj);
    }

    return undefined;
  }

  /**
   * @param  {string} 属性名，PropertySheet的key值
   * @param  {any} 数值
   * @param  {int} method=1   1代表使用“混合型Spec“进行操作，2代表旧版纯Profile形式进行操作，默认为1
   * PS：对于属性获取/更新，目前已知的有三种方式，这是由服务器端给出的意见书写的代码：
   * 1 - 混合型spec（method = 1）； => [{ did:0000001 props:{ prop.2.3 : true, prop.4.2: 10 } }] 这样的形式
   * 2 - 纯Profile（method = 2）； => [{ did:0000001 props:{ prop.s_night_vision : true, prop.s_motion_time: 10 } }] 这样的形式
   * 3 - 纯Spec（未添加，如需要，请参照spec.js中的getPropertiesValue与setPropertiesValue方法自行添加功能）.
   * @returns {Promise}
   */
  updateV2(property, value, method = 1) {
    if (method == 1) {
      let spProperty = this._spProp(property);

      let params = {
        props: {
          [spProperty]: value
        }
      };

      let combinedParams = Object.assign({}, params, this._generalParams);

      // 提交的参数是一个json数组
      let obj = [combinedParams];
      return new Promise((aResolve, aReject) => {
        Service.smarthome.batchSetDeviceDatas(obj)
          .then((aRet) => {
            // console.log(TAG, "update", aRet, params);
            if (EnablePropCache) {
              this.mCache.set(spProperty, value);
            }
            aResolve(aRet);
          })
          .catch((aErr) => {
            aReject(aErr);
          });
      });
    } else if (method == 2) {
      let params = {
        props: {
          [property]: value
        }
      };

      let combinedParams = Object.assign({}, params, this._generalParams);

      // 提交的参数是一个json对象
      let obj = combinedParams;

      return new Promise((resolve, reject) => {
        Service.smarthome
          .setDeviceProp(obj)
          .then((reponse) => {
          // RPC同步
            const rpcParams = {
              keys: [property]
            };

            // 旧版Profile时，设置完属性进行了一次RPC设定同步
            Device.getDeviceWifi()
              .callMethod("camera_common_setting_sync", rpcParams)
              .then((response) => {
                console.log(
                  "✅ RPC sync method succeed:",
                  rpcParams,
                  JSON.stringify(reponse)
                );
              })
              .catch((error) => {
                console.log(
                  "❌ RPC sync method failed:",
                  rpcParams,
                  JSON.stringify(error)
                );
              });
            resolve(reponse);
          })
          .catch((error) => {
            reject(error);
          });
      });
    }

    return undefined;
  }
}
