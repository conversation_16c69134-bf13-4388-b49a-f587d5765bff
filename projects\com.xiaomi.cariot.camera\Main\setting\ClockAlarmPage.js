import React from 'react';
import {
  ScrollView,
  View,
  BackHandler,
  FlatList,
  Platform,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity, Dimensions
} from 'react-native';
import Switch from 'miot/ui/Switch';
import Checkbox from 'miot/ui/Checkbox';
import CameraConfig, { IMG_DARKMODE_TINT } from '../util/CameraConfig';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlarmUtil from '../util/AlarmUtil';
import Toast from '../components/Toast';
import { DarkMode } from 'miot/Device';
import { NavigationBar } from 'mhui-rn';
import { Device } from 'miot/device';
import { AbstractDialog, MessageDialog } from 'miot/ui/Dialog';
import Util from "../util2/Util";
import AlarmUtilV2, {
  KEY_ALARM_REMIND_LIST_PIID,
  KEY_ALARM_REMIND_SIID
} from "../util/AlarmUtilV2";
import StatusBarUtil from "../util/StatusBarUtil";
import dayjs from "dayjs";
const kWindowHeight = Math.max(Dimensions.get("window").height, Dimensions.get("window").width);
const kWindowWidth = Math.min(Dimensions.get("window").height, Dimensions.get("window").width);
export default class ClockAlarmPage extends React.Component {
  static navigationOptions = (navigation) => {
    return {// 不要导航条
      headerTransparent: true,
      header:
        null
    };
  };

  constructor(props, context) {
    super(props, context);
    this.state = {
      isLoading: false,
      listDatas: [],
      alarmValues: {},
      showDelDialog: false,
      editMode: false,
      showBarTitle: false,
      isAllSelect: false,
      showEmpty: false,
      showMoreDlg: false,
      moreIconY: -1
    };
    this.statusBarTop = StatusBarUtil._getInset("top");
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.onResume();
      }
    );
    this.isPageForeGround = true;
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'didBlur',
      () => {
        this.isPageForeGround = false;
      }
    );
    this.keys = [];
    this.LONG_TIME_KEY_PREFIX = "prop.s_chuangmi_clocks";
    this.selectCount = 0;
    this.deleteArray = [];
    this.deleteAlarmKeys = {};
    this.titleHeight = 38;
    this.mDate = new Date();
  }

  renderTitleBar() {
    let titleStr  = this.state.editMode ? LocalizedStrings['edit'] : LocalizedStrings['clock_alarm'];
    // let rightKey = (!this.state.listDatas || this.state.listDatas.length === 0) ?
    //   null : this.state.editMode ?
    //     this.state.isAllSelect ? NavigationBar.ICON.SELECTED_ALL : NavigationBar.ICON.SELECT_ALL
    //     : NavigationBar.ICON.MORE;
    let rightKey = this.state.editMode ?
        this.state.isAllSelect ? NavigationBar.ICON.SELECTED_ALL : NavigationBar.ICON.SELECT_ALL
        : NavigationBar.ICON.MORE;
    let titleBarContent = {
      title: this.state.showBarTitle ? titleStr : "",
      type: this.state.darkMode ? NavigationBar.TYPE.DARK : NavigationBar.TYPE.LIGHT,
      left: [
        {
          key: this.state.editMode ? NavigationBar.ICON.CLOSE : NavigationBar.ICON.BACK,
          onPress: () => {
            if (this.state.editMode) {
              this.state.listDatas.forEach((item) => item.select = false);
              this.selectCount = 0;
              this.setState({ editMode: false, isAllSelect: false });
            } else {
              this.props.navigation.goBack();
            }
          }
        }
      ],
      right: [
        {
          // key: (!this.state.listDatas || this.state.listDatas.length === 0) ? null : this.state.editMode ? null : NavigationBar.ICON.EDIT,
          key: rightKey,
          onPress: () => {
            // if (this.selectCount >= this.state.listDatas.length) {
            //   this.state.listDatas.forEach((item) => item.select = false);
            //   this.selectCount = 0;
            // } else {
            //   this.state.listDatas.forEach((item) => item.select = true);
            //   this.selectCount = this.state.listDatas.length;
            // }

            // this.setState({ editMode: !this.state.editMode });

            if (this.state.editMode) {
              if (this.selectCount >= this.state.listDatas.length) {
                // this.state.listDatas.forEach((item) => item.select = false);
                this.selectCount = 0;
                this.setState({
                  listDatas: this.state.listDatas.map((item, _index) => {
                    return  {...item, select: false};
                  }),
                  isAllSelect: false
                });
              } else {
                // 弹出弹框
                this.selectCount = this.state.listDatas.length;
                this.setState({
                  listDatas: this.state.listDatas.map((item, _index) => {
                    return  {...item, select: true};
                  }),
                  isAllSelect: true
                });
              }
            } else {
              // this.selectCount = 0;
              // this.setState({ editMode: true });
              this.setState({ showMoreDlg: true });

            }
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    };
    // this.props.navigation.setParams(titleBarContent);
    return (
      <NavigationBar { ...titleBarContent } />
    );
  }

  componentDidMount() {
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
    this.loadData();
  }

  onItemLongClick(item) {
    item.select = true;
    this.selectCount = 1;
    this.setState({ editMode: true, isAllSelect: this.state.listDatas.length === this.selectCount });
  }

  renderItemView(item, index) {
    // {\"start\":\"07:00\",\"end\":\"09:00\",\"repeat\":127,\"enable\":false,\"clock_idx\":0,\"name\":\"早上无人出现\"}
    console.log("[[[[[[[[[[[[[[[[[[[[[",item);
    return (
      <View style={ { display: "flex", flexDirection: "column" } } key={item}>
        <View style={ { display: "flex", flexDirection: "row", alignItems: "center", paddingRight: 20 } }>
          <TouchableOpacity
            style={ { display: "flex", flexDirection: "column", flexGrow: 2, padding: 20, width: "80%" } }
            onLongPress={ () => this.onItemLongClick(item) }
            onPress={ () => {
              if (this.state.editMode) {
                item.select = !item.select;
                item.select ? this.selectCount++ : this.selectCount--;
                this.setState({isAllSelect: this.state.listDatas.length === this.selectCount});
              } else {
                this.props.navigation.navigate('ClockAlarmSet',
                  {
                    item: JSON.parse(JSON.stringify(item)),
                    callback: (data) => {
                      Object.assign(item, data);
                      this.onItemCheckChanged();
                      this.forceUpdate();
                    }
                  });
              }
            } }>
            <Text style={{fontSize: 30,color: '#000000', fontWeight: 'bold'}}>{ item.time }</Text>
            <Text style={{fontSize: 14,color: 'rgba(0,0,0,0.6)'}}> {this.getRepeatStr(item)}  |  {this.getNotesStr(item)}</Text>

          </TouchableOpacity>
          { this.state.editMode ? <Checkbox
            style={ { width: 20, height: 20, borderRadius: 20 } }
            checked={ item.select }
            onValueChange={ (checked) => {
              item.select = checked;
              item.select ? this.selectCount++ : this.selectCount--;
              this.setState({isAllSelect: this.state.listDatas.length === this.selectCount});
            } }
          /> : <View style={{ display: "flex", flexDirection: "row", alignItems: 'center' }}>
            <View style={{ width: 0.5, height: 26, backgroundColor: 'rgba(0, 0, 0, 0.2)', marginRight: 16.5 }}></View>
            <Switch
              value={item.enable}
              disabled={false}
              onValueChange={(checked) => {
                item.enable = checked;
                this.onItemCheckChanged();
              }}
            />
          </View>} 
        </View>
      </View>
    );
  }

  getRepeatStr(item) {
    if (item.type == 0) {
      this.mDate.setTime(item.repeat * 1000);
      return dayjs(this.mDate).format(LocalizedStrings["yyyymmdd"]);
    }
    if (item.type == 1) {
      return LocalizedStrings['plug_timer_everyday'];
    }
    if (item.type == 2) {
      return LocalizedStrings['clock_alarm_repeat_work'];
    }
    if (item.type == 3) {
      return LocalizedStrings['clock_alarm_repeat_rest'];
    }
    return Util.getRepeatString(item.repeat);
  }

  getNotesStr(item) {
    if (item.noteType == 0) {
      return LocalizedStrings['backup_none'];
    }
    if (item.noteType == 1) {
      return LocalizedStrings['take_medicine'];
    }
    if (item.noteType == 2) {
      return LocalizedStrings['eat_food'];
    }
    if (item.noteType == 3) {
      return LocalizedStrings['do_homework'];
    }
    if (item.noteType == 4) {
      return LocalizedStrings['wakeup'];
    }
    return item.notes;
  }

  getDurationText(start, end) {
    let text = `${start} - ${end}`;
    let startValue = parseInt(start.split(":")[0]) * 60 + parseInt(start.split(":")[1]);
    let endValue = parseInt(end.split(":")[0]) * 60 + parseInt(end.split(":")[1]);
    if (startValue > endValue) {
      text = `${start} - ${LocalizedStrings.setting_monitor_next_day} ${end}`;
    }
    return text;
  }
  // 渲染底部提示
  renderFooter() {
    return (
      <View style={ { backgroundColor: "#FFFFFF", paddingLeft: 20, paddingTop: 20 } }>
        <Text>{ LocalizedStrings['ss_long_time_nobody_tips'] }</Text>
      </View>
    );
  }

  scrollViewScroll = (event) => {
    const y = event.nativeEvent.contentOffset.y;
    // let flag = y > 28;
    let flag = y > this.titleHeight;
    if (this.showTitle == flag) {
      return;
    }
    if (flag) {
      this.showTitle = true;
      this.setState({ showBarTitle: true });
    } else {
      this.showTitle = false;
      this.setState({ showBarTitle: false });
    }
  };
  render() {
    let isDark = DarkMode.getColorScheme() == "dark";
    let titleStr  = this.state.editMode ? LocalizedStrings['edit'] : LocalizedStrings['clock_alarm'];
    return (<View style={ {
      display: "flex",
      height: "100%",
      width: "100%",
      flex: 1,
      flexDirection: "column",
      alignItems: "center"
    } }>
      { this.renderTitleBar() }
      <ScrollView scrollEventThrottle={16} onScroll={this.scrollViewScroll} style={{width: "100%",backgroundColor: Util.isDark() ? "#xm000000" : '#FFFFFF'}} contentContainerStyle={{ flexGrow: 1 }}>


      <View style={ {  flex: 1, backgroundColor: Util.isDark() ? "#xm000000" : '#FFFFFF' } }>

        <View style={{ flexDirection: "row", flexWrap: "wrap" }} key={0} onLayout={({ nativeEvent: { layout: { height } } }) => {
          this.titleHeight = height - 28;
        }}>
          <Text style={{ fontSize: 30, color: "rgba(0, 0, 0, 0.80)", fontWeight:"300",position: "relative", marginLeft: 25, marginTop: 3, marginBottom:23 }}>
            {titleStr}
          </Text>
        </View>


        <FlatList
          style={ { width: "100%", flex: 1,  marginBottom: 100 } }
          data={ this.state.listDatas }
          keyExtractor={(item, index) => `key_${index}`}
          renderItem={ (data) => this.renderItemView(data.item, data.index) }
          ListEmptyComponent={ () => this._renderEmptyList() }
          contentContainerStyle={ [{ flexGrow: 1, paddingHorizontal: 12 }] }
          refreshing={ this.state.isLoading }>

        </FlatList>
      </View>
      </ScrollView>
      { this.state.editMode ?
        <View style={ {
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          width: "100%",
          position: 'absolute',
          bottom: 15
        } }>
          <TouchableOpacity
            style={ {
              flexDirection: "column",
              alignItems: "center"
            } }
            onPress={ () => {
              this.deleteArray = [];
              this.deleteAlarmKeys = {};
              let needDelete = false;
              this.state.listDatas.forEach((item) => {
                if (!item.select) {
                  this.deleteArray.push({
                    enable: item.enable,
                    noteType: item.noteType,
                    notes: item.notes,
                    repeat: item.repeat,
                    time: item.time,
                    type: item.type
                  });
                } else {
                  needDelete = true;
                }
              });
              if (needDelete == false) {
                console.log(`onDeleteItem no item to delete`);
                Toast.success('idm_empty_tv_device_tips');
                return;
              }
              this.setState({ showDelDialog: true });
            } }>
            <Image
              style={ { width: 25, height: 25 } }
              source={ Util.isDark() ? require("../../Resources/Images/icon_delete_white.png") : require("../../Resources/Images/icon_delete_normal1.png") }
              tintColor={ isDark ? IMG_DARKMODE_TINT : null }
            />
            <Text style={ { color: "#000000", fontSize: 11 } }>
              { LocalizedStrings["delete_files"] }
            </Text>
          </TouchableOpacity>

        </View>
        :
        <TouchableOpacity
          style={ { position: 'absolute', right: 0, bottom: 0 } }
          disabled={this.state.listDatas.length >= 10}
          onPress={ () => {
            this.onAddItem();
          } }>
          <Image style={ { width: 120, height: 120 } }
                 source={ this.state.listDatas.length >= 10 ? require("../../Resources/Images/icon_add_timer_new_dis.webp") : require("../../Resources/Images/icon_add_timer_new.webp") }/>
        </TouchableOpacity>
      }
      { this.renderDeleteDialog() }
      { this._renderMoreItemDialog() }
    </View>);
  }
  _renderMoreItemDialog() {

    let left = kWindowWidth - 220;
    let modalStyle = {
      width: 200,
      top: this.statusBarTop + 46,
      // left: left,
      right: 20,
      height: 54 * 2 + 20,
      alignSelf: 'center',
      borderRadius: 16,
      paddingVertical: 10
    };
    return (
      <AbstractDialog
        style={modalStyle}
        showTitle={false}
        visible={this.state.showMoreDlg}
        showButton={false}
        onDismiss={() => {
          this.setState({ showMoreDlg: false });
        }}
        canDismiss={true}
        useNewTheme={true}
      >
        <View style={{flexGrow: 1}}>
          <TouchableOpacity
            style={{
              display: "flex",
              flexDirection: 'column',
              justifyContent: 'center',
              minHeight: 54
            }}
            onPress={() => {
              this.setState({ showMoreDlg: false });
              this.props.navigation.navigate('ClockAlarmSetting');
            }}>
            <Text style={{
              fontSize: 16,
              color: "#000000",
              fontWeight: "400",
              textAlignVertical: "center",
              paddingHorizontal: 28
            }}>{LocalizedStrings['more_setting']}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              display: "flex",
              flexDirection: 'column',
              justifyContent: 'center',
              minHeight: 54
            }}
            disabled={!this.state.listDatas || this.state.listDatas.length === 0}
            onPress={() => {
              // 删除，进入编辑模式
              if (!this.state.listDatas || this.state.listDatas.length === 0) {
                return;
              }
              this.selectCount = 0;
              this.setState({ editMode: true, showMoreDlg: false });
            }}>
            <Text style={[{
              fontSize: 16,
              color: "#000000",
              fontWeight: "400",
              textAlignVertical: "center",
              paddingHorizontal: 28
            }, (!this.state.listDatas || this.state.listDatas.length === 0) ? { color: "rgba(0,0,0,0.3)" } : null]}>{LocalizedStrings['f_delete']}</Text>
          </TouchableOpacity>
        </View>

      </AbstractDialog>
    );
  }
  _renderEmptyList() {
    if (this.state.isLoading) {
      return null;
    }
    return (
      <View style={ { width: "100%", display: 'flex', flex: 1, justifyContent: "center", alignItems: "center" } }>
        <Image style={ { width: 92, height: 60 } }
               source={ require("../../Resources/Images/icon_timer_empty.webp") }></Image>
        <Text style={ {
          color: "#999999",
          fontSize: 15,
          textAlign: 'center',
          marginTop: 5,
          paddingBottom: 10
        } }>{ LocalizedStrings['clock_set_timer_empty'] }</Text>
      </View>
    );
  }

  renderDeleteDialog() {
    console.log("========================",this.deleteArray);
    return (
      <MessageDialog
        visible={ this.state.showDelDialog }
        // title={ LocalizedStrings['confirm_delete_clock'] }
        message={ LocalizedStrings['confirm_delete_clock'] }

        messageStyle={{ textAlign: 'center' }}
        canDismiss={ true }
        onDismiss={() => this.setState({ showDelDialog: false })}
        buttons={ [
          {
            text: LocalizedStrings["btn_cancel"],
            // style: { color: 'lightpink' },
            callback: (_) => {
              this.setState({ showDelDialog: false });
            }
          },
          {
            text: LocalizedStrings["delete_files"],
            // style: { color: 'lightblue' },
            callback: (_) => {
              this.onDeleteItem();
              this.setState({ showDelDialog: false });
            }
          }
        ] }
      />
    );
  }

  onDeleteItem() {
    // let arrayStr = JSON.stringify({ values: array });
    let arrayStr = JSON.stringify(this.deleteArray);
    console.log(`onDeleteItem ${ arrayStr }`);
    this.putClockList(arrayStr).then(() => {
      this.setState({ editMode: false });
      this.selectCount = 0;
      this.loadData();
    }).catch((err) => {
      console.log(`onDeleteItem error=${ JSON.stringify(err) }`);
    });
  }
  //{"idx":2,"clock":[{"start":"08:00","end":"09:00","repeat":127,"enable":true,"clock_idx":0}, {"start":"11:00","end":"13:00","repeat":127,"enable":true,"clock_idx":1}]}
  onAddItem() {
    this.props.navigation.navigate('ClockAlarmSet', {
      callback: (data) => {
        console.log(`alarmDataResultListener=${ JSON.stringify(data) }`);
        let array = [];
        this.state.listDatas.forEach((item, index) => {
          array.push({
            enable: item.enable,
            time: item.time,
            type: item.type,
            repeat: item.repeat,
            noteType: item.noteType,
            notes: item.notes,
          });
        });

        array.push(data);

        let arrayStr = JSON.stringify(array);
        console.log(`onAddItem ${ arrayStr }`);

        this.putClockList(arrayStr).then((res) => {
          if (res[0].code == 0) {
            Toast.success("c_set_success");
            this.loadData();
          } else {
            Toast.fail("c_set_fail");
          }
        }).catch((err) => {
          Toast.fail("c_set_fail");
        });
      }
    });
  }

  onResume() {

  }

  onItemCheckChanged() {
    let array = [];
    this.state.listDatas.forEach((item) => {
      array.push({
        enable: item.enable,
        time: item.time,
        type: item.type,
        repeat: item.repeat,
        noteType: item.noteType,
        notes: item.notes,
      });
    });
    let data = JSON.stringify(array);
    console.log(`onItemCheckChanged ${ data }`);
    this.putClockList(data).then((res) => {
    }).catch((err) => {
      Toast.fail("c_set_fail");
    });
  }

  setAlarmKey(alarmKeys) {
    let alarmKeysStr = JSON.stringify(alarmKeys);
    console.log(alarmKeysStr);
    if (alarmKeysStr == "{}") {
      console.log("alarmKeys is {}");
      return;
    }
    let data = { did: Device.deviceID, props: alarmKeys };
    AlarmUtil.setProps(data).then((res) => {
      console.log(JSON.stringify(res));
      // this.loadData();
    }).catch((err) => {
      console.log(JSON.stringify(err));
    });
  }

  putClockList(data) {
    return new Promise((resolve, reject) => {
      let params = [{ sname: KEY_ALARM_REMIND_SIID, pname: KEY_ALARM_REMIND_LIST_PIID, value: data }];
      AlarmUtilV2.setSpecPValue(params).then((res)=>{
        resolve(res);
      }).catch((err) => {
        console.log(`putClockList err=${ JSON.stringify(err) }`);
        this.loadData();
        reject(err);
      });
    });
  }


  loadData() {
    this.setState({
      isLoading: true
    });

    let params = [{ sname: KEY_ALARM_REMIND_SIID, pname: KEY_ALARM_REMIND_LIST_PIID }];
    AlarmUtilV2.getSpecPValue(params).then((result) => {
      console.log("[[[[[[[[[",typeof (result),result);
      this.setState({ isLoading: false });
      if (result[0].code === 0) {
        let value = result[0].value;
        if (typeof (value) != "undefined" && value != "") {
          let values = JSON.parse(value);
          this.setState({
            listDatas: values
          });
        }
      }
    }).catch((err) => {
      this.setState({ isLoading: false });
      console.log("[[[[[[[[[[[[",err);
      Toast.fail("c_get_fail")
    });

  }

  componentWillUnmount() {
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  onBackHandler = () => {
    if (this.state.editMode) {
      this.state.listDatas.forEach((item) => item.select = false);
      this.setState({ editMode: false });
      this.selectCount = 0;
      return true;
    } else {
      return false;
    }
  };
}

