<a name="module_miot/device/bluetooth"></a>

## miot/device/bluetooth
蓝牙服务/特征操作类
蓝牙的开发，详见[设备使用标准BLE 协议](https://iot.mi.com/new/doc/extension-development/basic-functions/device-connection/BLE#%E8%AE%BE%E5%A4%87%E4%BD%BF%E7%94%A8%E6%A0%87%E5%87%86BLE%20%E5%8D%8F%E8%AE%AE)
本文件提供了蓝牙服务（Service）和蓝牙特征值（Characteristic）的读写监听方面的操作
蓝牙的开发简化流程为：发现设备 - 连接设备 - 发现服务 - 发现特征值 - 特征值读写 - 断开连接,本文件主要涉及到发现服务 - 发现特征值 - 特征值读写这么几步

**Export**: public  
**Doc_name**: 蓝牙服务-特征模块  
**Doc_index**: 5  
**Doc_directory**: bluetooth  
**Example**  
```js
import {Bluetooth} from 'miot/device/bluetooth'

 ...
 ble = Bluetooth.createBluetoothLE(result.uuid || result.mac);//android 用 mac 创建设备，ios 用 uuid 创建设备

    const charac = ble.getService('...').getCharacteristic('...')
    charac.read().then(characteristic=>{characteristic.value ... }).catch(err=>{});
    charac.write().then(characteristic=>{}).catch(err=>{})
 ...
```

* [miot/device/bluetooth](#module_miot/device/bluetooth)
    * [.IBluetoothCharacteristic](#module_miot/device/bluetooth.IBluetoothCharacteristic)
        * [.isDiscovered](#module_miot/device/bluetooth.IBluetoothCharacteristic+isDiscovered) : <code>boolean</code>
        * [.isValueLoaded](#module_miot/device/bluetooth.IBluetoothCharacteristic+isValueLoaded) : <code>boolean</code>
        * [.UUID](#module_miot/device/bluetooth.IBluetoothCharacteristic+UUID) : <code>string</code>
        * [.value](#module_miot/device/bluetooth.IBluetoothCharacteristic+value) ⇒
        * [.read()](#module_miot/device/bluetooth.IBluetoothCharacteristic+read) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;IBluetoothCharacteristic&gt;</code>
        * [.write(value)](#module_miot/device/bluetooth.IBluetoothCharacteristic+write) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;IBluetoothCharacteristic&gt;</code>
        * [.writeWithoutResponse(value)](#module_miot/device/bluetooth.IBluetoothCharacteristic+writeWithoutResponse) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;IBluetoothCharacteristic&gt;</code>
        * [.setNotify(flag)](#module_miot/device/bluetooth.IBluetoothCharacteristic+setNotify) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;IBluetoothCharacteristic&gt;</code>
    * [.IBluetoothService](#module_miot/device/bluetooth.IBluetoothService)
        * [.UUID](#module_miot/device/bluetooth.IBluetoothService+UUID) : <code>string</code>
        * [.isDiscovered](#module_miot/device/bluetooth.IBluetoothService+isDiscovered) : <code>boolean</code>
        * [.getCharacteristic](#module_miot/device/bluetooth.IBluetoothService+getCharacteristic) ⇒ <code>IBluetoothCharacteristic</code>
        * [.startDiscoverCharacteristics(...characteristicUUIDs)](#module_miot/device/bluetooth.IBluetoothService+startDiscoverCharacteristics) ⇒ <code>boolean</code>


* * *

<a name="module_miot/device/bluetooth.IBluetoothCharacteristic"></a>

### miot/device/bluetooth.IBluetoothCharacteristic
**Kind**: static interface of [<code>miot/device/bluetooth</code>](#module_miot/device/bluetooth)  

* [.IBluetoothCharacteristic](#module_miot/device/bluetooth.IBluetoothCharacteristic)
    * [.isDiscovered](#module_miot/device/bluetooth.IBluetoothCharacteristic+isDiscovered) : <code>boolean</code>
    * [.isValueLoaded](#module_miot/device/bluetooth.IBluetoothCharacteristic+isValueLoaded) : <code>boolean</code>
    * [.UUID](#module_miot/device/bluetooth.IBluetoothCharacteristic+UUID) : <code>string</code>
    * [.value](#module_miot/device/bluetooth.IBluetoothCharacteristic+value) ⇒
    * [.read()](#module_miot/device/bluetooth.IBluetoothCharacteristic+read) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;IBluetoothCharacteristic&gt;</code>
    * [.write(value)](#module_miot/device/bluetooth.IBluetoothCharacteristic+write) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;IBluetoothCharacteristic&gt;</code>
    * [.writeWithoutResponse(value)](#module_miot/device/bluetooth.IBluetoothCharacteristic+writeWithoutResponse) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;IBluetoothCharacteristic&gt;</code>
    * [.setNotify(flag)](#module_miot/device/bluetooth.IBluetoothCharacteristic+setNotify) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;IBluetoothCharacteristic&gt;</code>


* * *

<a name="module_miot/device/bluetooth.IBluetoothCharacteristic+isDiscovered"></a>

#### iBluetoothCharacteristic.isDiscovered : <code>boolean</code>
是否已经被发现，只有已经被发现的特征值才可以真正操作读写，如果蓝牙断开连接了，isDiscovered为false

**Kind**: instance property of [<code>IBluetoothCharacteristic</code>](#module_miot/device/bluetooth.IBluetoothCharacteristic)  
**Read only**: true  

* * *

<a name="module_miot/device/bluetooth.IBluetoothCharacteristic+isValueLoaded"></a>

#### iBluetoothCharacteristic.isValueLoaded : <code>boolean</code>
数值是否已经加载, 为 true 时,本类才能读到正确的 value。read/write/writeWithoutResponse等方法的成功调用，bluetoothCharacteristicValueChanged事件执行，都会将此属性置为true

**Kind**: instance property of [<code>IBluetoothCharacteristic</code>](#module_miot/device/bluetooth.IBluetoothCharacteristic)  
**Read only**: true  

* * *

<a name="module_miot/device/bluetooth.IBluetoothCharacteristic+UUID"></a>

#### iBluetoothCharacteristic.UUID : <code>string</code>
特征值的 UUID

**Kind**: instance property of [<code>IBluetoothCharacteristic</code>](#module_miot/device/bluetooth.IBluetoothCharacteristic)  
**Read only**: true  

* * *

<a name="module_miot/device/bluetooth.IBluetoothCharacteristic+value"></a>

#### iBluetoothCharacteristic.value ⇒
数值, 配合 isValueLoaded 使用

**Kind**: instance property of [<code>IBluetoothCharacteristic</code>](#module_miot/device/bluetooth.IBluetoothCharacteristic)  
**Returns**: hexstring  
**Read only**: true  
**Example**  
```js
...
  if(charateristic.isValueLoaded){
      const val = characteristic.value;
      ...
  }
  ...
```

* * *

<a name="module_miot/device/bluetooth.IBluetoothCharacteristic+read"></a>

#### iBluetoothCharacteristic.read() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;IBluetoothCharacteristic&gt;</code>
读取蓝牙数据

**Kind**: instance method of [<code>IBluetoothCharacteristic</code>](#module_miot/device/bluetooth.IBluetoothCharacteristic)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;IBluetoothCharacteristic&gt;</code> - resolve： 返回当前对象，value为读取到的value
     reject：100:设备正在连接中  101:设备不存在  102:服务或者特征值未发现  

* * *

<a name="module_miot/device/bluetooth.IBluetoothCharacteristic+write"></a>

#### iBluetoothCharacteristic.write(value) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;IBluetoothCharacteristic&gt;</code>
写数据
对应 writeWithResponse

**Kind**: instance method of [<code>IBluetoothCharacteristic</code>](#module_miot/device/bluetooth.IBluetoothCharacteristic)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;IBluetoothCharacteristic&gt;</code> - resolve： 返回当前对象，value为成功写入的value
     reject：100:设备正在连接中  102:服务或者特征值未发现  

| Param | Type | Description |
| --- | --- | --- |
| value | <code>hexstring</code> | hexstring 16进制字符串 |


* * *

<a name="module_miot/device/bluetooth.IBluetoothCharacteristic+writeWithoutResponse"></a>

#### iBluetoothCharacteristic.writeWithoutResponse(value) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;IBluetoothCharacteristic&gt;</code>
直接写数据
对应 writeWithoutResponse

**Kind**: instance method of [<code>IBluetoothCharacteristic</code>](#module_miot/device/bluetooth.IBluetoothCharacteristic)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;IBluetoothCharacteristic&gt;</code> - resolve： 返回当前对象，value为成功写入的value
     reject：{code: xxx, message: xxx} 100:设备正在连接中  102:服务或者特征值未发现  

| Param | Type | Description |
| --- | --- | --- |
| value | <code>hexstring</code> | 16进制字符串 |


* * *

<a name="module_miot/device/bluetooth.IBluetoothCharacteristic+setNotify"></a>

#### iBluetoothCharacteristic.setNotify(flag) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;IBluetoothCharacteristic&gt;</code>
设置数值变化监听开关，如果成功监听了，可以接收到属性变化事件bluetoothCharacteristicValueChanged

**Kind**: instance method of [<code>IBluetoothCharacteristic</code>](#module_miot/device/bluetooth.IBluetoothCharacteristic)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;IBluetoothCharacteristic&gt;</code> - resolve：当前对象
     reject：{code: xxx, message: xxx}  100:设备正在连接中  102:服务或者特征值未发现  

| Param | Type | Description |
| --- | --- | --- |
| flag | <code>boolean</code> | true 打开监听, false 则关闭监听 |

**Example**  
```js
...
    import {BluetoothEvent} from 'miot/device/bluetooth'

    character.setNotify(true).then(()=>{console.log("success")});

    BluetoothEvent.bluetoothCharacteristicValueChanged.addListener((bluetooth, service, character, value) => {
            if (character.UUID.indexOf("ffd5")>0){
                console.log("bluetoothCharacteristicValueChanged", character.UUID, value);
            }
        })
...
```

* * *

<a name="module_miot/device/bluetooth.IBluetoothService"></a>

### miot/device/bluetooth.IBluetoothService
**Kind**: static interface of [<code>miot/device/bluetooth</code>](#module_miot/device/bluetooth)  

* [.IBluetoothService](#module_miot/device/bluetooth.IBluetoothService)
    * [.UUID](#module_miot/device/bluetooth.IBluetoothService+UUID) : <code>string</code>
    * [.isDiscovered](#module_miot/device/bluetooth.IBluetoothService+isDiscovered) : <code>boolean</code>
    * [.getCharacteristic](#module_miot/device/bluetooth.IBluetoothService+getCharacteristic) ⇒ <code>IBluetoothCharacteristic</code>
    * [.startDiscoverCharacteristics(...characteristicUUIDs)](#module_miot/device/bluetooth.IBluetoothService+startDiscoverCharacteristics) ⇒ <code>boolean</code>


* * *

<a name="module_miot/device/bluetooth.IBluetoothService+UUID"></a>

#### iBluetoothService.UUID : <code>string</code>
蓝牙服务 UUID

**Kind**: instance property of [<code>IBluetoothService</code>](#module_miot/device/bluetooth.IBluetoothService)  
**Read only**: true  

* * *

<a name="module_miot/device/bluetooth.IBluetoothService+isDiscovered"></a>

#### iBluetoothService.isDiscovered : <code>boolean</code>
蓝牙服务是否已被发现,被发现的蓝牙服务才可以继续扫描特征值，蓝牙断开时，isDiscovered为false

**Kind**: instance property of [<code>IBluetoothService</code>](#module_miot/device/bluetooth.IBluetoothService)  
**Read only**: true  

* * *

<a name="module_miot/device/bluetooth.IBluetoothService+getCharacteristic"></a>

#### iBluetoothService.getCharacteristic ⇒ <code>IBluetoothCharacteristic</code>
获取蓝牙特征值，如果没有，会创建一个，然后保存到缓存中，注意新创建的并不能直接使用，需要被发现后才可真正使用

**Kind**: instance property of [<code>IBluetoothService</code>](#module_miot/device/bluetooth.IBluetoothService)  

| Param | Type |
| --- | --- |
| characteristicUUID | <code>string</code> | 


* * *

<a name="module_miot/device/bluetooth.IBluetoothService+startDiscoverCharacteristics"></a>

#### iBluetoothService.startDiscoverCharacteristics(...characteristicUUIDs) ⇒ <code>boolean</code>
发现蓝牙特征，此方法返回true or false，表示是否开始发现蓝牙特征值。发现的蓝牙特征值需要通过订阅BluetoothEvent的bluetoothCharacteristicDiscovered来使用

**Kind**: instance method of [<code>IBluetoothService</code>](#module_miot/device/bluetooth.IBluetoothService)  

| Param | Type | Description |
| --- | --- | --- |
| ...characteristicUUIDs | <code>string</code> | 特征的 UUID |


* * *

