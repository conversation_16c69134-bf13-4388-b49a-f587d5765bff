import React from 'react';
import { Text, View, Image, TouchableOpacity, Animated, Easing } from 'react-native';
import PropTypes from 'prop-types';
import { ConfigContext } from "mhui-rn/dist/components/configProvider";
import { styles, Radius, Constants, Opacity } from 'micariot-ui-sdk';
import { DarkMode } from "miot";
import { carStyles } from "../car/common/Styles";


const DEFAULT_HEIGHT = 88;
const DEFAULT_PADDING = 32;
const DEFAULT_IMAGE_SIZE = 40;
const DEFAULT_TEXT_MARGIN_RIGHT = 16;

/**
 * @export public
 * @module ClickableCard
 * @description ClickableCard for CarIoT
 * @property {object} style - style
 * @property {string} title - 标题文本
 * @property {function} onPress - 点击回调方法
 * @property {bool} disabled - 是否禁用，默认值 false
 */
const TYPE = {
  LIGHT: 'white',
  DARK: 'black'
};

class ClickableCardButton extends React.PureComponent {
  static contextType = ConfigContext;
  static propTypes = {
    style: PropTypes.object,
    titleStyle: PropTypes.object,
    source: PropTypes.object,
    title: PropTypes.string,
    onPress: PropTypes.func,
    disabled: PropTypes.bool,
    showImage: PropTypes.bool,
    showLoadingImage: PropTypes.bool,
    paddingLeft: PropTypes.number,
    paddingRight: PropTypes.number
  };

  static defaultProps = {
    showImage: true
  };

  constructor(props) {
    super(props);
    this.rotationAnim = new Animated.Value(0);

  }

  componentDidMount() {
    if (this.props.showLoadingImage) {
      this._startAnim();
    }
  }

  componentWillUnmount() {
    this._stopAnim();
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.showLoadingImage != undefined && nextProps.showLoadingImage != undefined && nextProps.showLoadingImage != this.props.showLoadingImage) {
      // 此时触发更新动画
      if (nextProps.showLoadingImage) {
        this._startAnim();
      } else {
        this._stopAnim();
      }
    }
  }

  _startAnim() {
    // this.setState({ isScaning: true });
    this.isScaning = true;
    this.rotationAnim.setValue(0);
    this.animation = Animated.timing(this.rotationAnim, {
      toValue: 1,
      duration: 1000,
      easing: Easing.linear,
      useNativeDriver: true,
      perspective: 1000
    });
    this.animation.start(() => {
      if (!this.isScaning) {
        return;
      }
      this._startAnim();
    });

  }

  _stopAnim() {
    this.rotationAnim.stopAnimation();
    if (this.animation != null) {
      this.animation.stop();
    }
    this.isScaning = false;
  }

  render() {
    const {
      colorScheme
    } = this.context;
    const opacity = this.props.disabled ? Opacity.Disabled : Opacity.Normal;
    let source = this.props.source ? this.props.source : colorScheme == 'dark' ? require('../../Resources/Images/car/down_arrow.png') : require('../../Resources/Images/car/down_arrow_light.png');
    let paddingLeft = this.props.paddingLeft ? this.props.paddingLeft : DEFAULT_PADDING;
    let paddingRight = this.props.paddingRight ? this.props.paddingRight : DEFAULT_PADDING;
    return (
      <View
        style={ { opacity } }
        pointerEvents={ this.props.disabled ? "none" : "auto" }
      >
        <TouchableOpacity
          style={ [carStyles.itemBgStyle, {
            flexDirection: 'row',
            alignItems: 'center',
            height: DEFAULT_HEIGHT,
            paddingLeft: paddingLeft,
            paddingRight: paddingRight,
            marginTop: Constants.DEFAULT_TEXT_MARGIN_BOTTOM,
            marginBottom: Constants.DEFAULT_MARGIN_TOP_BOTTOM,
            borderRadius: Radius.WidgetLevel
          }, this.props.style] }
          activeOpacity={0.7}
          onPress={ () => {
            if (this.props.onPress) {
              this.props.onPress();
            }
          } }>
          {/*<Text numberOfLines={1} style={[styles.titleTextStyle, { flex: 1, marginRight: DEFAULT_PADDING }]}>{this.props.title}</Text>*/ }
          <View
            style={ {
              alignItems: 'center',
              flexDirection: "row"
            } }
          >
            {
              this.props.showLoadingImage ? <Animated.Image
                ref={ (ref) => {
                  this.loadingImage = ref;
                } }
                style={ {
                  width: 40,
                  height: 40,
                  marginRight: 12,
                  transform: [{
                    rotate: this.rotationAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0deg', '360deg']
                    })
                  }]
                  // tintColor: this.props.type == TYPE.DARK ? "black" : null
                } }
                source={ DarkMode.getColorScheme() === 'dark' ? require("../../Resources/Images/car/ic_sd_format_loading.png") : require("../../Resources/Images/car/ic_sd_format_loading_light.png") }
              >
              </Animated.Image> : null
            }


            {
              this.props.title ? <Text numberOfLines={ 1 }
                                       style={ [styles.titleTextStyle, { marginRight: DEFAULT_TEXT_MARGIN_RIGHT }, this.props.titleStyle] }>{ this.props.title }</Text>
                : null
            }

            {
              this.props.showImage ?
                <Image style={ { width: DEFAULT_IMAGE_SIZE, height: DEFAULT_IMAGE_SIZE } } source={ source }/>
                : null
            }

          </View>


        </TouchableOpacity>
      </View>
    );
  }
}

export default ClickableCardButton;
export { ClickableCardButton };