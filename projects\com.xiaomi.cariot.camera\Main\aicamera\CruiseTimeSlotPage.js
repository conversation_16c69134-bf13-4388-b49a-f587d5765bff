import React, { Component } from "react";
import { View, Text, StyleSheet, SectionList, TouchableOpacity, Dimensions, Image } from "react-native";
import { HLSettingStyles } from "./HLSettingStyles";
import { ListItemWithSwitch, ListItem } from "miot/ui/ListItem";
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import MHDatePicker from "miot/ui/MHDatePicker";
import { ChoiceDialog, ImageButton } from "mhui-rn";
const { width: screenWidth, height: screenHeight } = Dimensions.get("screen");
import AbstractDialog from "miot/ui/Dialog/AbstractDialog";
import NavigationBar from "miot/ui/NavigationBar";
import Toast from "../Toast";
import { log } from "miot/utils/fns";
import { showLoading } from "miot/utils/dialog-manager";
import Radio from 'miot/ui/Radio';

let rawData = {};
export default class CruiseTimeSlotPage extends Component {
  constructor(props) {
    super(props);
    // {"freq":"00:10","start_time":"00:00","end_time":"23:59","repeat":127,"mode":1,"position":"[1,2,3,4]"}
    rawData = this.props.navigation.getParam("paramModel");
    this.oldRepeat = rawData.repeat;
    console.log("rawData-------------------", JSON.stringify(rawData));
    let endTime = rawData.start_time > rawData.end_time
      ? `次日${ rawData.end_time }`
      : rawData.end_time;
    this.startTimeText = rawData.start_time;
    this.endTimeText = endTime;
    this.data = {
      selectedIndexArray: [-1],
      multiIndexArray: [],
      IndexArray: []
    };
    [1, 2, 3, 4, 5, 6, 7].forEach((item) => {
      if (rawData.repeat & (0b00000001 << (item % 7))) {
        if (item == 1) {
          item = `${ LocalizedStrings['monday1'] },`;
          this.data.multiIndexArray.push(item);
        }
        if (item == 2) {
          item = `${ LocalizedStrings['tuesday1'] },`;
          this.data.multiIndexArray.push(item);
        }
        if (item == 3) {
          item = `${ LocalizedStrings['wednesday1'] },`;
          this.data.multiIndexArray.push(item);
        }
        if (item == 4) {
          item = `${ LocalizedStrings['thursday1'] },`;
          this.data.multiIndexArray.push(item);
        }
        if (item == 5) {
          item = `${ LocalizedStrings['friday1'] },`;
          this.data.multiIndexArray.push(item);
        }
        if (item == 6) {
          item = `${ LocalizedStrings['saturday1'] },`;
          this.data.multiIndexArray.push(item);
        }
        if (item == 7) {
          item = LocalizedStrings['sunday1']; 
          this.data.multiIndexArray.push(item);
        }
      }
    });
    console.log(`aaaa:${ JSON.stringify(this.data.multiIndexArray) }`);

    this.state = {
      isWholeDay: rawData.start_time == "00:00" && rawData.end_time == "23:59",
      isShowStartTimeDialog: false,
      isShowEndTimeDialog: false,
      repeatState: rawData.repeat == 0b00000000 ? LocalizedStrings.plug_timer_onetime : 
        rawData.repeat == 0b01111111 ? LocalizedStrings.plug_timer_everyday : 
          rawData.repeat == 0b00111110 ? LocalizedStrings.weekdays : 
            rawData.repeat == 0b01000001 ? LocalizedStrings.weekends : this.data.multiIndexArray
    };
    this._updateRepeatItems();
  }

  _updateRepeatItems() {
    this.data.IndexArray = [];
    [1, 2, 3, 4, 5, 6, 7].forEach((item) => {
      if (rawData.repeat & (0b00000001 << (item % 7))) {
        this.data.IndexArray.push(item);
      }
    });
    this.repeatItems = [
      { itemTile: LocalizedStrings.plug_timer_everyday,
        select: rawData.repeat == 0b01111111
      },
      { itemTile: LocalizedStrings.weekdays,
        select: rawData.repeat == 0b00111110
      },
      { itemTile: LocalizedStrings.weekends,
        select: rawData.repeat == 0b01000001
      },
      { itemTile: LocalizedStrings.plug_timer_onetime,
        select: rawData.repeat == 0b00000000
      },
      { itemTile: LocalizedStrings.plug_timer_sef_define,
        select: rawData.repeat != 0b00000000 && rawData.repeat != 0b01000001 && rawData.repeat != 0b00111110 && rawData.repeat != 0b01111111
      }
    ];
  }

  componentDidMount() {
    this.lastRepeatItems = JSON.parse(JSON.stringify(this.repeatItems));
    this.lastRepeatState = this.state.repeatState;

    this.props.navigation.setParams({
      // title: LocalizedStrings['巡航时间段'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.CLOSE,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      right: [
        {
          key: NavigationBar.ICON.COMPLETE,
          onPress: () => { 
            if (this.state.isWholeDay) {
              rawData.start_time = "00:00";
              rawData.end_time = "23:59";
              this.startTimeText = rawData.start_time;
              this.endTimeText = rawData.end_time;
            }
            console.log("call back data = ", rawData);
            if (this.startTimeText == this.endTimeText) {
              Toast._showToast(LocalizedStrings["failed_start_time_end_time_nosame_reset"]);
            } else {
              this.props.navigation.state.params.callBack(rawData);
              this.props.navigation.goBack();
            }
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
  }

  render() {
    let sectionDataSource = [];
    sectionDataSource = [
      {
        key: "setion0",
        data: [
          { itemIndex: "s0_item0",
            itemName: LocalizedStrings.alarm_time_all, // 全天开关
            itemValue: this.state.isWholeDay
          },
          { itemIndex: "s0_item3",
            itemName: LocalizedStrings.sps_custom, // 自定义时间段
            itemValue: !this.state.isWholeDay
          },
          { itemIndex: "s0_item1",
            itemName: LocalizedStrings.csps_start, // 开始时间
            itemValue: this.startTimeText
          },
          { itemIndex: "s0_item2",
            itemName: LocalizedStrings.csps_end, // 结束时间
            itemValue: this.endTimeText
          }
        ]
      },
      {
        key: "setion1",
        data: [{
          itemIndex: "s1_item0",
          itemName: LocalizedStrings.plug_timer_repeat, // 重 复
          itemValue: this.state.repeatState }
        ]
      }
    ];

    return (
      <View style={styles.container}>
        <Text style={styles.effectiveTimeStyle}>
          {LocalizedStrings["cruise_period"]}
        </Text>
        <SectionList
          keyExtractor={this.keyExtractor}
          sections={sectionDataSource}
          renderItem={this.rendSetionItem}
          renderSectionHeader={this.rendSetionHeadView}
        />
        {this.renderStartTimeView()}
        {this.renderEndTimeView()}
        {this.renderRepeatView()}
        {this.renderWeakDaysView()}
      </View>
    );
  }
  // 重 复
  renderRepeatView() {
    return (
      <AbstractDialog
        style={[styles.repeatViewStyle]}
        visible={this.state.repeatViewVisible}
        showSubtitle={false}
        onDismiss={() => {
          this._repeatDismiss();
          // this.setState({ repeatViewVisible: false });
        }}
        showTitle={false}
        showButton={false}
        // canDismiss={false}
      >
        {this._renderRepeatView()}
      </AbstractDialog>
    );
  }

  _repeatDismiss() {
    this.repeatItems = JSON.parse(JSON.stringify(this.lastRepeatItems));
    rawData.repeat = this.oldRepeat;
    this._updateRepeatItems();
    this._updateArrList();
    this.setState({ repeatViewVisible: false, repeatState: this.lastRepeatState });
  }

  _renderRepeatView() {
    return (
      <View style={{ alignItems: "center" }}>
        <Text
          style={{
            fontSize: 16, color: "#000000", marginTop: 25, fontWeight: "700"
          }}
        >
          {LocalizedStrings["plug_timer_repeat"]}
        </Text>
        <View style={{ marginTop: 15 }}>
          {this.repeatItems.map((item, index) => {
            return (
              <TouchableOpacity
                key={index}
                onPress={() => {
                  this.onClickRepeatItem({ index: index });
                }}
              >
                <View
                  style={{ maxWidth: "100%",
                    width: screenWidth, height: 54,
                    backgroundColor:
                      item.select == true ? "rgba(50,186,192,0.1)" : "#ffffff", flexDirection: "row", alignItems: "center", justifyContent: "space-between"
                  }}
                  key={index}
                >
                  <Text
                    style={{
                      marginLeft: 30, fontSize: 16, color: item.select == true ? "#32BAC0" : "#000000"
                    }}
                  >
                    {item.itemTile}
                  </Text>
                  {item.select == true && (
                    <Image
                      style={{ width: 22, height: 22, marginRight: 22 }}
                      source={require("../../Resources/Images/plugin_icon_customVoice_selectItem.png")}
                    ></Image>
                  )}
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
        <View style={{ flexDirection: "row", justifyContent: "space-between", marginTop: 20, marginLeft: 0, marginBottom: 10 }}>
          <TouchableOpacity
            onPress={() => {
              this._repeatDismiss();
            }}
          >
            <View style={{ width: 147, height: 46, backgroundColor: "#F5F5F5", borderRadius: 23, justifyContent: "center", alignItems: "center" }}>
              <Text style={{ fontSize: 16, color: "#4C4C4C" }}>{LocalizedStrings.btn_cancel}</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              this.lastRepeatItems = JSON.parse(JSON.stringify(this.repeatItems));
              this.lastRepeatState = this.state.repeatState;
              this.oldRepeat = rawData.repeat;
              this.setState({ repeatViewVisible: false });
            }}
          >
            <View style={{ width: 147, height: 46, backgroundColor: "#32BAC0", borderRadius: 23, justifyContent: "center", alignItems: "center", marginLeft: 20 }}>
              <Text style={{ fontSize: 16, color: "#ffffff" }}>{LocalizedStrings.btn_confirm}</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  renderWeakDaysView() {
    let initSelectData = [];
    this.data.IndexArray.forEach((item) => {
      initSelectData.push(--item);
    });
    console.log("aaaaaaaaaaaaaaaa", initSelectData);
    return (
      <ChoiceDialog
        type={ChoiceDialog.TYPE.MULTIPLE}
        visible={this.state.weakDayViewVisible}
        title={LocalizedStrings['plug_timer_custom_repeat']}
        options={[
          { title: LocalizedStrings['monday1'] },
          { title: LocalizedStrings['tuesday1'] },
          { title: LocalizedStrings['wednesday1'] },
          { title: LocalizedStrings['thursday1'] },
          { title: LocalizedStrings['friday1'] },
          { title: LocalizedStrings['saturday1'] },
          { title: LocalizedStrings['sunday1'] }
        ]}
        selectedIndexArray={initSelectData}
        color="#32BAC0"
        buttons={[
          {
            // style: { color: 'lightblue' },
            callback: () => {
              this.setState({ weakDayViewVisible: false });
            }
          },
          {
            // style: { color: 'lightblue' },
            callback: (result) => {
              console.log(`selected:`, result);
              if (result && result.length <= 0) {
                Toast.fail("smarthome_span_error");
              }
              rawData.repeat = 0b00000000;
              this.data.IndexArray = [];
              result.forEach((item) => {
                this.data.IndexArray.push(++item);
                rawData.repeat = rawData.repeat | (0b00000001 << (item % 7));
              });
              this._updateArrList();
              
              console.log("arrrrrrrrrrrrr", this.arrList);
              console.log(this.data.multiIndexArray, "multiIndexArray");
              console.log("rawData.repeat=", rawData.repeat);
              this.updateRepeatTimeView(4, { showRepeatWeekDialog: false, weakDayViewVisible: false });
            }
          }
        ]}
        onDismiss={() => this.setState({ weakDayViewVisible: false })}
      />);
  }

  _updateArrList() {
    let multiIndexArraynew = this.data.IndexArray;
    this.arrList = [];
    multiIndexArraynew.forEach((res) => {
      console.log(res);
      if (res == 1) {
        res = `${ LocalizedStrings['monday1'] },`;
        this.arrList.push(res);
      }
      if (res == 2) {
        res = `${ LocalizedStrings['tuesday1'] },`;
        this.arrList.push(res);
      }
      if (res == 3) {
        res = `${ LocalizedStrings['wednesday1'] },`;
        this.arrList.push(res);
      }
      if (res == 4) {
        res = `${ LocalizedStrings['thursday1'] },`;
        this.arrList.push(res);
      }
      if (res == 5) {
        res = `${ LocalizedStrings['friday1'] },`;
        this.arrList.push(res);
      }
      if (res == 6) {
        res = `${ LocalizedStrings['saturday1'] },`;
        this.arrList.push(res);
      }
      if (res == 7) {
        res = LocalizedStrings['sunday1']; 
        this.arrList.push(res);
      }
    });
  }

  onClickRepeatItem(item) {
    // this.setState({
    //   repeatItems: repeatItemNewArray
    // });

    if (item.index == 4) {
      this.setState({ weakDayViewVisible: true });
    } else {
      if (item.index == 0) {
        // 每天
        rawData.repeat = 0b01111111;
      } else if (item.index == 1) {
        // 工作日
        rawData.repeat = 0b00111110;
      } else if (item.index == 2) {
        // 休息日
        rawData.repeat = 0b01000001;
      } else if (item.index == 3) {
        // 休息日
        rawData.repeat = 0b00000000;
      }
      this.updateRepeatTimeView(item.index, {});
    }
  }

  // 时间选择弹框
  renderStartTimeView() {
    return (
      // 时间选择器（24小时制）
      <MHDatePicker
        visible={this.state.isShowStartTimeDialog}
        title={LocalizedStrings['csps_start']}
        type={MHDatePicker.TYPE.TIME24}
        onDismiss={(_) => this.onDismiss("start")}
        onSelect={(res) => this.onSelect(res, "start")}
        showSubtitle={true}
      />
    );
  }
  // 时间选择弹框
  renderEndTimeView() {
    return (
      // 时间选择器（24小时制）
      <MHDatePicker
        visible={this.state.isShowEndTimeDialog}
        title={LocalizedStrings['csps_end']}
        type={MHDatePicker.TYPE.TIME24}
        onDismiss={(_) => this.onDismiss("end")}
        onSelect={(res) => this.onSelect(res, "end")}
      />
    );
  }
  onDismiss(index) {
    console.log("onDismiss");
    if (index == "start") {
      this.setState({ isShowStartTimeDialog: false });
    } else {
      this.setState({ isShowEndTimeDialog: false });
    }
  }

  onSelect(res, index) {
    console.log("onSelect==============", res);
    if (index == "start") {
      rawData.start_time = res.rawString;
      this.startTimeText = rawData.start_time;
      let endTime = rawData.start_time > rawData.end_time
        ? `次日${ rawData.end_time }`
        : rawData.end_time;
      this.endTimeText = endTime;
    } else {
      rawData.end_time = res.rawString;
      let endTime = rawData.start_time > rawData.end_time
        ? `次日${ rawData.end_time }`
        : rawData.end_time;
      this.endTimeText = endTime;
    }
    if (this.startTimeText == this.endTimeText) {
      console.log("设置失败");
    }
  }

  rendSetionHeadView = (info) => {
    return (
      <View
        style={[
          HLSettingStyles.advanceSetionStyle,
          info.section.key == "setion0" ? { height: 21 } : { height: 64 }
        ]}
      >
        <View style={{ flex: 1 }}>
          {info.section.key == "setion1" && (
            <View style={{ marginLeft: 27, width: screenWidth - 54, height: 0.5, backgroundColor: "#E5E5E5", marginTop: 31 }}></View>
          )}
        </View>
      </View>
    );
  };
  keyExtractor = (item, index) => {
    return item + index;
  };
  rendSetionItem = (info) => {
    let iconSize = 40;
    let index = info.item.itemIndex;
    if (info.item.itemIndex == "s0_item0" || info.item.itemIndex == "s0_item3") {
      return (
        <View>
          <TouchableOpacity
            onPress={() => {
              if (info.item.itemValue == false) {
                if (info.item.itemIndex == "s0_item0") {
                  this.setionSwitchChange(true);
                } else {
                  this.setionSwitchChange(false);
                }
              }
            }}
          >
            <View
              style={{ marginLeft: 12, marginRight: 12, height: 80, borderRadius: 16, backgroundColor: "#ffffff", flexDirection: "row", alignItems: "center", justifyContent: "space-between",
                borderBottomRightRadius:
                  index == "s0_item3" && info.item.itemValue == true ? 0 : 16,
                borderBottomLeftRadius:
                  index == "s0_item3" && info.item.itemValue == true ? 0 : 16 }}>
              <Text
                style={{ color: info.item.itemValue == true ? "#32BAC0" : "#000000", fontSize: 16, marginLeft: 20 }}
              >
                {info.item.itemName}
              </Text>
              <View
                style={{ width: iconSize, height: iconSize, justifyContent: "center", alignItems: "center", marginRight: 10 }}>
                <Radio
                  style={{ width: 21, height: 21 }}
                  isChecked={info.item.itemValue}
                />
              </View>
            </View>
          </TouchableOpacity>
          {info.item.itemIndex == "s0_item0" && (
            <View style={{ height: 12 }}></View>
          )}
        </View>
      );
    } else if (info.item.itemIndex == "s0_item1" || info.item.itemIndex == "s0_item2") {
      return this.state.isWholeDay ? null : (
        <ListItem
          allowFontScaling={false}
          // disabled={
          //   index == "s1_item0" ? false : this.state.isWholeDay ? true : false
          // }
          containerStyle={{
            backgroundColor: "#ffffff", marginLeft: 12, width: screenWidth - 24, borderBottomLeftRadius: index == "s0_item2" ? 16 : 0, borderBottomRightRadius: index == "s0_item2" ? 16 : 0, paddingLeft: 20
          }}
          titleStyle={ index == "s1_item0"
            ? HLSettingStyles.sectionItemTitle
            : !this.state.isWholeDay
              ? HLSettingStyles.sectionItemTitle
              : HLSettingStyles.item_disabledStyle }
          valueStyle={ index == "s1_item0"
            ? HLSettingStyles.sectionItemValueTitle
            : !this.state.isWholeDay
              ? HLSettingStyles.sectionItemValueTitle
              : HLSettingStyles.value_disabledStyle }
          title={info.item.itemName}
          value={info.item.itemValue}
          onPress={(_) => this.sectionItemOnPress(index)}
          showSeparator={false}
        />
      );
    } else {
      return (
        <View>
          <ListItem
            allowFontScaling={false}
            // disabled={
            //   index == "s1_item0" ? false : this.state.isWholeDay ? true : false
            // }
            containerStyle={{
              backgroundColor: "#ffffff", marginLeft: 12, width: screenWidth - 24, height: 80, borderRadius: 16, paddingLeft: 20
            }}
            titleStyle={index == HLSettingStyles.sectionItemTitle}
            valueStyle={HLSettingStyles.sectionItemValueTitle}
            title={info.item.itemName}
            value={info.item.itemValue}
            onPress={(_) => this.sectionItemOnPress(index)}
            showSeparator={false}
          />
        </View>
      );
    }
  };

  /**
   * itemPress
   */
  sectionItemOnPress(index) {
    // console.log('___sectionItemOnPress:' + index);

    switch (index) {
      case "s0_item1":
        {
          this.setState({ isShowStartTimeDialog: true });
        }
        break;
      case "s0_item2":
        {
          this.setState({ isShowEndTimeDialog: true });
        }
        break;
      case "s1_item0":
        {
          this.setState({
            repeatViewVisible: true
          });
        }
        break;
    }
  }

  updateRepeatTimeView(index, params) {
    // this.repeatItems.map(function(currentValue, currentIndex) {
    //   if (index == currentIndex) {
    //     currentValue.select = true;
    //   } else {
    //     currentValue.select = false;
    //   }
    //   return currentValue;
    // });
    this._updateRepeatItems();
    let status = { ...params, repeatState: rawData.repeat == 0b00000000 ? LocalizedStrings.plug_timer_onetime : 
      rawData.repeat == 0b01111111 ? LocalizedStrings.plug_timer_everyday : 
        rawData.repeat == 0b00111110 ? LocalizedStrings.weekdays : 
          rawData.repeat == 0b01000001 ? LocalizedStrings.weekends : this.arrList };
    // console.log("updaRepeatTimeView==========", JSON.stringify(status));
    this.setState(status);
    console.log("rawData-------------------", JSON.stringify(rawData));
  }
  /**
   * itemSwitchPress
   */
  setionSwitchChange(value) {
    this.setState({ isWholeDay: value });
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f7f7f7"
  },
  titlStyle: {
    fontSize: 16,
    color: "black"
  },
  separator: {
    alignItems: "flex-start",
    height: 0.5,
    backgroundColor: "rgba(0, 0, 0, 0.15)"
  },
  effectiveTimeStyle: {
    marginLeft: 25,
    marginRight: 25,
    marginTop: 5,
    fontSize: 30,
    color: "#000000"
  },
  repeatViewStyle: {
    width: screenWidth,
    bottom: 0,
    // borderTopWidth:20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    marginHorizontal: 0
    // height: 400
    // backgroundColor:'white'
  }
});
