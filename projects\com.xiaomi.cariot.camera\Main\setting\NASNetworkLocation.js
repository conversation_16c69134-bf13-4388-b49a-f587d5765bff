'use strict';

import {Device, DarkMode, Service} from "miot";
import {strings as I18n, Styles} from 'miot/resources';
import ImageButton from "miot/ui/ImageButton";
import React from 'react';
import { ActivityIndicator, ScrollView, StyleSheet, View, Image, Text, SafeAreaView, Platform, Button } from 'react-native';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import Toast from '../components/Toast';

import NavigationBar from "miot/ui/NavigationBar";
import RPC from "../util/RPC";
import { ListItemWithSwitch, ListItem } from 'miot/ui/ListItem';
import { ChoiceDialog, MessageDialog, LoadingDialog } from 'miot/ui/Dialog';
import dayjs from 'dayjs';
import TrackUtil from '../util/TrackUtil';
import LogUtil from "../util/LogUtil";
import Util from "../util2/Util";
import ParsedText from "react-native-parsed-text";
import CameraConfig from "../util/CameraConfig";
import LoadingView from '../ui/LoadingView';
import Host from "miot/Host";
import RoundedButtonView from "../widget/RoundedButtonView";
import MessageDialogNew from "../components/MessageDialogNew";
import CameraPlayer from "../util/CameraPlayer";

const NAS_STATUS = {
  2:"PAUSE",
  3:"NORMAL",
  4:"ERROR"
}
const ExceptionReason = {
  LackSdCard: 'LackSdCard',
  SdCardException: 'SdCardException'
};

const STATUS_CARD_COLOR_CONFIG = {
  NORMAL:{
    backgroundColor:"#32BAC01A",
    titleColor:"#32BAC0",
    timeColor:'rgba(0, 0, 0, 0.4)'
  },
  ERROR:{
    backgroundColor:"#F5A6231A",
    titleColor:"#F5A623",
    timeColor:'rgba(0, 0, 0, 0.4)'
  },
  PAUSE:{
    backgroundColor:"#F6F7F8",
    titleColor:"#000000",
    timeColor:'rgba(0, 0, 0, 0.4)'
  },
};
export default class NASNetworkLocation extends React.Component {

  constructor(props, context) {
    super(props, context);
    //就是一进来要获取信息 看看有没有
    this.state = {
      findStorage: false,
      isVideoStored: false,

      videoStorageDuration: [
        { title: LocalizedStrings['nas_duration_week'] },
        { title: LocalizedStrings['nas_duration_month'] },
        { title: LocalizedStrings['nas_duration_3_months'] },
        { title: LocalizedStrings['nas_duration_6_months'] },
        { title: LocalizedStrings['nas_duration_12_months'] },
      ],
      videoStorageDurationOld: [ //老UI时的选项,新UI只改了对话框的文案，ListItem的value显示没改
        { title: LocalizedStrings['nas_duration_week'] },
        { title: LocalizedStrings['nas_recycle_month'] },
        { title: LocalizedStrings['nas_recycle_3_months'] },
        { title: LocalizedStrings['nas_recycle_6_months'] },
        { title: LocalizedStrings['nas_recycle_12_months'] },
      ],
      selectStorageDurationIndex: 1,//或者这里是一个数组 然后显示的是数组中的哪一个
      uploadInterval: [
        { title: LocalizedStrings['nas_interval_real'] },
        { title: LocalizedStrings['nas_interval_hour'] },
        { title: LocalizedStrings['nas_interval_day'] },
      ],
      selectUploadIntervalIndex: 0,
      syncInterval: 300,
      isPickerStorageDuration: false,
      isPickerUploadInterval: false,
      isClearHistoryVideo: false,
      isDeleteRouteDevice: false,
      hasStorage: true,
      alarmLevelDialogVisible: false,
      showLoading: true,
      transferIndex: 0,
      showNasErrorDialog: false,
      noNASView:false,
      nasTransferException: "", // 转存时SD卡出现无卡或者异常的具体原因
      showTransferExceptionDialog: false, // 是否显示因为上面状态而需要弹出的dialog
      transferExpectionPause: false // 是否因为nas转存时sd的问题导致的传输暂停，会改变视频存储按钮的状态和顶部传输状态
    };
    this.scan_result = [];
    this.startNasTime = 0;
    this.scanStarted = true;
  }

  // 监听列表滚动事件
  scrollViewScroll = (event) => {
    const y = event.nativeEvent.contentOffset.y;
    let flag = y > 28;
    if (this.showTitle == flag) {
      return;
    }
    if (flag) {
      this.showTitle = true;
      this.props.navigation.setParams({ title: LocalizedStrings['sts_2_n'] });
    } else {
      this.showTitle = false;
      this.props.navigation.setParams({ title: "" });
    }
  };

  render() {

    return (
      <View style={styles.container}>
        <ScrollView
            showsVerticalScrollIndicator={false}
                    scrollEventThrottle={1}
                    onScroll={this.scrollViewScroll}>
          <View style={{ flexDirection: "row", flexWrap: "wrap" }} key={'0'}>
            <Text style={{ fontSize: 30, color: "rgba(0, 0, 0, 0.80)", fontWeight: "300", position: "relative", marginLeft: 25, marginTop: 3, marginBottom: 23,fontFamily:'MI-LANTING--GBK1-Light' }}>
              { LocalizedStrings['sts_2_n']}</Text>
          </View>
        {this._renderSelectNASView()}
        {this._renderNasInfoView()}

        </ScrollView>

        <RoundedButtonView buttonText={LocalizedStrings.clear_nas_video}
                           disabled={false}
                           hide={!this.state.NASInfoView}
                           buttonStyle={{marginHorizontal: 27, marginBottom: 12,  backgroundColor: 'rgba(0,0,0,0.06)', paddingHorizontal: 15, paddingVertical: 8}}
                           buttonTextStyle={{fontSize:16,color:'rgba(0,0,0,0.8)'}}
                           onPress={() => {
                             this.setState({ isClearHistoryVideo: true });
                           }}/>

        <RoundedButtonView buttonText={LocalizedStrings.nas_disk_remove}
                           disabled={false}
                           hide={!this.state.NASInfoView}
                           buttonStyle={{marginHorizontal: 27, marginBottom: Host.isIphoneXSeries ? 35 : 27, backgroundColor: 'rgba(0,0,0,0.06)', paddingHorizontal: 15, paddingVertical: 8}}
                           buttonTextStyle={{fontSize:16,color:"#F43F31",}}
                           onPress={() => {
                             this.setState({ isDeleteRouteDevice: true });
                           }}/>

        {this._renderNoNASView()}
        {this._renderLoadingView()}
        {this._renderNasErrorDialog() }
      </View>
    );
  }

  componentDidMount() {
    /*this.timerID = setInterval(
      () => this.tick(),
      1000
    );*/

    this.props.navigation.setParams({
      title: "",
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
    /*this.props.navigation.setParams({
      title: LocalizedStrings['sts_2_n'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.navigate('StorageSetting'); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        fontWeight: 500
      }
      // 这里判断一下  就弄这一个页面吧
    });*/
    // 刷新的时候
    this.index = 0;
    this.willFocusSubscription = this.props.navigation.addListener(
      'didFocus', () => {
        this.setState({
          showLoading: true
        })
        setTimeout(() => {
          this._getStorage();
        }, 1100);

      }
    );

  }
  componentWillUnmount() {
    if (this.timerID > 0) {
      clearInterval(this.timerID);
      this.timerID = 0;
    }

    // clearInterval(this.getStorageInterval)
    this.willFocusSubscription.remove();
  }
  tick() {
    this.setState((state) => ({
      transferIndex: state.transferIndex > 8 ? 0 : state.transferIndex + 1
    }));
  }
  _doNasScan() {
    if (this.startNasTime > 0 && Date.now() - this.startNasTime >= 10000) {
      LogUtil.logOnAll("_doNasScan over 10s to end===");
      this._onScanResult();
      return;
    }
    RPC.callMethod("nas_scan", {})
      .then((res) => {

        console.log('nas_scan=======================', res);
        // setTimeout(() => {
        //     this.setState({ showLoading: false });
        // }, 300)
        if (res.result.length < this.scan_result.length) {
          LogUtil.logOnAll("_doNasScan length change to small end===");
          return;
        }
        this.scan_result = res.result;
        if (this.scan_result.length > 0) {
          this._onScanResult();
        }
        if (res.code < 0) {
          Toast.fail('c_set_fail');
          this.props.navigate.goBack();
          return;
        }
        setTimeout(() => {
          this._doNasScan();
        }, 1000);
      }).catch((err) => {
        LogUtil.logOnAll("NASNetworkLocation", "nas_scan failed" + JSON.stringify(err));

        // this.setState({
        //   showLoading: false,
        // });
        setTimeout(() => {
          this._doNasScan();
        }, 1000);
      });
  }
  _onScanResult() {
    if (this.scan_result.length == 0) {
      TrackUtil.reportResultEvent("Setting_NAS_Status", "type", 2)
      this.setState({
        showLoading: false,
        noNASView: true,
      });
    } else {
      TrackUtil.reportResultEvent("Setting_NAS_Status", "type", 1)
      this.setState({
        selectView: true,
        showLoading: false,
        DevicesList: this.scan_result
      });
    }
  }
  _getStorage() {
    RPC.callMethod("nas_get_config", {})
      .then(async(res) => {
        LogUtil.logOnAll('_getStorage nas_get_config===', JSON.stringify(res));
        if (res.code < 0) {
          Toast.fail('c_set_fail');
          this.props.navigate.goBack()
        }
        if (res.result.state == 0) {
          if (!this.scanStarted) {
            this._onScanResult();
            return;
          }
          this.scan_result = [];
          this.startNasTime = Date.now();
          this._doNasScan();
          this.scanStarted = false;
        } else {
          if (res.result.state == 1) {
            res.result.state = 3;
          }
          if (res.result.sync_err_count >= 3) {
            res.result.state = 4;
          }
          // 转存需要考虑SD卡的状态
          try {
            const { sdcardCode } = await CameraPlayer.getInstance().getSdcardStatus();
            if (sdcardCode == 1 || sdcardCode == 5) {
              this.setState({
                transferExpectionPause: true,
                nasTransferException: ExceptionReason.LackSdCard,
                showTransferExceptionDialog: !CameraConfig.nasTransferExceptionDueToLackSdCard
              });
              CameraConfig.nasTransferExceptionDueToLackSdCard = true;
            } else if (sdcardCode != 0 && sdcardCode != 2 && sdcardCode != 4) {
              this.setState({
                transferExpectionPause: true,
                nasTransferException: ExceptionReason.SdCardException,
                showTransferExceptionDialog: !CameraConfig.nasTransferExceptionDueToSdException
              });
              CameraConfig.nasTransferExceptionDueToSdException = true;
            }
          } catch (error) {
            LogUtil.logOnAll(`NASNetworkLocation: get SdcardStatus failed ${ JSON.stringify(error) }`);
          }
          this.setState({
            NASInfoView: true,
            showLoading: false,
            deviceMessage: res.result,
            last_sync_time: res.result.last_sync_time
          })
          // let last_sync_time = dayjs.unix(this.state.deviceMessage.last_sync_time)
          res.result.last_sync_time == 0 ?
            this.setState({
              showTime: false
            })
            :
            this.setState({
              showTime: true,
              last_sync_time: dayjs(res.result.last_sync_time * 1000).format(LocalizedStrings['yyyymmdd'] + " HH:mm")
            }, () => {
              this.firstGetNasInfo && clearTimeout(this.firstGetNasInfo);
              this.secondGetNasInfo && clearTimeout(this.secondGetNasInfo);
            });

          switch (res.result.video_retention_time) {
            case 604800:
              this.setState({
                selectStorageDurationIndex: 0
              })
              break;
            case 2592000:
              this.setState({
                selectStorageDurationIndex: 1
              })
              break;
            case 7776000:
              this.setState({
                selectStorageDurationIndex: 2
              })
              break;
            case 15552000:
              this.setState({
                selectStorageDurationIndex: 3
              })
              break;
            case 31104000:
              this.setState({
                selectStorageDurationIndex: 4
              })
              break;
          }
          switch (res.result.sync_interval) {
            case 300:
              this.setState({
                selectUploadIntervalIndex: 0,
                syncInterval: 300
              })
              break;
            case 3600:
              this.setState({
                selectUploadIntervalIndex: 1,
                syncInterval: 3600
              })
              break;
            //以下是按天、指定时间存储
            case 0:
            case 86400:
              this.setState({
                selectUploadIntervalIndex: 2,
                syncInterval: 86400
              })
              break;
            default:
              this.setState({
                selectUploadIntervalIndex: 2,
                syncInterval: res.result.sync_interval
              })
              break;
          }
          res.result.state == 2
            ? this.setState({
              isVideoStored: false
            })
            :
            this.setState({
              isVideoStored: true
            })


          // this.props.navigation.push('NASNetworkLocation', { deviceMessage: res.result ,selectStorageDurationIndex:selectStorageDurationIndex})
        }

      }).catch((err) => {
        LogUtil.logOnAll("NASNetworkLocation", "nas_get_config failed" + JSON.stringify(err));

        this.props.navigation.goBack()
        Toast.fail('c_get_fail', err);
      });

  }

  _renderLoadingView() {
    let isDark = DarkMode.getColorScheme() == "dark";
    if (this.state.showLoading) {
      return (
        <View
          style={{ position:"absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
        >
          <LoadingView
              type={isDark ? LoadingView.TYPE.LIGHT : LoadingView.TYPE.DARK}
              style={{ width: 54, height: 54 }}
          />
          <Text
            style={{ marginTop: 10, fontSize: 12, color: "#000000" }}>
            {LocalizedStrings["camera_loading"]}
          </Text>
        </View>
      );
    }


  }
  _renderNoNASView() {
    let imageUrl = require('../../Resources/Images/icon_nas_device_none.png');
    if (Util.isDark()) {
      imageUrl = require('../../Resources/Images/icon_nas_device_none.png');
    }
    if (this.state.noNASView) {
      return (
        <View style={{
          position:"absolute",
          width:"100%",
          height:"100%",
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          <View style={{
            justifyContent: 'center',
            alignItems: 'center',

          }}>
            <Image
              style={{ width: 63, height: 48 }}
              source={imageUrl}
            />
            {/* 这个后期都用改 */}
            <Text style={{ color: 'grey', marginTop: 12,textAlign:"center",marginHorizontal:50 }}>{LocalizedStrings['nas_device_none']}</Text>
            {/* mj_color_black_30_transparent" 这是啥颜色？？ */}
          </View>
        </View>
      );
    }
  }
  _renderSelectNASView() {
    if (this.state.selectView && !this.state.NASInfoView) {
      return (
        <View>
          <ScrollView showsVerticalScrollIndicator={false}  >
            <View>
              <View style={styles.titleContainer} key={1}>
                <Text style={styles.title}>{LocalizedStrings['nas_list']}</Text>
              </View>

              <View>
                {
                  this.state.DevicesList.map((item, index) => (
                    <ListItem
                      title={item.name}
                      key={index}
                      showSeparator={false}
                      onPress={() => {
                        this.props.navigation.navigate('AddNASSetting', { deviceMessage: item })
                      }
                      }
                      unlimitedHeightEnable={true}
                      titleNumberOfLines={3}
                      titleStyle={{ fontWeight: 'bold' }}
                    />
                  )
                  )
                }
              </View>


              {/*<View style={styles.tipsBack}
                key={3}
              >
                <Text
                  key={31}
                  style={styles.tips}
                >
                  {LocalizedStrings['nas_list_tip']}
                </Text>
              </View>*/}
            </View>
          </ScrollView>
        </View>
      )
    }

  }
  renderText(matchingString, matches) {
    let find = '\\[|\\]|1|2';
    let re = new RegExp(find, 'g');
    return matchingString.replace(re, '');
  }
  handleNasErrorHelp() {
    this.setState({ showNasErrorDialog: true });
  }

  _renderNasErrorDialog() {
    return (
      <MessageDialog
        visible={this.state.showNasErrorDialog}
        title={LocalizedStrings['nas_status_4_dialog_title']}
        messageStyle={{ color: "#666666" }}
        message={LocalizedStrings["nas_status_4_dialog_content"]}
        canDismiss={true}
        buttons={[
          {
            text: LocalizedStrings["offline_divice_ok"],
            // style: { color: 'lightblue' },
            callback: (_) => {
              this.setState({ showNasErrorDialog: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showNasErrorDialog: false });
        }}
      />
    );
  }

  _renderNasInfoView() {
    if (this.state.NASInfoView) {
      let nasConfig = this.state.deviceMessage;
      let nas_status_title = LocalizedStrings[`nas_status_${nasConfig.state}`];
      let colorConfig = STATUS_CARD_COLOR_CONFIG[NAS_STATUS[this.state.deviceMessage.state]];
      if (this.state.transferExpectionPause) {
        nas_status_title = LocalizedStrings['nas_status_2'];
        colorConfig = STATUS_CARD_COLOR_CONFIG[NAS_STATUS[2]];
      }

      return (
        <View>
          <View style={{padding:16,marginHorizontal:12,borderRadius:16,backgroundColor:colorConfig.backgroundColor,marginBottom:20}}>
           <View style={{flexDirection: 'row',justifyContent: "flex-start", alignItems: "center"}}>
             <View style={{flex:1}}>
               <Text style={{fontSize: 18, color: colorConfig.titleColor,marginBottom:2,fontWeight:'500',}}>
                 {nas_status_title}</Text>
               <Text style={{fontSize: 13, color: colorConfig.titleColor,fontWeight:'500'}}>
                 {nasConfig.share.name}</Text>
               <Text style={{fontSize: 13, color: colorConfig.timeColor,fontWeight:'400',display:this.state.showTime?"flex":"none"}}>
                 {(this.state.showTime) ? (LocalizedStrings['nas_last_time']) + ': ' + this.state.last_sync_time : ''}</Text>
             </View>
             {nasConfig.state==4?
             <ImageButton
                 style={{ width: 22, height: 22 }}
                 source={require("../../Resources/Images/icon_nas_error_detail.png")}
                 onPress={() => {
                  this.handleNasErrorHelp();
                 }}
             />:null}
           </View>
          </View>




          {/*<View style={{ backgroundColor: '#1782dd', height: 200, display: 'flex', flexDirection: "column" }}>
            <View style={{ width: '100%', height: 112, display: 'flex', flexDirection: 'row', justifyContent: "center", alignItems: "center" }}>
              <View>
                <Image style={{ width: 80, height: 100 }} source={require('../../resources2/images/mjcamera_smb_camera.png')}></Image>
              </View>

              <View>
                {this._renderPaginationView()}
              </View>
              <View>
                <Image style={{ width: 80, height: 100 }} source={require('../../resources2/images/IMI009_smb_store.png')}></Image>
              </View>
            </View>

            <View style={{ width: '100%', alignItems: "center", marginTop: 10, marginBottom: 10 }}>
              <ParsedText style={{ color: 'white', fontSize: 15 }}
                parse={[
                  { pattern: /\[1(.+?)\]/g, style: styles.url, onPress: this.handleNasErrorHelp.bind(this), renderText: this.renderText }
                ]}
                childrenProps={{ allowFontScaling: false }}>
                {nas_status_title}
              </ParsedText>
            </View>
            <View style={{ width: '100%', alignItems: "center", marginTop: 10 }}>
              <Text style={{ color: 'rgba(255,255,255,0.5)', fontSize: 15 }}>
                {(this.state.showTime) ? (LocalizedStrings['nas_last_time']) + ' ' + this.state.last_sync_time : ''}
              </Text>
            </View>
          </View>*/}
          <View style={styles.featureSetting}
            key={6}
          >
            <ListItemWithSwitch
              title={LocalizedStrings['show_nas_info']}
              containerStyle={{minHeight:72}}
              unlimitedHeightEnable={true}
              showSeparator={false}
              subtitle={LocalizedStrings['nas_info_tip1']}
              value={this.state.isVideoStored}
              onValueChange={
                (value) => this._onVideoStorageChange(value)
              }
              disabled={!!this.state.transferExpectionPause}
              titleStyle={{ fontWeight: 'bold' }}
              unlimitedHeightEnable={true}
              titleNumberOfLines={3}
              subtitleNumberOfLines={7}
            />
            <ListItem
                title={LocalizedStrings['nas_store_interval']}
                /*title={LocalizedStrings['nas_interval']}*/
                showSeparator={false}
                value={this.state.uploadInterval[this.state.selectUploadIntervalIndex].title}
                onPress={() =>{
                   // this._onSelectUploadInterval()
                  let deviceMessage = this.state.deviceMessage;
                  this.props.navigation.navigate('NasUploadIntervalSetting', {
                    title:LocalizedStrings.nas_store_interval_setting,
                    deviceMessage: deviceMessage,
                    selectUploadIntervalIndex:this.state.selectUploadIntervalIndex,
                    syncInterval:this.state.syncInterval,
                    callBack: (syncInterval,selectUploadIntervalIndex) => {
                      this._onUploadIntervalChanged(syncInterval,selectUploadIntervalIndex)
                    }
                  });

                }}
                unlimitedHeightEnable={true}
                titleNumberOfLines={3}
                titleStyle={{ fontWeight: 'bold' }}
            />
            <ListItem
              title={LocalizedStrings['nas_video_store_length']}
              showSeparator={false}
              value={this.state.videoStorageDurationOld[this.state.selectStorageDurationIndex].title}
              onPress={() =>
                this._onSelectStorageDuration()
              }
              unlimitedHeightEnable={true}
              titleNumberOfLines={3}
              titleStyle={{ fontWeight: 'bold' }}
            />

            <ListItem
              title={LocalizedStrings['set_nas_storage']}
              showSeparator={false}
              value={this.state.deviceMessage && this.state.deviceMessage.share ? this.state.deviceMessage.share.name : "null"}
              onPress={() =>
                this._onSelectRouteDevice()
              }
              unlimitedHeightEnable={true}
              titleNumberOfLines={3}
              titleStyle={{ fontWeight: 'bold' }}
            />

            {this.renderPickerStorageDuration()}

            {this.renderPickerUploadInterval()}
            <MessageDialog
              visible={this.state.isClearHistoryVideo}
              /*title={LocalizedStrings['clear_nas_video']}*/
              message={LocalizedStrings['clear_nas_video_confirm']}
              messageStyle={{textAlign:"center",fontWeight:"bold"}}
              onDismiss={() => this.setState({ isClearHistoryVideo: false })}
              buttons={[
                { text: LocalizedStrings["action_cancle"], callback: () => this.setState({ isClearHistoryVideo: false }) },
                { text: LocalizedStrings["action_confirm"], callback: () => this._onClearHistoryVideo() }
              ]}
            />
            <MessageDialog
              visible={this.state.isDeleteRouteDevice}
              /*title={LocalizedStrings['delete_nas_storage']}*/
              message={LocalizedStrings['delete_nas_storage_confirm']}
              messageStyle={{textAlign:"center",fontWeight:"bold"}}
              onDismiss={() => this.setState({ isDeleteRouteDevice: false })}
              buttons={[
                { text: LocalizedStrings["action_cancle"], callback: () => this.setState({ isDeleteRouteDevice: false }) },
                { text: LocalizedStrings["action_confirm"], callback: () => this._onDeleteRouteDevice() }
              ]}
            />

            <MessageDialogNew
              useNewTheme
              visible={this.state.showTransferExceptionDialog}
              title={LocalizedStrings['nas_status_2']}
              message={LocalizedStrings[this.state.nasTransferException == ExceptionReason.LackSdCard ? "nas_pause_due_to_no_sd" : "nas_pause_due_to_exception"]}
              onDismiss={() => { this.setState({ showTransferExceptionDialog: false, nasTransferException: "" }); }}
              buttons={[{
                text: LocalizedStrings["offline_divice_ok"],
                callback: () => { this.setState({ showTransferExceptionDialog: false, nasTransferException: "" }); }
              }, {
                condition: ExceptionReason.SdCardException,
                text: LocalizedStrings["nas_check_sd"],
                callback: () => {
                  this.setState({
                    showTransferExceptionDialog: false,
                    nasTransferException: ""
                  }, () => this.props.navigation.navigate("SDCardSetting"));
                }
              }].filter(({ condition }) => !condition || condition === this.state.nasTransferException)}
            />

          </View>
        </View>
      )
    }
  }
  _renderPaginationView() {
    let dots = []
    const StopDot = (
      <View
        style={{
          backgroundColor: '#fff',
          width: 3,
          height: 10,
          borderRadius: 3,
          margin: 2
        }}
      />
    )
    const ActiveDot = (
      <View
        style={{
          backgroundColor: '#fff',
          width: 4,
          height: 4,
          borderRadius: 2,
          margin: 2
        }}
      />
    )

    const Dot = (
      <View
        style={{
          backgroundColor: 'rgba(255,255,255,.2)',
          width: 3,
          height: 3,
          borderRadius: 1.5,
          margin: 2
        }}
      />
    )
    if (this.state.deviceMessage.state == 1) {
      // clearInterval(this.timerID);
      for (let i = 0; i < 10; i++) {
        dots.push(
          i === 0
            ? React.cloneElement(ActiveDot, { key: i })
            : React.cloneElement(Dot, { key: i })
        )
      }
    }

    if (this.state.deviceMessage.state == 2) {
      // clearInterval(this.timerID);
      for (let i = 0; i < 12; i++) {
        dots.push(
          i === 5 || i === 6
            ? React.cloneElement(StopDot, { key: i })
            : React.cloneElement(Dot, { key: i })
        )
      }
    }
    if (this.state.deviceMessage.state == 3) {
      for (let i = 0; i < 10; i++) {
        dots.push(
          i === this.state.transferIndex
            ? React.cloneElement(ActiveDot, { key: i })
            : React.cloneElement(Dot, { key: i })
        )
      }
    }

    return (
      <View
        style={{
          width: 100,
          top: 10,
          left: 0,
          right: 0,
          flexDirection: 'row',
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'transparent'
        }}
      >
        {dots}
      </View>
    )
  }
  renderPickerStorageDuration() {
    let selectedIndex = this.state.selectStorageDurationIndex
    this.selectedIndexArray = [selectedIndex];
    return (
      <ChoiceDialog
        visible={this.state.isPickerStorageDuration}
        title={LocalizedStrings['nas_video_store_length']}
        options={this.state.videoStorageDuration}
        selectedIndexArray={this.selectedIndexArray}
        useNewType={true}
        itemStyleType={2}
        onDismiss={(_) => this.setState({ isPickerStorageDuration: false })}
        buttons={[{
          text: LocalizedStrings.action_cancle,
          callback: () => this.setState({ isPickerStorageDuration: false })
        }, {
          text: LocalizedStrings.action_confirm,
          callback: (result) => {
            this.selectedIndexArray = result;
            this._onStorageDurationChanged(result);
            this.setState({ isPickerStorageDuration: false });
          }
        }]}
      />
    )
  }
  renderPickerUploadInterval() {
    let selectedIndex = this.state.selectUploadIntervalIndex
    this.selectedIndexArray = [selectedIndex];
    return (
      <ChoiceDialog
        style={{ width: 100 }}
        dialogStyle={{ itemSubtitleNumberOfLines: 1 }}
        useNewType={false}
        visible={this.state.isPickerUploadInterval}
        title={LocalizedStrings['nas_interval']}
        options={this.state.uploadInterval}
        selectedIndexArray={this.selectedIndexArray}
        onDismiss={(_) => this.setState({ isPickerUploadInterval: false })}
        onSelect={(result) => {
          this.selectedIndexArray = result;
          this._onUploadIntervalChanged(result);
          this.setState({ isPickerUploadInterval: false });
        }}

      />
    )
  }
  _onVideoStorageChange(value) {
    let tmpParams = Object.assign({}, this.state.deviceMessage, { state: value ? 3 : 2 })
    this.setState({
      deviceMessage: tmpParams
    })
    RPC.callMethod("nas_set_config", tmpParams).then((res) => {

      this.setState({
        isVideoStored: !this.state.isVideoStored
      }, () => {
        if (this.state.isVideoStored) {
          this.firstGetNasInfo = setTimeout(() => {
            this._getStorage();
            this.secondGetNasInfo = setTimeout(() => {
              this._getStorage();
            }, 3000);
          }, 3000);
        }
      });
    }).catch((err) => {
      LogUtil.logOnAll("NASNetworkLocation", "nas_scan failed1" + JSON.stringify(err));

      Toast.fail('c_set_fail', err);
    });

  }
  _onSelectStorageDuration() {
    this.setState({
      isPickerStorageDuration: true
    })
  }
  _onStorageDurationChanged(value) {
    //这里根据value是数字 然后转换成日子
    let video_retention_time = 7776000
    switch (value[0]) {
      case 0:
        TrackUtil.reportResultEvent("Setting_NASStorageTime_Status", "type", 2)
        video_retention_time = 604800
        break;
      case 1:
        TrackUtil.reportResultEvent("Setting_NASStorageTime_Status", "type", 3)
        video_retention_time = 2592000
        break;
      case 2:
        TrackUtil.reportResultEvent("Setting_NASStorageTime_Status", "type", 4)
        video_retention_time = 7776000
        break;
      case 3:
        TrackUtil.reportResultEvent("Setting_NASStorageTime_Status", "type", 5)
        video_retention_time = 15552000
        break;
      case 4:
        TrackUtil.reportResultEvent("Setting_NASStorageTime_Status", "type", 6)
        video_retention_time = 31104000
        break;
    }
    let params = this.state.deviceMessage

    params.video_retention_time = video_retention_time

    RPC.callMethod("nas_set_config", params).then((res) => {
      console.log("==========",res);
      if (res.result[0] == 'OK') {
        Toast.success('c_set_success');
        this.setState({
          isPickerStorageDuration: false,
          selectStorageDurationIndex: value[0]
        })
      }
    }).catch((err) => {
      LogUtil.logOnAll("NASNetworkLocation", "nas_set failed2" + JSON.stringify(err));

      Toast.fail('c_set_fail', err);
    });
    //要注意这里是value[0] 这里是个数组！

  }
  _onSelectUploadInterval() {

    this.setState({
      isPickerUploadInterval: true,

    })
  }
  _onUploadIntervalChanged(syncInterval,selectUploadIntervalIndex) {

    let params = this.state.deviceMessage
    params.sync_interval = syncInterval

    RPC.callMethod("nas_set_config", params).then((res) => {
      if (res.result[0] == 'OK') {
        Toast.success('c_set_success');
        this.setState({
          selectUploadIntervalIndex: selectUploadIntervalIndex,
          syncInterval:syncInterval
        })
      }
    }).catch((err) => {
      LogUtil.logOnAll("NASNetworkLocation", "nas_scan failed3" + JSON.stringify(err));

      Toast.fail('c_set_fail', err);
    });

  }
  _onSelectRouteDevice() {
    let deviceMessage = this.state.deviceMessage;
    this.props.navigation.navigate('ChangeNAS', { deviceMessage: deviceMessage });
  }
  _onClearHistoryVideo() {
    this.setState({ isClearHistoryVideo: false })
    RPC.callMethod("nas_clear_dir", {}).then((res) => {
      if (res.result == 'OK') {
        Toast.success('delete_success');
      }
      else {
        Toast.fail('action_fail');
      }

    }).catch((err) => {
      LogUtil.logOnAll("NASNetworkLocation", "nas_clear_dir failed" + JSON.stringify(err));

      // this.setState({ showLoading: false });
      Toast.fail('action_fail');
    });
  }
  _onDeleteRouteDevice() {
    Toast.loading('c_setting');
    this.setState({ isDeleteRouteDevice: false })
    // nas_reset
    RPC.callMethod("nas_reset", {}).then((res) => {
      if (res.result[0] = "OK") {
        Toast.success('delete_success');
        this.props.navigation.navigate('StorageSetting')
      }
      else {
        Toast.fail('delete_failed');
      }

    }).catch((err) => {
      LogUtil.logOnAll("NASNetworkLocation", "nas_reset failed" + JSON.stringify(err));

      // this.setState({ showLoading: false });
      Toast.fail('delete_failed');
    });
  }


}

export const styles = StyleSheet.create({
  container: {
    backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : '#fff',
    height: '100%'
  },
  titleContainer: {
    height: 32,
    backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : '#fff',
    justifyContent: 'center',
    paddingLeft: Styles.common.padding
  },
  title: {
    fontSize: 12,
    color: '#8c93b0',
    lineHeight: 14
  },
  tipsBack: {
    paddingTop: 3,
    paddingLeft: 24,
    paddingRight: 24
  },
  tips: {
    marginTop: 5,
    fontSize: 12,
    color: '#8C93B0',
    textAlign: "center"
  },
  url: {
    // color: '#32BAC0',
    textDecorationLine: 'underline'
  }
});
