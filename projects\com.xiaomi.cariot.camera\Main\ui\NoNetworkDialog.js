import { View, TouchableOpacity } from "react-native";

import React from 'react';
import { DarkMode, Host } from 'miot';
import { StyleSheet, Modal, Text } from 'react-native';
import ParsedText from 'react-native-parsed-text';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { RkButton } from 'react-native-ui-kitten';
import Util from '../util2/Util';

const kIsCN = Util.isLanguageCN();

export default class NoNetworkDialog extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      visible: false
    };
    console.log("recreate");
  }

  componentDidMount() {
  }

  componentWillUnmount() {

  }

  show() {
    this.setState({ visible: true });
  }

  hide() {
    this.setState({ visible: false });
  }
  renderText(matchingString, matches) {
    let find = '\\[|\\]|1|2';
    let re = new RegExp(find, 'g');
    return matchingString.replace(re, '');
  }

  handleRetryConnect = () => {
    this.hide();
    Host.ui.openResetAndConnectDevicePage();
  }

  handleFeedback = () => {
    this.hide();
    Host.ui.openFeedbackInput();
  }

  render() {

    let offline_help_tip = LocalizedStrings['connect_error_help_tip'];
    return (
      <Modal
        style={{ display: "flex", alignItems: "center", backgroundColor:"#00000055"}}
        // animationType="slide"
        transparent={true}
        visible={this.state.visible}
        onRequestClose={() => {
          this.hide();
        }}
      >
        <TouchableOpacity style={{ width: "100%", height: "100%", backgroundColor:"#00000055" }}
          onPress = {() => {
            this.hide();
          }}
        >
        </TouchableOpacity>
        <View style={styles.centeredView}>
          <View style={styles.modalView}>
            <Text style={styles.modalTitle}>{LocalizedStrings["connection_failure_dialog_title"]}</Text>

            <ParsedText
              style={styles.text}
              parse={
                [
                  { pattern: /\[1(.+?)\]/g, style: styles.url, onPress: this.handleRetryConnect, renderText: this.renderText },
                  { pattern: /\[2(.+?)\]/g, style: styles.url, onPress: this.handleFeedback, renderText: this.renderText }
                ]
              }
              childrenProps={{ allowFontScaling: false }}
            >
              {offline_help_tip}
            </ParsedText>

            <RkButton
              style={{ width: "100%", marginTop: 26, flexGrow: 1, height: 45, borderRadius: 86, backgroundColor: DarkMode.getColorScheme() === 'dark' ? '#EEEEEE' : 'rgba(0, 0, 0, 0.04)' }}
              activeOpacity={0.5}
              onPress={() => {
                this.hide();
              }}
            >
              <Text style={{ color: 'rgba(0,0,0,0.8)', fontSize: kIsCN ? 16 : 14, textAlign: 'center' }}>
                {LocalizedStrings['offline_divice_ok']}
              </Text>

            </RkButton>

          </View>
        </View>
      </Modal>
    );
  }

}

export const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    flexDirection: 'row'
  },
  modalView: {
    flex: 1,
    flexDirection: 'column',
    marginTop: 50,
    backgroundColor: "#ffffff",
    borderRadius: 15,
    padding: 35,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    position: 'absolute',
    bottom: 0,
    width: "100%"
  },

  textStyle: {
    color: "#000",
    textAlign: "center"
  },
  modalTitle: {
    marginBottom: 15,
    textAlign: "center",
    color: "#000",
    fontSize: kIsCN ? 15 : 13,
    fontWeight: 'bold'
  },

  url: {
    color: '#32BAC0',
    textDecorationLine: 'underline'
  },

  text: {
    color: '#666666',
    fontSize: kIsCN ? 13 : 11,
    lineHeight: 22
  }


});