import locale from "miot/host/locale";
const placeholderRegex = /(\{[\d|\w]+\})/;
const getStrings = (strings) => {
  const language = locale.language;
  return strings[language] || strings['en'];
};
const formatString = (str, ...valuesForPlaceholders) => {
  return (str || '')
    .split(placeholderRegex)
    .filter((textPart) => !!textPart)
    .map((textPart) => {
      if (textPart.match(placeholderRegex)) {
        const matchedKey = textPart.slice(1, -1);
        let valueForPlaceholder = valuesForPlaceholders[matchedKey];
        // If no value found, check if working with an object instead
        if (valueForPlaceholder == undefined) {
          const valueFromObjectPlaceholder = valuesForPlaceholders[0][matchedKey];
          if (valueFromObjectPlaceholder !== undefined) {
            valueForPlaceholder = valueFromObjectPlaceholder;
          } else {
            // If value still isn't found, then it must have been undefined/null
            return valueForPlaceholder;
          }
        }
        return valueForPlaceholder;
      }
      return textPart;
    }).join('');
};
// 为了 autoComplete
let strings = {
  setting: '',
  featureSetting: '',
  commonSetting: '',
  name: '',
  location: '',
  memberSet: '',
  share: '',
  btGateway: '',
  voiceAuth: '',
  ifttt: '',
  productBaike: '',
  firmwareUpgrade: '',
  firmwareUpdate: '',
  more: '',
  help: '',
  legalInfo: '',
  deleteDevice: '',
  autoUpgrade: '',
  checkUpgrade: '',
  security: '',
  networkInfo: '',
  feedback: '',
  timezone: '',
  addToDesktop: '',
  open: "",
  close: "",
  other: '',
  multipleKeyShowOnHome: "",
  // 常用设备
  favoriteDevices: "",
  favoriteCamera: "",
  favoriteAddDevices: "",
  // MHDatePicker
  cancel: '',
  ok: '',
  am: '',
  pm: '',
  months: '',
  days: '',
  hours: '',
  minutes: '',
  seconds: '',
  month: '',
  day: '',
  hour: '',
  minute: '',
  second: '',
  yearUnit: '',
  monthUnit: '',
  dayUnit: '',
  hourUnit: '',
  minuteUnit: '',
  secondUnit: '',
  dateSubTitle: '',
  time24SubTitle: '',
  time12SubTitle: '',
  singleSubTitle: '',
  firmwareUpgradeExit: '',
  firmwareUpgradeUpdate: '',
  firmwareUpgradeLook: '',
  firmwareUpgradeForceUpdate: '',
  firmwareUpgradeForceUpdating: '',
  firmwareUpgradeNew_pre: '',
  firmwareUpgradeNew_sub: '',
  handling: '',
  error: '',
  createLightGroup: '',
  linkDevice: '',
  noSuppurtedLinkageDevice: '',
  noSuppurtedLinkageTip: '',
  supportedLinkageDevices: '',
  linkageDistanceTip: '',
  linkageRemoveTip: '',
  link: '',
  removeLink: '',
  linkFail: '',
  removeLinkFail: '',
  linkConfirm: '',
  removeLinkConfirm: '',
  linking: '',
  linkDeviceBracelet: '',
  scanDeviceBracelet: '',
  scanDeviceBraceletTip: '',
  scanDeviceBraceletEmptyTitle: '',
  scanDeviceBraceletEmptyTip1: '',
  scanDeviceBraceletEmptyTip2: '',
  linkedDeviceBraceletHeaderTip: '',
  availableLinkDeviceBraceletHeaderTip: '',
  linkedDeviceBraceletFooterTip: '',
  availableLinkDeviceBraceletFooterTip: '',
  pluginVersion: '',
  helpAndFeedback: '',
  offline: '',
  downloading: '',
  installing: '',
  upgradeSuccess: '',
  upgradeFailed: '',
  upgradeTimeout: '',
  autoUpgradeInfo: '',
  today: '',
  tomorrow: '',
  currentIsLatestVersion: '',
  lastestVersion: '',
  currentVersion: '',
  fetchFailed: '',
  releaseNote: '',
  releaseVersionHistory: '',
  firmwareAutoUpdate: '',
  autoUpdateDescriptionNote: '',
  updateNow: '',
  requireBelMesh: '',
  createCurtainGroup: '',
  createCurtainGroupTip: '',
  act: '',
  create: '',
  chooseCurtainGroupTitle: '',
  currentDevice: '',
  curtain: '',
  noCurtainGroupTip: '',
  switchPlugin: '',
  stdGuideDialogTitle: '',
  stdGuideDialogSubTitle: '',
  stdGuideDialogNote: '',
  stdGuideDialogButtonOK: '',
  // 多键开关设置
  key: '',
  keyLeft: '',
  keyMiddle: '',
  keyRight: '',
  keyType: '',
  keyName: '',
  light: '',
  updateIcon: '',
  done: '',
  modifyName: '',
  keyUpdateIconTips: '',
  nameHasChars: '',
  nameTooLong: '',
  nameIsEmpty: '',
  nameNotSupportEmoji: '',
  // 房间
  room: '',
  room_nameInputTips: '',
  room_nameSuggest: '',
  room_createNew: '',
  room_bedroom: '',
  room_masterBedroom: '',
  room_secondBedroom: '',
  room_kitchen: '',
  room_diningRoom: '',
  room_washroom: '',
  room_childrensRoom: '',
  room_office: '',
  room_study: '',
  room_balcony: '',
  room_studio: '',
  room_bathroom: '',
  room_backyard: '',
  room_unassigned: '',
  no_privacy_tip_content: '',
  moreDeviceInfo: '',
  deviceNet: '',
  customizeName: '',
  software: '',
  hardware: '',
  bleMeshGateway: '',
  deviceDid: '',
  deviceSN: '',
  mcuVersion: '',
  sdkVersion: '',
  connected: '',
  notConnected: '',
  bleConnected: '',
  deviceOffline: ''
};
strings = getStrings({
  zh: {
    setting: '设置',
    featureSetting: '功能设置',
    commonSetting: '通用设置',
    name: '设备名称',
    deviceService: '设备服务',
    location: '位置管理',
    memberSet: '按键设置',
    share: '设备共享',
    btGateway: '蓝牙网关',
    voiceAuth: '语音授权',
    ifttt: '智能场景',
    productBaike: '产品百科',
    firmwareUpgrade: '固件升级',
    firmwareUpdate: '固件更新',
    more: '更多设置',
    help: '使用帮助',
    legalInfo: '法律信息',
    deleteDevice: '删除设备',
    autoUpgrade: '固件自动升级',
    checkUpgrade: '检查固件升级',
    security: '安全设置',
    networkInfo: '网络信息',
    feedback: '反馈问题',
    timezone: '设备时区',
    addToDesktop: '添加桌面快捷方式',
    open: "开",
    close: "关",
    other: '其他',
    multipleKeyShowOnHome: "在首页展示为{0}个按键",
    // 常用设备
    favoriteDevices: "米家首页显示",
    favoriteCamera: "放大卡片",
    favoriteAddDevices: "设为首页常用设备",
    // MHDatePicker
    cancel: '取消',
    ok: '确定',
    am: '上午',
    pm: '下午',
    months: '个月',
    days: '天',
    hours: '小时',
    minutes: '分钟',
    seconds: '秒钟',
    month: '个月',
    day: '天',
    hour: '小时',
    minute: '分钟',
    second: '秒钟',
    yearUnit: '年',
    monthUnit: '月',
    dayUnit: '日',
    hourUnit: '时',
    minuteUnit: '分',
    secondUnit: '秒',
    dateSubTitle: '{0}年{1}月{2}日', // 2019年06月03日
    time24SubTitle: '{0}:{1}', // 11:43
    time12SubTitle: '{0} {1}:{2}', // 上午 11:43
    singleSubTitle: '{0} {1}', // 5 小时
    // 升级相关
    firmwareUpgradeExit: '退出',
    firmwareUpgradeUpdate: '升级',
    firmwareUpgradeLook: '去看看',
    firmwareUpgradeForceUpdate: '由于您当前的设备固件版本过低，一些功能可能无法正常使用。请升级至最新版本，以体验更丰富的功能',
    firmwareUpgradeForceUpdating: '您的设备正在升级，请稍后，以体验更丰富的功能',
    firmwareUpgradeNew_pre: '检测到设备有最新固件版本',
    firmwareUpgradeNew_sub: '，是否升级',
    handling: '操作中...',
    error: '处理失败，请稍后再试',
    createLightGroup: '创建灯组（新）',
    manageLightGroup: '灯组管理（新）',
    deleteLightGroup: '解散灯组',
    deleteCurtainGroup: '解散组',
    linkDevice: '关联设备',
    noSuppurtedLinkageDevice: '当前无可关联设备',
    noSuppurtedLinkageTip: '1.请确保在米家APP中已添加需要关联的设备，并按要求分配到对应房间下；\n2.请将需要关联的蓝牙设备与本设备保持在一定范围内，否则将无法建立关联。',
    supportedLinkageDevices: '可关联如下设备',
    linkageDistanceTip: '请将需要关联的蓝牙设备与本设备保持在一定范围内，否则将无法建立关联',
    linkageRemoveTip: '如需更换关联的蓝牙设备，请先解除关联',
    link: '关联',
    removeLink: '解除关联',
    linkFail: '关联失败',
    removeLinkFail: '解除关联失败',
    linkConfirm: '确认关联该设备？',
    removeLinkConfirm: '确认解除关联？',
    linking: '正在关联...',
    linkDeviceBracelet: '关联手环',
    scanDeviceBracelet: '扫描手环中...',
    scanDeviceBraceletTip: '请将小米手环与本设备保持在一定的范围内, 并确保手环蓝牙广播已开启',
    scanDeviceBraceletEmptyTitle: '附近未发现可关联的小米手环',
    scanDeviceBraceletEmptyTip1: '1.请确认小米手环已开启蓝牙广播',
    scanDeviceBraceletEmptyTip2: '2.请确认小米手环在本设备附近',
    linkedDeviceBraceletHeaderTip: '已关联如下手环',
    availableLinkDeviceBraceletHeaderTip: '可关联如下手环',
    linkedDeviceBraceletFooterTip: '如需更换关联的手环，请先解除关联。',
    availableLinkDeviceBraceletFooterTip: '请将小米手环与本设备保持在一定的范围内并确保手环蓝牙广播已开启',
    pluginVersion: '插件版本',
    helpAndFeedback: '帮助与反馈',
    offline: '离线',
    downloading: '正在下载...',
    installing: '正在安装...',
    upgradeSuccess: '更新成功',
    upgradeFailed: '更新失败, 请稍后再试',
    upgradeTimeout: '更新超时',
    autoUpgradeInfo: '将在{0}尝试自动更新',
    today: '今天',
    tomorrow: '明天',
    currentIsLatestVersion: '当前已是最新版本',
    lastestVersion: '最新版本：',
    currentVersion: '当前版本：',
    fetchFailed: '获取失败，请稍后',
    releaseNote: '更新日志',
    releaseVersionHistory: '固件版本记录',
    firmwareAutoUpdate: '固件自动更新',
    autoUpdateDescriptionNote: '检测到新固件后，设备将在{0}尝试自动更新。设备必须处于空闲状态以完成更新。更新过程无声音提示和灯光打扰。',
    updateNow: '立即更新',
    requireBelMesh: '该功能需要搭配蓝牙Mesh网关使用',
    createCurtainGroup: '创建窗帘伴侣组',
    createCurtainGroupTip: '将两个窗帘伴侣组成一个窗帘伴侣组使用，组合后可以作为双开帘呈现和控制。',
    act: '动一下',
    create: '创建',
    chooseCurtainGroupTitle: '请选择窗帘伴侣',
    currentDevice: '本设备',
    curtain: '窗帘',
    noCurtainGroupTip: '暂无可成组的设备，请再添加一个窗帘伴侣后再试',
    switchPlugin: '标准插件',
    defaultPlugin: '默认样式',
    selectDefaultHP: '默认样式',
    stdPluginTitle: '标准样式',
    thirdPluginTitle: '传统样式',
    stdPluginSubTitle: '点击更多功能，支持打开传统样式页面',
    stdGuideDialogTitle: '全新升级',
    stdGuideDialogSubTitle: '软件界面已全新升级，简洁畅快抢先体验 ！',
    stdGuideDialogNote: '如需查找升级前功能界面可点击底部“更多功能”入口',
    stdGuideDialogButtonOK: '知道了',
    // 多键开关设置
    key: '按键',
    keyLeft: '左键',
    keyMiddle: '中键',
    keyRight: '右键',
    keyType: '按键类型',
    keyName: '名称',
    light: '灯',
    updateIcon: '更换图标',
    done: '完成',
    modifyName: '修改名称',
    keyUpdateIconTips: '将按键图标设为“{0}”后，可对小爱同学说“打开{0}”',
    nameHasChars: '名称不能包含特殊符号',
    nameTooLong: '名称不能超过40个字符',
    nameIsEmpty: '名称不能为空',
    nameNotSupportEmoji: '名称不支持emoji表情',
    // 房间
    room: '房间',
    room_nameInputTips: '请输入房间名',
    room_nameSuggest: '推荐房间名称',
    room_createNew: '创建新房间',
    room_bedroom: '卧室',
    room_masterBedroom: '主卧',
    room_secondBedroom: '次卧',
    room_kitchen: '厨房',
    room_diningRoom: '餐厅',
    room_washroom: '卫生间',
    room_childrensRoom: '儿童房',
    room_office: '办公室',
    room_study: '书房',
    room_balcony: '阳台',
    room_studio: '工作室',
    room_bathroom: '浴室',
    room_backyard: '后院',
    room_unassigned: '未分配房间',
    no_privacy_tip_content: '无法获取隐私，请检查手机网络或在米家APP中反馈问题。',
    moreDeviceInfo: '更多设备信息',
    deviceNet: '设备网络',
    customizeName: '自定义名称',
    software: '软件',
    hardware: '硬件',
    bleMeshGateway: '蓝牙Mesh网关',
    deviceDid: '设备Did',
    deviceSN: '设备SN',
    mcuVersion: 'MCU固件版本',
    sdkVersion: 'SDK固件版本',
    connected: '已连接',
    notConnected: '未连接',
    bleConnected: '蓝牙直连',
    deviceOffline: '设备离线'
  },
  zh_tw: {
    setting: '設定',
    featureSetting: '裝置設定',
    commonSetting: '一般設定',
    name: '裝置名稱',
    deviceService: 'Device service',
    location: '位置管理',
    memberSet: '按鍵設定',
    share: '裝置共用',
    btGateway: '藍牙網關',
    voiceAuth: '語音授權',
    ifttt: '智能場景',
    productBaike: '產品百科',
    firmwareUpgrade: '韌體更新',
    firmwareUpdate: '韌體更新',
    more: '更多設定',
    help: '使用説明',
    legalInfo: '法律資訊',
    deleteDevice: '刪除裝置',
    autoUpgrade: '韌體自动更新',
    checkUpgrade: '檢查韌體更新',
    security: '安全設定',
    networkInfo: '網路資訊',
    feedback: '反饋問題',
    timezone: '裝置時區',
    addToDesktop: '新増捷徑至桌面',
    open: "開",
    close: "關",
    other: '其他',
    multipleKeyShowOnHome: "在首頁展示為 {0} 個按鍵",
    // 常用设备
    favoriteDevices: "於米家首頁顯示",
    favoriteCamera: "切換至大卡片",
    favoriteAddDevices: "設為首頁常用裝置",
    // MHDatePicker
    cancel: "取消",
    ok: "確認",
    am: "上午",
    pm: "下午",
    months: "個月",
    days: "天",
    hours: "小時",
    minutes: "分鐘",
    seconds: "秒鐘",
    month: "個月",
    day: "天",
    hour: "小時",
    minute: "分鐘",
    second: "秒鐘",
    yearUnit: "年",
    monthUnit: "月",
    dayUnit: "日",
    hourUnit: "時",
    minuteUnit: "分",
    secondUnit: "秒",
    dateSubTitle: '{0}年{1}月{2}日', // 2019年06月03日
    time24SubTitle: '{0}:{1}', // 11:43
    time12SubTitle: '{0} {1}:{2}', // 上午 11:43
    singleSubTitle: '{0} {1}', // 5 小时
    // 升级相关
    firmwareUpgradeExit: '退出',
    firmwareUpgradeUpdate: '升級',
    firmwareUpgradeLook: '看看',
    firmwareUpgradeForceUpdate: '由於您目前的用戶端版本過低，一些功能可能無法正常使用。 請升級最新版本，以體驗更豐富的功能',
    firmwareUpgradeForceUpdating: '您的裝置正在升級，請稍後，以體驗更豐富的功能',
    firmwareUpgradeNew_pre: '檢測到裝置有最新韌體版本',
    firmwareUpgradeNew_sub: '，是否升級？',
    handling: '執行中...',
    error: '處理失敗，請重試',
    createLightGroup: "建立燈組 (新)",
    manageLightGroup: "燈組管理 (新)",
    deleteLightGroup: "解散燈組",
    deleteCurtainGroup: "解散組",
    linkDevice: "連結裝置",
    noSuppurtedLinkageDevice: "當前無可關聯設備",
    noSuppurtedLinkageTip: "1.請確保在米家APP中已添加需要關聯的設備，並按要求分配到對應房間下；\n 2.請將需要關聯的藍牙設備與本設備保持在一定範圍內，否則將無法建立關聯。",
    supportedLinkageDevices: "可連結以下裝置",
    linkageDistanceTip: "請將要連結的藍牙裝置與本裝置保持在一定範圍內，否則無法建立連結",
    linkageRemoveTip: " 如需更換關聯的藍牙設備，請先解除關聯",
    link: "連結",
    removeLink: "解除連結",
    linkFail: "連結失敗",
    removeLinkFail: "解除關聯失敗",
    linkConfirm: "確認要連結該裝置？",
    removeLinkConfirm: "確認解除連結",
    linking: "正在連結…",
    linkDeviceBracelet: '关联手环',
    scanDeviceBracelet: '扫描手环中...',
    scanDeviceBraceletTip: '请将小米手环与本设备保持在一定的范围内, 并确保手环蓝牙广播已开启',
    scanDeviceBraceletEmptyTitle: '附近未发现可关联的小米手环',
    scanDeviceBraceletEmptyTip1: '1.请确认小米手环已开启蓝牙广播',
    scanDeviceBraceletEmptyTip2: '2.请确认小米手环在本设备附近',
    linkedDeviceBraceletHeaderTip: '已关联如下手环',
    availableLinkDeviceBraceletHeaderTip: '可关联如下手环',
    linkedDeviceBraceletFooterTip: '如需更换关联的手环，请先解除关联。',
    availableLinkDeviceBraceletFooterTip: '请将小米手环与本设备保持在一定的范围内并确保手环蓝牙广播已开启',
    pluginVersion: '挿件版本號',
    helpAndFeedback: '幫助與反饋',
    offline: '離線',
    downloading: '正在下載...',
    installing: '正在安裝...',
    upgradeSuccess: '更新成功',
    upgradeFailed: '更新失敗，請稍後再試',
    upgradeTimeout: '更新超時',
    autoUpgradeInfo: '將於{0}嘗試自動更新',
    today: '今天',
    tomorrow: '明天',
    currentIsLatestVersion: '目前已是最新版本',
    lastestVersion: '最新版本：',
    currentVersion: '當前版本：',
    fetchFailed: '獲取失敗，請稍後',
    releaseNote: '更新日誌',
    releaseVersionHistory: '韌體版本記錄',
    firmwareAutoUpdate: '韌體自動更新',
    autoUpdateDescriptionNote: '偵測到新韌體後，裝置將會在{0}嘗試自動更新。裝置必須處於閒置狀態以完成更新。更新過程無聲音提示和燈光打擾。',
    updateNow: '立即更新',
    requireBelMesh: 'This function requires a Bluetooth Mesh gateway to be used.',
    createCurtainGroup: '创建窗帘伴侣组',
    createCurtainGroupTip: '将两个窗帘伴侣组成一个窗帘伴侣组使用，组合后可以作为双开帘呈现和控制。',
    act: '动一下',
    create: '创建',
    chooseCurtainGroupTitle: '请选择窗帘伴侣',
    currentDevice: '本设备',
    curtain: '窗帘',
    noCurtainGroupTip: '暂无可成组的设备，请再添加一个窗帘伴侣后再试',
    switchPlugin: '標準外掛程式',
    defaultPlugin: '預設樣式',
    selectDefaultHP: '預設樣式',
    stdPluginTitle: 'Standard',
    thirdPluginTitle: 'Traditional',
    stdPluginSubTitle: 'Tap additional features to enter the traditional home page',
    // 多键开关设置
    key: '按鍵',
    keyLeft: '左鍵',
    keyMiddle: '中鍵',
    keyRight: '右鍵',
    keyType: '按键类型',
    keyName: '名称',
    light: '灯',
    updateIcon: '更换图标',
    done: '完成',
    modifyName: '修改名称',
    keyUpdateIconTips: '将按键图标设为“{0}”后，可对小爱同学说“打开{0}”',
    nameHasChars: '名稱不能包含特殊符號',
    nameTooLong: '名稱不能超過40個字符',
    nameIsEmpty: '名稱不能為空',
    nameNotSupportEmoji: '名稱不支援emoji表情',
    // 房间
    room: '房间',
    room_nameInputTips: '请输入房间名',
    room_nameSuggest: '推荐房间名称',
    room_createNew: '创建新房间',
    room_bedroom: '卧室',
    room_masterBedroom: '主卧',
    room_secondBedroom: '次卧',
    room_kitchen: '厨房',
    room_diningRoom: '餐厅',
    room_washroom: '卫生间',
    room_childrensRoom: '儿童房',
    room_office: '办公室',
    room_study: '书房',
    room_balcony: '阳台',
    room_studio: '工作室',
    room_bathroom: '浴室',
    room_backyard: '后院',
    room_unassigned: '未分配房间',
    no_privacy_tip_content: '無法取得隱私權，請檢查手機網路或在米家APP中回報問題。',
    moreDeviceInfo: '更多裝置資訊',
    deviceNet: '裝置網路',
    customizeName: '自訂名稱',
    software: '軟體',
    hardware: '硬體',
    bleMeshGateway: '藍牙網狀閘道器',
    deviceDid: '裝置 ID',
    deviceSN: '裝置序號',
    mcuVersion: 'MCU 韌體版本',
    sdkVersion: 'SDK 韌體版本',
    connected: '已連線',
    notConnected: '未連線',
    bleConnected: '藍牙直接連線',
    deviceOffline: '裝置離線'
  },
  zh_hk: {
    setting: '設定',
    featureSetting: '裝置設定',
    commonSetting: '一般設定',
    name: '裝置名稱',
    deviceService: 'Device service',
    location: '位置管理',
    memberSet: '按鍵設定',
    share: '裝置共用',
    btGateway: '藍牙網關',
    voiceAuth: '語音授權',
    ifttt: '智慧場景',
    productBaike: '產品百科',
    firmwareUpgrade: '韌體更新',
    firmwareUpdate: '韌體更新',
    more: '更多設定',
    help: '使用説明',
    legalInfo: '法律資訊',
    deleteDevice: '刪除裝置',
    autoUpgrade: '韌體自动更新',
    checkUpgrade: '檢查韌體更新',
    security: '安全設定',
    networkInfo: '網路資訊',
    feedback: '反饋問題',
    timezone: '裝置時區',
    addToDesktop: '新增捷徑到桌面',
    open: "開",
    close: "關",
    other: '其他',
    multipleKeyShowOnHome: "在首頁顯示為{0}個按鍵",
    // 常用设备
    favoriteDevices: "在米家 App 首頁顯示",
    favoriteCamera: "切換至大卡片",
    favoriteAddDevices: "設為首頁常用裝置",
    // MHDatePicker
    cancel: "取消",
    ok: "確認",
    am: "上午",
    pm: "下午",
    months: "個月",
    days: "天",
    hours: "小時",
    minutes: "分鐘",
    seconds: "秒鐘",
    month: "個月",
    day: "天",
    hour: "小時",
    minute: "分鐘",
    second: "秒鐘",
    yearUnit: "年",
    monthUnit: "月",
    dayUnit: "日",
    hourUnit: "時",
    minuteUnit: "分",
    secondUnit: "秒",
    dateSubTitle: '{0}年{1}月{2}日', // 2019年06月03日
    time24SubTitle: '{0}:{1}', // 11:43
    time12SubTitle: '{0} {1}:{2}', // 上午 11:43
    singleSubTitle: '{0} {1}', // 5 小时
    // 升级相关
    firmwareUpgradeExit: '退出',
    firmwareUpgradeUpdate: '升級',
    firmwareUpgradeLook: '去看看',
    firmwareUpgradeForceUpdate: '由於您目前的用戶端版本過低，一些功能可能無法正常使用。 請升級最新版本，以體驗更豐富的功能',
    firmwareUpgradeForceUpdating: '您的裝置正在升級，請稍後，以體驗更豐富的功能',
    firmwareUpgradeNew_pre: '檢測到裝置有最新韌體版本',
    firmwareUpgradeNew_sub: '，是否升級？',
    handling: '執行中...',
    error: '處理失敗，請重試',
    createLightGroup: "建立燈組（新）",
    manageLightGroup: "燈組管理（新）",
    deleteLightGroup: "解散燈組",
    deleteCurtainGroup: "解散組",
    linkDevice: "關聯裝置",
    noSuppurtedLinkageDevice: "當前無可關聯設備",
    noSuppurtedLinkageTip: "1.請確保在米家APP中已添加需要關聯的設備，並按要求分配到對應房間下；\n 2.請將需要關聯的藍牙設備與本設備保持在一定範圍內，否則將無法建立關聯。",
    supportedLinkageDevices: "可關聯以下裝置",
    linkageDistanceTip: "請將需要關聯的藍牙裝置與本裝置保持在一定範圍內，否則將無法建立關聯",
    linkageRemoveTip: " 如需更換關聯的藍牙設備，請先解除關聯",
    link: "關聯",
    removeLink: "解除關聯",
    linkFail: "關聯失敗",
    removeLinkFail: "解除關聯失敗",
    linkConfirm: "確認關聯該裝置？",
    removeLinkConfirm: "確認解除關聯",
    linking: "正在關聯…",
    linkDeviceBracelet: '关联手环',
    scanDeviceBracelet: '扫描手环中...',
    scanDeviceBraceletTip: '请将小米手环与本设备保持在一定的范围内, 并确保手环蓝牙广播已开启',
    scanDeviceBraceletEmptyTitle: '附近未发现可关联的小米手环',
    scanDeviceBraceletEmptyTip1: '1.请确认小米手环已开启蓝牙广播',
    scanDeviceBraceletEmptyTip2: '2.请确认小米手环在本设备附近',
    linkedDeviceBraceletHeaderTip: '已关联如下手环',
    availableLinkDeviceBraceletHeaderTip: '可关联如下手环',
    linkedDeviceBraceletFooterTip: '如需更换关联的手环，请先解除关联。',
    availableLinkDeviceBraceletFooterTip: '请将小米手环与本设备保持在一定的范围内并确保手环蓝牙广播已开启',
    pluginVersion: '挿件版本號',
    helpAndFeedback: '幫助與反饋',
    offline: '離線',
    downloading: '正在下載...',
    installing: '正在安裝...',
    upgradeSuccess: '更新成功',
    upgradeFailed: '更新失敗，請稍後再試',
    upgradeTimeout: '更新超時',
    autoUpgradeInfo: '將在{0}嘗試自動更新 將在{0}嘗試自動更新',
    today: '今天',
    tomorrow: '明天',
    currentIsLatestVersion: '目前已是最新版本',
    lastestVersion: '最新版本：',
    currentVersion: '當前版本：',
    fetchFailed: '獲取失敗，請稍後',
    releaseNote: '更新記錄',
    releaseVersionHistory: '韌體版本記錄',
    firmwareAutoUpdate: '韌體自動更新',
    autoUpdateDescriptionNote: '偵測到新韌體後，裝置將在上午{0}嘗試自動更新。裝置必須處於空閒狀態以完成更新。更新過程將無聲音提示和燈光打擾。',
    updateNow: '立即更新',
    requireBelMesh: 'This function requires a Bluetooth Mesh gateway to be used.',
    createCurtainGroup: '创建窗帘伴侣组',
    createCurtainGroupTip: '将两个窗帘伴侣组成一个窗帘伴侣组使用，组合后可以作为双开帘呈现和控制。',
    act: '动一下',
    create: '创建',
    chooseCurtainGroupTitle: '请选择窗帘伴侣',
    currentDevice: '本设备',
    curtain: '窗帘',
    noCurtainGroupTip: '暂无可成组的设备，请再添加一个窗帘伴侣后再试',
    switchPlugin: '標準外掛程式',
    defaultPlugin: '預設風格',
    selectDefaultHP: '預設風格',
    stdPluginTitle: 'Standard',
    thirdPluginTitle: 'Traditional',
    stdPluginSubTitle: 'Tap additional features to enter the traditional home page',
    // 多键开关设置
    key: '按鍵',
    keyLeft: '左鍵',
    keyMiddle: '中鍵',
    keyRight: '右鍵',
    keyType: '按键类型',
    keyName: '名称',
    light: '灯',
    updateIcon: '更换图标',
    done: '完成',
    modifyName: '修改名称',
    keyUpdateIconTips: '将按键图标设为“{0}”后，可对小爱同学说“打开{0}”',
    nameHasChars: '名稱不能包含特殊符號',
    nameTooLong: '名稱最多只支援40個字元',
    nameIsEmpty: '名稱不能為空',
    nameNotSupportEmoji: '名稱不支援emoji表情',
    // 房间
    room: '房间',
    room_nameInputTips: '请输入房间名',
    room_nameSuggest: '推荐房间名称',
    room_createNew: '创建新房间',
    room_bedroom: '卧室',
    room_masterBedroom: '主卧',
    room_secondBedroom: '次卧',
    room_kitchen: '厨房',
    room_diningRoom: '餐厅',
    room_washroom: '卫生间',
    room_childrensRoom: '儿童房',
    room_office: '办公室',
    room_study: '书房',
    room_balcony: '阳台',
    room_studio: '工作室',
    room_bathroom: '浴室',
    room_backyard: '后院',
    room_unassigned: '未分配房间',
    no_privacy_tip_content: '無法取得私隱政策，請檢查手機網絡或在米家應用程式中反映問題。',
    moreDeviceInfo: '更多裝置資訊',
    deviceNet: '裝置網絡',
    customizeName: '自訂名稱',
    software: '軟件',
    hardware: '硬件',
    bleMeshGateway: '藍牙 Mesh 網關',
    deviceDid: '裝置 ID',
    deviceSN: '裝置 SN',
    mcuVersion: 'MCU 韌體版本',
    sdkVersion: 'SDK 韌體版本',
    connected: '已連接',
    notConnected: '未連接',
    bleConnected: '藍牙直連',
    deviceOffline: '裝置離線'
  },
  en: {
    setting: 'Settings',
    featureSetting: 'Device settings',
    commonSetting: 'General settings',
    name: 'Device name',
    deviceService: 'Device service',
    location: 'Manage location',
    memberSet: 'Key settings',
    share: 'Share device',
    btGateway: 'BLE Gateway',
    voiceAuth: 'Voice authorization',
    ifttt: 'Automation',
    productBaike: 'Product intro',
    firmwareUpgrade: 'Firmware update',
    firmwareUpdate: 'Firmware update',
    more: 'Additional settings',
    help: 'Help',
    legalInfo: 'Legal information',
    deleteDevice: 'Delete device',
    autoUpgrade: 'Automatically update firmware',
    checkUpgrade: 'Check for firmware update',
    security: 'Security settings',
    networkInfo: 'Network info',
    feedback: 'Feedback',
    timezone: 'Device time zone',
    addToDesktop: 'Add to Home screen',
    open: "On",
    close: "Off",
    other: 'Other',
    multipleKeyShowOnHome: "The number of buttons shown on the home page: {0}",
    // 常用设备
    favoriteDevices: "Display on Mi Home/Xiaomi Home homepage",
    favoriteCamera: "Switch to the large card",
    favoriteAddDevices: "Add to favorites",
    // MHDatePicker
    cancel: 'Cancel',
    ok: 'Confirm',
    am: 'AM',
    pm: 'PM',
    months: 'months',
    days: 'days',
    hours: 'hours',
    minutes: 'minutes',
    seconds: 'seconds',
    month: 'month',
    day: 'day',
    hour: 'hour',
    minute: 'minute',
    second: 'second',
    yearUnit: '',
    monthUnit: '',
    dayUnit: '',
    hourUnit: '',
    minuteUnit: '',
    secondUnit: '',
    dateSubTitle: '{0}-{1}-{2}', // 2019-06-03
    time24SubTitle: '{0}:{1}', // 11:43
    time12SubTitle: '{1}:{2} {0}', // 11:43 am
    singleSubTitle: '{0} {1}', // 1 hour | 2 hours
    // 升级相关
    firmwareUpgradeExit: 'Exit',
    firmwareUpgradeUpdate: 'Update',
    firmwareUpgradeLook: 'Take a look',
    firmwareUpgradeForceUpdate: 'Current firmware may be too old to run some features. Update to the latest version for better experience.',
    firmwareUpgradeForceUpdating: 'Your device is updating, try again later',
    firmwareUpgradeNew_pre: 'Firmware update ',
    firmwareUpgradeNew_sub: ' available. Update now?',
    handling: 'Running…',
    error: 'Could not operate, please try again later',
    createLightGroup: 'Create light group(new)',
    manageLightGroup: 'Manage devices(new)',
    deleteLightGroup: 'Disband light group',
    deleteCurtainGroup: "Dissolve group",
    linkDevice: 'Link devices',
    noSuppurtedLinkageDevice: 'No devices available for linking now',
    noSuppurtedLinkageTip: '1.Please make sure the device to link with has been added to the app, and is assigned to the preferred room.\n 2.Please keep the Bluetooth device to link with within a certain distance of this device, otherwise it will be unable to link.',
    supportedLinkageDevices: 'Can be linked with the following devices',
    linkageDistanceTip: 'Please keep the Bluetooth device to link with within a certain distance of this device, otherwise it will be unable to link.',
    linkageRemoveTip: 'If the linked Bluetooth device needs to be changed, please remove link first.',
    link: 'Link',
    removeLink: 'Remove link',
    linkFail: 'Couldn\'t link',
    removeLinkFail: 'Couldn\'t remove link',
    linkConfirm: 'Link with this device now?',
    removeLinkConfirm: 'Remove link now?',
    linking: 'Linking…',
    linkDeviceBracelet: 'Link band',
    scanDeviceBracelet: 'Scanning for band…',
    scanDeviceBraceletTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    scanDeviceBraceletEmptyTitle: 'No Mi band to link with nearby',
    scanDeviceBraceletEmptyTip1: '1.Please ensure the Bluetooth Broadcasting of the Mi band is enabled.',
    scanDeviceBraceletEmptyTip2: '2.Please ensure the Mi band is close to this device.',
    linkedDeviceBraceletHeaderTip: 'Linked with the following bands',
    availableLinkDeviceBraceletHeaderTip: 'Can be linked with the following bands',
    linkedDeviceBraceletFooterTip: 'If the linked band needs to be changed, please remove link first.',
    availableLinkDeviceBraceletFooterTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    pluginVersion: 'Plugin version',
    helpAndFeedback: 'Help & Feedback',
    offline: 'Offline',
    downloading: 'Downloading',
    installing: 'Installing',
    upgradeSuccess: 'Upgrade success',
    upgradeFailed: 'Upgrade failed, please retry later',
    upgradeTimeout: 'Upgrade timeout',
    autoUpgradeInfo: 'Automated updates will be attempted between {0}.',
    today: 'today between ',
    tomorrow: 'tomorrow between ',
    currentIsLatestVersion: 'The current version is the latest.',
    lastestVersion: 'Latest version：',
    currentVersion: 'Current version：',
    fetchFailed: 'Failed to get',
    releaseNote: 'Upgrade Log',
    releaseVersionHistory: 'Firmware version history',
    firmwareAutoUpdate: 'Automatic firmware upgrade',
    autoUpdateDescriptionNote: 'Once a new firmware is detected, the device will attempt to update automatically {0}. The device must be inactive to perform the update. There are no audio or light notifications during the update process.',
    updateNow: 'Upgrade Now',
    requireBelMesh: 'This function requires a Bluetooth Mesh gateway to be used.',
    createCurtainGroup: 'Create a curtain group',
    createCurtainGroupTip: 'Two curtain mates can be combined as a curtain mate group, which can be controlled as a double-sided curtain.',
    act: 'Move',
    create: 'Create',
    chooseCurtainGroupTitle: 'Please select a curtain mate',
    currentDevice: 'this device',
    curtain: 'curtain',
    noCurtainGroupTip: 'The device cannot be grouped at the moment, please add another curtain mate and try again.',
    switchPlugin: 'Standard plugin',
    defaultPlugin: 'Default style',
    selectDefaultHP: 'Default style',
    stdPluginTitle: 'Standard',
    thirdPluginTitle: 'Traditional',
    stdPluginSubTitle: 'Tap additional features to enter the traditional home page',
    stdGuideDialogTitle: 'New update',
    stdGuideDialogSubTitle: 'Update the interface to be the first to enjoy a simpler and faster experience.',
    stdGuideDialogNote: 'To find the interface before the update, tap the "More functions" at the bottom.',
    stdGuideDialogButtonOK: 'OK',
    // 多键开关设置
    key: 'Switch',
    keyLeft: 'Left switch',
    keyMiddle: 'Central switch',
    keyRight: 'Right switch',
    keyType: 'Switch type',
    keyName: 'name',
    light: 'light',
    updateIcon: 'Update icon',
    done: 'Done',
    modifyName: 'modify name',
    keyUpdateIconTips: 'After set switch icon with “{0}”，You can call 小爱同学 with “打开{0}”',
    nameHasChars: 'Name can\'t contain special characters',
    nameTooLong: 'Name can contain up to 40 characters',
    nameIsEmpty: 'Name can\'t be empty',
    nameNotSupportEmoji: 'Emoji not supported',
    // 房间
    room: 'Room',
    room_nameInputTips: 'Please enter room name',
    room_nameSuggest: 'Recommended name',
    room_createNew: 'Create a new room',
    room_bedroom: 'Bedroom',
    room_masterBedroom: 'Master bedroom',
    room_secondBedroom: 'Second bedroom',
    room_kitchen: 'Kitchen',
    room_diningRoom: 'Dining room',
    room_washroom: 'Washroom',
    room_childrensRoom: 'Children\'s Room',
    room_office: 'Office',
    room_study: 'Study room',
    room_balcony: 'Balcony',
    room_studio: 'Studio',
    room_bathroom: 'Bathroom',
    room_backyard: 'Backyard',
    room_unassigned: 'Unassigned',
    no_privacy_tip_content: 'Couldn\'t acquire the Privacy Policy, please check the mobile network or provide feedback in the Mi Home/Xiaomi Home app.',
    moreDeviceInfo: 'More device info',
    deviceNet: 'Device network',
    customizeName: 'Custom name',
    software: 'Software',
    hardware: 'Hardware',
    bleMeshGateway: 'Bluetooth Mesh gateway',
    deviceDid: 'Device Did',
    deviceSN: 'Device SN',
    mcuVersion: 'MCU firmware version',
    sdkVersion: 'SDK firmware version',
    connected: 'Connected',
    notConnected: 'Not connected',
    bleConnected: 'Direct Bluetooth connection',
    deviceOffline: 'Device is offline'
  },
  ko: {
    setting: '설정',
    featureSetting: '기기 설정',
    commonSetting: '일반 설정',
    name: '기기 이름',
    deviceService: 'Device service',
    location: '위치 관리',
    memberSet: '버튼 설정',
    share: '기기 공유',
    btGateway: 'BLE 게이트웨이',
    voiceAuth: '음성 권한',
    ifttt: '자동화',
    productBaike: '제품 백과사전',
    firmwareUpgrade: '펌웨어 업데이트',
    firmwareUpdate: '펌웨어 업데이트',
    more: '추가 설정',
    help: '도움말',
    legalInfo: '법률 정보',
    deleteDevice: '기기 제거',
    autoUpgrade: '펌웨어 자동 업데이트',
    checkUpgrade: '펌웨어 업데이트 확인',
    security: '보안 설정',
    networkInfo: '네트워크 정보',
    feedback: '피드백',
    timezone: '디바이스 시간대',
    addToDesktop: '홈 화면에 추가',
    open: "켜기",
    close: "끄기",
    other: 'Other',
    multipleKeyShowOnHome: "홈페이지에 표시할 버튼 수: {0}",
    // 常用设备
    favoriteDevices: "Mi Home/Xiaomi Home 홈페이지에 표시",
    favoriteCamera: "대형 카드로 전환",
    favoriteAddDevices: "초기 화면 자주 사용하는 기기로 설정",
    // MHDatePicker
    cancel: "취소",
    ok: "정보 확인",
    am: "오전",
    pm: "오후",
    months: "달",
    days: "일",
    hours: "시간",
    minutes: "분",
    seconds: "초",
    month: "월",
    day: "일",
    hour: "시",
    minute: "분",
    second: "초",
    yearUnit: '',
    monthUnit: '',
    dayUnit: '',
    hourUnit: '',
    minuteUnit: '',
    secondUnit: '',
    dateSubTitle: '{0}-{1}-{2}', // 2019-06-03
    time24SubTitle: '{0}:{1}', // 11:43
    time12SubTitle: '{1}:{2} {0}', // 11:43 am
    singleSubTitle: '{0} {1}', // 1 hour | 2 hours
    // 升级相关
    firmwareUpgradeExit: '나가기',
    firmwareUpgradeUpdate: '업데이트',
    firmwareUpgradeLook: '확인해보세요',
    firmwareUpgradeForceUpdate: '현재 펌웨어 버전이 너무 오래되었습니다. 몇몇 기능이 정상적으로 작동하지 않을 수 있습니다. 더 나은 사용을 위해 최신 버전으로 업데이트 하십시오.',
    firmwareUpgradeForceUpdating: '기기를 업데이트 중입니다. 나중에 다시 시도하십시오.',
    firmwareUpgradeNew_pre: '펌웨어 업데이트 ',
    firmwareUpgradeNew_sub: ' 가능, 지금 업데이트 하시겠습니까?',
    handling: '실행 중...',
    error: '다시 시도',
    createLightGroup: "전등 그룹 생성(신규)",
    manageLightGroup: "전등 그룹 관리(신규)",
    deleteLightGroup: "전등 그룹 해제",
    deleteCurtainGroup: "Dissolve group",
    linkDevice: "연결 기기",
    noSuppurtedLinkageDevice: "연결 가능한 기기가 없습니다.",
    noSuppurtedLinkageTip: "1. 기기가 APP에 연결되고 대으의 방에 설치되는지 확인하십시오.  \n 2. 연결할 블루투스 기기와 본 기기를 일정 거리 내로 유지해 주세요. 거리가 먼 경우 연결할 수 없습니다.",
    supportedLinkageDevices: "연결 가능한 기기",
    linkageDistanceTip: "연결할 블루투스 기기와 본 기기를 일정 거리 내로 유지해 주세요. 거리가 먼 경우 연결할 수 없습니다.",
    linkageRemoveTip: "연결된 블루투스 기기를 바꾸려면 미리 연결을 해제 하십시오",
    link: "연결",
    removeLink: "연결 해제",
    linkFail: "연결 실패",
    removeLinkFail: "연결 해제 실패",
    linkConfirm: "해당 기기와 연결하시겠습니까?",
    removeLinkConfirm: "연결 해제",
    linking: "연결 중...",
    linkDeviceBracelet: 'Link band',
    scanDeviceBracelet: 'Scanning for band…',
    scanDeviceBraceletTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    scanDeviceBraceletEmptyTitle: 'No Mi band to link with nearby',
    scanDeviceBraceletEmptyTip1: '1.Please ensure the Bluetooth Broadcasting of the Mi band is enabled.',
    scanDeviceBraceletEmptyTip2: '2.Please ensure the Mi band is close to this device.',
    linkedDeviceBraceletHeaderTip: 'Linked with the following bands',
    availableLinkDeviceBraceletHeaderTip: 'Can be linked with the following bands',
    linkedDeviceBraceletFooterTip: 'If the linked band needs to be changed, please remove link first.',
    availableLinkDeviceBraceletFooterTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    pluginVersion: "플러그인 버전",
    helpAndFeedback: '도움말 & 피드백',
    offline: '오프라인',
    downloading: '다운로드 중...',
    installing: '설치 중...',
    upgradeSuccess: '업데이트 완료',
    upgradeFailed: '업데이트에 실패했습니다. 나중에 다시 시도해 주세요.',
    upgradeTimeout: '업데이트 시간 초과',
    autoUpgradeInfo: '자동 업데이트는 {0}에 시도됩니다.',
    today: '오늘 ',
    tomorrow: '내일 ',
    currentIsLatestVersion: '현재 최신 버전',
    lastestVersion: '최신 버전:',
    currentVersion: '현재 버전:',
    fetchFailed: '실패하세요, 잠시만 기다려 주세요',
    releaseNote: '업데이트 로그',
    releaseVersionHistory: '펌웨어 버전 기록',
    firmwareAutoUpdate: '자동 펌웨어 업데이트',
    autoUpdateDescriptionNote: '새 펌웨어를 감지한 후 기기는 {0}에서 자동으로 업데이트를 시도합니다. 업데이트를 완료하려면 장치가 유휴 상태여야 합니다. 업데이트가 진행되는 동안 소리와 빛이 끊기지 않습니다.',
    updateNow: '즉시 업데이트',
    requireBelMesh: 'This function requires a Bluetooth Mesh gateway to be used.',
    createCurtainGroup: 'Create a curtain group',
    createCurtainGroupTip: 'Two curtain mates can be combined as a curtain mate group, which can be controlled as a double-sided curtain.',
    act: 'Move',
    create: 'Create',
    chooseCurtainGroupTitle: 'Please select a curtain mate',
    currentDevice: 'this device',
    curtain: 'curtain',
    noCurtainGroupTip: 'The device cannot be grouped at the moment, please add another curtain mate and try again.',
    switchPlugin: '표준 플러그인',
    defaultPlugin: '기본 스타일',
    selectDefaultHP: '기본 스타일',
    stdPluginTitle: 'Standard',
    thirdPluginTitle: 'Traditional',
    stdPluginSubTitle: 'Tap additional features to enter the traditional home page',
    // 多键开关设置
    key: 'Switch',
    keyLeft: 'Left switch',
    keyMiddle: 'Central switch',
    keyRight: 'Right switch',
    keyType: 'Switch type',
    keyName: 'name',
    light: 'light',
    updateIcon: 'Update icon',
    done: 'Done',
    modifyName: 'modify name',
    keyUpdateIconTips: 'After set switch icon with “{0}”，You can call 小爱同学 with “打开{0}”',
    nameHasChars: '이름에 특수기호를 포함할 수 없습니다.',
    nameTooLong: '집 이름은 최대 40문자까지 지원됩니다.',
    nameIsEmpty: '기기 이름을 비워둘 수 없습니다',
    nameNotSupportEmoji: '기기 이름에 이모티콘을 포함할 수 없습니다',
    // 房间
    room: 'Room',
    room_nameInputTips: 'Please enter room name',
    room_nameSuggest: 'Recommended name',
    room_createNew: 'Create a new room',
    room_bedroom: 'Bedroom',
    room_masterBedroom: 'Master bedroom',
    room_secondBedroom: 'Second bedroom',
    room_kitchen: 'Kitchen',
    room_diningRoom: 'Dining room',
    room_washroom: 'Washroom',
    room_childrensRoom: 'Children\'s Room',
    room_office: 'Office',
    room_study: 'Study room',
    room_balcony: 'Balcony',
    room_studio: 'Studio',
    room_bathroom: 'Bathroom',
    room_backyard: 'Backyard',
    room_unassigned: 'Unassigned',
    no_privacy_tip_content: '개인정보 취급방침을 가져올 수 없습니다. 모바일 네트워크를 확인하고 Mi Home/Xiaomi Home 앱에서 피드백을 제공하세요.',
    moreDeviceInfo: '자세한 기기 정보',
    deviceNet: '기기 네트워크',
    customizeName: '사용자 지정 이름',
    software: '소프트웨어',
    hardware: '하드웨어',
    bleMeshGateway: 'Bluetooth 메시 게이트웨이',
    deviceDid: '기기 ID',
    deviceSN: '기기 SN',
    mcuVersion: 'MCU 펌웨어 버전',
    sdkVersion: 'SDK 펌웨어 버전',
    connected: '연결됨',
    notConnected: '연결되지 않음',
    bleConnected: 'Bluetooth 직접 연결',
    deviceOffline: '기기가 오프라인 상태입니다.'
  },
  ru: {
    setting: 'Настройки',
    featureSetting: 'Настройки устройств',
    commonSetting: 'Общие настройки',
    name: 'Имя устройства',
    deviceService: 'Device service',
    location: 'Управление местами',
    memberSet: 'Настройка кнопок',
    share: 'Поделиться устройством',
    btGateway: 'BLE шлюз',
    voiceAuth: 'Голосовая авторизация',
    ifttt: 'Автоматизация',
    productBaike: 'Знакомство с продуктом',
    firmwareUpgrade: 'Проверить наличие обновлений',
    firmwareUpdate: 'Проверить наличие обновлений',
    more: 'Дополнительные настройки',
    help: 'Справка',
    legalInfo: 'Легальная информация',
    deleteDevice: 'Удалить устройство',
    autoUpgrade: 'Автоматическое обновление встроенного ПО',
    checkUpgrade: 'Проверить наличие обновлений',
    security: 'Параметры безопасности',
    networkInfo: 'Информация о сети',
    feedback: 'Отзыв',
    timezone: 'Часовой пояс устройства',
    addToDesktop: 'Добавить на главный экран',
    open: "Вкл",
    close: "Выкл",
    other: 'Прочее',
    multipleKeyShowOnHome: "Количество кнопок, отображаемых на домашней странице: {0}",
    // 常用设备
    favoriteDevices: "Отобразить на главной странице Mi Home/Xiaomi Home",
    favoriteCamera: "Переключиться на крупные карточки",
    favoriteAddDevices: "Добавить в избранное",
    // MHDatePicker
    cancel: "Отменить",
    ok: "Подтвердить",
    am: "ДП",
    pm: "ПП",
    months: "месяцы",
    days: "дни",
    hours: "часы",
    minutes: "минуты",
    seconds: "секунды",
    month: "месяц ",
    day: "день",
    hour: "час ",
    minute: "минута",
    second: "сеунда",
    yearUnit: '',
    monthUnit: '',
    dayUnit: '',
    hourUnit: '',
    minuteUnit: '',
    secondUnit: '',
    dateSubTitle: '{0}-{1}-{2}', // 2019-06-03
    time24SubTitle: '{0}:{1}', // 11:43
    time12SubTitle: '{1}:{2} {0}', // 11:43 am
    singleSubTitle: '{0} {1}', // 1 hour | 2 hours
    // 升级相关
    firmwareUpgradeExit: 'Выход',
    firmwareUpgradeUpdate: 'Обновление',
    firmwareUpgradeLook: 'Посмотреть',
    firmwareUpgradeForceUpdate: 'Текущая версия ПО устарела, некоторые возможности могут быть недоступны. Обновите до последней версии ПО.',
    firmwareUpgradeForceUpdating: 'Устройство обновляется, попробуйте позже',
    firmwareUpgradeNew_pre: 'Доступно обновление ПО ',
    firmwareUpgradeNew_sub: ', обновить сейчас?',
    handling: 'Загрузка...',
    error: 'Попробовать снова',
    createLightGroup: "Создать группу светильников (новинка)",
    manageLightGroup: "Управление устройствами (новинка)",
    deleteLightGroup: "Расформировать группу светильников",
    deleteCurtainGroup: "Dissolve group",
    linkDevice: "Связать устройства",
    noSuppurtedLinkageDevice: "Нет доступных устройств для связи",
    noSuppurtedLinkageTip: "1.Убедитесь, что связываемое устройство было добавлено в приложение и назначено для соответствующей комнаты.\n 2.Держите связываемое устройство Bluetooth рядом с этим устройством, иначе не удастся установить связь.",
    supportedLinkageDevices: "Можно установить связь со следующими устройствами",
    linkageDistanceTip: "Держите связываемое устройство Bluetooth рядом с этим устройством, иначе не удастся установить связь.",
    linkageRemoveTip: "Если необходимо изменить связываемое устройство Bluetooth, сначала удалите связь.",
    link: "Связать",
    removeLink: "Удалить связь",
    linkFail: "Не удалось установить связь",
    removeLinkFail: "Не удалось удалить связь",
    linkConfirm: "Связать с этим устройством?",
    removeLinkConfirm: "Удалить связь",
    linking: "Связывание...",
    linkDeviceBracelet: 'Link band',
    scanDeviceBracelet: 'Scanning for band…',
    scanDeviceBraceletTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    scanDeviceBraceletEmptyTitle: 'No Mi band to link with nearby',
    scanDeviceBraceletEmptyTip1: '1.Please ensure the Bluetooth Broadcasting of the Mi band is enabled.',
    scanDeviceBraceletEmptyTip2: '2.Please ensure the Mi band is close to this device.',
    linkedDeviceBraceletHeaderTip: 'Linked with the following bands',
    availableLinkDeviceBraceletHeaderTip: 'Can be linked with the following bands',
    linkedDeviceBraceletFooterTip: 'If the linked band needs to be changed, please remove link first.',
    availableLinkDeviceBraceletFooterTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    pluginVersion: "Версия плагина",
    helpAndFeedback: 'Справка и обратная связь',
    offline: 'Не в сети',
    downloading: 'Скачивание',
    installing: 'Установка',
    upgradeSuccess: 'Обновление успешно',
    upgradeFailed: 'Ошибка обновления. Повторите попытку позже.',
    upgradeTimeout: 'Время ожидания обновления',
    autoUpgradeInfo: 'Автоматические обновления будут предприняты между {0}.',
    today: 'Сегодня ',
    tomorrow: 'Завтра ',
    currentIsLatestVersion: 'Текущая версия - самая последняя.',
    lastestVersion: 'Последняя версия:',
    currentVersion: 'Текущая версия:',
    fetchFailed: 'Не удалось получить',
    releaseNote: 'Журнал обновлений',
    releaseVersionHistory: 'История версий прошивки',
    firmwareAutoUpdate: 'Автоматическое обновление прошивки',
    autoUpdateDescriptionNote: 'После того, как новая прошивка будет обнаружена, устройство будет пытаться автоматически {0} обновить. Для выполнения обновления устройство должно быть неактивным. Во время процесса обновления нет звуковых или световых уведомлений.',
    updateNow: 'Обнови сейчас',
    requireBelMesh: 'This function requires a Bluetooth Mesh gateway to be used.',
    createCurtainGroup: 'Create a curtain group',
    createCurtainGroupTip: 'Two curtain mates can be combined as a curtain mate group, which can be controlled as a double-sided curtain.',
    act: 'Move',
    create: 'Create',
    chooseCurtainGroupTitle: 'Please select a curtain mate',
    currentDevice: 'this device',
    curtain: 'curtain',
    noCurtainGroupTip: 'The device cannot be grouped at the moment, please add another curtain mate and try again.',
    switchPlugin: 'Стандартный плагин',
    defaultPlugin: 'Стиль по умолчанию',
    selectDefaultHP: 'Стиль по умолчанию',
    stdPluginTitle: 'Standard',
    thirdPluginTitle: 'Traditional',
    stdPluginSubTitle: 'Tap additional features to enter the traditional home page',
    // 多键开关设置
    key: 'Switch',
    keyLeft: 'Left switch',
    keyMiddle: 'Central switch',
    keyRight: 'Right switch',
    keyType: 'Switch type',
    keyName: 'name',
    light: 'light',
    updateIcon: 'Update icon',
    done: 'Done',
    modifyName: 'modify name',
    keyUpdateIconTips: 'After set switch icon with “{0}”，You can call 小爱同学 with “打开{0}”',
    nameHasChars: 'Название не может содержать специальные символы',
    nameTooLong: 'Название дома может содержать до 40 символов',
    nameIsEmpty: 'Имя устройства не может быть пустым',
    nameNotSupportEmoji: 'Имя устройства не может содержать смайлики',
    // 房间
    room: 'Room',
    room_nameInputTips: 'Please enter room name',
    room_nameSuggest: 'Recommended name',
    room_createNew: 'Create a new room',
    room_bedroom: 'Bedroom',
    room_masterBedroom: 'Master bedroom',
    room_secondBedroom: 'Second bedroom',
    room_kitchen: 'Kitchen',
    room_diningRoom: 'Dining room',
    room_washroom: 'Washroom',
    room_childrensRoom: 'Children\'s Room',
    room_office: 'Office',
    room_study: 'Study room',
    room_balcony: 'Balcony',
    room_studio: 'Studio',
    room_bathroom: 'Bathroom',
    room_backyard: 'Backyard',
    room_unassigned: 'Unassigned',
    no_privacy_tip_content: 'Не удалось получить доступ к Политике конфиденциальности, проверьте мобильную сеть или оставьте отзыв в приложении Mi Home/Xiaomi Home.',
    moreDeviceInfo: 'Дополнительная информация об устройстве',
    deviceNet: 'Сеть устройства',
    customizeName: 'Пользовательское имя',
    software: 'Программное обеспечение',
    hardware: 'Аппаратное обеспечение',
    bleMeshGateway: 'Mesh-шлюз Bluetooth',
    deviceDid: 'Идентификатор устройства',
    deviceSN: 'Серийный номер устройства',
    mcuVersion: 'Версия встроенного ПО MCU',
    sdkVersion: 'Версия встроенного ПО SDK',
    connected: 'Подключено',
    notConnected: 'Не подключен',
    bleConnected: 'Прямое соединение по Bluetooth',
    deviceOffline: 'Устройство находится в автономном режиме'
  },
  es: {
    setting: 'Ajustes',
    featureSetting: 'Ajustes de dispositivo',
    commonSetting: 'Ajustes generales',
    name: 'Nombre del dispositivo',
    deviceService: 'Device service',
    location: 'Gestionar ubicaciones',
    memberSet: 'Ajustes de los botones',
    share: 'Compartir dispositivo',
    btGateway: 'Entrada BLE',
    voiceAuth: 'Autorización de voz',
    ifttt: 'Automatización',
    productBaike: 'Introducción al producto',
    firmwareUpgrade: 'Actualizaciones de firmware',
    firmwareUpdate: 'Actualizaciones de firmware',
    more: 'Ajustes adicionales',
    help: 'Ayuda',
    legalInfo: 'Información legal',
    deleteDevice: 'Eliminar dispositivo',
    autoUpgrade: 'Actualizar automáticamente el firmware',
    checkUpgrade: 'Buscar actualizaciones de firmware',
    security: 'Ajustes de seguridad',
    networkInfo: 'Información de red',
    feedback: 'Comentario',
    timezone: 'Zona horaria del dispositivo',
    addToDesktop: 'Añadir a la pantalla de Inicio',
    open: "Activado",
    close: "Desactivado",
    other: 'Otros',
    multipleKeyShowOnHome: "El número de botones mostrados en la página de inicio: {0}",
    // 常用设备
    favoriteDevices: "Mostrar en la página de inicio de Mi Home/Xiaomi Home",
    favoriteCamera: "Cambiar a la tarjeta grande",
    favoriteAddDevices: "Añadir a favoritos",
    // MHDatePicker
    cancel: "Cancelar",
    ok: "Confirmar",
    am: "AM",
    pm: "PM",
    months: "meses",
    days: "días",
    hours: "horas",
    minutes: "minutos",
    seconds: "segundos",
    month: "meses",
    day: "día",
    hour: "hora",
    minute: "minuto",
    second: "segundo ",
    yearUnit: '',
    monthUnit: '',
    dayUnit: '',
    hourUnit: '',
    minuteUnit: '',
    secondUnit: '',
    dateSubTitle: '{0}-{1}-{2}', // 2019-06-03
    time24SubTitle: '{0}:{1}', // 11:43
    time12SubTitle: '{1}:{2} {0}', // 11:43 am
    singleSubTitle: '{0} {1}', // 1 hour | 2 hours
    // 升级相关
    firmwareUpgradeExit: 'Salir',
    firmwareUpgradeUpdate: 'Actualizar',
    firmwareUpgradeLook: 'Echa un vistazo',
    firmwareUpgradeForceUpdate: 'La versión de firmware actual es demasiado antigua, puede que algunas características no funcionen correctamente. Actualiza a la última versión para una mejor experiencia.',
    firmwareUpgradeForceUpdating: 'Tu dispositivo se está actualizando, inténtalo más tarde',
    firmwareUpgradeNew_pre: 'Actualización ',
    firmwareUpgradeNew_sub: ' de firmware disponible, ¿actualizar ahora?',
    handling: 'Ejecutándose...',
    error: 'Reintentar',
    createLightGroup: "Crear grupo de luces (nuevo)",
    manageLightGroup: "Administrar dispositivos (nuevo)",
    deleteLightGroup: "Deshacer grupo de luces",
    deleteCurtainGroup: "Dissolve group",
    linkDevice: "Vincular dispositivos",
    noSuppurtedLinkageDevice: "No hay dispositivos disponibles para vincular ahora",
    noSuppurtedLinkageTip: "1. Por favor, asegúreses que el dispositivo que quiere vincular se ha añadido a la aplicación, y asígnelo a la habitación preferida. \n 2.Por favor mantenga el dispositivo Bluetooth que quiere vincular cerca de este dispositivo,en caso contrario no podrá vincularse.",
    supportedLinkageDevices: "Puede vincularse con los siguientes dispositivos",
    linkageDistanceTip: "Mantenga el dispositivo Bluetooth que quiere vincular cerca de este dispositivo, de lo contrario no podrá vincularse.",
    linkageRemoveTip: "Si el dispositivo Bluetooth necesita cargarse, por favor elimine el vinculo primero.",
    link: "Vincular",
    removeLink: "Eliminar vinculación",
    linkFail: "No se ha podido vincular",
    removeLinkFail: "No se pudo eliminar el enlace.",
    linkConfirm: "¿Vincular ahora con este dispositivo?",
    removeLinkConfirm: "Eliminar vinculación ahora",
    linking: "Vinculando…",
    linkDeviceBracelet: 'Link band',
    scanDeviceBracelet: 'Scanning for band…',
    scanDeviceBraceletTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    scanDeviceBraceletEmptyTitle: 'No Mi band to link with nearby',
    scanDeviceBraceletEmptyTip1: '1.Please ensure the Bluetooth Broadcasting of the Mi band is enabled.',
    scanDeviceBraceletEmptyTip2: '2.Please ensure the Mi band is close to this device.',
    linkedDeviceBraceletHeaderTip: 'Linked with the following bands',
    availableLinkDeviceBraceletHeaderTip: 'Can be linked with the following bands',
    linkedDeviceBraceletFooterTip: 'If the linked band needs to be changed, please remove link first.',
    availableLinkDeviceBraceletFooterTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    pluginVersion: "Versión de Plugin",
    helpAndFeedback: 'Ayuda y Comentarios',
    offline: 'Desconectado',
    downloading: 'Descargando',
    installing: 'Instalando',
    upgradeSuccess: 'Éxito de la actualización',
    upgradeFailed: 'La actualización falló. Vuelva a intentarlo más tarde.',
    upgradeTimeout: 'Tiempo de espera de actualización',
    autoUpgradeInfo: 'Se intentarán actualizaciones automáticas entre {0}.',
    today: 'Hoy ',
    tomorrow: 'Mañana ',
    currentIsLatestVersion: 'La versión actual es la más reciente.',
    lastestVersion: 'Ultima versión:',
    currentVersion: 'Versión actual:',
    fetchFailed: 'No pudo conseguir',
    releaseNote: 'Registro de actualización',
    releaseVersionHistory: 'Historial de versiones de firmware',
    firmwareAutoUpdate: 'Actualización automática de firmware',
    autoUpdateDescriptionNote: 'Una vez que se detecta un nuevo firmware, el dispositivo intentará actualizarse automáticamente {0}. El dispositivo debe estar inactivo para realizar la actualización. No hay notificaciones de audio o luz durante el proceso de actualización.',
    updateNow: 'Actualizar ahora',
    requireBelMesh: 'This function requires a Bluetooth Mesh gateway to be used.',
    createCurtainGroup: 'Create a curtain group',
    createCurtainGroupTip: 'Two curtain mates can be combined as a curtain mate group, which can be controlled as a double-sided curtain.',
    act: 'Move',
    create: 'Create',
    chooseCurtainGroupTitle: 'Please select a curtain mate',
    currentDevice: 'this device',
    curtain: 'curtain',
    noCurtainGroupTip: 'The device cannot be grouped at the moment, please add another curtain mate and try again.',
    switchPlugin: 'Plugin estándar',
    defaultPlugin: 'Estilo predeterminado',
    selectDefaultHP: 'Estilo predeterminado',
    stdPluginTitle: 'Standard',
    thirdPluginTitle: 'Traditional',
    stdPluginSubTitle: 'Tap additional features to enter the traditional home page',
    // 多键开关设置
    key: 'Switch',
    keyLeft: 'Left switch',
    keyMiddle: 'Central switch',
    keyRight: 'Right switch',
    keyType: 'Switch type',
    keyName: 'name',
    light: 'light',
    updateIcon: 'Update icon',
    done: 'Done',
    modifyName: 'modify name',
    keyUpdateIconTips: 'After set switch icon with “{0}”，You can call 小爱同学 with “打开{0}”',
    nameHasChars: 'El nombre no puede contener caracteres especiales',
    nameTooLong: 'El nombre de la casa puede tener hasta 40 caracteres',
    nameIsEmpty: 'El nombre del dispositivo no puede estar vacío',
    nameNotSupportEmoji: 'Nombre de dispositivo no puede contener emoticonos',
    // 房间
    room: 'Room',
    room_nameInputTips: 'Please enter room name',
    room_nameSuggest: 'Recommended name',
    room_createNew: 'Create a new room',
    room_bedroom: 'Bedroom',
    room_masterBedroom: 'Master bedroom',
    room_secondBedroom: 'Second bedroom',
    room_kitchen: 'Kitchen',
    room_diningRoom: 'Dining room',
    room_washroom: 'Washroom',
    room_childrensRoom: 'Children\'s Room',
    room_office: 'Office',
    room_study: 'Study room',
    room_balcony: 'Balcony',
    room_studio: 'Studio',
    room_bathroom: 'Bathroom',
    room_backyard: 'Backyard',
    room_unassigned: 'Unassigned',
    no_privacy_tip_content: 'No se pudo ver la Política de privacidad, compruebe la red móvil o deje un comentario en la aplicación Mi Home/Xiaomi Home.',
    moreDeviceInfo: 'Más información del dispositivo',
    deviceNet: 'Red del dispositivo',
    customizeName: 'Nombre personalizado',
    software: 'Software',
    hardware: 'Hardware',
    bleMeshGateway: 'Puerta de enlace de malla Bluetooth',
    deviceDid: 'ID de dispositivo',
    deviceSN: 'NS de dispositivo',
    mcuVersion: 'Versión de firmware de MCU',
    sdkVersion: 'Versión de firmware de SDK',
    connected: 'Conectado',
    notConnected: 'No conectado',
    bleConnected: 'Conexión Bluetooth directa',
    deviceOffline: 'El dispositivo está desconectado'
  },
  fr: {
    setting: "Paramètres",
    featureSetting: "Paramètres de l'appareil",
    commonSetting: "Paramètres généraux",
    name: "Nom de l'appareil",
    deviceService: 'Device service',
    location: "Gérer emplacement",
    memberSet: "Paramètres des boutons",
    share: "Partager l'appareil",
    btGateway: "Passerelle BLE",
    voiceAuth: "Autorisation vocale",
    ifttt: "Automatisation",
    productBaike: 'Introduction du produit',
    firmwareUpgrade: "Mise à jour logiciel",
    firmwareUpdate: 'Mise à jour logiciel',
    more: "Paramètres supplémentaires",
    help: "Aide",
    legalInfo: "Information légale",
    deleteDevice: "Supprimer l'appareil",
    autoUpgrade: "Mettre à jour le logiciel automatiquement",
    checkUpgrade: "Vérifier les mises à jour du micrologiciel",
    security: "Paramètres de sécurité",
    networkInfo: "Informations sur le réseau",
    feedback: "Commentaire",
    timezone: "Fuseau horaire de l’appareil",
    addToDesktop: "Ajouter à l'écran d'accueil",
    open: "Allumé",
    close: "Éteint",
    other: 'Autre',
    multipleKeyShowOnHome: "Nombre de boutons affichés sur la page d’accueil : {0}",
    // 常用设备
    favoriteDevices: "Afficher sur la page d\'accueil Mi Home/Xiaomi Home",
    favoriteCamera: "Passer à la carte grand format",
    favoriteAddDevices: "Ajouter aux Favoris",
    // MHDatePicker
    cancel: "Annuler",
    ok: "Confirmer",
    am: "AM",
    pm: "PM",
    months: "mois",
    days: "jours",
    hours: "heures",
    minutes: "minutes",
    seconds: "secondes",
    month: "mois",
    day: "jours",
    hour: "heure",
    minute: "minute",
    second: "seconde",
    yearUnit: '',
    monthUnit: '',
    dayUnit: '',
    hourUnit: '',
    minuteUnit: '',
    secondUnit: '',
    dateSubTitle: '{0}-{1}-{2}', // 2019-06-03
    time24SubTitle: '{0}:{1}', // 11:43
    time12SubTitle: '{1}:{2} {0}', // 11:43 am
    singleSubTitle: '{0} {1}', // 1 hour | 2 hours
    // 升级相关
    firmwareUpgradeExit: 'Quitter',
    firmwareUpgradeUpdate: 'Mettre à jour',
    firmwareUpgradeLook: 'Jeter un coup d\'œil',
    firmwareUpgradeForceUpdate: "La version actuelle du micrologiciel est trop ancienne. Certaines fonctionnalités peuvent ne pas fonctionner correctement. Mettez à jour la dernière version pour une meilleure expérience",
    firmwareUpgradeForceUpdating: 'Votre appareil est en cours de mise à jour. Réessayez plus tard',
    firmwareUpgradeNew_pre: 'Mise à jour du micrologiciel ',
    firmwareUpgradeNew_sub: ' disponible. Mettre à jour maintenant ?',
    handling: 'En cours d’exécution…',
    error: "Échec de l'opération, veuillez réessayer plus tard",
    createLightGroup: "Créer un groupe d’éclairage (nouveau)",
    manageLightGroup: "Gérer les appareils (nouveau)",
    deleteLightGroup: "Supprimer un groupe d’éclairage",
    deleteCurtainGroup: "Dissolve group",
    linkDevice: "Associer des appareils",
    noSuppurtedLinkageDevice: "Pas d'appareils disponibles pour apparier",
    noSuppurtedLinkageTip: "1. Veuillez vérifier que l'appareil à associer a été ajouté à l'app et attribué à la pièce préférée. \n 2. Veuillez garder l'appareil Bluetooth à associer à proximité de cet appareil, sinon vous ne pourrez pas l'associer.",
    supportedLinkageDevices: "Peut être associé aux appareils suivants",
    linkageDistanceTip: "Veuillez garder l’appareil Bluetooth à une certaine distance de cet appareil, sinon l’association ne fonctionnera pas.",
    linkageRemoveTip: "Si l'appareil Bluetooth a besoin d'être remplacé, veuillez dissocier d'abord.",
    link: "Associer",
    removeLink: "Dissocier",
    linkFail: "Association impossible",
    removeLinkFail: "impossible de supprimer le lien",
    linkConfirm: "Associer à cet appareil maintenant ?",
    removeLinkConfirm: "Dissocier maintenant",
    linking: "Association...",
    linkDeviceBracelet: 'Link band',
    scanDeviceBracelet: 'Scanning for band…',
    scanDeviceBraceletTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    scanDeviceBraceletEmptyTitle: 'No Mi band to link with nearby',
    scanDeviceBraceletEmptyTip1: '1.Please ensure the Bluetooth Broadcasting of the Mi band is enabled.',
    scanDeviceBraceletEmptyTip2: '2.Please ensure the Mi band is close to this device.',
    linkedDeviceBraceletHeaderTip: 'Linked with the following bands',
    availableLinkDeviceBraceletHeaderTip: 'Can be linked with the following bands',
    linkedDeviceBraceletFooterTip: 'If the linked band needs to be changed, please remove link first.',
    availableLinkDeviceBraceletFooterTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    pluginVersion: "Version plugin",
    helpAndFeedback: 'Aide et commentaires',
    offline: 'Hors ligne',
    downloading: 'Téléchargement',
    installing: 'Installation',
    upgradeSuccess: 'Mise à niveau réussie',
    upgradeFailed: 'Échec de la mise à niveau, veuillez réessayer plus tard',
    upgradeTimeout: 'Délai de mise à niveau',
    autoUpgradeInfo: 'Les mises à jour automatiques seront tentées entre {0}.',
    today: 'Aujourd\'hui ',
    tomorrow: 'Demain ',
    currentIsLatestVersion: 'La version actuelle est la dernière.',
    lastestVersion: 'Dernière version:',
    currentVersion: 'Version actuelle:',
    fetchFailed: 'Échoué à obtenir',
    releaseNote: 'Journal de mise à niveau',
    releaseVersionHistory: 'Historique des versions du micrologiciel',
    firmwareAutoUpdate: 'Mise à niveau automatique du micrologiciel',
    autoUpdateDescriptionNote: 'Une fois qu\'un nouveau firmware est détecté, l\'appareil tentera de se mettre à jour automatiquement {0}. L\'appareil doit être inactif pour effectuer la mise à jour. Il n\'y a pas de notifications audio ou lumineuses pendant le processus de mise à jour.',
    updateNow: 'Mettre à jour maintenant',
    requireBelMesh: 'This function requires a Bluetooth Mesh gateway to be used.',
    createCurtainGroup: 'Create a curtain group',
    createCurtainGroupTip: 'Two curtain mates can be combined as a curtain mate group, which can be controlled as a double-sided curtain.',
    act: 'Move',
    create: 'Create',
    chooseCurtainGroupTitle: 'Please select a curtain mate',
    currentDevice: 'this device',
    curtain: 'curtain',
    noCurtainGroupTip: 'The device cannot be grouped at the moment, please add another curtain mate and try again.',
    switchPlugin: 'Plug-in standard',
    defaultPlugin: 'Style par défaut',
    selectDefaultHP: 'Style par défaut',
    stdPluginTitle: 'Standard',
    thirdPluginTitle: 'Traditional',
    stdPluginSubTitle: 'Tap additional features to enter the traditional home page',
    // 多键开关设置
    key: 'Switch',
    keyLeft: 'Left switch',
    keyMiddle: 'Central switch',
    keyRight: 'Right switch',
    keyType: 'Switch type',
    keyName: 'name',
    light: 'light',
    updateIcon: 'Update icon',
    done: 'Done',
    modifyName: 'modify name',
    keyUpdateIconTips: 'After set switch icon with “{0}”，You can call 小爱同学 with “打开{0}”',
    nameHasChars: 'Le nom ne peut pas contenir de caractères spéciaux',
    nameTooLong: 'Le nom de la maison peut comporter jusqu\'à 40 caractères',
    nameIsEmpty: 'Le nom de l\'appareil doit être renseigné',
    nameNotSupportEmoji: 'Le nom de l\'appareil ne peut comprendre d\'émoticônes',
    // 房间
    room: 'Room',
    room_nameInputTips: 'Please enter room name',
    room_nameSuggest: 'Recommended name',
    room_createNew: 'Create a new room',
    room_bedroom: 'Bedroom',
    room_masterBedroom: 'Master bedroom',
    room_secondBedroom: 'Second bedroom',
    room_kitchen: 'Kitchen',
    room_diningRoom: 'Dining room',
    room_washroom: 'Washroom',
    room_childrensRoom: 'Children\'s Room',
    room_office: 'Office',
    room_study: 'Study room',
    room_balcony: 'Balcony',
    room_studio: 'Studio',
    room_bathroom: 'Bathroom',
    room_backyard: 'Backyard',
    room_unassigned: 'Unassigned',
    no_privacy_tip_content: 'Impossible de récupérer la Politique de confidentialité. Veuillez vérifier l’état de votre réseau mobile ou envoyer des commentaires dans l’application Mi Home/Xiaomi Home.',
    moreDeviceInfo: 'Plus d\'informations sur l\'appareil',
    deviceNet: 'Réseau de l\'appareil',
    customizeName: 'Nom du client',
    software: 'Logiciel',
    hardware: 'Matériel',
    bleMeshGateway: 'Passerelle maillée Bluetooth',
    deviceDid: 'ID de l\'appareil',
    deviceSN: 'Numéro de série de l\'appareil',
    mcuVersion: 'Version du micrologiciel MCU',
    sdkVersion: 'Version du micrologiciel SDK',
    connected: 'Connecté',
    notConnected: 'Non connectée',
    bleConnected: 'Connexion Bluetooth directe',
    deviceOffline: 'Appareil hors ligne'
  },
  it: {
    setting: 'Impostazioni',
    featureSetting: 'Impostazioni dispositivo',
    commonSetting: 'Impostazioni generali',
    name: 'Nome dispositivo',
    deviceService: 'Device service',
    location: "Gestisci posizione",
    memberSet: 'Impostazioni pulsante',
    share: 'Condividi dispositivo',
    btGateway: 'Gateway BLE',
    voiceAuth: 'Autorizzazione vocale',
    ifttt: 'Automazione',
    productBaike: 'Introduzione al prodotto',
    firmwareUpgrade: "Aggiornamento firmware",
    firmwareUpdate: 'Aggiornamento firmware',
    more: 'Impostazioni aggiuntive',
    help: 'Guida',
    legalInfo: 'Informazioni legali',
    deleteDevice: 'Rimuovi dispositivo',
    autoUpgrade: 'Aggiorna automaticamente il firmware',
    checkUpgrade: "Controlla aggiornamento firmware",
    security: 'Impostazioni di sicurezza',
    networkInfo: 'Informazioni di rete',
    feedback: 'Feedback',
    timezone: 'Fuso orario del dispositivo',
    addToDesktop: 'Aggiungi a schermata iniziale',
    open: "Attivato",
    close: "Disattivato",
    other: 'Altro',
    multipleKeyShowOnHome: "Numero di pulsanti visualizzati nella pagina iniziale: {0}",
    // 常用设备
    favoriteDevices: "Visualizza sulla pagina iniziale di Mi Home/Xiaomi Home",
    favoriteCamera: "Passare alla scheda grande",
    favoriteAddDevices: "Aggiungi ai Preferiti",
    // MHDatePicker
    cancel: "Annulla",
    ok: "Conferma",
    am: "AM",
    pm: "PM",
    months: "Mesi",
    days: "Giorni",
    hours: "Ore",
    minutes: "Minuti",
    seconds: "Secondi",
    month: "Mese",
    day: "Giorno",
    hour: "Ora",
    minute: "Minuto",
    second: "Secondo",
    yearUnit: '',
    monthUnit: '',
    dayUnit: '',
    hourUnit: '',
    minuteUnit: '',
    secondUnit: '',
    dateSubTitle: '{0}-{1}-{2}', // 2019-06-03
    time24SubTitle: '{0}:{1}', // 11:43
    time12SubTitle: '{1}:{2} {0}', // 11:43 am
    singleSubTitle: '{0} {1}', // 1 hour | 2 hours
    // 升级相关
    firmwareUpgradeExit: 'Esci',
    firmwareUpgradeUpdate: 'Aggiorna',
    firmwareUpgradeLook: 'Guarda',
    firmwareUpgradeForceUpdate: "La versione firmware in uso è obsoleta. Alcune funzioni potrebbero non funzionare correttamente. Aggiorna all'ultima versione per una migliore esperienza.",
    firmwareUpgradeForceUpdating: 'Il dispositivo è in fase di aggiornamento. Riprova più tardi',
    firmwareUpgradeNew_pre: 'Aggiornamento firmware ',
    firmwareUpgradeNew_sub: ' disponibile. Aggiornare adesso?',
    handling: 'In esecuzione…',
    error: "Potrebbe non funzionare, riprova più tardi",
    createLightGroup: "Crea gruppo luci(nuovo)",
    manageLightGroup: "Gestisci dispositivi(nuovo)",
    deleteLightGroup: "Elimina gruppo luci",
    deleteCurtainGroup: "Dissolve group",
    linkDevice: "Collega dispositivi",
    noSuppurtedLinkageDevice: " Nessun dispositivo disponibile adesso per il collegamento ",
    noSuppurtedLinkageTip: "1. Assicurati che il dispositivo da collegare sia stato aggiunto alla app e assegnato alla stanza preferita. \n 2. Mantieni il dispositivo Bluetooth da collegare entro una certa distanza con questo dispositivo, altrimenti non sarà possibile di collegarlo.",
    supportedLinkageDevices: "Può essere collegato ai seguenti dispositivi",
    linkageDistanceTip: "Tieni il dispositivo Bluetooth da collegare entro una certa distanza da questo dispositivo o non sarà possibile collegarlo.",
    linkageRemoveTip: "Se il dispositivo Bluetooth collegato necessita di essere cambiato, prima rimuovi il collegamento",
    link: "Collega",
    removeLink: "Rimuovi il collegamento",
    linkFail: "Impossibile collegare",
    removeLinkFail: "Impossibile rimuovere il collegamento",
    linkConfirm: "Collegare questo dispositivo?",
    removeLinkConfirm: "Rimuovi il collegamento",
    linking: "Collegamento...",
    linkDeviceBracelet: 'Link band',
    scanDeviceBracelet: 'Scanning for band…',
    scanDeviceBraceletTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    scanDeviceBraceletEmptyTitle: 'No Mi band to link with nearby',
    scanDeviceBraceletEmptyTip1: '1.Please ensure the Bluetooth Broadcasting of the Mi band is enabled.',
    scanDeviceBraceletEmptyTip2: '2.Please ensure the Mi band is close to this device.',
    linkedDeviceBraceletHeaderTip: 'Linked with the following bands',
    availableLinkDeviceBraceletHeaderTip: 'Can be linked with the following bands',
    linkedDeviceBraceletFooterTip: 'If the linked band needs to be changed, please remove link first.',
    availableLinkDeviceBraceletFooterTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    pluginVersion: 'Versione plugin',
    helpAndFeedback: 'Guida e feedback',
    offline: 'Disconnesso',
    downloading: 'Download',
    installing: 'Installazione',
    upgradeSuccess: 'Aggiornamento riuscito',
    upgradeFailed: 'Aggiornamento non riuscito, riprova più tardi',
    upgradeTimeout: 'Timeout aggiornamento',
    autoUpgradeInfo: 'Gli aggiornamenti automatici verranno tentati tra {0}.',
    today: 'Oggi ',
    tomorrow: 'Domani ',
    currentIsLatestVersion: 'La versione attuale è l\'ultima.',
    lastestVersion: 'Ultima versione:',
    currentVersion: 'Versione corrente:',
    fetchFailed: 'Impossibile ottenere',
    releaseNote: 'Registro di aggiornamento',
    releaseVersionHistory: 'Cronologia delle versioni del firmware',
    firmwareAutoUpdate: 'Aggiornamento automatico del firmware',
    autoUpdateDescriptionNote: 'Una volta rilevato un nuovo firmware, il dispositivo tenterà di aggiornarsi automaticamente {0}. Il dispositivo deve essere inattivo per eseguire l\'aggiornamento. Non ci sono notifiche audio o luminose durante il processo di aggiornamento.',
    updateNow: 'Aggiorna ora',
    requireBelMesh: 'This function requires a Bluetooth Mesh gateway to be used.',
    createCurtainGroup: 'Create a curtain group',
    createCurtainGroupTip: 'Two curtain mates can be combined as a curtain mate group, which can be controlled as a double-sided curtain.',
    act: 'Move',
    create: 'Create',
    chooseCurtainGroupTitle: 'Please select a curtain mate',
    currentDevice: 'this device',
    curtain: 'curtain',
    noCurtainGroupTip: 'The device cannot be grouped at the moment, please add another curtain mate and try again.',
    switchPlugin: 'Plugin standard',
    defaultPlugin: 'Stile predefinito',
    selectDefaultHP: 'Stile predefinito',
    stdPluginTitle: 'Standard',
    thirdPluginTitle: 'Traditional',
    stdPluginSubTitle: 'Tap additional features to enter the traditional home page',
    // 多键开关设置
    key: 'Switch',
    keyLeft: 'Left switch',
    keyMiddle: 'Central switch',
    keyRight: 'Right switch',
    keyType: 'Switch type',
    keyName: 'name',
    light: 'light',
    updateIcon: 'Update icon',
    done: 'Done',
    modifyName: 'modify name',
    keyUpdateIconTips: 'After set switch icon with “{0}”，You can call 小爱同学 with “打开{0}”',
    nameHasChars: 'Il nome non può contenere caratteri speciali',
    nameTooLong: 'Il nome casa può contenere fino a 40 caratteri',
    nameIsEmpty: 'Il campo Nome dispositivo non può essere vuoto',
    nameNotSupportEmoji: 'Il campo Nome dispositivo non può contenere emoticon',
    // 房间
    room: 'Room',
    room_nameInputTips: 'Please enter room name',
    room_nameSuggest: 'Recommended name',
    room_createNew: 'Create a new room',
    room_bedroom: 'Bedroom',
    room_masterBedroom: 'Master bedroom',
    room_secondBedroom: 'Second bedroom',
    room_kitchen: 'Kitchen',
    room_diningRoom: 'Dining room',
    room_washroom: 'Washroom',
    room_childrensRoom: 'Children\'s Room',
    room_office: 'Office',
    room_study: 'Study room',
    room_balcony: 'Balcony',
    room_studio: 'Studio',
    room_bathroom: 'Bathroom',
    room_backyard: 'Backyard',
    room_unassigned: 'Unassigned',
    no_privacy_tip_content: 'Impossibile acquisire l’Informativa sulla Privacy, controllare la rete mobile o fornire un feedback nell’app Mi Home/Xiaomi Home.',
    moreDeviceInfo: 'Ulteriori informazioni sul dispositivo',
    deviceNet: 'Rete del dispositivo',
    customizeName: 'Nome cliente',
    software: 'Software',
    hardware: 'Hardware',
    bleMeshGateway: 'Gateway Mesh Bluetooth',
    deviceDid: 'ID dispositivo',
    deviceSN: 'SN dispositivo',
    mcuVersion: 'Versione firmware MCU',
    sdkVersion: 'Versione firmware SDK',
    connected: 'Connesso',
    notConnected: 'Non connesso',
    bleConnected: 'Connessione Bluetooth diretta',
    deviceOffline: 'Dispositivo offline'
  },
  de: {
    setting: "Einstellungen",
    featureSetting: "Geräteinstellungen",
    commonSetting: "Allgemeine einstellungen",
    name: "Gerätename",
    deviceService: 'Device service',
    location: "Standort verwalten",
    memberSet: "Schaltflächen-Einstellungen",
    share: "Gerät teilen",
    btGateway: "BLE-Gateway",
    voiceAuth: "Sprachautorisierung",
    ifttt: "Automatisierung",
    productBaike: 'Produkt-Intro',
    firmwareUpgrade: "Firmware-Aktualisierung",
    firmwareUpdate: 'Firmware-Aktualisierung',
    more: "Zusätzliche Einstellungen",
    help: "Hilfe",
    legalInfo: "Rechtsinformation",
    deleteDevice: "Gerät entfernen",
    autoUpgrade: "Firmware automatisch aktualisieren",
    checkUpgrade: "Nach Firmware-Aktualisierung suchen.",
    security: 'Sicherheitseinstellungen',
    networkInfo: 'Netzwerk-Info',
    feedback: 'Feedback',
    timezone: 'Gerätezeitzone',
    addToDesktop: 'Zum Startbildschirm hinzufügen',
    open: "Ein",
    close: "Aus",
    other: 'Sonstiges',
    multipleKeyShowOnHome: "Die Anzahl der Schaltflächen auf der Startseite: {0}",
    // 常用设备
    favoriteDevices: "Auf der Mi Home-/Xiaomi Home-Startseite anzeigen",
    favoriteCamera: "Zu großer Karte wechseln",
    favoriteAddDevices: "Zu Favoriten hinzufügen",
    // MHDatePicker
    cancel: "Abbrechen",
    ok: "Bestätigen",
    am: "AM",
    pm: "PM",
    months: "Monate",
    days: "Tage",
    hours: "Stunden",
    minutes: "Minuten",
    seconds: "Sekunden",
    month: "Monat ",
    day: "Tag ",
    hour: "Stunde ",
    minute: "Minute ",
    second: "Sekunde ",
    yearUnit: '',
    monthUnit: '',
    dayUnit: '',
    hourUnit: '',
    minuteUnit: '',
    secondUnit: '',
    dateSubTitle: '{0}-{1}-{2}', // 2019-06-03
    time24SubTitle: '{0}:{1}', // 11:43
    time12SubTitle: '{1}:{2} {0}', // 11:43 am
    singleSubTitle: '{0} {1}', // 1 hour | 2 hours
    // 升级相关
    firmwareUpgradeExit: 'Beenden',
    firmwareUpgradeUpdate: 'Aktualisieren',
    firmwareUpgradeLook: 'Ansehen',
    firmwareUpgradeForceUpdate: "Die aktuelle Firmware ist möglicherweise zu alt, um einige Funktionen auszuführen. Aktualisieren Sie auf die neueste Version, um eine verbesserte Funktionsausführung zu erzielen.",
    firmwareUpgradeForceUpdating: 'Ihr Gerät wird aktualisiert, versuchen Sie es später erneut',
    firmwareUpgradeNew_pre: 'Firmware-Aktualisierung ',
    firmwareUpgradeNew_sub: ' verfügbar, jetzt aktualisieren?',
    handling: 'Wird ausgeführt...',
    error: "Funktionierte nicht, bitte versuchen Sie es später erneut.",
    createLightGroup: "Lichtgruppe erstellen (neu)",
    manageLightGroup: "Geräte verwalten (neu)",
    deleteLightGroup: "Lichtgruppe auflösen",
    deleteCurtainGroup: "Dissolve group",
    linkDevice: "Geräte verbinden",
    noSuppurtedLinkageDevice: "Derzeit sind keine Geräte zur Verbindung verfügbar",
    noSuppurtedLinkageTip: "1.Bitte stellen Sie sicher, dass das Gerät, mit dem eine Verbindung hergestellt werden soll, zur App hinzugefügt wurde und dem bevorzugten Raum zugewiesen ist. \n 2.Bitte halten Sie das Bluetooth-Gerät, mit dem eine Verbindung hergestellt werden soll, in einem bestimmten Abstand von diesem Gerät, da sonst keine Verbindung möglich ist.",
    supportedLinkageDevices: "Kann mit den folgenden Geräten verbunden werden",
    linkageDistanceTip: "Stellen Sie sicher, dass sich das zu verbindende Bluetooth-Gerät innerhalb einer bestimmten Entfernung zu diesem Gerät befindet, andernfalls kann es nicht verbunden werden.",
    linkageRemoveTip: "Wenn das verbundete Bluetooth-Gerät geändert werden muss, entfernen Sie bitte zuerst den Link.",
    link: "Verbinden",
    removeLink: "Verbindung entfernen",
    linkFail: "Verbindung konnte nicht hergestellt werden",
    removeLinkFail: "Verbindung konnte nicht entfernt werden",
    linkConfirm: "Mit diesem Gerät jetzt verbinden?",
    removeLinkConfirm: "Verbindung jetzt entfernen?",
    linking: "Wird verbunden...",
    linkDeviceBracelet: 'Link band',
    scanDeviceBracelet: 'Scanning for band…',
    scanDeviceBraceletTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    scanDeviceBraceletEmptyTitle: 'No Mi band to link with nearby',
    scanDeviceBraceletEmptyTip1: '1.Please ensure the Bluetooth Broadcasting of the Mi band is enabled.',
    scanDeviceBraceletEmptyTip2: '2.Please ensure the Mi band is close to this device.',
    linkedDeviceBraceletHeaderTip: 'Linked with the following bands',
    availableLinkDeviceBraceletHeaderTip: 'Can be linked with the following bands',
    linkedDeviceBraceletFooterTip: 'If the linked band needs to be changed, please remove link first.',
    availableLinkDeviceBraceletFooterTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    pluginVersion: "Plugin Version",
    helpAndFeedback: "Hilfe und Feedback",
    offline: 'Offline',
    downloading: 'wird heruntergeladen',
    installing: 'Installation',
    upgradeSuccess: 'Upgrade-Erfolg',
    upgradeFailed: 'Upgrade fehlgeschlagen, bitte versuchen Sie es später erneut',
    upgradeTimeout: 'Upgrade-Zeitüberschreitung',
    autoUpgradeInfo: 'Automatisierte Updates werden zwischen {0} versucht.',
    today: 'Heute ',
    tomorrow: 'Morgen ',
    currentIsLatestVersion: 'Die aktuelle Version ist die neueste.',
    lastestVersion: 'Letzte Version:',
    currentVersion: 'Aktuelle Version:',
    fetchFailed: 'Fehler beim Abrufen',
    releaseNote: 'Upgrade-Log',
    releaseVersionHistory: 'Verlauf der Firmware-Version',
    firmwareAutoUpdate: 'Automatisches Firmware-Upgrade',
    autoUpdateDescriptionNote: 'Sobald eine neue Firmware erkannt wird, versucht das Gerät automatisch zu aktualisieren {0}. Das Gerät muss inaktiv sein, um das Update durchzuführen. Während des Aktualisierungsvorgangs gibt es keine Audio- oder Lichtbenachrichtigungen.',
    updateNow: 'Jetzt upgraden',
    requireBelMesh: 'This function requires a Bluetooth Mesh gateway to be used.',
    createCurtainGroup: 'Create a curtain group',
    createCurtainGroupTip: 'Two curtain mates can be combined as a curtain mate group, which can be controlled as a double-sided curtain.',
    act: 'Move',
    create: 'Create',
    chooseCurtainGroupTitle: 'Please select a curtain mate',
    currentDevice: 'this device',
    curtain: 'curtain',
    noCurtainGroupTip: 'The device cannot be grouped at the moment, please add another curtain mate and try again.',
    switchPlugin: 'Standard-Plug-in',
    defaultPlugin: 'Standardstil',
    selectDefaultHP: 'Standardstil',
    stdPluginTitle: 'Standard',
    thirdPluginTitle: 'Traditional',
    stdPluginSubTitle: 'Tap additional features to enter the traditional home page',
    // 多键开关设置
    key: 'Switch',
    keyLeft: 'Left switch',
    keyMiddle: 'Central switch',
    keyRight: 'Right switch',
    keyType: 'Switch type',
    keyName: 'name',
    light: 'light',
    updateIcon: 'Update icon',
    done: 'Done',
    modifyName: 'modify name',
    keyUpdateIconTips: 'After set switch icon with “{0}”，You can call 小爱同学 with “打开{0}”',
    nameHasChars: 'Name darf keine Sonderzeichen enthalten',
    nameTooLong: 'Name des Zuhauses kann bis zu 40 Zeichen enthalten',
    nameIsEmpty: 'Gerätename darf nicht leer sein',
    nameNotSupportEmoji: 'Gerätename darf keine Emoticons enthalten',
    // 房间
    room: 'Room',
    room_nameInputTips: 'Please enter room name',
    room_nameSuggest: 'Recommended name',
    room_createNew: 'Create a new room',
    room_bedroom: 'Bedroom',
    room_masterBedroom: 'Master bedroom',
    room_secondBedroom: 'Second bedroom',
    room_kitchen: 'Kitchen',
    room_diningRoom: 'Dining room',
    room_washroom: 'Washroom',
    room_childrensRoom: 'Children\'s Room',
    room_office: 'Office',
    room_study: 'Study room',
    room_balcony: 'Balcony',
    room_studio: 'Studio',
    room_bathroom: 'Bathroom',
    room_backyard: 'Backyard',
    room_unassigned: 'Unassigned',
    no_privacy_tip_content: 'Die Datenschutzerklärung konnte nicht aufgerufen werden, bitte prüfen Sie das mobile Netzwerk oder hinterlassen Sie Feedback über die Mi Home-App/Xiaomi Home-App.',
    moreDeviceInfo: 'Weitere Geräteinformationen',
    deviceNet: 'Gerätenetzwerk',
    customizeName: 'Benutzerdefinierter Name',
    software: 'Software',
    hardware: 'Hardware',
    bleMeshGateway: 'Bluetooth-Mesh-Gateway',
    deviceDid: 'Geräte-ID',
    deviceSN: 'Geräte-SN',
    mcuVersion: 'MCU-Firmware-Version',
    sdkVersion: 'SDK-Firmware-Version',
    connected: 'Verbunden',
    notConnected: 'Nicht verbunden',
    bleConnected: 'Direkte Bluetooth-Verbindung',
    deviceOffline: 'Gerät ist offline'
  },
  id: {
    setting: 'Pengaturan',
    featureSetting: 'Pengaturan perangkat',
    commonSetting: 'Pengaturan umum',
    name: 'Nama perangkat',
    deviceService: 'Device service',
    location: 'Kelola lokasi',
    memberSet: 'Pengaturan tombol',
    share: 'Bagikan perangkat',
    btGateway: 'Gerbang BLE',
    voiceAuth: 'Otorisasi suara',
    ifttt: 'Otomatisasi',
    productBaike: 'Pengenalan produk',
    firmwareUpgrade: 'Pembaruan firmware',
    firmwareUpdate: 'Pembaruan firmware',
    more: 'Pengaturan tambahan',
    help: 'Bantuan',
    legalInfo: 'Informasi hukum',
    deleteDevice: 'Hapus perangkat',
    autoUpgrade: 'Perbarui firmware secara otomatis',
    checkUpgrade: 'Periksa pembaruan firmware',
    security: 'Pengaturan keamanan',
    networkInfo: 'Pengaturan jaringan',
    feedback: 'Umpan balik',
    timezone: 'Zona waktu perangkat',
    addToDesktop: 'Tambahkan ke layar Beranda',
    open: "Aktif",
    close: "Nonaktif",
    other: 'Lainnya',
    multipleKeyShowOnHome: "Jumlah tombol yang ditunjukkan di halaman beranda: {0}",
    // 常用设备
    favoriteDevices: "Tampilan di beranda Mi Home/Xiaomi Home",
    favoriteCamera: "Ganti ke kartu besar",
    favoriteAddDevices: "Tambahkan ke favorit",
    // MHDatePicker
    cancel: "Batal",
    ok: "Konfirmasi",
    am: "AM",
    pm: "PM",
    months: "bulan",
    days: "hari",
    hours: "jam",
    minutes: "menit",
    seconds: "detik",
    month: "bulan",
    day: "hari ",
    hour: "jam",
    minute: "menit",
    second: "detik",
    yearUnit: '',
    monthUnit: '',
    dayUnit: '',
    hourUnit: '',
    minuteUnit: '',
    secondUnit: '',
    dateSubTitle: '{0}-{1}-{2}', // 2019-06-03
    time24SubTitle: '{0}:{1}', // 11:43
    time12SubTitle: '{1}:{2} {0}', // 11:43 am
    singleSubTitle: '{0} {1}', // 1 hour | 2 hours
    // 升级相关
    firmwareUpgradeExit: 'Keluar',
    firmwareUpgradeUpdate: 'Perbarui',
    firmwareUpgradeLook: 'Lihat',
    firmwareUpgradeForceUpdate: 'Versi firmware saat ini sudah terlalu lama. Beberapa fitur mungkin tidak berfungsi dengan benar. Perbarui ke versi terbaru untuk menikmati pengalaman yang lebih baik.',
    firmwareUpgradeForceUpdating: 'Perangkat sedang diperbarui, coba lagi nanti',
    firmwareUpgradeNew_pre: 'Pembaruan firmware ',
    firmwareUpgradeNew_sub: ' tersedia, perbarui sekarang?',
    handling: 'Menjalankan...',
    error: "Tidak dapat beroperasi, coba lagi nanti",
    createLightGroup: "Buat grup lampu(baru)",
    manageLightGroup: "Kelola perangkat(baru)",
    deleteLightGroup: "Hapus grup lampu",
    deleteCurtainGroup: "Dissolve group",
    linkDevice: "Tautkan perangkat",
    noSuppurtedLinkageDevice: "Tidak ada perangkat yang tersedia untuk ditautkan sekarang",
    noSuppurtedLinkageTip: "1.Pastikan perangkat yang akan ditautkan telah ditambahkan ke aplikasi, dan ditugaskan ke ruang pilihan. \n 2.Harap simpan perangkat Bluetooth yang akan ditautkan dengan jarak tertentu dari perangkat ini, jika tidak maka perangkat ini tidak akan dapat ditautkan.",
    supportedLinkageDevices: "Dapat ditautkan dengan perangkat berikut",
    linkageDistanceTip: "Pastikan perangkat Bluetooth yang akan ditautkan berada dalam jarak tertentu dari perangkat ini, jika tidak, perangkat tidak akan dapat ditautkan.",
    linkageRemoveTip: "Jika perangkat Bluetooth yang ditautkan perlu diubah, harap hapus tautan terlebih dahulu.",
    link: "Tautkan",
    removeLink: "Hapus tautan",
    linkFail: "Tidak dapat menautkan",
    removeLinkFail: "Tidak dapat menghapus tautan",
    linkConfirm: "Tautkan dengan perangkat ini sekarang?",
    removeLinkConfirm: "Hapus tautan sekarang",
    linking: "Menautkan...",
    linkDeviceBracelet: 'Link band',
    scanDeviceBracelet: 'Scanning for band…',
    scanDeviceBraceletTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    scanDeviceBraceletEmptyTitle: 'No Mi band to link with nearby',
    scanDeviceBraceletEmptyTip1: '1.Please ensure the Bluetooth Broadcasting of the Mi band is enabled.',
    scanDeviceBraceletEmptyTip2: '2.Please ensure the Mi band is close to this device.',
    linkedDeviceBraceletHeaderTip: 'Linked with the following bands',
    availableLinkDeviceBraceletHeaderTip: 'Can be linked with the following bands',
    linkedDeviceBraceletFooterTip: 'If the linked band needs to be changed, please remove link first.',
    availableLinkDeviceBraceletFooterTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    pluginVersion: "Versi Plugin",
    helpAndFeedback: "Bantuan & Umpan Balik",
    offline: 'Offline',
    downloading: 'Mengunduh',
    installing: 'Menginstal',
    upgradeSuccess: 'Tingkatkan kesuksesan',
    upgradeFailed: 'Upgrade gagal, coba lagi nanti',
    upgradeTimeout: 'Batas waktu peningkatan',
    autoUpgradeInfo: 'Pembaruan otomatis akan dicoba antara {0}.',
    today: 'Hari ini ',
    tomorrow: 'Besok ',
    currentIsLatestVersion: 'Versi saat ini adalah yang terbaru.',
    lastestVersion: 'Versi terbaru:',
    currentVersion: 'Versi sekarang:',
    fetchFailed: 'Gagal mendapatkan',
    releaseNote: 'Tingkatkan Log',
    releaseVersionHistory: 'Riwayat versi firmware',
    firmwareAutoUpdate: 'Pembaruan firmware otomatis',
    autoUpdateDescriptionNote: 'Setelah mendeteksi firmware baru, perangkat akan mencoba memperbarui secara otomatis pada {0}. Perangkat harus menganggur untuk menyelesaikan pembaruan. Tidak ada gangguan suara dan cahaya selama proses update.',
    updateNow: 'Tingkatkan sekarang',
    requireBelMesh: 'This function requires a Bluetooth Mesh gateway to be used.',
    createCurtainGroup: 'Create a curtain group',
    createCurtainGroupTip: 'Two curtain mates can be combined as a curtain mate group, which can be controlled as a double-sided curtain.',
    act: 'Move',
    create: 'Create',
    chooseCurtainGroupTitle: 'Please select a curtain mate',
    currentDevice: 'this device',
    curtain: 'curtain',
    noCurtainGroupTip: 'The device cannot be grouped at the moment, please add another curtain mate and try again.',
    switchPlugin: 'Plugin standar',
    defaultPlugin: 'Gaya default',
    selectDefaultHP: 'Gaya default',
    stdPluginTitle: 'Standard',
    thirdPluginTitle: 'Traditional',
    stdPluginSubTitle: 'Tap additional features to enter the traditional home page',
    // 多键开关设置
    key: 'Switch',
    keyLeft: 'Left switch',
    keyMiddle: 'Central switch',
    keyRight: 'Right switch',
    keyType: 'Switch type',
    keyName: 'name',
    light: 'light',
    updateIcon: 'Update icon',
    done: 'Done',
    modifyName: 'modify name',
    keyUpdateIconTips: 'After set switch icon with “{0}”，You can call 小爱同学 with “打开{0}”',
    nameHasChars: 'Nama tidak boleh berisi karakter khusus',
    nameTooLong: 'Nama rumah mendukung hingga 40 karakter',
    nameIsEmpty: 'Nama perangkat tidak boleh kosong',
    nameNotSupportEmoji: 'Nama perangkat tidak boleh berisi emoji',
    // 房间
    room: 'Room',
    room_nameInputTips: 'Please enter room name',
    room_nameSuggest: 'Recommended name',
    room_createNew: 'Create a new room',
    room_bedroom: 'Bedroom',
    room_masterBedroom: 'Master bedroom',
    room_secondBedroom: 'Second bedroom',
    room_kitchen: 'Kitchen',
    room_diningRoom: 'Dining room',
    room_washroom: 'Washroom',
    room_childrensRoom: 'Children\'s Room',
    room_office: 'Office',
    room_study: 'Study room',
    room_balcony: 'Balcony',
    room_studio: 'Studio',
    room_bathroom: 'Bathroom',
    room_backyard: 'Backyard',
    room_unassigned: 'Unassigned',
    no_privacy_tip_content: 'Tidak dapat memperoleh Kebijakan Privasi, silakan periksa jaringan seluler atau berikan umpan balik di aplikasi Mi Home/Xiaomi Home.',
    moreDeviceInfo: 'Info perangkat selengkapnya',
    deviceNet: 'Jaringan perangkat',
    customizeName: 'Nama kustom',
    software: 'Perangkat lunak',
    hardware: 'Perangkat keras',
    bleMeshGateway: 'Gateway Mesh Bluetooth',
    deviceDid: 'ID Perangkat',
    deviceSN: 'SN Perangkat',
    mcuVersion: 'Versi firmware MCU',
    sdkVersion: 'Versi firmware SDK',
    connected: 'Terhubung',
    notConnected: 'Tidak terhubung',
    bleConnected: 'Koneksi Bluetooth langsung',
    deviceOffline: 'Perangkat offline'
  },
  pl: {
    setting: 'Ustawienia',
    featureSetting: 'Ustawienia urządzenia',
    commonSetting: 'Ustawienia ogólne',
    name: 'Nazwa urządzenia',
    deviceService: 'Device service',
    location: 'Zarządzaj lokalizacją',
    memberSet: 'Ustawienia przycisku',
    share: 'Udostępnij urządzenie',
    btGateway: 'Bramka BLE',
    voiceAuth: 'Autoryzacja głosu',
    ifttt: 'Automatyzacja',
    productBaike: 'Prezentacja produktu',
    firmwareUpgrade: 'Aktualizacja oprogramowania sprzętowego',
    firmwareUpdate: 'Aktualizacja oprogramowania sprzętowego',
    more: 'Dodatkowe ustawienia',
    help: 'Pomoc',
    legalInfo: 'Informacje prawne',
    deleteDevice: 'Usuń urządzenie',
    autoUpgrade: 'Automatyczna aktualizacja oprogramowania sprzętowego',
    checkUpgrade: 'Sprawdź aktualizacje oprogramowania sprzętowego',
    security: 'Ustawienia bezpieczeństwa',
    networkInfo: 'Informacje o sieci',
    feedback: 'Informacje zwrotne',
    timezone: 'Strefa czasowa urządzenia',
    addToDesktop: 'Dodaj do ekranu głównego',
    open: "Włączone",
    close: "Wyłączone",
    other: 'Inny',
    multipleKeyShowOnHome: "Liczba przycisków pokazana na stronie głównej: {0}",
    // 常用设备
    favoriteDevices: "Wyświetlaj na stronie głównej Mi Home / Xiaomi Home",
    favoriteCamera: "Przełącz na dużą kartę",
    favoriteAddDevices: "Dodaj do ulubionych",
    // MHDatePicker
    cancel: "Anuluj",
    ok: "Potwierdź",
    am: "AM ",
    pm: "PM",
    months: "miesiące",
    days: "dni",
    hours: "godziny",
    minutes: "minuty",
    seconds: "sekundy",
    month: "miesiąc",
    day: "dzień",
    hour: "godzina",
    minute: "minuta",
    second: "sekunda",
    yearUnit: '',
    monthUnit: '',
    dayUnit: '',
    hourUnit: '',
    minuteUnit: '',
    secondUnit: '',
    dateSubTitle: '{0}-{1}-{2}', // 2019-06-03
    time24SubTitle: '{0}:{1}', // 11:43
    time12SubTitle: '{1}:{2} {0}', // 11:43 am
    singleSubTitle: '{0} {1}', // 1 hour | 2 hours
    // 升级相关
    firmwareUpgradeExit: 'Wyjdź',
    firmwareUpgradeUpdate: 'Aktualizuj',
    firmwareUpgradeLook: "Przeglądaj",
    firmwareUpgradeForceUpdate: "Aktualna wersja oprogramowania sprzętowego jest zbyt stara, aby niektóre funkcje działały poprawnie. Zaktualizuj do najnowszej wersji, aby działo sprawniej.",
    firmwareUpgradeForceUpdating: 'Urządzenie jest aktualizowane, spróbuj ponownie później.',
    firmwareUpgradeNew_pre: "Dostępna jest aktualizacja oprogramowania sprzętowego",
    firmwareUpgradeNew_sub: '. Zaktualizować teraz?',
    handling: 'Pracuje…',
    error: 'Spróbuj ponownie',
    createLightGroup: "Utwórz grupę oświetleniową (nowość)",
    manageLightGroup: "Zarządzaj urządzeniami (nowość)",
    deleteLightGroup: "Rozgrupuj grupę oświetleniową",
    deleteCurtainGroup: "Dissolve group",
    linkDevice: "Połącz urządzenia",
    noSuppurtedLinkageDevice: "Brak urządzeń do połączenia",
    noSuppurtedLinkageTip: "1. Upewnij się, że urządzenie do połączenia zostało dodane do aplikacji i przydzielone do preferowanego pomieszczenia.\n2. Trzymaj urządzenie Bluetooth w odpowiedniej odległości. W przeciwnym razie urządzenie nie będzie w stanie nawiązać połączenia.",
    supportedLinkageDevices: "Można połączyć z następującymi urządzeniami",
    linkageDistanceTip: "Trzymaj urządzenie Bluetooth w pewnej odległości od tego urządzenia, w przeciwnym razie nie będzie ono w stanie nawiązać połączenia.",
    linkageRemoveTip: "Usuń najpierw istniejące połączenie, jeśli połączone urządzenie Bluetooth trzeba zmienić.",
    link: "Połącz",
    removeLink: "Usuń połączenie",
    linkFail: "Nie można połączyć",
    removeLinkFail: "Nie można usunąć linku",
    linkConfirm: "Czy połączyć z tym urządzeniem teraz?",
    removeLinkConfirm: "Usuń połączenie",
    linking: "Łączenie...",
    linkDeviceBracelet: 'Link band',
    scanDeviceBracelet: 'Scanning for band…',
    scanDeviceBraceletTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    scanDeviceBraceletEmptyTitle: 'No Mi band to link with nearby',
    scanDeviceBraceletEmptyTip1: '1.Please ensure the Bluetooth Broadcasting of the Mi band is enabled.',
    scanDeviceBraceletEmptyTip2: '2.Please ensure the Mi band is close to this device.',
    linkedDeviceBraceletHeaderTip: 'Linked with the following bands',
    availableLinkDeviceBraceletHeaderTip: 'Can be linked with the following bands',
    linkedDeviceBraceletFooterTip: 'If the linked band needs to be changed, please remove link first.',
    availableLinkDeviceBraceletFooterTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    pluginVersion: "Wersja wtyczki",
    helpAndFeedback: "Pomoc i informacja zwrotna",
    offline: 'Offline',
    downloading: 'Ściąganie',
    installing: 'Instalowanie',
    upgradeSuccess: 'Powodzenie aktualizacji',
    upgradeFailed: 'Uaktualnienie nie powiodło się, spróbuj ponownie później',
    upgradeTimeout: 'Limit czasu aktualizacji',
    autoUpgradeInfo: 'Aktualizacje automatyczne będą podejmowane między {0}.',
    today: 'Dzisiaj ',
    tomorrow: 'Jutro ',
    currentIsLatestVersion: 'Obecna wersja jest najnowsza.',
    lastestVersion: 'Najnowsza wersja:',
    currentVersion: 'Obecna wersja:',
    fetchFailed: 'Nie udało się uzyskać',
    releaseNote: 'Dziennik aktualizacji',
    releaseVersionHistory: 'Historia wersji oprogramowania',
    firmwareAutoUpdate: 'Automatyczna aktualizacja oprogramowania',
    autoUpdateDescriptionNote: 'Po wykryciu nowego oprogramowania układowego urządzenie podejmie próbę automatycznej aktualizacji {0}. Aby przeprowadzić aktualizację, urządzenie musi być nieaktywne. Podczas procesu aktualizacji nie ma powiadomień dźwiękowych ani świetlnych.',
    updateNow: 'Ulepsz teraz',
    requireBelMesh: 'This function requires a Bluetooth Mesh gateway to be used.',
    createCurtainGroup: 'Create a curtain group',
    createCurtainGroupTip: 'Two curtain mates can be combined as a curtain mate group, which can be controlled as a double-sided curtain.',
    act: 'Move',
    create: 'Create',
    chooseCurtainGroupTitle: 'Please select a curtain mate',
    currentDevice: 'this device',
    curtain: 'curtain',
    noCurtainGroupTip: 'The device cannot be grouped at the moment, please add another curtain mate and try again.',
    switchPlugin: 'Standardowa wtyczka',
    defaultPlugin: 'Styl domyślny',
    selectDefaultHP: 'Styl domyślny',
    stdPluginTitle: 'Standard',
    thirdPluginTitle: 'Traditional',
    stdPluginSubTitle: 'Tap additional features to enter the traditional home page',
    // 多键开关设置
    key: 'Switch',
    keyLeft: 'Left switch',
    keyMiddle: 'Central switch',
    keyRight: 'Right switch',
    keyType: 'Switch type',
    keyName: 'name',
    light: 'light',
    updateIcon: 'Update icon',
    done: 'Done',
    modifyName: 'modify name',
    keyUpdateIconTips: 'After set switch icon with “{0}”，You can call 小爱同学 with “打开{0}”',
    nameHasChars: 'Nazwa nie może zawierać znaków specjalnych',
    nameTooLong: 'Nazwa strony głównej obsługuje do 40 znaków',
    nameIsEmpty: 'Nazwa urządzenia nie może być pusta',
    nameNotSupportEmoji: 'Nazwa urządzenia nie może zawierać emotikonów',
    // 房间
    room: 'Room',
    room_nameInputTips: 'Please enter room name',
    room_nameSuggest: 'Recommended name',
    room_createNew: 'Create a new room',
    room_bedroom: 'Bedroom',
    room_masterBedroom: 'Master bedroom',
    room_secondBedroom: 'Second bedroom',
    room_kitchen: 'Kitchen',
    room_diningRoom: 'Dining room',
    room_washroom: 'Washroom',
    room_childrensRoom: 'Children\'s Room',
    room_office: 'Office',
    room_study: 'Study room',
    room_balcony: 'Balcony',
    room_studio: 'Studio',
    room_bathroom: 'Bathroom',
    room_backyard: 'Backyard',
    room_unassigned: 'Unassigned',
    no_privacy_tip_content: 'Nie można pobrać Polityki prywatności, sprawdź sieć mobilną albo przekaż informację zwrotną w aplikacji Mi Home/Xiaomi Home.',
    moreDeviceInfo: 'Więcej informacji o urządzeniu',
    deviceNet: 'Sieć urządzenia',
    customizeName: 'Nazwa niestandardowa',
    software: 'Oprogramowanie',
    hardware: 'Sprzęt',
    bleMeshGateway: 'Brama sieciowa Bluetooth Mesh',
    deviceDid: 'ID urządzenia',
    deviceSN: 'Numer seryjny urządzenia',
    mcuVersion: 'Wersja oprogramowania sprzętowego MCU',
    sdkVersion: 'Wersja oprogramowania sprzętowego SDK',
    connected: 'Połączono',
    notConnected: 'Nie połączono',
    bleConnected: 'Bezpośrednie połączenie Bluetooth',
    deviceOffline: 'Urządzenie jest w trybie offline'
  },
  vi: {
    setting: 'Cài đặt',
    featureSetting: 'Cài đặt thiết bị',
    commonSetting: 'Cài đặt chung',
    name: 'Tên thiết bị',
    deviceService: 'Device service',
    location: 'Quản lí vị trí',
    memberSet: 'Cài đặt nút',
    share: 'Chia sẻ thiết bị',
    btGateway: 'Cổng BLE',
    voiceAuth: 'Cấp quyền bằng giọng nói',
    ifttt: 'Tự động',
    productBaike: 'Bách khoa sản phẩm',
    firmwareUpgrade: 'Kiểm tra bản cập nhật chương trình cơ sở',
    firmwareUpdate: 'Kiểm tra bản cập nhật chương trình cơ sở',
    more: 'Cài đặt khác',
    help: 'Trợ giúp',
    legalInfo: 'Thông tin hợp pháp',
    deleteDevice: 'Xóa thiết bị',
    autoUpgrade: 'Tự động cập nhật chương trình cơ sở',
    checkUpgrade: 'Kiểm tra bản cập nhật chương trình cơ sở',
    security: 'Cài đặt bảo mật',
    networkInfo: 'Thiết lập mạng',
    feedback: 'Phản hồi',
    timezone: 'Múi giờ thiết bị',
    addToDesktop: 'Thêm vào Màn hình chính',
    open: "Bật",
    close: "Tắt",
    other: 'Khác',
    multipleKeyShowOnHome: "Số nút hiển thị trên trang chủ: {0}",
    // 常用设备
    favoriteDevices: "Hiển thị trên trang chủ Mi Home/Xiaomi Home",
    favoriteCamera: "Chuyển sang thẻ lớn",
    favoriteAddDevices: "Thêm vào mục yêu thích",
    // MHDatePicker
    cancel: "Huỷ bỏ",
    ok: "Xác nhận",
    am: "Sáng",
    pm: "Tối",
    months: "Tháng",
    days: "Ngày",
    hours: "Giờ",
    minutes: "Phút",
    seconds: "Giây",
    month: "Tháng",
    day: "Ngày",
    hour: "Giờ",
    minute: "Phút",
    second: "Giây",
    yearUnit: "Năm",
    monthUnit: "Tháng",
    dayUnit: "Ngày",
    hourUnit: "Giờ",
    minuteUnit: "Phút",
    secondUnit: "Giây",
    dateSubTitle: '{0}-{1}-{2}', // 2019-06-03
    time24SubTitle: '{0}:{1}', // 11:43
    time12SubTitle: '{1}:{2} {0}', // 11:43 am
    singleSubTitle: '{0} {1}', // 1 hour | 2 hours
    // 升级相关
    firmwareUpgradeExit: 'Thoát',
    firmwareUpgradeUpdate: 'Cập nhật',
    firmwareUpgradeLook: 'Xem xét',
    firmwareUpgradeForceUpdate: 'Phiên bản chương trình cơ sở hiện tại quá cũ, một số tính năng có thể không hoạt động bình thường. Cập nhật lên phiên bản mới nhất để có trải nghiệm tốt hơn.',
    firmwareUpgradeForceUpdating: 'Thiết bị của bạn đang cập nhật, hãy thử lại sau',
    firmwareUpgradeNew_pre: 'Đã có bản cập nhật chương trình cơ sở ',
    firmwareUpgradeNew_sub: ', cập nhật ngay bây giờ?',
    handling: 'Đang thực hiện',
    error: 'Thử lại',
    createLightGroup: "Tạo nhóm đèn (mới)",
    manageLightGroup: "Quản lý thiết bị (mới)",
    deleteLightGroup: "Hủy nhóm đèn",
    deleteCurtainGroup: "Dissolve group",
    linkDevice: "Liên kết thiết bị",
    noSuppurtedLinkageDevice: "Không tìm thấy thiết bị để liên kết",
    noSuppurtedLinkageTip: "1. Vui lòng đảm bảo thiết bị cần liên kết đã được thêm vào ứng dụng và được chỉ định cho phòng tương ứng theo yêu cầu. \n 2. Vui lòng đặt thiết bị cần liên kết Bluetooth trong phạm vi cho phép của thiết bị để liên kết thành công, nếu không, liên kết sẽ không thể thiết lập.",
    supportedLinkageDevices: "Có thể liên kết với những thiết bị sau",
    linkageDistanceTip: "Vui lòng đặt thiết bị Bluetooth bạn muốn liên kết ở gần thiết bị này. Nếu không thì bạn sẽ không liên kết được.",
    linkageRemoveTip: "Nếu muốn thay đổi thiết bị Bluetooth được liên kết, vui lòng huỷ kết nối liên kết trước.",
    link: "Liên kết",
    removeLink: "Gỡ liên kết",
    linkFail: "Không thể liên kết",
    removeLinkFail: "Kết nối thất bại",
    linkConfirm: "Liên kết ngay với thiết bị này?",
    removeLinkConfirm: "Hủy liên kết ngay",
    linking: "Đang liên kết…",
    linkDeviceBracelet: 'Link band',
    scanDeviceBracelet: 'Scanning for band…',
    scanDeviceBraceletTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    scanDeviceBraceletEmptyTitle: 'No Mi band to link with nearby',
    scanDeviceBraceletEmptyTip1: '1.Please ensure the Bluetooth Broadcasting of the Mi band is enabled.',
    scanDeviceBraceletEmptyTip2: '2.Please ensure the Mi band is close to this device.',
    linkedDeviceBraceletHeaderTip: 'Linked with the following bands',
    availableLinkDeviceBraceletHeaderTip: 'Can be linked with the following bands',
    linkedDeviceBraceletFooterTip: 'If the linked band needs to be changed, please remove link first.',
    availableLinkDeviceBraceletFooterTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    pluginVersion: "Phiên bản Plugin",
    helpAndFeedback: "Trợ giúp và phản hồi",
    offline: 'Ngoại tuyến',
    downloading: 'Đang tải xuống',
    installing: 'Đang cài đặt',
    upgradeSuccess: 'Nâng cấp thành công',
    upgradeFailed: 'Nâng cấp không thành công, vui lòng thử lại sau',
    upgradeTimeout: 'Nâng cấp thời gian chờ',
    autoUpgradeInfo: 'Cập nhật tự động sẽ được cố gắng thực hiện giữa {0}.',
    today: 'Hôm nay ',
    tomorrow: 'Ngày mai ',
    currentIsLatestVersion: 'Phiên bản hiện tại là phiên bản mới nhất.',
    lastestVersion: 'Phiên bản mới nhất:',
    currentVersion: 'Phiên bản hiện tại:',
    fetchFailed: 'Đã nộp để lấy',
    releaseNote: 'Nhật ký nâng cấp',
    releaseVersionHistory: 'Lịch sử phiên bản chương trình cơ sở',
    firmwareAutoUpdate: 'Nâng cấp firmware tự động',
    autoUpdateDescriptionNote: 'Sau khi phát hiện chương trình cơ sở mới, thiết bị sẽ cố gắng cập nhật tự động tại {0}. Thiết bị phải ở chế độ chờ để hoàn tất cập nhật. Không có âm thanh và ánh sáng bị gián đoạn trong quá trình cập nhật.',
    updateNow: 'Nâng cấp ngay bây giờ',
    requireBelMesh: 'This function requires a Bluetooth Mesh gateway to be used.',
    createCurtainGroup: 'Create a curtain group',
    createCurtainGroupTip: 'Two curtain mates can be combined as a curtain mate group, which can be controlled as a double-sided curtain.',
    act: 'Move',
    create: 'Create',
    chooseCurtainGroupTitle: 'Please select a curtain mate',
    currentDevice: 'this device',
    curtain: 'curtain',
    noCurtainGroupTip: 'The device cannot be grouped at the moment, please add another curtain mate and try again.',
    switchPlugin: 'Plugin tiêu chuẩn',
    defaultPlugin: 'Kiểu mặc định',
    selectDefaultHP: 'Kiểu mặc định',
    stdPluginTitle: 'Standard',
    thirdPluginTitle: 'Traditional',
    stdPluginSubTitle: 'Tap additional features to enter the traditional home page',
    // 多键开关设置
    key: 'Switch',
    keyLeft: 'Left switch',
    keyMiddle: 'Central switch',
    keyRight: 'Right switch',
    keyType: 'Switch type',
    keyName: 'name',
    light: 'light',
    updateIcon: 'Update icon',
    done: 'Done',
    modifyName: 'modify name',
    keyUpdateIconTips: 'After set switch icon with “{0}”，You can call 小爱同学 with “打开{0}”',
    nameHasChars: 'Tên không được chứa các ký tự đặc biệt',
    nameTooLong: 'Tên nhà riêng có thể chứa tối đa 40 ký tự',
    nameIsEmpty: 'Không được để trống tên thiết bị',
    nameNotSupportEmoji: 'Tên thiết bị không được chứa biểu tượng cảm xúc',
    // 房间
    room: 'Room',
    room_nameInputTips: 'Please enter room name',
    room_nameSuggest: 'Recommended name',
    room_createNew: 'Create a new room',
    room_bedroom: 'Bedroom',
    room_masterBedroom: 'Master bedroom',
    room_secondBedroom: 'Second bedroom',
    room_kitchen: 'Kitchen',
    room_diningRoom: 'Dining room',
    room_washroom: 'Washroom',
    room_childrensRoom: 'Children\'s Room',
    room_office: 'Office',
    room_study: 'Study room',
    room_balcony: 'Balcony',
    room_studio: 'Studio',
    room_bathroom: 'Bathroom',
    room_backyard: 'Backyard',
    room_unassigned: 'Unassigned',
    no_privacy_tip_content: 'Không tải được Chính sách quyền riêng tư, vui lòng kiểm tra mạng di động hoặc đưa ra phản hồi trong ứng dụng Mi Home/Xiaomi Home.',
    moreDeviceInfo: 'Thêm thông tin thiết bị',
    deviceNet: 'Mạng thiết bị',
    customizeName: 'Tên tùy chỉnh',
    software: 'Phần mềm',
    hardware: 'Phần cứng',
    bleMeshGateway: 'Cổng lưới Bluetooth',
    deviceDid: 'ID thiết bị',
    deviceSN: 'Số sêri thiết bị',
    mcuVersion: 'Phiên bản phần mềm điều khiển MCU',
    sdkVersion: 'Phiên bản phần mềm điều khiển SDK',
    connected: 'Đã kết nối',
    notConnected: 'Chưa kết nối',
    bleConnected: 'Kết nối Bluetooth trực tiếp',
    deviceOffline: 'Thiết bị đang ngoại tuyến'
  },
  ja: {
    setting: '設定',
    featureSetting: 'デバイス設定',
    commonSetting: '全般設定',
    name: 'デバイス名',
    deviceService: 'Device service',
    location: '位置を管理する',
    memberSet: 'ボタン設定',
    share: 'デバイスを共有する',
    btGateway: 'BLE ゲートウェイ',
    voiceAuth: '音声承認',
    ifttt: '自動化',
    productBaike: '製品百科',
    firmwareUpgrade: 'ファームウェア更新する',
    firmwareUpdate: 'ファームウェア更新する',
    more: '詳細設定',
    help: 'ヘルプ',
    legalInfo: '法情報',
    deleteDevice: 'デバイスを削除する',
    autoUpgrade: 'ファームウェアの自動アップグレード',
    checkUpgrade: 'ファームウェア更新プログラムの有無を確認する',
    security: 'セキュリティ設定',
    networkInfo: 'ネットワーク情報',
    feedback: 'フィードバック',
    timezone: 'デバイスタイムゾーン',
    addToDesktop: 'ホーム画面に追加する',
    open: "オン",
    close: "オフ",
    other: 'その他',
    multipleKeyShowOnHome: "ホームページに表示されるボタンの数：{0}",
    // 常用设备
    favoriteDevices: "Mi Home/Xiaomi ホームページに表示する",
    favoriteCamera: "大きなカードに切り替える",
    favoriteAddDevices: "ホームページによく使うデバイスを設定",
    // MHDatePicker
    cancel: "キャンセル",
    ok: "確定",
    am: "午前",
    pm: "午後",
    months: "月",
    days: "日",
    hours: "時間",
    minutes: "分",
    seconds: "秒",
    month: "月",
    day: "日",
    hour: "時間",
    minute: "分",
    second: "秒",
    yearUnit: "年",
    monthUnit: "月",
    dayUnit: "日",
    hourUnit: "時",
    minuteUnit: "分",
    secondUnit: "秒",
    dateSubTitle: '{0}-{1}-{2}', // 2019-06-03
    time24SubTitle: '{0}:{1}', // 11:43
    time12SubTitle: '{1}:{2} {0}', // 11:43 am
    singleSubTitle: '{0} {1}', // 1 hour | 2 hours
    // 升级相关
    firmwareUpgradeExit: '終了',
    firmwareUpgradeUpdate: '更新',
    firmwareUpgradeLook: '調べる',
    firmwareUpgradeForceUpdate: "現在ご使用のファームウェアは旧バージョンのため、一部の機能が正しく作動しない場合があります。より良いエクスペリエンスのために、最新バージョンに更新してください。",
    firmwareUpgradeForceUpdating: "デバイス更新中です。後ほど再試行してください。",
    firmwareUpgradeNew_pre: "ファームウェア更新",
    firmwareUpgradeNew_sub: "使用可能です。今すぐ更新しますか？",
    handling: '実行中',
    error: '再試行する',
    createLightGroup: "照明グループ作成（新）",
    manageLightGroup: "照明グループ管理（新）",
    deleteLightGroup: "照明グループ解除",
    deleteCurtainGroup: "Dissolve group",
    linkDevice: "デバイス連結",
    noSuppurtedLinkageDevice: "連結されているデバイスがありません",
    noSuppurtedLinkageTip: "１．デバイスがアプリにきちんと繋がれていること、お好みのルームに設定されていることを確認してください。このデバイスから一定の範囲内にBluetoothデバイスを置いてください。そうしなければ連結できません。",
    supportedLinkageDevices: "以下のデバイスと連結できます",
    linkageDistanceTip: "このデバイスから一定の範囲内にBluetoothデバイスを置いてください。そうしなければ連結できません。",
    linkageRemoveTip: "連結するBluetoothデバイスを変更したい場合は、初めに同期化を解除してください。",
    link: "連結",
    removeLink: "連結を解除する",
    linkFail: "連結に失敗しました",
    removeLinkFail: "連結の解除に失敗しました",
    linkConfirm: "このデバイスと連結しますか",
    removeLinkConfirm: "連結を解除します",
    linking: "連結中",
    linkDeviceBracelet: 'Link band',
    scanDeviceBracelet: 'Scanning for band…',
    scanDeviceBraceletTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    scanDeviceBraceletEmptyTitle: 'No Mi band to link with nearby',
    scanDeviceBraceletEmptyTip1: '1.Please ensure the Bluetooth Broadcasting of the Mi band is enabled.',
    scanDeviceBraceletEmptyTip2: '2.Please ensure the Mi band is close to this device.',
    linkedDeviceBraceletHeaderTip: 'Linked with the following bands',
    availableLinkDeviceBraceletHeaderTip: 'Can be linked with the following bands',
    linkedDeviceBraceletFooterTip: 'If the linked band needs to be changed, please remove link first.',
    availableLinkDeviceBraceletFooterTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    pluginVersion: "プラグインのバージョン",
    helpAndFeedback: "ヘルプおよびフィードバック",
    offline: 'オフライン',
    downloading: 'ダウンロード中...',
    installing: 'インストール中...',
    upgradeSuccess: '更新が完了しました',
    upgradeFailed: '更新に失敗しました。しばらくしてからもう一度お試しください',
    upgradeTimeout: '更新タイムアウト',
    autoUpgradeInfo: '自動更新は{0}で試行されます',
    today: '今日 ',
    tomorrow: '明日 ',
    currentIsLatestVersion: '現在最新バージョン',
    lastestVersion: '最新バージョン：',
    currentVersion: '現行版：',
    fetchFailed: '失敗します、お待ちください',
    releaseNote: '更新ログ',
    releaseVersionHistory: 'ファームウェアバージョンレコード',
    firmwareAutoUpdate: '自動ファームウェアアップデート',
    autoUpdateDescriptionNote: '新しいファームウェアを検出した後、デバイスは{0}で自動的に更新を試みます。 更新を完了するには、デバイスがアイドル状態である必要があります。 更新プロセス中に音と光の中断はありません。',
    updateNow: 'すぐに更新',
    requireBelMesh: 'This function requires a Bluetooth Mesh gateway to be used.',
    createCurtainGroup: 'Create a curtain group',
    createCurtainGroupTip: 'Two curtain mates can be combined as a curtain mate group, which can be controlled as a double-sided curtain.',
    act: 'Move',
    create: 'Create',
    chooseCurtainGroupTitle: 'Please select a curtain mate',
    currentDevice: 'this device',
    curtain: 'curtain',
    noCurtainGroupTip: 'The device cannot be grouped at the moment, please add another curtain mate and try again.',
    switchPlugin: '標準プラグイン',
    defaultPlugin: 'デフォルトのスタイル',
    selectDefaultHP: 'デフォルトのスタイル',
    stdPluginTitle: 'Standard',
    thirdPluginTitle: 'Traditional',
    stdPluginSubTitle: 'Tap additional features to enter the traditional home page',
    // 多键开关设置
    key: 'Switch',
    keyLeft: 'Left switch',
    keyMiddle: 'Central switch',
    keyRight: 'Right switch',
    keyType: 'Switch type',
    keyName: 'name',
    light: 'light',
    updateIcon: 'Update icon',
    done: 'Done',
    modifyName: 'modify name',
    keyUpdateIconTips: 'After set switch icon with “{0}”，You can call 小爱同学 with “打开{0}”',
    nameHasChars: '名前の中で特殊文字を使用することはできません',
    nameTooLong: '自宅名は 40 文字以内で指定してください',
    nameIsEmpty: 'デバイス名は指定必須です',
    nameNotSupportEmoji: 'デバイス名の中で顔文字を使用することはできません',
    // 房间
    room: 'Room',
    room_nameInputTips: 'Please enter room name',
    room_nameSuggest: 'Recommended name',
    room_createNew: 'Create a new room',
    room_bedroom: 'Bedroom',
    room_masterBedroom: 'Master bedroom',
    room_secondBedroom: 'Second bedroom',
    room_kitchen: 'Kitchen',
    room_diningRoom: 'Dining room',
    room_washroom: 'Washroom',
    room_childrensRoom: 'Children\'s Room',
    room_office: 'Office',
    room_study: 'Study room',
    room_balcony: 'Balcony',
    room_studio: 'Studio',
    room_bathroom: 'Bathroom',
    room_backyard: 'Backyard',
    room_unassigned: 'Unassigned',
    no_privacy_tip_content: 'プライバシーポリシーを取得できませんでした。モバイルネットワークをご確認いただくか、Mi Home/Xiaomi Homeアプリでフィードバックをお寄せください。',
    moreDeviceInfo: 'デバイスの詳細情報',
    deviceNet: 'デバイスネットワーク',
    customizeName: 'カスタム名',
    software: 'ソフトウェア',
    hardware: 'ハードウェア',
    bleMeshGateway: 'Bluetooth メッシュゲートウェイ',
    deviceDid: 'デバイス ID',
    deviceSN: 'デバイスのシリアル番号',
    mcuVersion: 'MCU ファームウェアバージョン',
    sdkVersion: 'SDK ファームウェアバージョン',
    connected: '接続済み',
    notConnected: '未接続',
    bleConnected: 'Bluetooth 直接接続',
    deviceOffline: 'デバイスがオフライン状態です'
  },
  th: {
    setting: 'การตั้งค่า',
    featureSetting: 'การตั้งค่าอุปกรณ์',
    commonSetting: 'การตั้งค่าทั่วไป',
    name: 'ชื่ออุปกรณ์',
    deviceService: 'Device service',
    location: 'จัดการตำแหน่งที่ตั้ง',
    memberSet: 'การตั้งค่าปุ่ม',
    share: 'แชร์อุปกรณ์',
    btGateway: 'เกตเวย์ BLE',
    voiceAuth: 'การอนุญาตด้วยเสียง',
    ifttt: 'ระบบอัตโนมัติ',
    productBaike: 'สารานุกรมของผลิตภัณฑ์',
    firmwareUpgrade: 'การอัปเดตเฟิร์มแวร์',
    firmwareUpdate: 'การอัปเดตเฟิร์มแวร์',
    more: 'การตั้งค่าเพิ่มเติม',
    help: 'ความช่วยเหลือ',
    legalInfo: 'ข้อมูลทางกฎหมาย',
    deleteDevice: 'ลบอุปกรณ์ออก',
    autoUpgrade: 'อัปเดตเฟิร์มแวร์โดยอัตโนมัติ',
    checkUpgrade: 'ตรวจสอบหาการอัปเดตเฟิร์มแวร์',
    security: 'การตั้งค่าความปลอดภัย',
    networkInfo: 'การตั้งค่าเครือข่าย',
    feedback: 'คำติชม',
    timezone: 'โซนเวลาของอุปกรณ์',
    addToDesktop: 'เพิ่มไปยังหน้าโฮม',
    open: "เปิด",
    close: "ปิด",
    other: 'อื่นๆ',
    multipleKeyShowOnHome: "จำนวนปุ่มที่แสดงบนหน้าหลัก: {0}",
    // 常用设备
    favoriteDevices: "แสดงบนหน้าแรกของ Mi Home/Xiaomi Home",
    favoriteCamera: "เปลี่ยนเป็นการ์ดใหญ่",
    favoriteAddDevices: "เพิ่มไปยังรายการโปรด",
    // MHDatePicker
    cancel: "ยกเลิก",
    ok: "ตกลง",
    am: "AM",
    pm: "PM",
    months: "เดือน",
    days: "วัน",
    hours: "ชั่วโมง",
    minutes: "นาที",
    seconds: "วินาที",
    month: "เดือน",
    day: "วัน",
    hour: "ชั่วโมง",
    minute: "นาที",
    second: "วินาที",
    yearUnit: "ปี",
    monthUnit: "เดือน",
    dayUnit: "วัน",
    hourUnit: "ชั่วโมง",
    minuteUnit: "นาที",
    secondUnit: "วินาที",
    dateSubTitle: '{0}-{1}-{2}', // 2019-06-03
    time24SubTitle: '{0}:{1}', // 11:43
    time12SubTitle: '{1}:{2} {0}', // 11:43 am
    singleSubTitle: '{0} {1}', // 1 hour | 2 hours
    // 升级相关
    firmwareUpgradeExit: 'ออก',
    firmwareUpgradeUpdate: 'อัปเดต',
    firmwareUpgradeLook: 'ลองดู',
    firmwareUpgradeForceUpdate: 'เวอร์ชั่นเฟิร์มแวร์ปัจจุบันเก่าไป ฟีเจอร์บางอย่างอาจทำงานไม่ถูกต้อง อัปเดตเป็นเวอร์ชั่นล่าสุดเพื่อประสบการณ์การใช้งานที่ดีขึ้น',
    firmwareUpgradeForceUpdating: 'อุปกรณ์กำลังอัปเดต ลองอีกครั้งในภายหลัง',
    firmwareUpgradeNew_pre: 'เฟิร์มแวร์อัปเดต ',
    firmwareUpgradeNew_sub: ' ใช้ได้แล้ว อัปเดตตอนนี้หรือไม่',
    handling: 'ในการดำเนินการ',
    error: 'ลองอีกครั้ง',
    createLightGroup: "สร้างกลุ่มแสงไฟ(ใหม่)",
    manageLightGroup: "จัดการอุปกรณ์(ใหม่)",
    deleteLightGroup: "สลายกลุ่มแสงไฟ",
    deleteCurtainGroup: "Dissolve group",
    linkDevice: "เชื่อมโยงอุปกรณ์",
    noSuppurtedLinkageDevice: "ขณะนี้ไม่มีอุปกรณ์ให้เชื่อมโยง",
    noSuppurtedLinkageTip: "1. โปรดตรวจสอบให้แน่ใจว่าได้เพิ่มอุปกรณ์ที่จะเชื่อมโยงเข้ากับแอปและกำหนดให้กับห้องที่ต้องการ 2. โปรดวางอุปกรณ์บลูทูธที่ต้องการเชื่อมโยงกับอุปกรณ์นี้ในระยะที่กำหนดมิฉะนั้นอุปกรณ์นั้นจะไม่สามารถเชื่อมโยงได้",
    supportedLinkageDevices: "สามารถเชื่อมโยงกับอุปกรณ์ต่อไปนี้ได้",
    linkageDistanceTip: "โปรดนำอุปกรณ์บลูทูธที่ต้องการเชื่อมโยงไปวางไว้ในระยะที่กำหนดของอุปกรณ์นี้ มิฉะนั้นจะไม่สามารถเชื่อมโยงได้",
    linkageRemoveTip: "หากต้องการเปลี่ยนอุปกรณ์บลูทูธที่เชื่อมโยง โปรดยกเลิกการเชื่อมโยงอันแรกออกก่อน",
    link: "เชื่อมโยง",
    removeLink: "ยกเลิกการเชื่อมโยง",
    linkFail: "ไม่สามารถเชื่อมโยงได้",
    removeLinkFail: "การยกเลิกการเชื่อมโยงล้มเหลว",
    linkConfirm: "เชื่อมโยงกับอุปกรณ์เครื่องนี้ตอนนี้เลยหรือไม่",
    removeLinkConfirm: "ยกเลิกการเชื่อมโยงตอนนี้",
    linking: "กำลังเชื่อมโยง…",
    linkDeviceBracelet: 'Link band',
    scanDeviceBracelet: 'Scanning for band…',
    scanDeviceBraceletTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    scanDeviceBraceletEmptyTitle: 'No Mi band to link with nearby',
    scanDeviceBraceletEmptyTip1: '1.Please ensure the Bluetooth Broadcasting of the Mi band is enabled.',
    scanDeviceBraceletEmptyTip2: '2.Please ensure the Mi band is close to this device.',
    linkedDeviceBraceletHeaderTip: 'Linked with the following bands',
    availableLinkDeviceBraceletHeaderTip: 'Can be linked with the following bands',
    linkedDeviceBraceletFooterTip: 'If the linked band needs to be changed, please remove link first.',
    availableLinkDeviceBraceletFooterTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    pluginVersion: "เวอร์ชั่นปลั๊กอิน",
    helpAndFeedback: "ความช่วยเหลือและคำติชม",
    offline: 'ออฟไลน์',
    downloading: 'กำลังดาวน์โหลด',
    installing: 'กำลังติดตั้ง',
    upgradeSuccess: 'อัปเกรดสำเร็จ',
    upgradeFailed: 'การอัปเกรดล้มเหลว โปรดลองอีกครั้งในภายหลัง',
    upgradeTimeout: 'หมดเวลาอัปเกรด',
    autoUpgradeInfo: 'จะพยายามอัปเดตอัตโนมัติระหว่าง {0}',
    today: 'วันนี้ ',
    tomorrow: 'พรุ่งนี้ ',
    currentIsLatestVersion: 'รุ่นปัจจุบันเป็นรุ่นล่าสุด',
    lastestVersion: 'รุ่นล่าสุด:',
    currentVersion: 'เวอร์ชันปัจจุบัน：',
    fetchFailed: 'ไม่สามารถรับ',
    releaseNote: 'บันทึกการอัพเกรด',
    releaseVersionHistory: 'ประวัติเวอร์ชันเฟิร์มแวร์',
    firmwareAutoUpdate: 'อัพเกรดเฟิร์มแวร์อัตโนมัติ',
    autoUpdateDescriptionNote: 'หลังจากตรวจพบเฟิร์มแวร์ใหม่ อุปกรณ์จะพยายามอัปเดตโดยอัตโนมัติที่ {0} อุปกรณ์จะต้องไม่มีการใช้งานเพื่อให้การอัปเดตเสร็จสมบูรณ์ ไม่มีเสียงและแสงขัดจังหวะระหว่างกระบวนการอัพเดต',
    updateNow: 'อัพเกรดเดี๋ยวนี้',
    requireBelMesh: 'This function requires a Bluetooth Mesh gateway to be used.',
    createCurtainGroup: 'Create a curtain group',
    createCurtainGroupTip: 'Two curtain mates can be combined as a curtain mate group, which can be controlled as a double-sided curtain.',
    act: 'Move',
    create: 'Create',
    chooseCurtainGroupTitle: 'Please select a curtain mate',
    currentDevice: 'this device',
    curtain: 'curtain',
    noCurtainGroupTip: 'The device cannot be grouped at the moment, please add another curtain mate and try again.',
    switchPlugin: 'ปลั๊กอินมาตรฐาน',
    defaultPlugin: 'สไตล์เริ่มต้น',
    selectDefaultHP: 'สไตล์เริ่มต้น',
    stdPluginTitle: 'Standard',
    thirdPluginTitle: 'Traditional',
    stdPluginSubTitle: 'Tap additional features to enter the traditional home page',
    // 多键开关设置
    key: 'Switch',
    keyLeft: 'Left switch',
    keyMiddle: 'Central switch',
    keyRight: 'Right switch',
    keyType: 'Switch type',
    keyName: 'name',
    light: 'light',
    updateIcon: 'Update icon',
    done: 'Done',
    modifyName: 'modify name',
    keyUpdateIconTips: 'After set switch icon with “{0}”，You can call 小爱同学 with “打开{0}”',
    nameHasChars: 'ชื่อไม่สามารถมีอักขระพิเศษ',
    nameTooLong: 'ชื่อบ้านจะต้องมีไม่เกิน 40 อักขระ',
    nameIsEmpty: 'ชื่ออุปกรณ์ไม่สามารถเว้นว่างได้',
    nameNotSupportEmoji: 'ชื่ออุปกรณ์จะต้องไม่มีอีโมติคอน',
    // 房间
    room: 'Room',
    room_nameInputTips: 'Please enter room name',
    room_nameSuggest: 'Recommended name',
    room_createNew: 'Create a new room',
    room_bedroom: 'Bedroom',
    room_masterBedroom: 'Master bedroom',
    room_secondBedroom: 'Second bedroom',
    room_kitchen: 'Kitchen',
    room_diningRoom: 'Dining room',
    room_washroom: 'Washroom',
    room_childrensRoom: 'Children\'s Room',
    room_office: 'Office',
    room_study: 'Study room',
    room_balcony: 'Balcony',
    room_studio: 'Studio',
    room_bathroom: 'Bathroom',
    room_backyard: 'Backyard',
    room_unassigned: 'Unassigned',
    no_privacy_tip_content: 'ไม่สามารถดึงข้อมูลนโยบายความเป็นส่วนตัวได้ โปรดตรวจสอบเครือข่ายมือถือหรือมอบคำติชมในแอป Mi Home/Xiaomi Home',
    moreDeviceInfo: 'ข้อมูลอุปกรณ์เพิ่มเติม',
    deviceNet: 'เครือข่ายอุปกรณ์',
    customizeName: 'ชื่อที่กำหนดเอง',
    software: 'ซอฟต์แวร์',
    hardware: 'ฮาร์ดแวร์',
    bleMeshGateway: 'เกตเวย์โครงข่ายบลูทูธ',
    deviceDid: 'ID อุปกรณ์',
    deviceSN: 'SN ของอุปกรณ์',
    mcuVersion: 'เวอร์ชันเฟิร์มแวร์ MCU',
    sdkVersion: 'เวอร์ชันเฟิร์มแวร์ SDK',
    connected: 'เชื่อมต่อแล้ว',
    notConnected: 'ไม่ได้เชื่อมต่อ',
    bleConnected: 'การเชื่อมต่อบลูทูธทางตรง',
    deviceOffline: 'อุปกรณ์ออฟไลน์'
  },
  tr: {
    setting: 'Ayarlar',
    featureSetting: 'Cihaz ayarları',
    commonSetting: 'Genel ayarlar',
    name: 'Cihaz adı',
    deviceService: 'Device service',
    location: 'Konum yönetimi',
    memberSet: 'Tuş ayarları',
    share: 'Cihazı paylaş',
    btGateway: 'BLE ağ geçidi',
    voiceAuth: 'Sesli yetkilendirme',
    ifttt: 'Otomasyon',
    productBaike: 'Ürün bilgisi',
    firmwareUpgrade: 'Cihaz güncelleştirmeleri',
    firmwareUpdate: 'Cihaz güncelleştirmeleri',
    more: 'Ek ayarlar',
    help: 'Yardım',
    legalInfo: 'Yasal bilgi',
    deleteDevice: 'Cihazı sil',
    autoUpgrade: 'Ürün yazılımını otomatik olarak güncelle',
    checkUpgrade: 'Üretici yazılımı güncelleştirmelerini denetle',
    security: 'Güvenlik ayarları',
    networkInfo: 'Ağ bilgileri',
    feedback: 'Geri bildirimlerim',
    timezone: 'Cihazın saat dilimi',
    addToDesktop: 'Ana ekrana ekleyin',
    open: "Açık",
    close: "Kapalı",
    other: 'Diğer',
    multipleKeyShowOnHome: "Ana sayfada gösterilen düğme sayısı: {0}",
    // 常用设备
    favoriteDevices: "Mi Home/Xiaomi Home ana sayfasında göster",
    favoriteCamera: "Büyük karta geç",
    favoriteAddDevices: "Favorilere ekle",
    // MHDatePicker
    cancel: "İptal ",
    ok: "Onayla",
    am: "AM",
    pm: "PM",
    months: "aylar",
    days: "günler",
    hours: "saatler",
    minutes: "dakikalar",
    seconds: "saniyeler",
    month: "ay",
    day: "gün",
    hour: "saat",
    minute: "dakika",
    second: "saniye",
    yearUnit: "",
    monthUnit: "",
    dayUnit: "",
    hourUnit: "sa. ",
    minuteUnit: "dk.",
    secondUnit: "sn.",
    dateSubTitle: '{0}-{1}-{2}', // 2019-06-03
    time24SubTitle: '{0}:{1}', // 11:43
    time12SubTitle: '{1}:{2} {0}', // 11:43 am
    singleSubTitle: '{0} {1}', // 1 hour | 2 hours
    // 升级相关
    firmwareUpgradeExit: 'Çıkış',
    firmwareUpgradeUpdate: 'Şimdi güncelleştir',
    firmwareUpgradeLook: 'Şimdi göz atın',
    firmwareUpgradeForceUpdate: 'Mevcut ürün yazılımı bazı özellikleri çalıştırmak için çok eski olabilir. Daha iyi bir deneyim için en son sürüme güncelleyin.',
    firmwareUpgradeForceUpdating: 'Cihazınız güncelleştiriliyor, daha sonra yeniden deneyin',
    firmwareUpgradeNew_pre: 'Üretici yazılımı güncelleştirmesi',
    firmwareUpgradeNew_sub: 'kullanılabilir. Şimdi güncelleştir',
    handling: 'İşleniyor…',
    error: 'Kon niet werken, probeer het later opnieuw.',
    createLightGroup: "Işık grubu oluştur (yeni)",
    manageLightGroup: "Cihazları yönet (yeni)",
    deleteLightGroup: "Işık grubunu kaldır",
    deleteCurtainGroup: "Dissolve group",
    linkDevice: "Cihazları bağla",
    noSuppurtedLinkageDevice: "Şu anda bağlantı için cihaz yok",
    noSuppurtedLinkageTip: "1. Lütfen bağlanacak cihazın uygulamaya eklendiğinden ve tercih edilen odaya atandığından emin olun. \n 2.Lütfen bağlanmak için Bluetooth cihazını bu cihaza belirli bir uzaklıkta tutun, aksi takdirde bağlantı kurulamaz. ",
    supportedLinkageDevices: "Aşağıdaki cihazlara bağlanabilir",
    linkageDistanceTip: "Lütfen bağlanmak için Bluetooth cihazını bu cihaza belirli bir uzaklıkta tutun, aksi takdirde bağlantı kurulamaz.",
    linkageRemoveTip: "Bağlı Bluetooth cihazının değiştirilmesi gerekiyorsa, lütfen önce bağlantıyı kaldırın.",
    link: "Bağlantı",
    removeLink: "Bağlantıyı kaldır",
    linkFail: "Bağlanılamadı",
    removeLinkFail: "Bağlantı kaldırılamadı",
    linkConfirm: "Bu cihaz bağlanılsın mı?",
    removeLinkConfirm: "Bağlantıyı şimdi kaldır",
    linking: "Bağlanıyor...",
    linkDeviceBracelet: 'Link band',
    scanDeviceBracelet: 'Scanning for band…',
    scanDeviceBraceletTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    scanDeviceBraceletEmptyTitle: 'No Mi band to link with nearby',
    scanDeviceBraceletEmptyTip1: '1.Please ensure the Bluetooth Broadcasting of the Mi band is enabled.',
    scanDeviceBraceletEmptyTip2: '2.Please ensure the Mi band is close to this device.',
    linkedDeviceBraceletHeaderTip: 'Linked with the following bands',
    availableLinkDeviceBraceletHeaderTip: 'Can be linked with the following bands',
    linkedDeviceBraceletFooterTip: 'If the linked band needs to be changed, please remove link first.',
    availableLinkDeviceBraceletFooterTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    pluginVersion: "Eklenti sürümü",
    helpAndFeedback: "Yardım ve Geri Bildirim",
    offline: 'Çevrimdışı',
    downloading: 'indiriliyor',
    installing: 'yükleme',
    upgradeSuccess: 'Yükseltme başarısı',
    upgradeFailed: 'Yükseltme başarısız oldu, lütfen daha sonra tekrar deneyin',
    upgradeTimeout: 'Yükseltme zaman aşımı',
    autoUpgradeInfo: '{0} tarihleri arasında otomatik güncellemeler denenecek.',
    today: 'Bugün ',
    tomorrow: 'Yarın ',
    currentIsLatestVersion: 'Mevcut sürüm en son sürümdür.',
    lastestVersion: 'En son sürüm:',
    currentVersion: 'Şimdiki versiyonu:',
    fetchFailed: 'alınamadı',
    releaseNote: 'Yükseltme Günlüğü',
    releaseVersionHistory: 'Bellenim sürüm geçmişi',
    firmwareAutoUpdate: 'Otomatik ürün yazılımı yükseltmesi',
    autoUpdateDescriptionNote: 'Yeni bir üretici yazılımı algılandığında, cihaz otomatik olarak güncellemeyi deneyecektir {0}. Güncellemeyi gerçekleştirmek için cihazın etkin olmaması gerekir. Güncelleme işlemi sırasında sesli veya ışıklı bildirimler yoktur.',
    updateNow: 'Hemen Yükselt',
    requireBelMesh: 'This function requires a Bluetooth Mesh gateway to be used.',
    createCurtainGroup: 'Create a curtain group',
    createCurtainGroupTip: 'Two curtain mates can be combined as a curtain mate group, which can be controlled as a double-sided curtain.',
    act: 'Move',
    create: 'Create',
    chooseCurtainGroupTitle: 'Please select a curtain mate',
    currentDevice: 'this device',
    curtain: 'curtain',
    noCurtainGroupTip: 'The device cannot be grouped at the moment, please add another curtain mate and try again.',
    switchPlugin: 'Standart eklenti',
    defaultPlugin: 'Varsayılan stil',
    selectDefaultHP: 'Varsayılan stil',
    stdPluginTitle: 'Standard',
    thirdPluginTitle: 'Traditional',
    stdPluginSubTitle: 'Tap additional features to enter the traditional home page',
    // 多键开关设置
    key: 'Switch',
    keyLeft: 'Left switch',
    keyMiddle: 'Central switch',
    keyRight: 'Right switch',
    keyType: 'Switch type',
    keyName: 'name',
    light: 'light',
    updateIcon: 'Update icon',
    done: 'Done',
    modifyName: 'modify name',
    keyUpdateIconTips: 'After set switch icon with “{0}”，You can call 小爱同学 with “打开{0}”',
    nameHasChars: 'Ad, özel karakterler içeremez',
    nameTooLong: 'Ev adı en fazla 40 karakter içerebilir',
    nameIsEmpty: 'Cihaz adı boş bırakılamaz',
    nameNotSupportEmoji: 'Emoji desteklenmiyor',
    // 房间
    room: 'Room',
    room_nameInputTips: 'Please enter room name',
    room_nameSuggest: 'Recommended name',
    room_createNew: 'Create a new room',
    room_bedroom: 'Bedroom',
    room_masterBedroom: 'Master bedroom',
    room_secondBedroom: 'Second bedroom',
    room_kitchen: 'Kitchen',
    room_diningRoom: 'Dining room',
    room_washroom: 'Washroom',
    room_childrensRoom: 'Children\'s Room',
    room_office: 'Office',
    room_study: 'Study room',
    room_balcony: 'Balcony',
    room_studio: 'Studio',
    room_bathroom: 'Bathroom',
    room_backyard: 'Backyard',
    room_unassigned: 'Unassigned',
    no_privacy_tip_content: 'Gizlilik İlkesi alınamadı, lütfen mobil ağı kontrol edin veya Mi Home/Xiaomi Home uygulamasından geri bildirimde bulunun.',
    moreDeviceInfo: 'Daha fazla cihaz bilgisi',
    deviceNet: 'Cihaz ağı',
    customizeName: 'Özel ad',
    software: 'Yazılım',
    hardware: 'Donanım',
    bleMeshGateway: 'Bluetooth ağ geçidi',
    deviceDid: 'Cihaz Kimliği',
    deviceSN: 'Cihaz Seri Numarası',
    mcuVersion: 'MCU bellenim sürümü',
    sdkVersion: 'SDK ürün yazılımı sürümü',
    connected: 'Bağlı',
    notConnected: 'Bağlı değil',
    bleConnected: 'Doğrudan Bluetooth bağlantısı',
    deviceOffline: 'Cihaz çevrimdışı'
  },
  nl: {
    setting: 'Instellingen',
    featureSetting: 'Apparaatinstellingen',
    commonSetting: 'Algemene instellingen',
    name: 'Apparaatnaam',
    deviceService: 'Device service',
    location: 'Locatiebeheer',
    memberSet: 'Toetsinstellingen',
    share: 'Apparaat delen',
    btGateway: 'BLE-gateway',
    voiceAuth: 'Autorisatie via spraak',
    ifttt: 'Automatisering',
    productBaike: 'Productinfo',
    firmwareUpgrade: 'Updates van het apparaat',
    firmwareUpdate: 'Updates van het apparaat',
    more: 'Extra instellingen',
    help: 'Help',
    legalInfo: 'Legale informatie',
    deleteDevice: 'Apparaat verwijderen',
    autoUpgrade: 'Firmware automatisch bijwerken',
    checkUpgrade: 'Controleren op firmware-updates',
    security: 'Beveiligingsinstellingen',
    networkInfo: 'Netwerkgegevens',
    feedback: 'Mijn feedback',
    timezone: 'Tijdzone van het apparaat',
    addToDesktop: 'Toevoegen aan het startscherm',
    open: "Aan",
    close: "Uit",
    other: 'Overig',
    multipleKeyShowOnHome: "Het aantal knoppen dat op de startpagina wordt getoond: {0}",
    // 常用设备
    favoriteDevices: "Weergeven op Mi Home/Xiaomi Home-startpagina",
    favoriteCamera: "Overschakelen naar de grote kaart",
    favoriteAddDevices: "Aan favorieten toevoegen",
    // MHDatePicker
    cancel: "Annuleren",
    ok: "Bevestigen",
    am: "AM",
    pm: "PM",
    months: "Maanden",
    days: "Dagen",
    hours: "Uren",
    minutes: "Minuten",
    seconds: "Seconden",
    month: "Maand ",
    day: "Dag ",
    hour: "Uur",
    minute: "Minuut",
    second: "Seconde ",
    yearUnit: '',
    monthUnit: '',
    dayUnit: '',
    hourUnit: '',
    minuteUnit: '',
    secondUnit: '',
    dateSubTitle: '{0}-{1}-{2}', // 2019-06-03
    time24SubTitle: '{0}:{1}', // 11:43
    time12SubTitle: '{1}:{2} {0}', // 11:43 am
    singleSubTitle: '{0} {1}', // 1 hour | 2 hours
    // 升级相关
    firmwareUpgradeExit: 'Afsluiten',
    firmwareUpgradeUpdate: 'Nu updaten',
    firmwareUpgradeLook: 'Nu rondkijken',
    firmwareUpgradeForceUpdate: 'De huidige firmware is mogelijk te oud om bepaalde functies uit te voeren. Update naar de nieuwste versie voor een betere ervaring.',
    firmwareUpgradeForceUpdating: 'Uw toestel is aan het updaten, probeer het later nog eens',
    firmwareUpgradeNew_pre: 'Firmware-update',
    firmwareUpgradeNew_sub: 'beschikbaar. Nu updaten',
    handling: 'Verwerken...',
    error: 'Kon niet werken, probeer het later opnieuw',
    createLightGroup: "Lichtgroep aanmaken (nieuw)",
    manageLightGroup: "Apparaten beheren (nieuw)",
    deleteLightGroup: "Lichtgroep opheffen",
    deleteCurtainGroup: "Dissolve group",
    linkDevice: "Apparaten koppelen",
    noSuppurtedLinkageDevice: "Geen apparaten beschikbaar om te verbinden",
    noSuppurtedLinkageTip: "Bevestig het apparaat is verbonden met de app. En is verwezen naar de aanbevolen kamer.\n2. Houd het Bluetooth apparaat binnen bereik, anders zal de verbinding verbroken worden.",
    supportedLinkageDevices: "Kan met de volgende apparaten worden gekoppeld",
    linkageDistanceTip: "Hou het apparaat met Bluetooth waarmee moet worden gekoppeld dicht bij het apparaat, anders mislukt het.",
    linkageRemoveTip: "Als het Bluetooth apparaat moet gewijzigd worden, verwijder dan de verbinding eerst.",
    link: "Koppelen",
    removeLink: "Koppeling verwijderen",
    linkFail: "Koppelen mislukt",
    removeLinkFail: "Kan link niet verwijderen",
    linkConfirm: "Met dit apparaat koppelen?",
    removeLinkConfirm: "Koppeling verwijderen",
    linking: "Koppelen...",
    linkDeviceBracelet: 'Link band',
    scanDeviceBracelet: 'Scanning for band…',
    scanDeviceBraceletTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    scanDeviceBraceletEmptyTitle: 'No Mi band to link with nearby',
    scanDeviceBraceletEmptyTip1: '1.Please ensure the Bluetooth Broadcasting of the Mi band is enabled.',
    scanDeviceBraceletEmptyTip2: '2.Please ensure the Mi band is close to this device.',
    linkedDeviceBraceletHeaderTip: 'Linked with the following bands',
    availableLinkDeviceBraceletHeaderTip: 'Can be linked with the following bands',
    linkedDeviceBraceletFooterTip: 'If the linked band needs to be changed, please remove link first.',
    availableLinkDeviceBraceletFooterTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    pluginVersion: 'Plugin versie',
    helpAndFeedback: 'Help & Feedback',
    offline: 'Offline',
    downloading: 'Downloaden',
    installing: 'Installeren',
    upgradeSuccess: 'Upgrade succes',
    upgradeFailed: 'Upgrade mislukt, probeer het later opnieuw',
    upgradeTimeout: 'Upgrade-time-out',
    autoUpgradeInfo: 'Geautomatiseerde updates zullen worden geprobeerd tussen {0}.',
    today: 'Vandaag ',
    tomorrow: 'Morgen ',
    currentIsLatestVersion: 'De huidige versie is de nieuwste.',
    lastestVersion: 'Laatste versie:',
    currentVersion: 'Huidige versie:',
    fetchFailed: 'Gefaald om te krijgen',
    releaseNote: 'Upgradelogboek',
    releaseVersionHistory: 'Geschiedenis van firmwareversie',
    firmwareAutoUpdate: 'Automatische firmware-upgrade',
    autoUpdateDescriptionNote: 'Zodra er nieuwe firmware is gedetecteerd, probeert het apparaat automatisch te updaten {0}. Het apparaat moet inactief zijn om de update uit te voeren. Er zijn geen audio- of lichtmeldingen tijdens het updateproces.',
    updateNow: 'Waardeer nu op',
    requireBelMesh: 'This function requires a Bluetooth Mesh gateway to be used.',
    createCurtainGroup: 'Create a curtain group',
    createCurtainGroupTip: 'Two curtain mates can be combined as a curtain mate group, which can be controlled as a double-sided curtain.',
    act: 'Move',
    create: 'Create',
    chooseCurtainGroupTitle: 'Please select a curtain mate',
    currentDevice: 'this device',
    curtain: 'curtain',
    noCurtainGroupTip: 'The device cannot be grouped at the moment, please add another curtain mate and try again.',
    switchPlugin: 'Standaard plug-in',
    defaultPlugin: 'Standaardstijl',
    selectDefaultHP: 'Standaardstijl',
    stdPluginTitle: 'Standard',
    thirdPluginTitle: 'Traditional',
    stdPluginSubTitle: 'Tap additional features to enter the traditional home page',
    // 多键开关设置
    key: 'Switch',
    keyLeft: 'Left switch',
    keyMiddle: 'Central switch',
    keyRight: 'Right switch',
    keyType: 'Switch type',
    keyName: 'name',
    light: 'light',
    updateIcon: 'Update icon',
    done: 'Done',
    modifyName: 'modify name',
    keyUpdateIconTips: 'After set switch icon with “{0}”，You can call 小爱同学 with “打开{0}”',
    nameHasChars: 'Naam mag geen speciale tekens bevatten',
    nameTooLong: 'Huisnaam mag maximaal 40 tekens bevatten',
    nameIsEmpty: 'Apparaatnaam mag niet leeg zijn',
    nameNotSupportEmoji: 'Emoji wordt niet ondersteund',
    // 房间
    room: 'Room',
    room_nameInputTips: 'Please enter room name',
    room_nameSuggest: 'Recommended name',
    room_createNew: 'Create a new room',
    room_bedroom: 'Bedroom',
    room_masterBedroom: 'Master bedroom',
    room_secondBedroom: 'Second bedroom',
    room_kitchen: 'Kitchen',
    room_diningRoom: 'Dining room',
    room_washroom: 'Washroom',
    room_childrensRoom: 'Children\'s Room',
    room_office: 'Office',
    room_study: 'Study room',
    room_balcony: 'Balcony',
    room_studio: 'Studio',
    room_bathroom: 'Bathroom',
    room_backyard: 'Backyard',
    room_unassigned: 'Unassigned',
    no_privacy_tip_content: 'Kan het privacybeleid niet ophalen, controleer het mobiele netwerk of geef feedback in de Mi Home/Xiaomi Home-app.',
    moreDeviceInfo: 'Meer apparaatgegevens',
    deviceNet: 'Apparaatnetwerk',
    customizeName: 'Aangepaste naam',
    software: 'Software',
    hardware: 'Hardware',
    bleMeshGateway: 'Bluetooth-Mesh-gateway',
    deviceDid: 'Apparaat-id',
    deviceSN: 'Apparaat-SN',
    mcuVersion: 'MCU-firmwareversie',
    sdkVersion: 'SDK-firmwareversie',
    connected: 'Verbonden',
    notConnected: 'Niet verbonden',
    bleConnected: 'Directe Bluetooth-verbinding',
    deviceOffline: 'Apparaat offline'
  },
  pt: {
    setting: 'Configurações',
    featureSetting: 'Configurações do dispositivo',
    commonSetting: 'Configurações gerais',
    name: 'Nome do dispositivo',
    deviceService: 'Device service',
    location: 'Gerenciamento de localização',
    memberSet: 'Configurações do teclado',
    share: 'Compartilhar dispositivo',
    btGateway: 'Gateway BLE',
    voiceAuth: 'Autorização de voz',
    ifttt: 'Automação',
    productBaike: 'Informações de produto',
    firmwareUpgrade: 'Atualizações de dispositivo',
    firmwareUpdate: 'Atualizações de dispositivo',
    more: 'Configurações adicionais',
    help: 'Ajuda',
    legalInfo: 'Informação legal',
    deleteDevice: 'Excluir dispositivo',
    autoUpgrade: 'Atualizar automaticamente o firmware',
    checkUpgrade: 'Verificar se há atualizações de firmware',
    security: 'Configurações de segurança',
    networkInfo: 'Informações de rede',
    feedback: 'Meu feedback',
    timezone: 'Fuso horário do dispositivo',
    addToDesktop: 'Adicionar à tela de início',
    open: "Ligar",
    close: "Desligar",
    other: 'Outro',
    multipleKeyShowOnHome: "Número de botões exibidos na tela inicial: {0}",
    // 常用设备
    favoriteDevices: "Exibir na página inicial do Mi Home/Xiaomi Home",
    favoriteCamera: "Mudar para o cartão grande",
    favoriteAddDevices: "Adicionar aos favoritos",
    // MHDatePicker
    cancel: "Cancelar",
    ok: "Confirmar",
    am: "Manhã",
    pm: "Tarde",
    months: "meses",
    days: "dias",
    hours: "horas",
    minutes: "minutos",
    seconds: "segundos",
    month: "mês",
    day: "dia",
    hour: "hora",
    minute: "minuto",
    second: "segundo",
    yearUnit: "a",
    monthUnit: "m",
    dayUnit: "d",
    hourUnit: "h",
    minuteUnit: "min",
    secondUnit: "s",
    dateSubTitle: '{0}-{1}-{2}', // 2019-06-03
    time24SubTitle: '{0}:{1}', // 11:43
    time12SubTitle: '{1}:{2} {0}', // 11:43 am
    singleSubTitle: '{0} {1}', // 1 hour | 2 hours
    // 升级相关
    firmwareUpgradeExit: 'Sair',
    firmwareUpgradeUpdate: 'Atualizar agora',
    firmwareUpgradeLook: 'Ver agora',
    firmwareUpgradeForceUpdate: 'O firmware atual pode ser muito antigo para executar alguns recursos. Atualize para a versão mais recente para obter uma melhor experiência.',
    firmwareUpgradeForceUpdating: 'Seu dispositivo está sendo atualizado. Tente novamente mais tarde',
    firmwareUpgradeNew_pre: 'Atualização de firmware',
    firmwareUpgradeNew_sub: 'disponível. Atualizar agora',
    handling: 'Processando...',
    error: 'Não foi possível operar, tente novamente mais tarde',
    createLightGroup: "Criar grupo de lâmpadas (novo)",
    manageLightGroup: "Gerenciar dispositivos (novo)",
    deleteLightGroup: "Desfazer grupo de lâmpadas",
    deleteCurtainGroup: "Dissolve group",
    linkDevice: "Conectar dispositivos",
    noSuppurtedLinkageDevice: "Não há dispositivos disponíveis para conexão agora",
    noSuppurtedLinkageTip: "1.Por favor, verifique se o dispositivo a ser conectado foi adicionado ao aplicativo e se está atribuído à sala preferida.\n2.Mantenha o dispositivo Bluetooth a ser conectado a uma certa distância deste dispositivo. Caso contrário, não será possível se conectar.",
    supportedLinkageDevices: "Podem ser conectados aos seguintes dispositivos",
    linkageDistanceTip: "Mantenha o dispositivo Bluetooth a ser conectado a uma determinada distância deste dispositivo. Caso contrário, não será possível se conectar.",
    linkageRemoveTip: "Se o dispositivo Bluetooth conectado precisar ser alterado, remova a conexão primeiro.",
    link: "Conectar",
    removeLink: "Remover conexão",
    linkFail: "Não foi possível conectar",
    removeLinkFail: "Não foi possível remover a conexão",
    linkConfirm: "Conectar a este dispositivo agora?",
    removeLinkConfirm: "Remover conexão agora",
    linking: "Conectando...",
    linkDeviceBracelet: 'Link band',
    scanDeviceBracelet: 'Scanning for band…',
    scanDeviceBraceletTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    scanDeviceBraceletEmptyTitle: 'No Mi band to link with nearby',
    scanDeviceBraceletEmptyTip1: '1.Please ensure the Bluetooth Broadcasting of the Mi band is enabled.',
    scanDeviceBraceletEmptyTip2: '2.Please ensure the Mi band is close to this device.',
    linkedDeviceBraceletHeaderTip: 'Linked with the following bands',
    availableLinkDeviceBraceletHeaderTip: 'Can be linked with the following bands',
    linkedDeviceBraceletFooterTip: 'If the linked band needs to be changed, please remove link first.',
    availableLinkDeviceBraceletFooterTip: 'Please keep the Mi band within a certain distance of this device, and make sure the band\'s Bluetooth is enabled.',
    pluginVersion: 'Versão do plugin',
    helpAndFeedback: 'Ajuda e feedback',
    offline: 'Desconectado',
    downloading: 'Descargando',
    installing: 'Instalando',
    upgradeSuccess: 'Actualizar o sucesso',
    upgradeFailed: 'A atualização falhou, por favor tente mais tarde',
    upgradeTimeout: 'Tempo de atualização',
    autoUpgradeInfo: 'Actualizações automatizadas serão tentadas entre {0}.',
    today: 'Hoje ',
    tomorrow: 'Amanhã ',
    currentIsLatestVersion: 'A versão atual é a mais recente.',
    lastestVersion: 'A última versão',
    currentVersion: 'Versão atual',
    fetchFailed: 'Não conseguimos',
    releaseNote: 'Actualizar o Rexistro',
    releaseVersionHistory: 'História da versão da Firmware',
    firmwareAutoUpdate: 'Actualização automática do firmware',
    autoUpdateDescriptionNote: 'Uma vez detectado um novo firmware, o dispositivo tentará atualizar automaticamente {0}.O dispositivo deve ser inactivo para realizar a atualização.Não há notificações de áudio ou luz durante o processo de atualização.',
    updateNow: 'Anovar agora',
    requireBelMesh: 'This function requires a Bluetooth Mesh gateway to be used.',
    createCurtainGroup: 'Create a curtain group',
    createCurtainGroupTip: 'Two curtain mates can be combined as a curtain mate group, which can be controlled as a double-sided curtain.',
    act: 'Move',
    create: 'Create',
    chooseCurtainGroupTitle: 'Please select a curtain mate',
    currentDevice: 'this device',
    curtain: 'curtain',
    noCurtainGroupTip: 'The device cannot be grouped at the moment, please add another curtain mate and try again.',
    switchPlugin: 'Plugin padrão',
    defaultPlugin: 'Estilo padrão',
    selectDefaultHP: 'Estilo padrão',
    stdPluginTitle: 'Standard',
    thirdPluginTitle: 'Traditional',
    stdPluginSubTitle: 'Tap additional features to enter the traditional home page',
    // 多键开关设置
    key: 'Switch',
    keyLeft: 'Left switch',
    keyMiddle: 'Central switch',
    keyRight: 'Right switch',
    keyType: 'Switch type',
    keyName: 'name',
    light: 'light',
    updateIcon: 'Update icon',
    done: 'Done',
    modifyName: 'modify name',
    keyUpdateIconTips: 'After set switch icon with “{0}”，You can call 小爱同学 with “打开{0}”',
    nameHasChars: 'O nome não pode ter caracteres especiais',
    nameTooLong: 'O nome da casa pode ter até 40 caracteres',
    nameIsEmpty: 'O nome do dispositivo não pode estar em branco',
    nameNotSupportEmoji: 'Emoji incompatível',
    // 房间
    room: 'Room',
    room_nameInputTips: 'Please enter room name',
    room_nameSuggest: 'Recommended name',
    room_createNew: 'Create a new room',
    room_bedroom: 'Bedroom',
    room_masterBedroom: 'Master bedroom',
    room_secondBedroom: 'Second bedroom',
    room_kitchen: 'Kitchen',
    room_diningRoom: 'Dining room',
    room_washroom: 'Washroom',
    room_childrensRoom: 'Children\'s Room',
    room_office: 'Office',
    room_study: 'Study room',
    room_balcony: 'Balcony',
    room_studio: 'Studio',
    room_bathroom: 'Bathroom',
    room_backyard: 'Backyard',
    room_unassigned: 'Unassigned',
    no_privacy_tip_content: 'Não foi possível adquirir a Política de Privacidade. Verifique a rede móvel ou forneça comentários no aplicativo Mi Home/Xiaomi Home.',
    moreDeviceInfo: 'Mais informações sobre o dispositivo',
    deviceNet: 'Rede do dispositivo',
    customizeName: 'Nome personalizado',
    software: 'Software',
    hardware: 'Hardware',
    bleMeshGateway: 'Gateway de malha Bluetooth',
    deviceDid: 'ID do dispositivo',
    deviceSN: 'Número de série do dispositivo',
    mcuVersion: 'Versão do firmware MCU',
    sdkVersion: 'Versão do firmware SDK',
    connected: 'Conectado',
    notConnected: 'Não conectado',
    bleConnected: 'Conexão Bluetooth direta',
    deviceOffline: 'Dispositivo offline'
  },
  ar: {
    setting: 'الإعدادات',
    featureSetting: 'إعدادات الجهاز',
    commonSetting: 'إعدادات عامة',
    name: 'اسم الجهاز',
    deviceService: 'Device service',
    location: 'إدارة الموقع',
    memberSet: 'إعدادات المفاتيح',
    share: 'مشاركة الجهاز',
    btGateway: 'بوابة البلوتوث منخفض الطاقة (BLE)',
    voiceAuth: 'إذن صوتي',
    ifttt: 'المشاهد الذكية',
    productBaike: 'مقدمة المنتج',
    firmwareUpgrade: 'تحديث البرنامج الثابت',
    firmwareUpdate: 'تحديث البرنامج الثابت',
    more: 'الإعدادات الإضافية',
    help: 'التعليمات',
    legalInfo: 'المعلومات القانونية',
    deleteDevice: 'حذف الجهاز',
    autoUpgrade: 'تحديث البرنامج الثابت تلقائيًا',
    checkUpgrade: 'للتحقق من وجود تحديث للبرنامج الثابت',
    security: 'إعدادات الأمان',
    networkInfo: 'معلومات الشبكة',
    feedback: 'ملاحظات',
    timezone: 'المنطقة الزمنية للجهاز',
    addToDesktop: 'العودة إلى الشاشة الرئيسية',
    open: "قيد التشغيل",
    close: "متوقف",
    other: 'Other',
    multipleKeyShowOnHome: "عدد الأزرار التي تظهر في الصفحة الرئيسية: {0}",
    // 常用设备
    favoriteDevices: "العرض على الصفحة الرئيسية في Mi Home/Xiaomi Home",
    favoriteCamera: "تبديل إلى البطاقة الكبيرة",
    favoriteAddDevices: "إضافة إلى المفضلة",
    // MHDatePicker
    cancel: 'إلغاء',
    ok: 'موافق',
    am: 'صباحاً',
    pm: 'مساءً',
    months: 'شهر',
    days: 'ي',
    hours: 'س',
    minutes: 'د',
    seconds: 'ث',
    month: 'شهر',
    day: 'يوم',
    hour: 'ساعة',
    minute: 'دقيقة',
    second: 'ثانية',
    yearUnit: '',
    monthUnit: '',
    dayUnit: '',
    hourUnit: '',
    minuteUnit: '',
    secondUnit: '',
    dateSubTitle: '{0}-{1}-{2}', // 2019年06月03日
    time24SubTitle: '{0}:{1}', // 11:43
    time12SubTitle: '{1}:{2} {0}', // 上午 11:43
    singleSubTitle: '{0} {1}', // 5 小时
    // 升级相关
    firmwareUpgradeExit: 'الخروج/ تسجيل الخروج',
    firmwareUpgradeUpdate: 'الترقية',
    firmwareUpgradeLook: 'ألقِ نظرة',
    firmwareUpgradeForceUpdate: 'قد يكون البرنامج الثابت الحالي قديمًا جدًا لتشغيل بعض الميزات. قم بالتحديث إلى أحدث إصدار للحصول على تجربة أفضل.',
    firmwareUpgradeForceUpdating: 'جهازك قيد التحديث، أعد المحاولة لاحقًا',
    firmwareUpgradeNew_pre: 'تحديث البرنامج الثابت ',
    firmwareUpgradeNew_sub: ' متوفر. التحديث الآن؟',
    handling: 'جارٍ التشغيل...',
    error: 'تعذر التشغيل، يرجى إعادة المحاولة لاحقًا',
    createLightGroup: 'إنشاء مجموعة إضاءة (جديدة)',
    manageLightGroup: 'إدارة أجهزة (جديدة)',
    deleteLightGroup: 'حل مجموعة الإضاءة',
    deleteCurtainGroup: 'حل مجموعة',
    linkDevice: 'ربط أجهزة',
    noSuppurtedLinkageDevice: 'لا توجد أجهزة متاحة للربط الآن',
    noSuppurtedLinkageTip: '"1.يُرجى التأكد من إضافة الجهاز المراد ربطه إلى التطبيق، وتخصيصه للغرفة المفضلة.' +
      ' 2.يُرجى إبقاء جهاز البلوتوث لربطه ضمن مسافة معينة من هذا الجهاز، وإلا فسيتعذر ربطه."',
    supportedLinkageDevices: 'يمكن ربطه بالأجهزة التالية',
    linkageDistanceTip: 'يُرجى إبقاء جهاز البلوتوث لربطه ضمن مسافة معينة من هذا الجهاز، وإلا فسيتعذر ربطه.',
    linkageRemoveTip: 'إذا تعيّن تغيير جهاز البلوتوث المرتبط، فيُرجى إزالة الرابط أولاً.',
    link: 'رابط',
    removeLink: 'إزالة الرابط',
    linkFail: 'تعذّر الربط',
    removeLinkFail: 'تعذر إزالة الرابط',
    linkConfirm: 'ربط بهذا الجهاز الآن؟',
    removeLinkConfirm: 'إزالة الرابط الآن؟',
    linking: 'جارِ الربط...',
    linkDeviceBracelet: 'ربط سوار',
    scanDeviceBracelet: 'جارٍ البحث عن سوار...',
    scanDeviceBraceletTip: 'يُرجى إبقاء سوار Mi ضمن مسافة معينة من هذا الجهاز، وتأكد من تمكين بلوتوث السوار.',
    scanDeviceBraceletEmptyTitle: 'لا يوجد سوار Mi قريب لربطه',
    scanDeviceBraceletEmptyTip1: '1.يُرجى التأكد من تمكين بث البلوتوث من سوار Mi.',
    scanDeviceBraceletEmptyTip2: '2.يُرجى التأكد من قرب سوار Mi من هذا الجهاز.',
    linkedDeviceBraceletHeaderTip: 'مرتبط بالأسورة التالية',
    availableLinkDeviceBraceletHeaderTip: 'يمكن ربطه بالأسورة التالية',
    linkedDeviceBraceletFooterTip: 'إذا تعيّن تغيير السوار، فيُرجى إزالة الرابط أولاً.',
    availableLinkDeviceBraceletFooterTip: 'يُرجى إبقاء سوار Mi ضمن مسافة معينة من هذا الجهاز، وتأكد من تمكين بلوتوث السوار.',
    pluginVersion: 'إصدار المكوّن الإضافي',
    helpAndFeedback: 'التعليمات والملاحظات',
    offline: 'غير متصل بالإنترنت',
    downloading: 'التنزيل',
    installing: 'جارِ التثبيت',
    upgradeSuccess: 'نجاح الترقية',
    upgradeFailed: 'فشل الترقية، يُرجى إعادة المحاولة لاحقًا',
    upgradeTimeout: 'انتهاء مهلة الترقية',
    autoUpgradeInfo: 'ستتم محاولة القيام بالتحديثات التلقائية بين {0}.',
    today: 'اليوم بين',
    tomorrow: 'غدًا بين',
    currentIsLatestVersion: 'الإصدار الحالي هو أحدث إصدار.',
    lastestVersion: 'أحدث إصدار:',
    currentVersion: 'الإصدار الحالي:',
    fetchFailed: 'فشل الحصول عليه',
    releaseNote: 'سجل الترقية',
    releaseVersionHistory: 'سجل إصدار البرنامج الثابت',
    firmwareAutoUpdate: 'ترقية تلقائية للبرنامج الثابت',
    autoUpdateDescriptionNote: 'فور اكتشاف برنامج ثابت جديد، سيحاول الجهاز التحديث {0} تلقائيًا. يجب أن يكون الجهاز غير نشط لإجراء التحديث. لا توجد إشعارات صوتية أو ضوئية أثناء عملية التحديث.',
    updateNow: 'الترقية الآن',
    requireBelMesh: 'تتطلب هذه الوظيفة استخدام بوابة شبكة البلوتوث.',
    createCurtainGroup: 'إنشاء مجموعة للستارة',
    createCurtainGroupTip: 'يُمكن الجمع بين زوج من الستائر كمجموعة ستائر، والتي يُمكن التحكم بها كستارة مزدوجة الجانب.',
    act: 'نقل',
    create: 'إنشاء',
    chooseCurtainGroupTitle: 'يُرجى تحديد زوج من الستائر',
    currentDevice: 'هذا الجهاز',
    curtain: 'الستارة',
    noCurtainGroupTip: 'لا يُمكن تجميع الجهاز في الوقت الحالي، يُرجى إضافة زوج آخر من الستائر وإعادة المحاولة.',
    switchPlugin: 'مكون إضافي قياسي',
    defaultPlugin: 'الأسلوب الافتراضي',
    selectDefaultHP: 'الأسلوب الافتراضي',
    stdPluginTitle: 'قياسية',
    thirdPluginTitle: 'تقليدية',
    stdPluginSubTitle: 'انقر فوق الميزات الإضافية للدخول إلى الصفحة الرئيسية التقليدية',
    stdGuideDialogTitle: 'تحديث جديد',
    stdGuideDialogSubTitle: 'قم بتحديث الواجهة لتكون أول من يتمتع بتجربة أبسط وأسرع.',
    stdGuideDialogNote: 'للعثور على الواجهة قبل التحديث، انقر فوق "مزيد من الوظائف" بالأسفل.',
    stdGuideDialogButtonOK: 'موافق',
    // 多键开关设置
    key: 'المفتاح',
    keyLeft: 'المفتاح الأيسر',
    keyMiddle: 'المفتاح الأوسط',
    keyRight: 'المفتاح الأيمن',
    keyType: 'نوع المفتاح',
    keyName: 'الاسم',
    light: 'الضوء',
    updateIcon: 'أيقونة التحديث',
    done: 'تم',
    modifyName: 'تعديل الاسم',
    keyUpdateIconTips: 'بعد تعيين أيقونة المفتاح على "{0}"، يمكن الاتصال بـ Xiao Ai Tong Xue باستخدام "Da Kai{0}"”',
    nameHasChars: 'لا يمكن أن يحتوي الاسم على أحرف خاصة',
    nameTooLong: 'يمكن أن يحتوي الاسم على ما يصل إلى 40 حرفًا',
    nameIsEmpty: 'لا يُمكن أن يكون اسم الجهاز فارغًا',
    nameNotSupportEmoji: 'الرموز التعبيرية غير مدعومة',
    // 房间
    room: 'غرفة',
    room_nameInputTips: 'يُرجى إدخال اسم الغرفة',
    room_nameSuggest: 'الاسم الموصى به',
    room_createNew: 'إنشاء غرفة جديدة',
    room_bedroom: 'غرفة النوم',
    room_masterBedroom: 'غرفة النوم الرئيسية',
    room_secondBedroom: 'غرفة النوم الثانية',
    room_kitchen: 'المطبخ',
    room_diningRoom: 'غرفة الطعام',
    room_washroom: 'غرفة الحمام',
    room_childrensRoom: 'غرفة الأطفال',
    room_office: 'المكتب',
    room_study: 'غرفة الدراسة',
    room_balcony: 'الشرفة',
    room_studio: 'الاستوديو',
    room_bathroom: 'الحمام',
    room_backyard: 'الفناء الخلفي',
    room_unassigned: 'غير مخصص',
    no_privacy_tip_content: 'الخصوصية غير متوفرة، يُرجى التحقق من الشبكة أو الاتصال بـ Mijia للإبلاغ عن المشكلة.',
    moreDeviceInfo: 'مزيد من معلومات الجهاز',
    deviceNet: 'شبكة الجهاز',
    customizeName: 'اسم مخصص',
    software: 'البرامج',
    hardware: 'الأجهزة',
    bleMeshGateway: 'بوابة شبكة البلوتوث',
    deviceDid: 'معرِّف الجهاز',
    deviceSN: 'الرقم التسلسلي للجهاز',
    mcuVersion: 'إصدار البرنامج الثابت لـ MCU',
    sdkVersion: 'إصدار البرنامج الثابت لـ SDK',
    connected: 'متصل',
    notConnected: 'غير متصل',
    bleConnected: 'الاتصال بالبلوتوث المباشر',
    deviceOffline: 'Offline'
  },
  he: {
    setting: 'הגדרות',
    featureSetting: 'הגדרות מכשיר',
    commonSetting: 'הגדרות כלליות',
    name: 'שם המכשיר',
    deviceService: 'Device service',
    location: 'נהל מיקום',
    memberSet: 'הגדרות מפתח',
    share: 'שתף מכשיר',
    btGateway: 'שער BLE',
    voiceAuth: 'אישור קולי',
    ifttt: 'אוטומציה',
    productBaike: 'מבוא המוצר',
    firmwareUpgrade: 'עדכון קושחה',
    firmwareUpdate: 'עדכון קושחה',
    more: 'הגדרות נוספות',
    help: 'עזרה',
    legalInfo: 'מידע משפטי',
    deleteDevice: 'מחק מכשיר',
    autoUpgrade: 'עדכן אוטומטית את הקושחה',
    checkUpgrade: 'בדוק אם יש עדכון קושחה',
    security: 'הגדרות אבטחה',
    networkInfo: 'מידע על הרשת',
    feedback: 'משוב',
    timezone: 'אזור הזמן של המכשיר',
    addToDesktop: 'הוסף למסך הבית=',
    open: "דולק",
    close: "כבוי",
    other: 'אחר',
    multipleKeyShowOnHome: "מספר הלחצנים המוצגים בדף הבית: {0}",
    // 常用设备
    favoriteDevices: "הצגה בדף הבית Mi Home/Xiaomi Home",
    favoriteCamera: "החלף לכרטיס הגדול",
    favoriteAddDevices: "הוסף למועדפים",
    // MHDatePicker
    cancel: 'ביטול',
    ok: 'אישור',
    am: 'AM',
    pm: 'PM',
    months: 'חודשים',
    days: 'ימים',
    hours: 'שעות',
    minutes: 'דקות',
    seconds: 'שניות',
    month: 'חודש',
    day: 'יום',
    hour: 'שעה',
    minute: 'דקה',
    second: 'שנייה',
    yearUnit: '',
    monthUnit: '',
    dayUnit: '',
    hourUnit: '',
    minuteUnit: '',
    secondUnit: '',
    dateSubTitle: '{0}-{1}-{2}', // 2019年06月03日
    time24SubTitle: '{0}:{1}', // 11:43
    time12SubTitle: '{1}:{2} {0', // 上午 11:43
    singleSubTitle: '{0} {1}', // 5 小时
    // 升级相关
    firmwareUpgradeExit: 'צא',
    firmwareUpgradeUpdate: 'עדכן',
    firmwareUpgradeLook: 'הסתכל',
    firmwareUpgradeForceUpdate: 'הקושחה הנוכחית עשויה להיות ישנה מדי להפעלת תכונות מסוימות. עדכן לגרסה העדכנית ביותר לחוויה טובה יותר.',
    firmwareUpgradeForceUpdating: 'המכשיר שלך מעודכן, נסה שוב מאוחר יותר',
    firmwareUpgradeNew_pre: 'עדכון קושחה ',
    firmwareUpgradeNew_sub: ' זמין. לעדכן עכשיו?',
    handling: 'פועל...',
    error: 'לא ניתן היה לפעול. נסה שוב מאוחר יותר',
    createLightGroup: 'צור קבוצת אור (חדש)',
    manageLightGroup: 'נהל מכשירים (חדשים)',
    deleteLightGroup: 'פרק קבוצת אור',
    deleteCurtainGroup: 'המס קבוצה',
    linkDevice: 'קישור מכשירים',
    noSuppurtedLinkageDevice: 'אין מכשירים זמינים לקישור כעת',
    noSuppurtedLinkageTip: '"1. ודא שהמכשיר שאליו יש לקשר נוסף לאפליקציה, והוקצה לחדר המועדף.' +
    ' 2. שמור את מכשיר ה- Bluetooth לקשר איתו במרחק מסוים ממכשיר זה, אחרת הוא לא יוכל לקשר."',
    supportedLinkageDevices: 'ניתן לקשר עם המכשירים הבאים',
    linkageDistanceTip: 'שמור את מכשיר ה- Bluetooth שאליו יש לקשר במרחק מסוים ממכשיר זה, אחרת הוא לא יוכל לקשר.',
    linkageRemoveTip: 'אם יש צורך לשנות את מכשיר ה- Bluetooth המקושר, הסר תחילה את הקישור.',
    link: 'קישור',
    removeLink: 'הסר את הקישור',
    linkFail: 'לא ניתן היה לקשר',
    removeLinkFail: 'לא ניתן להסיר את הקישור',
    linkConfirm: 'לקשר למכשיר זה עכשיו?',
    removeLinkConfirm: 'להסיר את הקישור עכשיו?',
    linking: 'מקשר…',
    linkDeviceBracelet: 'קישור צמיד',
    scanDeviceBracelet: 'סורק עבור צמיד…',
    scanDeviceBraceletTip: 'שמור את ה Mi Band במרחק מסוים ממכשיר זה וודא ש- Bluetooth הלהקה מופעל.',
    scanDeviceBraceletEmptyTitle: 'אין Mi Band לקשר איתה בקרבת מקום',
    scanDeviceBraceletEmptyTip1: '1. אנא ודא כי שידור Bluetooth של צמיד Mi מופעל.',
    scanDeviceBraceletEmptyTip2: '2. אנא ודא שצמיד ה- Mi קרוב למכשיר זה.',
    linkedDeviceBraceletHeaderTip: 'מקושר לצמידים הבאים',
    availableLinkDeviceBraceletHeaderTip: 'ניתן לקשר עם הצמידים הבאים',
    linkedDeviceBraceletFooterTip: 'אם צריך לשנות את הצמיד המקושר, הסר תחילה את הקישור.',
    availableLinkDeviceBraceletFooterTip: 'שמור את ה Mi Band במרחק מסוים ממכשיר זה וודא ש- Bluetooth הלהקה מופעל.',
    pluginVersion: 'גרסת תוסף',
    helpAndFeedback: 'עזרה ומשוב',
    offline: 'לא מקוון',
    downloading: 'מוריד',
    installing: 'מתקין',
    upgradeSuccess: 'הצלחת שדרוג',
    upgradeFailed: 'השדרוג נכשל. נסה שוב מאוחר יותר',
    upgradeTimeout: 'הפסקת שדרוג',
    autoUpgradeInfo: 'יתקיים ניסיון לבצע עדכונים אוטומטיים בין {0}.',
    today: 'היום בין השעות',
    tomorrow: 'מחר בין השעות',
    currentIsLatestVersion: 'הגרסה הנוכחית היא העדכנית ביותר.',
    lastestVersion: 'הגרסה העדכנית ביותר:',
    currentVersion: 'גרסה נוכחית:',
    fetchFailed: 'נכשל להשיג',
    releaseNote: 'יומן שדרוג',
    releaseVersionHistory: 'היסטוריית גירסאות הקושחה',
    firmwareAutoUpdate: 'שדרוג קושחה אוטומטי',
    autoUpdateDescriptionNote: 'לאחר שזוהתה קושחה חדשה, המכשיר ינסה לעדכן אוטומטית {0}. המכשיר חייב להיות לא פעיל כדי לבצע את העדכון. אין התראות שמע או אור במהלך תהליך העדכון.',
    updateNow: 'שדרג עכשיו',
    requireBelMesh: 'פונקציה זו דורשת שימוש בשער רשת Bluetooth.',
    createCurtainGroup: 'צור קבוצת וילון',
    createCurtainGroupTip: 'ניתן לשלב שני בני זוג לווילון כקבוצת חבר לווילון, הניתנת לשליטה כווילון דו צדדי.',
    act: 'הזזה',
    create: 'צור',
    chooseCurtainGroupTitle: 'אנא בחר חבר לווילון',
    currentDevice: 'המכשיר הזה',
    curtain: 'וִילוֹן',
    noCurtainGroupTip: 'לא ניתן לקבץ את המכשיר כרגע, אנא הוסף עוד חבר לווילון ונסה שוב.',
    switchPlugin: 'תוסף רגיל',
    defaultPlugin: 'סגנון ברירת המחדל',
    selectDefaultHP: 'סגנון ברירת המחדל',
    stdPluginTitle: 'סטנדרטי',
    thirdPluginTitle: 'מסורתי',
    stdPluginSubTitle: 'הקש על תכונות נוספות כדי להיכנס לדף הבית המסורתי',
    stdGuideDialogTitle: 'עדכון חדש',
    stdGuideDialogSubTitle: 'עדכן את הממשק כדי להיות הראשון שנהנה מחוויה פשוטה ומהירה יותר.',
    stdGuideDialogNote: 'כדי למצוא את הממשק לפני העדכון, הקש על \'עוד פונקציות\' למטה.',
    stdGuideDialogButtonOK: 'אישור',
    // 多键开关设置
    key: 'מתג',
    keyLeft: 'מתג שמאלי',
    keyMiddle: 'מתג מרכזי',
    keyRight: 'מתג ימני',
    keyType: 'סוג המתג',
    keyName: 'שם',
    light: 'תאורה',
    updateIcon: 'עדכן סמל',
    done: 'בוצע',
    modifyName: 'שנה שם',
    keyUpdateIconTips: 'לאחר הגדרת סמל המתג עם "{0}"，ניתן להתקשר ל-Xiao Ai Tong Xue עם "Da Kai{0}"',
    nameHasChars: 'שם לא יכול להכיל תווים מיוחדים',
    nameTooLong: 'שם יכול להכיל עד 40 תווים',
    nameIsEmpty: 'יש למלא שם מכשיר',
    nameNotSupportEmoji: 'אימוגי לא נתמך',
    // 房间
    room: 'חדר',
    room_nameInputTips: 'הזן שם חדר',
    room_nameSuggest: 'שם מומלץ',
    room_createNew: 'צור חדר חדש',
    room_bedroom: 'חדר שינה',
    room_masterBedroom: 'חדר שינה ראשי',
    room_secondBedroom: 'חדר שינה שני',
    room_kitchen: 'מטבח',
    room_diningRoom: 'חדר אוכל',
    room_washroom: 'חדר רחצה',
    room_childrensRoom: 'חדר ילדים',
    room_office: 'משרד',
    room_study: 'חדר עבודה',
    room_balcony: 'מרפסת',
    room_studio: 'סטודיו',
    room_bathroom: 'חדר אמבטיה',
    room_backyard: 'חצר אחורית',
    room_unassigned: 'לא משויך',
    no_privacy_tip_content: '\'פרטיות\' אינה זמינה, בדוק את הרשת או צור קשר עם Mijia כדי לדווח על הבעיה.',
    moreDeviceInfo: 'מידע נוסף על המכשיר',
    deviceNet: 'רשת המכשיר',
    customizeName: 'שם מותאם אישית',
    software: 'תוכנה',
    hardware: 'חומרה',
    bleMeshGateway: 'שער כניסה לאריג Bluetooth',
    deviceDid: 'מזהה המכשיר',
    deviceSN: 'מספר סידורי של המכשיר',
    mcuVersion: 'גרסת קושחה של MCU',
    sdkVersion: 'גרסת קושחה של SDK',
    connected: 'מחובר',
    notConnected: 'לא מחובר',
    bleConnected: 'חיבור Bluetooth ישיר',
    deviceOffline: 'Offline'
  }
});
export default strings;
export { formatString };