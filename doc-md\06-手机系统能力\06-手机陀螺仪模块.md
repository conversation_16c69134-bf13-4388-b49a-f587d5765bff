<a name="module_miot/system"></a>

## miot/system
手机的陀螺仪

**Export**: public  
**Doc_name**: 手机陀螺仪模块  
**Doc_index**: 6  
**Doc_directory**: system  
**Example**  
```js
import {System} from "miot"
import {CompassChangeEvent} from "miot"
...
System.gyroscope.startGyroscope(//interval).then(() => {
    alert(`startGyroscope: ${ JSON.stringify(res) }`);
   })
...
   System.gyroscope.stopGyroscope().then(() => {})
...
```

* [miot/system](#module_miot/system)
    * _static_
        * [.GyroscopeChangeEvent](#module_miot/system.GyroscopeChangeEvent) ⇒ <code>object</code>
    * _inner_
        * [~IGyroscope](#module_miot/system..IGyroscope)
            * [.startGyroscope(interval)](#module_miot/system..IGyroscope+startGyroscope) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
            * [.stopGyroscope()](#module_miot/system..IGyroscope+stopGyroscope) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>


* * *

<a name="module_miot/system.GyroscopeChangeEvent"></a>

### miot/system.GyroscopeChangeEvent ⇒ <code>object</code>
监听陀螺仪数据变化事件。频率根据 wx.startGyroscope() 的 interval 参数。可以使用 wx.stopGyroscope() 停止监听。

**Kind**: static constant of [<code>miot/system</code>](#module_miot/system)  
**Returns**: <code>object</code> - result 包含X、Y、Z轴的角速度  
**Since**: 10043  
**Example**  
```js
GyroscopeChangeEvent.onGyroscopeChange.addListener((result) => {
      console.log(result);
    });
```

* * *

<a name="module_miot/system..IGyroscope"></a>

### miot/system~IGyroscope
**Kind**: inner interface of [<code>miot/system</code>](#module_miot/system)  

* [~IGyroscope](#module_miot/system..IGyroscope)
    * [.startGyroscope(interval)](#module_miot/system..IGyroscope+startGyroscope) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
    * [.stopGyroscope()](#module_miot/system..IGyroscope+stopGyroscope) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>


* * *

<a name="module_miot/system..IGyroscope+startGyroscope"></a>

#### iGyroscope.startGyroscope(interval) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
开始监听陀螺仪数据

**Kind**: instance method of [<code>IGyroscope</code>](#module_miot/system..IGyroscope)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - 成功时：{code:0,message:'success'}  
**Since**: 10043  

| Param | Type | Description |
| --- | --- | --- |
| interval | <code>string</code> | 监听陀螺仪数据回调函数的执行频率。其合法值如下： game 适用于更新游戏的回调频率，在 20ms/次 左右； ui 适用于更新 UI 的回调频率，在 60ms/次 左右； normal 普通的回调频率，在 200ms/次 左右。 |

**Example**  
```js
System.gyroscope.startGyroscope(interval.c).then((res) => {
        alert(`startGyroscope: ${ JSON.stringify(res) }`);
      }).catch((error) => {
        console.log(error);
      });
```

* * *

<a name="module_miot/system..IGyroscope+stopGyroscope"></a>

#### iGyroscope.stopGyroscope() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
停止监听陀螺仪数据

**Kind**: instance method of [<code>IGyroscope</code>](#module_miot/system..IGyroscope)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - 成功时：{code:0,message:'success'}  
**Since**: 10043  
**Example**  
```js
System.gyroscope.stopGyroscope().then((res) => {
          alert(`stopGyroscope: ${ JSON.stringify(res) }`);
        }).catch((error) => {
          console.log(error);
        });
      }
```

* * *

