import { Service } from "miot";
import { Device } from "miot";
import Host from "miot/Host";
import { SD_PRIORITY, TIMELINE_EVENT_TYPE } from "./EventTypeConfig";
const TAG = "CloudVideoCarUtil";


export const CLOUD_VIDEO_STATUS = {
  SUCCESS: 2,
  FAIL: 0,
  EMPTY: 1
}

export default class CloudVideoCarUtil {

  static videoExistMap = new Map(); // 判断视频是否存在
  static videoExistArray = [];
  static cloudVideoDayMap = new Map();
  static alarmEventDayMap = new Map();
  static cloudDayList = [];// 初始化30天
  static dateTime = new Date();
  static videoTimestampRange = [];
  static currentQueryIndex = 0;

  static removeAllDatas() {
    this.videoExistMap.clear();
    this.videoExistArray = [];
    this.cloudDayList = [];
    this.cloudVideoDayMap = new Map();
    this.alarmEventDayMap = new Map();
    this.videoTimestampRange = [];
    this.currentQueryIndex = 0;
  }

  static fetchCloudVideoDataSerials(did, model, isVip) { // 不是vip就要做什么
    console.log("-=-=-=fetchCloudVideoDataSerials================");
    this.videoExistMap = new Map();
    this.videoExistArray = [];
    this.cloudDayList = [];
    this.cloudVideoDayMap = new Map();
    this.alarmEventDayMap = new Map();
    this.videoTimestampRange = [];
    this.currentQueryIndex = 0;

    let counter = 0;
    let date = new Date();
    date.setHours(0);
    date.setMinutes(0);
    date.setSeconds(0, 0);
    let startDayTime = date.getTime();
    let paramArray = [];
    for (let i = 0; i < 5; i++) {
      let jsonObject = {};
      jsonObject.region = Host.locale.language.includes("en") ? "US" : "CN";
      let jsonArray = [];
      for (let i = 0; i < 6; i++) {
        let obj = {};
        let beginTime = startDayTime - (counter) * (24 * 60 * 60 * 1000);
        obj.beginTime = this.getGMT8TimeZone(beginTime);
        obj.endTime = this.getGMT8TimeZone(beginTime + 24 * 60 * 60 * 1000 - 1000);
        this.dateTime.setTime(beginTime);
        let dayItem = {};
        let day = this.dateTime.getDate();
        let month = this.dateTime.getMonth() + 1;
        let year = this.dateTime.getFullYear();
        let week = this.dateTime.getDay();// 星期几  方便得到周几的字串
        dayItem.day = day > 9 ? (`${ day }`) : (`0${ day }`);
        dayItem.month = month > 9 ? (`${ month }`) : (`0${ month }`);
        dayItem.year = `${ year }`;
        dayItem.week = week;
        dayItem.startTimestamp = beginTime;
        dayItem.startTime = beginTime;
        dayItem.endTime = beginTime + 24 * 60 * 60 * 1000 - 1000;
        dayItem.isHasVideo = false;
        dayItem.dateStr = (month > 9 ? `${ month }` : `0${ month }`) + (day > 9 ? `${ day }` : `0${ day }`);

        this.cloudDayList.push(dayItem); // 这里是没有纠正过gmt8时区的
        jsonArray.push(obj);
        counter++;
      }
      let interval = {};
      interval.intervals = jsonArray;
      jsonObject.intervals = interval;
      jsonObject.did = did;
      jsonObject.model = model;
      paramArray.push(jsonObject);
    }
    if (!isVip) { // 不是vip，直接return吧
      // this.dateListCallback && this.dateListCallback();
      this.receivedCallback && this.receivedCallback(CLOUD_VIDEO_STATUS.FAIL);
      return;
    }

    Promise.all(paramArray.map((param) => {
      return Service.callSmartHomeCameraAPI("/common/app/v1/cloud/file/exist", "business.smartcamera", false, param).catch((error) => {
        console.log("+++++++++++++get file exist error",error);
        return JSON.stringify(error);

      });
    }))
      .then((resultArray) => {
        //
        for (let i = 0; i < resultArray.length; i++) {
          let item = resultArray[i];
          if (item instanceof String) {
            // here error
          } else {
            let code = item.code;
            let data = item.data;
            if (code != 0 || data == null || data.filesExist == null) {
              continue;
            }
            data = data.filesExist;
            for (let j = 0; j < data.length; j++) {
              this.videoExistMap.set(this.getLocalTimeZone(data[j].timeStamp), data[j].exist);// key 都还原到本地时间了。
              data[j].exist && this.videoExistArray.push(data[j].timeStamp);
            }
          }
        }
        if (this.videoExistMap.size == 0) {
          this.receivedCallback && this.receivedCallback(CLOUD_VIDEO_STATUS.FAIL);// 请求云存数据为空
          return;
        }
        
        let values = [...this.videoExistMap.values()];
        let hasVideo = false;
        for (let i = 0; i < values.length; i++) {
          if (values[i] == true) {
            hasVideo = true;
            break;
          }
        }
        for (let [key, value] of this.videoExistMap) {
          if (value == true) {
            hasVideo = true;
            break;
          }
        }
        if (!hasVideo) {
          this.receivedCallback && this.receivedCallback(CLOUD_VIDEO_STATUS.EMPTY);// 这么多天 都没有数据。
          return;
        }

        this.queryCloudDayDetails(did, model);

      })
      .catch((error) => {
        // here just nothing
        console.log("errrrrr:", JSON.stringify(error));
        this.receivedCallback && this.receivedCallback(CLOUD_VIDEO_STATUS.FAIL);// 这么多天 都没有数据。
      });
  }

  // 每次拉取一天的数据吧，拉完通知外面。
  static queryCloudDayDetails(did, model) {
    if (this.videoTimestampRange == null || this.videoTimestampRange.length <= 0) { // 刷新dayList
      for (let i = 0; i < this.cloudDayList.length; i++) { // 每一天 其实终止时间。
        let item = this.cloudDayList[i];
        let hasVideo = this.videoExistMap.get(item.endTime);// 本地时间匹配。
        item.hasVideo = hasVideo;
        if (!hasVideo) {
          continue;
        }
        this.videoTimestampRange.push({ startTime: this.cloudDayList[i].startTime, endTime: this.cloudDayList[i].endTime });
      }
      // this.videoTimestampRange.sort((item1, item2) => {
      //   return (item2.startTime - item1.startTime);
      // }); // 降序排列。
      // this.dateListCallback && this.dateListCallback();

    }
    if (this.currentQueryIndex >= this.videoTimestampRange.length) {
      this.receivedCallback && this.receivedCallback(CLOUD_VIDEO_STATUS.SUCCESS,true);
      console.log("all cloud and event data pull success");
      return;
    }
    let subArray = this.videoTimestampRange.slice(this.currentQueryIndex, this.currentQueryIndex + 1);// 每次切一块下来 丢给后面去访问；//某一天的其实终止时间 去获取这一天的video数据，然后再查询下一天的数据。
    this.currentQueryIndex++;
    // 每次加载一天的数据，加载完成了，再循环请求下一天的数据u。
    Promise.all(subArray.map((item) => {
      console.log("queryOneDayVideos-=-=-=-=-=", item);
      return this.queryOneDayVideos(did, model, item.startTime, item.endTime);
    }))
      .then((dayArray) => {
        this.receivedCallback && this.receivedCallback(CLOUD_VIDEO_STATUS.SUCCESS);// 收到了一天的数据，通知外面刷新一下；
        // 然后开始请求下一天的数据；
        this.queryCloudDayDetails(Device.deviceID, Device.model);
      })
      .catch((error) => {
        console.log(TAG, "videos", error);
      });

    // 先拉一天的数据；
    Promise.all(subArray.map((item) => {
      return this.queryOneDayAlarmEvents(did, model, item.startTime, item.endTime);
    }))
      .then((eventArray) => {
        let time2 = new Date().getTime();
        // console.log("time diff:" + (time2 - time1));
        // here merge eventArray && dayVideoArray;
        // 请求到事件列表数据了，通知外面刷新；
        this.receivedCallback && this.receivedCallback(CLOUD_VIDEO_STATUS.SUCCESS);
      })
      .catch((error) => {
        // ignore
        console.log(TAG, "events", error);
      });
  }


  static queryOneDayAlarmEvents(did, model, startTime, endTime) {
    return new Promise((resolve, reject) => {
      this.queryOneDayAlarmEventsLoop(did, model, startTime, endTime, resolve, reject);
    });
  }

  static queryOneDayAlarmEventsLoop(did, model, startTime, endTime, resolve, reject) {
    let param = {};
    param.did = did;
    param.model = model;
    param.eventType = "Default";
    param.beginTime = startTime;
    param.endTime = endTime;
    param.limit = 50;
    param.needMerge = true;
    // console.log("get event list: param", JSON.stringify(param));
    Service.callSmartHomeCameraAPI("/common/app/get/eventlist", "business.smartcamera", false, param)
      .then((result) => {
        let code = result.code;

        if (code != 0) {
          resolve && resolve(`request failed with code:${ code }`);
          return;
        }
        let data = result.data;
        if (data == null) {
          resolve && resolve("data == null");
          return;
        }
        let playUnits = data.thirdPartPlayUnits;
        if (playUnits == null || playUnits.length <= 0) {
          resolve && resolve("playunits == null");
          return;
        }
        let nextTime = data.nextTime;
        let isContinue = data.isContinue;

        let videoItems = [];
        try {
          for (let i = 0; i < playUnits.length; i++) {
            let playUnit = playUnits[i];
            let item = {};
            // item.duration = playUnit.duration;
            item.expireTime = playUnit.expireTime;
            item.createTime = playUnit.createTime;
            item.imgStoreId = playUnit.imgStoreId;
            item.fileId = playUnit.fileId;
            let eventType = playUnit.eventType;
            if (eventType == null) {
              item.eventType = -1;
              item.eventTypes = [];
            } else {
              let eventTypes = eventType.split(":");
              if (eventTypes != null && eventTypes.length >= 2) {
                eventTypes.sort((item1, item2) => {
                  let type1 = item1;
                  let type2 = item2;
                  let number1 = TIMELINE_EVENT_TYPE[type1] || -1;
                  let number2 = TIMELINE_EVENT_TYPE[type2] || -1;
                  return SD_PRIORITY[number2] - SD_PRIORITY[number1];// 逆序排列
                });
                // let eventType1 = eventTypes[0].eventType;
                // let eventTypeNum = TIMELINE_EVENT_TYPE[eventType1] || -1;// 换算成AI
                // item.eventType = eventTypeNum;
                item.eventType = eventType;
                item.eventTypes = eventTypes;
              } else {
                // let eventTypeNum = TIMELINE_EVENT_TYPE[eventType] || -1;// 换算成AI
                item.eventType = eventType;
                item.eventTypes = [eventType];
              }
            }

            // 这里需要找到cloudVideo 并替换里面的eventype。

            // item.isHuman = playUnit.tags && playUnit.tags.includes("people") ? true : false;
            videoItems.push(item);
          }
        } catch (e) {
          console.log("event is error");
        }


        if (videoItems.length <= 0) {
          resolve && resolve("list data error");
          return;
        }
        this.dateTime.setTime(videoItems[0].createTime);
        this.dateTime.setHours(0);
        this.dateTime.setMinutes(0);
        this.dateTime.setSeconds(0, 0);
        let dayBeginTime = this.dateTime.getTime(0);

        let alarmEventDay = this.alarmEventDayMap.get(dayBeginTime);
        if (!alarmEventDay) {
          alarmEventDay = [];
          this.alarmEventDayMap.set(dayBeginTime, alarmEventDay);
        }
        // 后续研究下，此处添加某些数据时会异常
        // alarmEventDay.push(...videoItems);

        if (isContinue) { // 这一天还有数据，需要再去拉取，nextime作为下一次的endTime array里的数据是逆序返回的
          this.queryOneDayAlarmEventsLoop(did, model, startTime, nextTime, resolve, reject);
        } else {
          // alarmEventDay = alarmEventDay.sort((item1, item2) => {
          //   return (item1.createTime - item2.createTime); //把顺序改成顺序排列。
          // });
          // 这里需要更新这天第一个有视频的时间点。
          resolve && resolve(alarmEventDay);// 数据拉取完毕，一天的数据都下来了。
        }


      })
      .catch((error) => {
        resolve && resolve(`request error:${ JSON.stringify(error) }`);

      });

  }

  // static async queryOneDayAlarmEventsLoop(did, model, startTime, endTime, resolve, reject) {
  //   let param = {};
  //   param.did = did;
  //   param.model = model;
  //   param.eventType = "Default";
  //   param.beginTime = startTime;
  //   param.endTime = endTime;
  //   param.limit = 40;
  //   param.needMerge = true;
  //   // console.log("get event list: param", JSON.stringify(param));
  //   let result = await Util.requestEvents(param, null);
  //   // Service.callSmartHomeCameraAPI("/common/app/get/eventlist", "business.smartcamera", false, param)
  //   //   .then((result) => {
  //   if (result == null || result == undefined) {
  //     resolve && resolve("playunits == null");
  //     return;
  //   }
  //   let nextTime = result.nextTime.getTime();
  //   let isContinue = result.hasMore;

  //   let videoItems = result.items.map((item) => {
  //     let eventType = item.type;
  //     // console.log("-=-=-=-=-=-=-", eventType);
  //     if (eventType == null) {
  //       item.eventType = -1;
  //       item.eventTypes = [];
  //     } else {
  //       let eventTypes = eventType.split(":");
  //       if (eventTypes != null && eventTypes.length >= 2) {
  //         eventTypes.sort((item1, item2) => {
  //           let type1 = item1;
  //           let type2 = item2;
  //           let number1 = TIMELINE_EVENT_TYPE[type1] || -1;
  //           let number2 = TIMELINE_EVENT_TYPE[type2] || -1;
  //           return number2 - number1;// 逆序排列
  //         });
  //         // let eventType1 = eventTypes[0].eventType;
  //         // let eventTypeNum = TIMELINE_EVENT_TYPE[eventType1] || -1;// 换算成AI
  //         // item.eventType = eventTypeNum;
  //         item.eventType = eventType;
  //         item.eventTypes = eventTypes;
  //       } else {
  //         // let eventTypeNum = TIMELINE_EVENT_TYPE[eventType] || -1;// 换算成AI
  //         item.eventType = eventType;
  //         item.eventTypes = [eventType];
  //       }
  //     }
  //     return item;
  //   })
  //   if (videoItems.length <= 0) {
  //     resolve && resolve("list data error");
  //     return;
  //   }
  //   this.dateTime.setTime(videoItems[0].createTime);
  //   this.dateTime.setHours(0);
  //   this.dateTime.setMinutes(0);
  //   this.dateTime.setSeconds(0, 0);
  //   let dayBeginTime = this.dateTime.getTime(0);

  //   let alarmEventDay = this.alarmEventDayMap.get(dayBeginTime);
  //   if (!alarmEventDay) {
  //     alarmEventDay = [];
  //     this.alarmEventDayMap.set(dayBeginTime, alarmEventDay);
  //   }
  //   alarmEventDay.push(...videoItems);
  //   if (isContinue) { // 这一天还有数据，需要再去拉取，nextime作为下一次的endTime array里的数据是逆序返回的
  //     await this.queryOneDayAlarmEventsLoop(did, model, startTime, nextTime, resolve, reject);
  //   } else {
  //     // alarmEventDay = alarmEventDay.sort((item1, item2) => {
  //     //   return (item1.createTime - item2.createTime); //把顺序改成顺序排列。
  //     // });
  //     // 这里需要更新这天第一个有视频的时间点。
  //     resolve && resolve(alarmEventDay);// 数据拉取完毕，一天的数据都下来了。
  //   }


  //   // })
  //   // .catch((error) => {
  //   //   resolve && resolve(`request error:${ JSON.stringify(error) }`);

  //   // });

  // }

  static queryOneDayVideos(did, model, startTime, endTime) {
    return new Promise((resolve, reject) => { //
      // 循环拉取一天的数据
      this.queryOneDayVideosLoop(did, model, startTime, endTime, resolve, reject);
    });
  }

  static queryOneDayVideosLoop(did, model, startTime, endTime, resolve, reject) {
    let param = {};
    param.did = did;
    param.model = model;
    param.region = Host.locale.language.includes("en") ? "US" : "CN";
    param.language = Host.locale.language;
    param.beginTime = this.getGMT8TimeZone(startTime);
    param.endTime = this.getGMT8TimeZone(endTime);
    param.limit = 50;// 一次拉取50条数据
    param.url_for_car = true;
    Service.callSmartHomeCameraAPI("/common/app/get/cloudlist", "business.smartcamera", false, param)
    // Service.callSmartHomeCameraAPI("/common/app/car/get/cloudlist", "business.smartcamera", false, param)
      .then((result) => {
        let code = result.code;
        if (code != 0) {
          resolve && resolve(`request failed with code:${ code }`);
          return;
        }
        let data = result.data;
        if (data == null) {
          resolve && resolve("data == null");
          return;
        }
        let playUnits = data.thirdPartPlayUnits;
        if (playUnits == null || playUnits.length <= 0) {
          console.log("reqeust cloudVideo error: playUnits null");
          resolve && resolve("playunits == null");
          return;
        }
        let nextTime = this.getLocalTimeZone(data.nextTime);// 服务器返回的时间 要切换成本地的时间。
        let isContinue = data.isContinue;

        let videoItems = [];
        try {
          for (let i = 0; i < playUnits.length; i++) {
            let playUnit = playUnits[i];
            let item = {};
            item.duration = playUnit.duration;
            item.expireTime = playUnit.expireTime;

            item.startTime = playUnit.createTime;
            item.endTime = playUnit.createTime + playUnit.duration * 1000;
            item.imgStoreId = playUnit.imgStoreId;
            item.fileId = playUnit.fileId;
            item.isHuman = playUnit.tags && playUnit.tags.includes("people") ? true : false;
            item.images = playUnit.carOffsetImgStoreIdList;
            videoItems.push(item);
          }
        } catch (e) {
          console.log("video error:",e)
        }

        if (videoItems.length <= 0) {
          console.log("reqeust cloudVideo error: listdata  null");

          resolve && resolve("list data error");
          return;
        }
        this.dateTime.setTime(videoItems[0].startTime); //开始时间肯定是在当天的  endTime有可能超过了
        this.dateTime.setHours(0);
        this.dateTime.setMinutes(0);
        this.dateTime.setSeconds(0, 0);
        let dayBeginTime = this.dateTime.getTime();
        
        if (nextTime > 0) { // 数据合法性问题
          let cloudVideoDay = this.cloudVideoDayMap.get(dayBeginTime);
          if (!cloudVideoDay) {
            cloudVideoDay = [];
            this.cloudVideoDayMap.set(dayBeginTime, cloudVideoDay);
          }
          cloudVideoDay.push(...videoItems);
          if (isContinue) { // 这一天还有数据，需要再去拉取，nextime作为下一次的endTime array里的数据是逆序返回的
            this.queryOneDayVideosLoop(did, model, startTime, nextTime, resolve, reject);
          } else {
            // cloudVideoDay = cloudVideoDay.sort((item1, item2) => {
            //   return (item1.createTime - item2.createTime); //把顺序改成顺序排列。
            // });
            // 查询完毕一天的数据后，就继续走这个逻辑
            // cloudVideoDay.sort((a, b) => {
            //   return b.startTime - a.startTime;//逆序的 
            // });
            for (let i = 0; i < this.cloudDayList.length; i++) {
              let item = this.cloudDayList[i];
              if (item.startTime == startTime) {
                // 逆序数据
                let lastOne = cloudVideoDay[cloudVideoDay.length - 1];
                item.startTimestamp = lastOne.startTime;// 更新当天的视频起始时间。
                break;
              }
            }

            resolve && resolve(cloudVideoDay);// 数据拉取完毕，一天的数据都下来了。
          }
        } else {
          console.log("reqeust cloudVideo error: " + "daybegin time not equal with expectedTime");

          resolve && resolve("daybegin time not equal with expectedTime");
          return;
        }

      })
      .catch((error) => {
        let errorCode = error.code;
        console.log(`reqeust cloudVideo error: ${ JSON.stringify(error) }`);

        resolve && resolve(`request error${ JSON.stringify(error) }`);
      });
  }

  static getGMT8TimeZone(timestamp) { // 减去本地的时差，再补上+8时区的时差。
    let targetTimezone = -8;
    let _diff = new Date().getTimezoneOffset();
    let east8Time = timestamp + _diff * 60 * 1000 - (targetTimezone * 60 * 60 * 1000);
    return east8Time;
  }

  static getLocalTimeZone(timestamp) { // 主要还原服务器下发的timestmap 服务器下发的都是+8时区的，就要先还原到标准时区，再偏移到本地时区。
    let targetTimezone = -8; // +8时区变成了负数
    let _diff = new Date().getTimezoneOffset();// 这玩意也是负数。
    let localTime = timestamp + (targetTimezone * 60 * 60 * 1000) - _diff * 60 * 1000;
    return localTime;
  }

  static setCloudFilesReceivedCallback(receivedCallback) {
    this.receivedCallback = receivedCallback;
  }

  static setCloudDateListCallback(dateListCallback) {
    this.dateListCallback = dateListCallback;
  }

  static getLastestVideo() {
    if (this.cloudDayList == null || this.cloudDayList.length <= 0 || this.cloudVideoDayMap == null || this.cloudVideoDayMap.size <= 0) {
      console.log("getLatVideo is null",this.cloudDayList == null,this.cloudDayList.length <= 0 , this.cloudVideoDayMap == null , this.cloudVideoDayMap.size <= 0 );
      return null;
    }
    for (let i = 0; i < this.cloudDayList.length; i++) {
      let item = this.cloudDayList[i];
      if (item.hasVideo) {
        let startTime = item.startTime;
        let videoArrays = this.cloudVideoDayMap.get(startTime);
        if (videoArrays == null || videoArrays.length <= 0) {
          return null;
        }
        return videoArrays[0];// 找到最后一个视频的开始时间；
      }
    }
    console.log("=========getLatVideo not find=============");

    return null;
  }

  static getFastMovePrevTime(currentTime) {
    console.log("handling long press left");

    if (this.cloudVideoDayMap == null || this.cloudVideoDayMap.size <= 0) {
      return -1;
    }
    let moveTime = 30000;
    let selectedTime = currentTime - moveTime;
    let array = [...this.cloudVideoDayMap.keys()];
    array.sort((a1, a2) => {
      return a1 - a2;
    });
    for (let i = array.length - 1; i >= 0; i--) { // array本身是正序的 从后往前，找到第一个满足 time < selectTime的view
      let key = array[i];
      let videoArrays = this.cloudVideoDayMap.get(key);
      if (videoArrays == null || videoArrays.length <= 0) {
        continue;
      }
      // 先找到这一天的
      // 如果selectTime小于这一天的起始时间 跳过
      if (videoArrays[videoArrays.length - 1].startTime > selectedTime) {
        continue;
      }

      for (let i = 0; i < videoArrays.length; i++) { // 因为要找前面的，需要从大往小的查找 找到第一个startime 小于指定时间的
        let video = videoArrays[i];
        if (selectedTime > video.startTime) {
          if (selectedTime < video.endTime) {
            return selectedTime;
          } else {
            return video.endTime;
          }
        }
      }
    }
    return -2;
  }

  static getFastMoveNextTime(currentTime) {
    console.log("handling long press right");
    if (this.cloudVideoDayMap == null || this.cloudVideoDayMap.size <= 0) {
      return -1;
    }
    let moveTime = 30000;
    let selectedTime = currentTime + moveTime;
    let array = [...this.cloudVideoDayMap.keys()];
    array.sort((a1, a2) => {
      return a1 - a2;
    });
    for (let i = 0; i < array.length; i++) { // array本身是正序的 从前往后，找到第一个满足 endTime > selectTime的view;
      let key = array[i];
      let videoArrays = this.cloudVideoDayMap.get(key);
      if (videoArrays == null || videoArrays.length <= 0) {
        continue;
      }
      // 先找到这一天的
      // 如果selectTime小于这一天的起始时间 跳过
      if (videoArrays[0].endTime < selectedTime) {
        continue;
      }
      for (let i = videoArrays.length - 1; i >= 0; i--) { // 逆序查找  由于要找到后面一个，所以要从小的往大的找
        let video = videoArrays[i];
        if (selectedTime < video.endTime) { // 从小到大找到第一个区间，selectedime《 endime
          if (selectedTime > video.startTime) {
            return selectedTime;
          } else {
            return video.startTime;
          }
        }
      }
    }

    return -2;//
  }

  static moveNext(currentTime) {
    if (this.cloudVideoDayMap == null || this.cloudVideoDayMap.size <= 0) {
      return -1;
    }
    let selectedTime = currentTime + 30000;
    let video = this.getNeedItemForCloud(selectedTime, true);
    console.log('video.startTime', video);
    // 当有视频的时候 返回开始时间 没有视频的时候 返回最后一个视频的结束时间
    if (video != null) {
      return video.startTime;
    } 
    return -2;
  }

  static movePrev(currentTime) {
    if (this.cloudVideoDayMap == null || this.cloudVideoDayMap.size <= 0) {
      return -1;
    }
    let selectedTime = currentTime - 30000;
    let video = this.getNeedItemForCloud(selectedTime, false);
    if (video != null) {
      return video.startTime;
    }
    return -2;
  }

  static getNeedItemForCloud(selectedTime, isNext) {
    if (isNext) {
      let array = [...this.cloudVideoDayMap.keys()];
      array.sort((a1, a2) => {
        return a1 - a2;
      });
      for (let i = 0; i < array.length; i++) { // array本身是正序的 从前往后，找到第一个满足 startTime > selectTime的view;
        let key = array[i];
        let videoArrays = this.cloudVideoDayMap.get(key);
        if (videoArrays == null || videoArrays.length <= 0) {
          continue;
        }
        // 先找到这一天的
        // 如果selectTime大于这一天的终止时间 跳过
        if (videoArrays[0].endTime < selectedTime) {
          continue;
        }
        for (let i = videoArrays.length - 1; i >= 0; i--) { // 逆序查找  由于要找到后面一个，所以要从小的往大的找
          let video = videoArrays[i];
          if (selectedTime < video.startTime) { // 从小到大找到第一个区间，selectedime小于startTime的
            return video;
          }
        }
      }
      return null;
    } else {
      let array = [...this.cloudVideoDayMap.keys()];
      array.sort((a1, a2) => {
        return a1 - a2;
      });
      for (let i = array.length - 1; i >= 0; i--) { // array本身是正序的 从后往前，找到第一个满足 startTime < selectTime的view;
        let key = array[i];
        let videoArrays = this.cloudVideoDayMap.get(key);
        if (videoArrays == null || videoArrays.length <= 0) {
          continue;
        }
        // 先找到这一天的
        // 如果某一天的起始时间比指定时间还大，那就不用搜索了
        if (videoArrays[videoArrays.length - 1].starTime < selectedTime) {
          continue;
        }
        for (let i = 0; i < videoArrays.length; i++) { // 正序查找  由于要找到后面一个，所以要从大的往小的找
          let video = videoArrays[i];
          if (selectedTime > video.endTime) { // 从大到小找到第一个区间，selectedime大于endTime的
            return video;
          }
        }
      }
      // 往前跳动的时候，如果一直到头都没有找到

      let videoArray = this.cloudVideoDayMap.get(array[0]);
      if (videoArray == null || videoArray.length <= 0) {
        return -2;
      }
      return videoArray[videoArray.length - 1];
    }
  }
  static getItemByID(selectedTime, fileId) {
    let array = [...this.cloudVideoDayMap.keys()];
    array.sort((a1, a2) => {
      return a1 - a2;
    });
    for (let i = array.length - 1; i >= 0; i--) { // array本身是正序的 从后往前，找到第一个满足 startTime < selectTime的view;
      let key = array[i];
      let videoArrays = this.cloudVideoDayMap.get(key);
      if (videoArrays == null || videoArrays.length <= 0) {
        continue;
      }
      // 先找到这一天的
      // 如果某一天的起始时间比指定时间还大，那就不用搜索了
      if (videoArrays[videoArrays.length - 1].starTime < selectedTime) {
        continue;
      }
      for (let i = 0; i < videoArrays.length; i++) { // 正序查找  由于要找到后面一个，所以要从大的往小的找
        let video = videoArrays[i];
        if (fileId == video.fileId) { // 从大到小找到第一个区间，selectedime大于endTime的
          return video;
        }
      }
    }
    // 往前跳动的时候，如果一直到头都没有找到
    return CloudVideoCarUtil.searchNeareastVideoItem(selectedTime);
  }

  static searchNeareastVideoItem(selectedTime, needPreItem = false) {
    console.log(TAG, `searchNeareastVideoItem: selectedTime:${ this.convertTimestampToTimeStr(selectedTime) }`);
    if (this.cloudVideoDayMap != null && this.cloudVideoDayMap.size <= 0) {
      console.log(TAG, "empty cloudVideoMap, return");
      return null;
    }

    let array = [...this.cloudVideoDayMap.keys()];// 这里经过排序后，
    array.sort((a1, a2) => {
      return a1 - a2;
    });
    let prevItem = null;
    console.log(TAG, `total days size:${ array.length }`, ` arrayContent:${ JSON.stringify(array) }`);
    for (let i = 0; i < array.length; i++) { // 从小往大的搜索，直到找到第一个 当天的endTime > selectedTime的
      let key = array[i];
      let videoArrays = this.cloudVideoDayMap.get(key);
      if (videoArrays == null || videoArrays.length <= 0) {
        continue;
      }
      // console.log(TAG, `current day startTime:${ this.convertTimestampToTimeStr(videoArrays[videoArrays.length - 1].startTime) } ; endTime:${ this.convertTimestampToTimeStr(videoArrays[0].endTime) }`); // 因为videoArray是逆序排列的
      if (videoArrays[0].endTime < selectedTime) { // 所有endTime <= selectedTime的 都不是预期的天。
        continue;
      }
      console.log(TAG, `found expected day! ${ this.convertTimestampToTimeStr(videoArrays[videoArrays.length - 1].startTime) }`);

      for (let i = 0; i < videoArrays.length; i++) { // 需要查找的必然就在这一天里，      逆序查找，找到第一个  selectedTime > endTime的timeItem的后一个  就是预期的。
        let video = videoArrays[i];
        // console.log(TAG, `currentVideoItem: startTime:${ this.convertTimestampToTimeStr(video.startTime) } endTime:${ this.convertTimestampToTimeStr(video.endTime) }`);
        if (selectedTime == video.endTime) {
          if (needPreItem && prevItem != null) {
            return prevItem
          }
          return video;
        } else if (selectedTime > video.endTime) { // 从大到小找到第一个区间，selectedime大于endTimeTime的   fanhui 
          console.log(TAG, `found expected timeItem: startTime:${ this.convertTimestampToTimeStr(prevItem.startTime) } endTime:${ this.convertTimestampToTimeStr(prevItem.endTime) }`);
          return prevItem;
        } else {
          prevItem = video;
        }
      }
      // 所有的都没有找到  一直都没有搜到，返回当天第一个吧
      console.log(TAG, `found all nothing, return first item: startTime:${ this.convertTimestampToTimeStr(prevItem.startTime) } endTime:${ this.convertTimestampToTimeStr(prevItem.endTime) }`);

      return prevItem;

    }

  }

  static convertTimestampToTimeStr(timestamp) {
    if (this.date == null) {
      this.date = new Date();
    }
    this.date.setTime(timestamp);
    let str = "";
    str = `${ str + (this.date.getMonth() + 1) }/${ this.date.getDate() } ${ this.date.getHours() }:${ this.date.getMinutes() }:${ this.date.getSeconds() }`;
    return str;
  }

  static deleteVideos(fileIds) {
    if (this.cloudVideoDayMap == null || this.cloudVideoDayMap.size == 0) {
      return;
    }
    let array = [...this.cloudVideoDayMap.keys()];
    for (let i = 0; i < array.length; i++) {
      let videoArray = this.cloudVideoDayMap.get(array[i]);
      if (videoArray == null || videoArray.length == 0) {
        continue;
      }
      videoArray = videoArray.filter((item) => {
        return fileIds.find(fileId => item.fileId ==fileId) == null;//删除视频； 过滤条件：fileId不在待删除fileIds里
      });
      this.cloudVideoDayMap.set(array[i], videoArray);
      if (videoArray.length == 0) {
        this.cloudDayList = this.cloudDayList.map(item => {
          if (item.startTime == array[i]) {
            item.hasVideo = false;
          }
          return item;
        });
      }
    }
    if (this.alarmEventDayMap == null || this.alarmEventDayMap.size == 0) {
      return;
    }
    
    array = [...this.alarmEventDayMap.keys()];
    for (let i = 0; i < array.length; i++) {
      let videoArray = this.alarmEventDayMap.get(array[i]);
      if (videoArray == null || videoArray.length == 0) {
        continue;
      }
      videoArray = videoArray.filter((item) => {
        return fileIds.find(fileId => item.fileId ==fileId) == null;//删除视频； 过滤条件：fileId不在待删除fileIds里
      });
      this.alarmEventDayMap.set(array[i], videoArray);
    }

  }



}