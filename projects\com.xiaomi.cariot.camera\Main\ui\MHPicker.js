'use strict';
import React from 'react';
import { Dimensions, Modal, TouchableHighlight, TouchableWithoutFeedback, View, Text } from 'react-native';
import PropTypes from 'prop-types';

import Separator from 'miot/ui/Separator';
import StringSpinner from 'miot/ui/StringSpinner';
import { strings, Styles } from 'miot/resources';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { Platform } from 'react-native';
import Host from 'miot/Host';


export default class MHPicker extends React.Component {

  static propTypes = {
    animationType: PropTypes.string,
    visible: PropTypes.bool,
    title: PropTypes.string,
    confirmColor: PropTypes.string,
    dataSource: PropTypes.arrayOf(PropTypes.string),
    unit: PropTypes.string,
    currentValue: PropTypes.string,
    onDismiss: PropTypes.func,
    onConfirm: PropTypes.func
  }

  static defaultProps = {
    animationType: 'fade',
    visible: false,
    confirmColor: Styles.common.MHGreen
  }

  constructor(props, context) {
    super(props, context);

    this.state = {
      visible: this.props.visible,
      currentValue: this.props.currentValue
    };
  }

  componentWillReceiveProps(newProps) {
    try {
      let number = Number.parseInt(newProps.currentValue);
      if (number < 0 || isNaN(number)) {
        console.log("defaultValue is illegal");
        return;
      }
    } catch (err) {
      console.log("defaultValue is illegal");
      return;
    }

    if (newProps.visible !== this.state.visible) {
      this.setState({ visible: newProps.visible });
    }
    if (newProps.currentValue !== this.state.currentValue) {
      this.setState({ currentValue: newProps.currentValue });
      if (Host.isAndroid) {
        this.fastValue = newProps.currentValue;
      }
    }
  }

  _renderTitle() {
    return (
      <View style={{ justifyContent: 'center', alignItems: 'center', height: 66 }}>
        <Text
          numberOfLines={1}
          style={[Styles.common.title, { fontFamily: 'D-DINCondensed-Bold' }]}
        >
          {this.props.title || ''}
        </Text>
      </View>
    );
  }

  _renderContent() {
    return (
      <View style={{}}>
        <StringSpinner
          style={{ height: Math.min(this.props.dataSource.length, 4) * 52 }}
          unit={this.props.unit}
          dataSource={this.props.dataSource}
          defaultValue={this.props.currentValue}
          pickerInnerStyle={{
            // lineColor: Styles.common.hairlineColor,
            textColor: '#666666',
            fontSize: 15,
            selectTextColor: "#333333",
            selectFontSize: 20,
            unitTextColor: '#333333',
            unitFontSize: 12,
            rowHeight: 52,
            
            selectBgColor: "#f3f3f3"
          }}
          onValueChanged={(data) => {
            console.log(`mhpicker:${ JSON.stringify(data) }`);
            this.setState({
              currentValue: data.newValue
            });
          }}
          onValueFastChanged={(data) => {
            // console.log(`mhpicker:${JSON.stringify(data)}`);
            console.log("ValueFastChanged", data.nativeEvent);
            this.fastValue = data.nativeEvent.newValue;
          }}
        />
      </View>
    );
  }

  _renderButton() {
    let button = {
      flex: 1,
      backgroundColor: 'transparent',
      justifyContent: 'center',
      alignItems: 'center'
    };
    let buttonText = {
      fontSize: 14,
      lineHeight: 19,
      color: '#666',
      fontFamily: 'D-DINCondensed-Bold' // TODO: 英文字体，中文加粗效果
    };
    return (
      <View style={{ height: 50, flexDirection: 'row', backgroundColor: 'transparent', justifyContent: 'space-between' }}>
        <TouchableHighlight
          style={[button, { borderBottomLeftRadius: 15 }]}
          onPress={(_) =>{
            this._dismiss();
            console.log('this.props.defaultValue', this.props.defaultValue);
            this.setState({
              currentValue: this.props.defaultValue
            });
            this.fastValue = this.props.defaultValue;
          }}
          underlayColor="rgba(0,0,0,.05)"
        >
          <Text style={buttonText}>
            {LocalizedStrings["action_cancle"]}
          </Text>
        </TouchableHighlight>

        <Separator type="column" style={{ height: 50 }} />

        <TouchableHighlight
          style={[button, { borderBottomRightRadius: 15 }]}
          onPress={(_) => {
            // console.log('this.fastValue', this.fastValue);
            this._dismiss();
            this.props.onConfirm(Platform.OS === "android" ? (this.fastValue == null ? this.state.currentValue : this.fastValue) : this.state.currentValue);
          }}
          underlayColor="rgba(0,0,0,.05)"
        >
          <Text style={[buttonText, { color: this.props.confirmColor }]}>
            {LocalizedStrings["action_confirm"]}
          </Text>
        </TouchableHighlight>
      </View>
    );
  }

  render() {
    return (
      <Modal
        animationType={this.props.animationType}
        transparent={true}
        visible={this.state.visible}
        onRequestClose={(_) => this._dismiss()}
      >
        <View style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.4)' }}>
          <TouchableWithoutFeedback
            onPress={(_) => this._dismiss()}
          >
            <View style={Dimensions.get('window')} />
          </TouchableWithoutFeedback>
          <View style={{
            position: 'absolute',
            bottom: 20,
            width: Dimensions.get('window').width - 20,
            marginHorizontal: 10,
            backgroundColor: '#fff',
            borderRadius: 15
          }}>
            {this._renderTitle()}
            <Separator />
            {this._renderContent()}
            <Separator />
            {this._renderButton()}
          </View>
        </View>
      </Modal>
    );
  }

  _dismiss() {
    this.setState({ visible: false });
    this.props.onDismiss && this.props.onDismiss();
  }
}