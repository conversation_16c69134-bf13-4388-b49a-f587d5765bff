import { View, Image, TouchableOpacity } from "react-native";
import PropTypes from "prop-types";

import React from 'react';
import { StyleSheet, Modal, Text} from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import Util from '../util2/Util';

const kIsCN = Util.isLanguageCN();

export default class CommonMsgDialog extends React.Component {

static propTypes = {
  visible: PropTypes.bool,
  title: PropTypes.string,
  text: PropTypes.string,
  cancelText: PropTypes.string,
  confirmText: PropTypes.string,
  onCancelPress: PropTypes.func,
  onConfirmPress: PropTypes.func
}

static defaultProps = {
  visible: false
};

state = {
  visible: 0
}

constructor(props) {
  super(props);
  this.state = {
    visible: props.visible
  };
}

componentDidMount() {
}

componentWillUnmount() {
}

hide() {
  this.setState({ visible: false });
}

show() {
  this.setState({ visible: true });
}

render() {
  if (!this.state.visible) {
    return null;
  }
  let cancelButtonText = this.props.cancelText ? this.props.cancelText : LocalizedStrings["btn_cancel"];
  let confirmButtonText = this.props.confirmText ? this.props.confirmText : LocalizedStrings["offline_divice_ok"];

  return (
    <Modal
      style={{ display: "flex", width: "100%", height: "100%" }}
      animationType="none"
      transparent={true}
      onRequestClose={() => {
        this.hide();
      }}
    >

      <View style={{ width: "100%", height: "100%", backgroundColor: "#00000066" }}>
        <TouchableOpacity style={{ width: "100%", height: "100%" }}
          onPress = {() => {
            if (this.props.onCancelPress) {
              this.props.onCancelPress();
            } else {
              this.hide();
            }
          }}
        >

        </TouchableOpacity>
        <View style={styles.dialogBgStyle}>

          {
            this.props.title != null ?
              <Text style={styles.titleStyle}>
                {this.props.title}
              </Text>
              : null
          }
          {
            this.props.text != null ?
              <View style={styles.descContainerStyle}>
                <Text style={styles.descStyle}>
                  {this.props.text}
                </Text>
              </View>
              : null
          }
          
          <View style={styles.btnGroupStyle}>
            {this.props.onCancelPress ? this._renderButton(cancelButtonText, "#000000", this.props.onCancelPress) : null}
            {this.props.onConfirmPress ? this._renderButton(confirmButtonText, "#34ADB3", this.props.onConfirmPress) : null}
          </View>
        </View>
      </View>

    </Modal>
  );
}

_renderInstallItem(item) {
  let installGroupStyle = {
    display: "flex",
    flexDirection: "column",
    position: "relative",
    width: "30%",
    height: "100%",
    alignItems: "center",
    justifyContent: "center"
  };

  let imgUnselect = require("../../Resources/Images/icon_camera_panoram_angle_unselect.png");
  let imgSelect = require("../../Resources/Images/icon_camera_panoram_angle_select.png");

  return (
    <View style={installGroupStyle}>
      <Image style={{ position: "relative", width: 60, height: 60 }}
        source={item.source}
      >
      </Image>

      <Text style={{ position: "relative", marginTop: 10, fontSize: kIsCN ? 13 : 11, textAlign: 'center', textAlignVertical: 'center' }}>
        {item.title}
      </Text>

      <Text style={{ position: "relative", marginTop: 5, fontSize: kIsCN ? 12 : 10, textAlign: 'center', textAlignVertical: 'center' }}>
        {item.degree}
      </Text>

      <View style={{ position: "relative", marginTop: 15, width: 30, height: 30 }}>
        <Image style={{ position: "absolute" }}
          source={this.state.selectedType == item.panoType ? imgSelect : imgUnselect}
        />
        <TouchableOpacity style={{ position: "absolute", width: "100%", height: "100%" }}
          onPress={() => {
            this.setState({ selectedType: item.panoType });
          }}
        />
      </View>
    </View>
  );
}

_renderButton(btnText, btnTextColor, onBtnPress) {

  let btnStyle = {
    position: "relative",
    fontSize: kIsCN ? 16 : 14, 
    fontWeight: "normal",
    color: btnTextColor,
    textAlign: 'center',
    textAlignVertical: 'center',
    marginHorizontal: 30
  };

  let btnContainerStyle = styles.btnContainerStyleFull;
  if (this.props.onConfirmPress && this.props.onCancelPress) {
    btnContainerStyle = styles.btnContainerStyleHalf;
  }
  return (
      
    <TouchableOpacity style={btnContainerStyle}
      onPress={() => {
        onBtnPress(this.state.selectedType);
      }}
    >
      <Text style={btnStyle}>
        {btnText}
      </Text>
    </TouchableOpacity>
  );
}
}

export const styles = StyleSheet.create({
  dialogBgStyle: {
    display: "flex",
    position: "absolute", 
    bottom: 0, 
    width: "100%", 
    height: "auto", 
    backgroundColor: "#ffffff",
    borderTopLeftRadius: 20, 
    borderTopRightRadius: 20       
  },

  titleStyle: {
    position: "relative", 
    marginTop: 20,
    marginVertical: 15, 
    fontSize: kIsCN ? 16 : 16, 
    fontWeight: "bold", 
    textAlign: 'center', 
    textAlignVertical: 'center',
    marginHorizontal: 40
  },
  descContainerStyle: {
    display: "flex", 
    flexDirection: "column", 
    // alignItems: "center", 
    // justifyContent: "center",
    // minHeight: 50,
    // paddingHorizontal: 40,
    marginHorizontal: 40
  },
  descStyle: {
    color: "#666666",
    position: "relative", 
    fontSize: kIsCN ? 14 : 12, 
    textAlign: 'center', 
    textAlignVertical: 'center'
  },

  installGroupStyle: {
    display: "flex",
    flexDirection: "row",
    position: "relative",
    marginTop: 10,
    width: "100%",
    height: 180,
    alignItems: "center",
    justifyContent: "space-around"
  },


  btnGroupStyle: {
    display: "flex",
    position: "relative",
    flexDirection: "row",
    bottom: 0,
    marginBottom: 30,
    width: "100%",
    height: 46,
    alignItems: "center",
    justifyContent: "space-around",
    marginTop: 27
  },

  btnContainerStyleHalf: {
    display: "flex",
    flexDirection: "row",
    position: "relative",
    width: "40%",
    height: "100%",
    backgroundColor: "#f5f5f5",
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "stretch",
    borderRadius: 23
  },

  btnContainerStyleFull: {
    display: "flex",
    flexDirection: "row",
    position: "relative",
    width: "85%",
    height: "100%",
    backgroundColor: "#f5f5f5",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 23
  }


});