import React from 'react';
import EventGrid, { FooterH } from "./EventGrid";
import EventCard, { CardHeight, CardMB } from "./EventCard";
import { BaseStyles } from "../BasePage";
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import { Order } from "../framework/EventLoaderInf";
import { Device } from 'miot';
import dayjs from 'dayjs';
import ABTest from '../util/ABTest';
import { CldDldTypes } from "../framework/CloudEventLoader";
import NoFreeSVLTipsCard from "./NoFreeSVLTipsCard";
import TrackUtil from '../util/TrackUtil';

/*
UI data form
[
section0 = {
  title: XXX,
  data:[itm1, itm2, itm3]
}
.....
sectionX = {
  title: XXX,
  data:[itm1, itm2, itm3]
}
]
*/
const TAG = "VipEventSectionList";
export const EvSectionFilter = (aEv) => {
  return false;
};

export function evList2SectionList(aEvL) {
  let sectionLst = [];
  let dic = {};
  for (let i = 0; i < aEvL.length; i++) {
    let item = aEvL[i];
    buildSection(dic, item);
  }
  for (let key in dic) {
    let section = {
      title: key,
      data: dic[key],
      selected: false
    };
    sectionLst.push(section);
  }
  return sectionLst;
}

function buildSection(aSecDict, aItm) {
  let dStr = dayjs.unix(aItm.createTime / 1000).format(LocalizedStrings["yyyymmdd"]);
  
  let sec = aSecDict[dStr];
  if (!sec) {
    console.log(TAG, "buildSection create", dStr);
    aSecDict[dStr] = [aItm];
    return dStr;
  } else {
    sec.push(aItm);
    return null;
  }
}

export default class VipEventSectionList extends EventGrid {
  constructor(aProps) {
    super(aProps);
    this.mCardH = CardHeight + CardMB;
    this.mSecHeaderBg = BaseStyles.mainBg.backgroundColor;
  }

  getInitScroll() {
    let ev = this.state.events;
    console.log("=====>playingid", this.props.playingId);
    if (this.props.playingId) {
      let playIdInit = this.props.playingId;
      if (this.props.event) {
        // 携带event
        playIdInit = this.props.event.fileId + "_"+this.getPlayingOffset(this.props.event);
      }

      for (let sectionIndex = 0; sectionIndex < ev.length; ++sectionIndex) {
        let section = ev[sectionIndex].data;
        if (section == null || section.length <= 0) return null;
        for (let itemIndex = 0; itemIndex < section.length; ++itemIndex) {
          let itm = section[itemIndex];
          let mPId = itm.offset != null ? `${ itm.fileId }_${ itm.offset }` : `${ itm.fileId }`;
          console.log("=====>playingid 00", mPId, playIdInit );
          if (mPId === playIdInit) {
            console.log("=====>playingid 11", itemIndex, sectionIndex );
            // return { animated: false, itemIndex, sectionIndex, viewOffset: 0, viewPosition: this.props.abType != ABTest.Types.A ? 0.9 : 0 };
            let scrollIndex = itemIndex + 1;
            return { animated: false, itemIndex: scrollIndex, sectionIndex, viewOffset: 0, viewPosition:  0 };
          }
        }
      }
    }
    return null;
  }

  onGetDataDone(events) {
    console.log("+++++++++++VipEventSectionList onGetDataDone is do");
    let dates = [];
    for (let evt in events) {
      let mdt = events[evt]?.date;
      if (mdt && !dates.includes(mdt)) { // 日期已经归类，不会重复的
        dates.push(mdt);
      }
    }
    if (events?.length > 0 && (events[0]?.data[0]?.isNoSVLTips || events[0]?.data[0]?.isCloudTips)) {
      this.props.onGetDataDone(events.length - 1, events, dates);
    } else {
      this.props.onGetDataDone(events.length, events, dates);
    }

    // if (this.props.abType == ABTest.Types.A) {
    //   // 查找当前播放的item并滚动到指定位置
    //   setTimeout(() => {
    //    let aLoc = this.getInitScroll();
    //    this.scrollTo(aLoc);
    //   },500);
    //
    // }
  }

  async getData(date, event, isMore = false, aOrder = Order.Desc) {
    console.log(TAG, 'getData', this.props.event);
    if (this.props.abType == ABTest.Types.A) {
      return await this.mLoader.getEventByFileId(Device.deviceID, Device.model, this.props.event.isAlarm, this.props.event.fileId, this.props.loaderArgs.filter);
    } else {
      return await this.mLoader.getOneDayAllEvent(date, this.props.loaderArgs.filter, isMore, aOrder, CldDldTypes.Events);
    }
  }

  applyFilter(aEvs, aFilter) {
    let curEv = aEvs;
    for (let sec of curEv) {
      sec.data = sec.data.filter(aFilter);
    }
    return curEv;
  }

  findEvent(aEv) {
    if (this.state.events && this.state.events.length > 0 && this.state.events[0].hasOwnProperty("data")) {
      let fileId = aEv?.fileId;
      let curOffset = aEv?.offset;
      for (let j = 0; j < this.state.events.length; j++) {
        let events = this.state.events[j].data;
        for (let i = 0; i < events.length; ++i) {
          if (events[i] && events[i].fileId == fileId && events[i]?.offset == curOffset) {
            return events[i];
          }
        }
      }
    }
    return null;
  }

  getAdjacentEventForMarkListitem(aEv) {
    if (this.state.events && this.state.events.length > 0 && this.state.events[0].hasOwnProperty("data")) {
      for (let j = 0; j < this.state.events.length; j++) {
        let events = this.state.events[j].data;
        let curOffset = aEv?.offset;
        let fileId = aEv?.fileId;
        if (this.props.abType == ABTest.Types.A) { // 升序，vip, geteventsByFileId 返回的是升序列表，其他默认的都是降序。
          for (let i = 0; i < events.length; ++i) {
            if (events[i] && fileId == events[i].fileId && events[i].offset > curOffset) {
              return events[i];
            }
          }
        } else { // 降序
          for (let i = events.length - 1; i > -1; --i) {
            if (fileId == events[i].fileId && events[i].offset > curOffset) {
              return events[i];
            }
          }
        }
      }
    }
    return null;
  }

  // events offset 按从小到大顺序
  getCurrentEventForMarkListitemByTime(aItm, time) { // 这个实现对geteventsByFileId事件列表
    if (this.state.events && this.state.events.length > 0 && this.state.events[0].hasOwnProperty("data")) {
      for (let j = 0; j < this.state.events.length; j++) {
        let events = this.state.events[j].data;
        if (this.props.abType == ABTest.Types.A) { // 升序。 vip, geteventsByFileId 返回的是升序列表，其他默认的都是降序。
          for (let i = events.length - 1; i > -1; --i) {
            if (aItm?.fileId == events[i].fileId && (events[i].offset * 3 + 0.1) <= time) {
              return events[i];
            }
          }
        } else { // 降序
          for (let i = events.length - 1; i > -1; --i) {
            if (aItm?.fileId == events[i].fileId && (events[i].offset * 3 + 0.1) >= time) {
              return events[i];
            }
          }
        }
        return aItm;
      }
    }
    return null;
  }

  getPlayingOffset(aItm) { // 播放的offset可能没有对应事件的开始，需要即使是那个item正在被播放
    if (!aItm) return -1;
    if (this.state.events && this.state.events.length > 0 && this.state.events[0].hasOwnProperty("data")) {
      let events = this.getEventListOfCurentFile(aItm.fileId);
      let offset = aItm.offset;

      for (let i = 0; i < events.length; ++i) {
        if (events[i].offset == offset) {
          return offset;
        }
      }
      for (let i = 0; i < events.length; ++i) {
        if (events[i].offset > offset) {
          if (i > 0) {
            return events[i-1].offset;
          }
          return events[0].offset;
        }
      }
      if (events.length > 0) {
        return events[events.length - 1].offset;
      }
    }
    return aItm.offset;
  }

  // 获得所有文件列表
  getFileList() {
    let res = [];
    if (this.state.events && this.state.events.length > 0 && this.state.events[0].hasOwnProperty("data")) {
      for (let j = 0; j < this.state.events.length; j++) {
        let events = this.state.events[j].data;
        for (let i = 0; i < events.length; i++) {
          if (!res.includes(events[i].fileId)) {
            res.push(events[i].fileId);
          }
        }
      }
    }
    return res;
  }

  // 获取当前播放文件的前后事件
  getAdjacentEvent(aEv) {
    if (!this.state.events) {
      return null;
    }

    let fileList = this.getFileList();
    let idx = 0;
    for (let i = 0; i < fileList.length; i++) {
      if (aEv.fileId == fileList[i]) {
        idx = i;
      }
    }
    let fid = idx;
    if (idx == fileList.length - 1) {
      fid = fileList.length - 2;
      if (fid < 0) { 
        fid = 0;
      }
    } else if (idx == 0) {
      if (fileList.length > 1) {
        fid = 1;
      }
    } else {
      fid = idx + 1;
    }

    if (fid == idx) {
      return null;
    }
    let eList = this.getEventListOfCurentFile(fileList[fid]);
    return eList[eList.length - 1];
  }

  getEventListOfCurentFile(fileId) {
    let res = [];
    if (this.state.events && this.state.events.length > 0 && this.state.events[0].hasOwnProperty("data")) {
      for (let j = 0; j < this.state.events.length; j++) {
        let events = this.state.events[j].data;
        let fId = fileId;
        for (let i = 0; i < events.length; i++) {
          if (fId == events[i].fileId) {
            res.push(events[i]);
          }
        }
      }
    }
    return res;
  }

  buildSection(aSecDict, aItm, aOrder) {
    return buildSection(aSecDict, aItm);
  }


  mRItem = ({ section, index }) => {
    let item = section.data[index];
    let mPId = item.offset != null ? `${ item.fileId }_${ item.offset }` : `${ item.fileId }`;
    if (item?.isNoSVLTips == true && this.props.abType == ABTest.Types.A) {
      return null;
    }
    if (!item?.statTrackexposed) {
      item.statTrackexposed = true;
      TrackUtil.newOneTrack(['expose'], { item_type: 'item', item_position: item.sIndex, item_time: dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss'), fid: item.fileId, offset: item?.offset });
    }
    return (item?.isNoSVLTips ?
      <NoFreeSVLTipsCard
        item={item}
        cardPressed={(aItm) => {
          this.props.onEventPress(aItm, {}); // aItm中添加点击的位置信息表示调整或者关闭
          this.removeNoFreeSVLTipItem();
        }}
      >
      </NoFreeSVLTipsCard>
      : item?.isCloudTips ? 
        (this.props?.onRCloudTips ? this.props?.onRCloudTips() : null)
        :
        <EventCard item={item}
          type={'details'}
          isPlaying = {this.props.playingId === mPId}
          postNoFreeSVL={this.props?.postNoFreeSVL && (item.createTime > this.props?.noFreeSVLEndTime)}
          cardPressed={(aItm) => {

            item.isRead = true;
            let events = this.state.events;
            let nextDate = this.state.nextDate;
            this.props.onEventPress(item, { events, nextDate });
            TrackUtil.newOneTrack(['click'], { item_type: 'item', item_position: item.sIndex, item_time: dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss'), fid: item.fileId, offset: item?.offset });
          }}/>);
  }
}
