import React from 'react';
import PropTypes from 'prop-types';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
export default class ItemLongTimeAlarm extends React.Component {
  static propTypes = {
    onPress: PropTypes.func,
    title: PropTypes.string,
    sub_title: PropTypes.string
  };

  constructor(props, context) {
    super(props, context);
  }

  render() {
    return (
      <View style={{ display: "flex", flexDirection: "column", width: "100%" }}>
        <View style={{ display: "flex", flexDirection: "row", alignItems: "center", paddingRight: 20, width: "100%", paddingTop: 8, paddingBottom: 8 }}>
          <TouchableOpacity style={{ display: "flex", flexDirection: "column", flexGrow: 1, paddingLeft: 20, paddingRight: 20 }}
            onPress={() => { 
              if (this.props.onPress) {
                this.props.onPress(); 
              }
            }}>
            <Text style={{ fontSize: 15 }}>{this.props.title}</Text>
            <Text style={{ color: "#555555", fontSize: 13 }}>{this.props.sub_title}</Text>
          </TouchableOpacity>
          <Image
            style={{ width: 13, height: 13 }}
            source={require("../../Resources/Images/icon_right_anchor_black.png")}
          />
        </View>
        <View style={{ backgroundColor: "#FFFFFF", paddingLeft: 20 }}>
          <View style={{ height: 0, borderTopWidth: StyleSheet.hairlineWidth, borderColor: '#bdbdbd', opacity: 0.7, margin: StyleSheet.hairlineWidth }} />
        </View>
      </View>
    );
  }
}