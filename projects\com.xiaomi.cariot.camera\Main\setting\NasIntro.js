'use strict';

import React from 'react';
import { ScrollView, View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import { NavigationBar } from 'mhui-rn';
import AlarmUtilV2, {
  PIID_SCREEN_LAN_SIID, PIID_SCREEN_LAN_SWITCH,
  SIID_SCREEN_CONTROL
} from "../util/AlarmUtilV2";
import Toast from "../components/Toast";
import CameraPlayer from "../util/CameraPlayer";
import RPC from "../util/RPC";
import LogUtil from "../util/LogUtil";
import CameraConfig from "../util/CameraConfig";
import BaseSettingPage from "../BaseSettingPage";


/**
 * @Author: byh
 * @Date: 2024/5/10
 * @explanation:
 * nas介绍页
 *********************************************************/
export default class NasIntro extends BaseSettingPage {

  constructor(props, context) {
    super(props, context);
    this.state = {
      signalSwitch: false,
      signalValue: "",
      sn: 0,
      version: ""
    };
  }

  getTitle() {
    return LocalizedStrings['sts_2_n'];
  }

  componentDidMount() {
    super.componentDidMount();
    // this.props.navigation.setParams({
    //   title: LocalizedStrings['sts_2_n'],
    //   titleNumberOfLines: 2,
    //   left: [{
    //     key: NavigationBar.ICON.BACK,
    //     onPress: () => {
    //       this.props.navigation.goBack();
    //     }
    //   }],
    //   titleStyle: {
    //     fontSize: 18,
    //     color: '#333333',
    //     fontWeight: 500
    //   }
    // });

  }


  renderSettingContent() {
    return (
      <View style={ [styles.container] }>
          <View style={ { alignItems: "center", marginHorizontal: 24, marginTop: 20 } }>
            <Image style={ { width: '100%', height: 175 } }
                   source={ require('../../Resources/Images/pic_nas_intro.webp') }/>
          </View>

          <View style={ { height: 10 } }/>
          <Text style={ {
            color: "#000000",
            fontSize: 18,
            fontWeight: "bold",
            paddingHorizontal: 28
          } }>{ LocalizedStrings['function_intro'] }</Text>
          <Text
            style={ {
              fontSize: 14,
              marginTop: 10,
              lineHeight: 21,
              fontFamily: "MI Lan Pro",
              color: "rgba(0,0,0,0.6)",
              paddingHorizontal: 28
            } }>{ LocalizedStrings['nas_function_introduction_transfer'] }</Text>

      </View>
    );
  }

  renderSettingBottomContent() {

    return (<View style={ { flexDirection: "column" } }>
      <TouchableOpacity
        style={ {
          marginBottom: 10,
          marginTop: 20,
          minHeight: 46,
          position: "relative",
          bottom: 10,
          backgroundColor: "#32BAC0",
          borderRadius: 30,
          marginHorizontal: 27,
          paddingVertical: 10,
          display: "flex",
          alignItems: "center",
          justifyContent: "center"
        } }
        onPress={ () => {
          CameraPlayer.getInstance().getSdcardStatus()
            .then(({ sdcardCode }) => {
              let enableSms = sdcardCode == 0 || sdcardCode == 2 || sdcardCode == 4;
              if (enableSms){
                CameraPlayer.getInstance().getNasConfig()
                  .then((res) => {
                    LogUtil.logOnAll("nas_get_config4-=-=-=-=", JSON.stringify(res));
                    if (res) {
                      this.props.navigation.replace("NASNetworkLocation");
                      CameraConfig.checkNasVersion = true;
                      // CameraConfig.nasUpgradeDlgBtnChecked = true;
                    }
                  }).catch((err) => {
                  Toast.fail('c_get_fail');
                });
              }else {
                Toast.fail('nas_function_introduction_tip');
                // this._navigate('NASNoSD');
              }
            }).catch((error) => {
            Toast.fail('c_get_fail');
          });
        } }
      >
        <Text style={ { color: "#ffffff", fontSize: 16, textAlign: "center" } }>
          { LocalizedStrings["nas_function_introduction_connect"] }
        </Text>
      </TouchableOpacity>
    </View>);
  }

  _onSwitchValue(value) {
    console.log("==============", value);
    let params = [{ sname: SIID_SCREEN_CONTROL, pname: PIID_SCREEN_LAN_SWITCH, value: value }];
    AlarmUtilV2.setSpecPValue(params).then((res) => {
      console.log("_onSwitchValue success", res);
      if (res[0].code != 0) {
        this.setState({ signalSwitch: !value });
        Toast.fail("c_set_fail");
      }
      if (res[0].code == 0) {
        Toast.success("c_set_success");
        this.setState({ signalSwitch: value });
      }
    }).catch((err) => {
      this.setState({ signalSwitch: !value });
      Toast.fail("c_set_fail", err);
      console.log("_onSwitchValue error", err);
    });
  }
}

const stylesSignal = StyleSheet.create({
  container: {
    backgroundColor: '#f5f5f5',
    height: "100%"
  },
  itemContainer: {
    paddingHorizontal: 27,
    minHeight: 60,
    display: "flex",
    flexDirection: 'row',
    alignItems: "center",
    justifyContent: "space-between"
  },

  title: {
    paddingVertical: 10,
    flex: 1,
    color: '#333333',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: "left"
  },
  subTitle: {
    paddingVertical: 10,
    color: 'rgba(0,0,0,0.4)',
    fontSize: 14,
    textAlign: "right"
  }
});
