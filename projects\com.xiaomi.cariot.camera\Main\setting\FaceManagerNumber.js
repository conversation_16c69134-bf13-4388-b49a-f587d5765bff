'use strict';
import React from 'react';
import { ScrollView, Button, BackHandler, View, Text, Image, StyleSheet, FlatList, Dimensions, TouchableOpacity, ActivityIndicator, Platform, PermissionsAndroid} from 'react-native';
import { Device, Service, DarkMode, Host, System} from 'miot';
import { MessageDialog } from "mhui-rn";
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import MultiSelectableNavigationBar from '../ui/MultiSelectableNavigationBar';
import AlbumHelper from '../util/AlbumHelper';
import Toast from '../components/Toast';
import API from '../API';
import { getOrientation } from 'react-native-orientation';
import { ChoiceDialog,AbstractDialog} from 'miot/ui/Dialog';
import Util from "../util2/Util";
import InputDlgEx from '../widget/InputDlgEx';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import { NavigationBar } from 'mhui-rn';
// import { turn } from '../../../../bin/local-cli/bundle/bundleCommandLineArgs';
import TrackUtil from '../util/TrackUtil';
import StorageKeys from '../StorageKeys';
import LogUtil from '../util/LogUtil';
const TAG = 'FaceManagerNumber';
export default class FaceManagerNumber extends React.Component {
  state = {
    isSelectMode: false,
    index: 0,
    isEmpty: true,
    isCurrentDayEmpty: false,
    calendarDays: [],
    dialogVisible: false,
    albumFiles: [],
    showLoading: true,
    coverFaceInfosList: [],
    unmarkFacesImgList: [],
    selectedFiguresList: []
  };
  constructor(props) {
    super(props);
    this.isDelete = false;
    this.dateTime = new Date();
    this.figureInfos = [];
    this.coverFaceInfosList = []
    this.isDark = DarkMode.getColorScheme() == "dark";
  }

  dimensionListener = (args) => {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    if (Platform.OS === "ios") {
      console.log('why!, setDimensionsIos000: ', args);
      console.log('why!, Dimensions', Dimensions.get('window'));
      if (args && args.screen && args.window) {
        if (args.screen.width !== args.window.width || args.screen.height !== args.window.height) {
          setTimeout(() => Dimensions.set({ 'window': args.screen }), 10);
          console.log('纠正========');
        }
      }
    }
  }
  //头部导航
  setNavigation(isSelectAll, isSelectMode, isDisableSelect, title) {
    if (Device.isReadonlyShared) {
      isDisableSelect = true;
    }
    if(this.state.figureInfosLength !==0){
      this.props.navigation.setParams({
        title: isSelectMode ? title : LocalizedStrings["lowpower_face_manager"], //"lowpower_face_manager": "人脸管理",  
        left: [
          {
            key: isSelectMode ? NavigationBar.ICON.CLOSE : NavigationBar.ICON.BACK,
            onPress: () => {
              if (this.state.isSelectMode) {// 如果已经是选择模式，点左边 应该退出选择模式
                this.onSelectAllChanged(false);// 将isSelected重置
                this.setNavigation(false, false, false,);
                this.setState({ isSelectMode: false });
              } else { //不是选择模式就返回
                this.props.navigation.goBack();
              }
            }
          }
        ],
        right: [
          {
            key: !isSelectMode ? NavigationBar.ICON.EDIT : (isSelectAll ? NavigationBar.ICON.SELECTED_ALL : NavigationBar.ICON.SELECT_ALL),
            onPress: () => {
              if (!this.state.isSelectMode) { //进入选择模式
                this.setNavigation(false, true, false, LocalizedStrings["action_select"]); //"action_select": "请选择"
                this.setState({ isSelectMode: true });
              } else if (this.state.isSelectAll) { //退出全选模式
                this.onSelectAllChanged(false);
              } else { //进入全选模式
                this.onSelectAllChanged(true);
              }
            }
          }
        ],
        titleStyle: {
          fontSize: 18,
          color: '#333333',
          fontWeight: 500
        },
      });
    }else{
      this.props.navigation.setParams({
        title: LocalizedStrings["lowpower_face_manager"], //"lowpower_face_manager": "人脸管理",  
        left: [
          {
            key: NavigationBar.ICON.BACK,
            onPress: () => {
                this.props.navigation.goBack();
            }
          }
        ],
        right: [
          {
            key:  NavigationBar.ICON.EDIT ,
            onPress: () => {
            }
          }
        ],
        titleStyle: {
          fontSize: 18,
          color: '#333333',
          fontWeight: 500
        },
      });
    }
  }

  componentDidMount() { 
    Dimensions.addEventListener('change', this.dimensionListener);
    self.windowWidth = Dimensions.get("window").width;
    self.windowHeight = Dimensions.get("window").height;
    if (self.windowHeight < self.windowWidth) {
      let sw = self.windowWidth;
      self.windowWidth = self.windowHeight;
      self.windowHeight = sw;
    }
    this.setNavigation(false, false, true);
    this.setState({ index: 1 });
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
    this.didFocusListener = this.props.navigation.addListener(
      'didFocus', //页面第一次加载会调用一次
      () => {
        this._onGetData();
      }
    );
    this.didBlurListener = this.props.navigation.addListener(
      'didBlur', //离开当前页面时会调用一次
      () => {
        this.isPageForeGround = false;
      }
    );
  }
  isEmojiCharacterV2(codePoint) {
    return !((codePoint == 0x0) ||
      (codePoint == 0x9) ||
      (codePoint == 0xA) ||
      (codePoint == 0xD) ||
      ((codePoint >= 0x20) && (codePoint <= 0xD7FF)) ||
      ((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) ||
      ((codePoint >= 0x10000))) ||
      (codePoint == 0xa9 || codePoint == 0xae || codePoint == 0x2122 ||
        codePoint == 0x3030 || (codePoint >= 0x25b6 && codePoint <= 0x27bf) ||
        codePoint == 0x2328 || (codePoint >= 0x23e9 && codePoint <= 0x23fa))
      || ((codePoint >= 0x1F000 && codePoint <= 0x1FFFF))
      || ((codePoint >= 0x2702) && (codePoint <= 0x27B0))
      || ((codePoint >= 0x1F601) && (codePoint <= 0x1F64F));
  }
  // 不支持输入表情
  containsEmoji(str) {
    let length = str.length;
    for (let i = 0; i < length; ++i) {
      let c = str.charCodeAt(i);
      if (this.isEmojiCharacterV2(c)) {
        return true;
      }
    }
    return false;
  }


  //获取数据
  _onGetData() {
    this.setNavigation(this.state.isSelectAll, this.state.isSelectMode, this.state.isCurrentDayEmpty);
    this.setState({ index: 1 });
    //获取已备注的人脸和名字 
    Util.getAllFigure().then((res) => {
      console.log(res, '已备注的人脸和名字')
      this.setState({
        figureInfos: [...res, { faceUrl: false, figureName:LocalizedStrings["add"] }],
        showMarkView: true,
        figureInfosLength: res.length,
        showLoading: false
      })
      this.setNavigation(this.state.isSelectAll, this.state.isSelectMode, false);
      console.log(this.figureInfos, 'this.figureInfos')
    })
      .catch((err) => {
        LogUtil.logOnAll("FaceManagerNumber", "getAllFigure" +err);
        console.log('获取已备注的人脸和名字err', err)
      });
  
    Util.getAllFigure().then((ret) => {
      this.setState({
        mAllFigureInf: ret
      })
      console.log(ret, 'ret')
    })
      .catch((aErr) => {
        LogUtil.logOnAll("FaceManagerNumber", "getAllFigure" +aErr);
        console.log(TAG, "getAllFigure failed", aErr);
      });
  }
  //在组件卸载及销毁之前掉用
  componentWillUnmount() {
    Dimensions.removeEventListener('change', this.dimensionListener);
    this.didFocusListener.remove();
    this.didBlurListener.remove();
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
  }
  //全体渲染
  render() {
    return (
      <View style={styles.container}>
        {/* {this._renderHeader()} 相册不需要day view */}
        {/* {this._renderDayFiles()} */}
        {/* {this._renderEmptyLayout()} */}
        {this._renderFaceView()}
        {this._renderBottomSelectView()}
        {this._renderLoadingView()}
        {this._renderChoiceAddMarkedFaceWayDialog()}
        {this._renderDialog()}
        {this.state.commentDlg ? this._renderCommentDlg() : null}
        {this._renderTipsDialogView()}
        { this._renderPermissionDialog()}
      </View>
    );
  }
  //这里开始渲染人脸
  _renderFaceView() {
    if (this.state.showMarkView) {
      return (
        <View style={{ width: '100%', flex: 1 }}>
          {this._renderMarkFacesListView()}
        </View>
      )
    }
  }
  //渲染人脸列表
  _renderMarkFacesListView() {
    if (this.state.showMarkView) {
      console.log(this.state.figureInfos.length, 'this.state.figureInfos.length')
      return (
        <View style={{ flex: 1 }}>
          <FlatList
            data={this.state.figureInfos}
            renderItem={({ item, index }) => this._renderMarkFacesView(item, index)}
            numColumns={3}
            keyExtractor={(item, index) => index}
          />
        </View>
      )
    }
  }
  //渲染人脸列表源头
  _renderMarkFacesView(item, index) {
    if (this.state.showMarkView) {
      // console.log(item.faceUrl, 'item')
      let path = null
      path = item.faceUrl;
      let containerWidth = Dimensions.get("window").width / 3;
      // 这里弄个列表 最后一个是添加  然后限制长度是11 如果大于11就弹窗
      return (
        <View style={{ marginTop: 25 }}>
          <TouchableOpacity
            style={{ width: containerWidth, alignItems: "center", justifyContent: "center", }}
            // 去每个人脸的事件页面
            onPress={() => this._onPressFigureInfo(index)}
            onLongPress={() => {
              this.setState((state) => {
                return {
                  isSelectMode: true,
                  isSelectAll: false
                };
              }, () => {
                this._onPressFigureInfo(index)
              });
            }}
          >
            { path ?
              <Image style={{ width: 64, height: 64, borderRadius: 32 }}
                source={path ? path : require('../../Resources/Images/home_icon_add3_pres.png')}
              /> : 
              <View style={{ width: 64, height: 64, borderRadius: 180, backgroundColor: this.isDark ? '#EEEEEE' : '#f5f5f5', alignItems: "center", justifyContent: "center" }}>
                <Image style={{ width: 35, height: 35, borderRadius: 10, backgroundColor: this.isDark ? '#EEEEEE' : '#f5f5f5' }}
                  source={require('../../Resources/Images/home_icon_add2_pres.png')}/>
              </View>
            }
            <Text style={{ fontSize: 12, color: '#000000', marginTop: 15 }}>
              {item.name ? item.name : LocalizedStrings["add"]}
            </Text>
            {
              this.state.isSelectMode && index !== this.state.figureInfos.length - 1 ?
                <Image
                  style={{ width: 22, height: 22, position: "absolute", top: 42, left: 76 }}
                  source={item.isSelected ? require("../../Resources/Images/icon_selected.png") : require("../../Resources/Images/icon_unselected.png")}
                /> :
                null
            }
          </TouchableOpacity>
        </View>
      )
    }
  }
  //点击页面头像触发的事件
  _onPressFigureInfo(index) {
    console.log(index, 'index')
    if (!this.state.isSelectMode) {
      console.log(this.state.isSelectMode, this.state.figureInfos.length, 'this.state.figureInfos.lengththis.state.figureInfos.length')
      if (index == this.state.figureInfos.length - 1) {
        this.setNavigation(false, false, false);
        if (this.state.figureInfos.length < 11) {
          this.setState({
            addMarkedFaceDialogVisible: true
          })
        } else {
          Toast.show(LocalizedStrings["figure_max_tips"])
        }
      }
      else {
        this.props.navigation.navigate("FacesDetailManager", { 
          figureInfo: this.state.figureInfos[index] ,
          callback: (headerInfo) => {
          }
        })
      }
    }
    else {
      if (index == this.state.figureInfos.length - 1) {
        this.onSelectAllChanged(false);// 将选择所有重置
        this.setNavigation(false, false, false,);
        if (this.state.figureInfos.length < 11) {
          this.setState({
            isSelectMode: false,
            addMarkedFaceDialogVisible: true
          })
        } else {
          this.setState({
            isSelectMode: false,
            addMarkedFaceDialogVisible: false
          })
        }
        return

      }
      let figureInfos = this.state.figureInfos[index];
      figureInfos.isSelected = !figureInfos.isSelected;
      let selectedCount = 0;
      for (let file of this.state.figureInfos) {
        if (file.isSelected) {
          selectedCount++;
        }
      }
      // 在这里重新设置标题栏 
      this.setNavigation(false, true, false, LocalizedStrings["selected_count"].replace("%1$d", selectedCount))
      if (selectedCount == 0) {
        this.onSelectAllChanged(false);
      } else if (selectedCount == this.state.figureInfos.length - 1) {
        this.onSelectAllChanged(true);
      } else {
        this.setState({ figureInfos: this.state.figureInfos });// 刷新页面 状态不要保留在ui控件里
      }
    }
  }
  //（添加备注）         
  _renderCommentDlg() {
    let obj = {}
    obj.uri = this.state.unMarkfaceUrl
    if (this.state.commentDlg)
      return (
        <InputDlgEx
          title={LocalizedStrings["cloud_comment_dlg_title"]} //"cloud_comment_dlg_title": "备注名称",
          visible={this.state.commentDlg}
          icon={obj}
          listData={this.state.mAllFigureInf}
          onPressed={(aDat) => {
            this.setState({ defVal: aDat.name });
            console.log(aDat, "11111111111111111111111111111111")
          }}
          onDismiss={(_) => {
            this.renameItem = null;
            this.setState({ commentDlg: false, isRename: false });
          }}
          inputWarnText={this.state.commentErr}
          inputs={[{
            onChangeText: (text) => {
              console.log(TAG, "onChangeText1", text);
              if (this.state.commentErr != null) {
                this.setState({ commentErr: null });
              }
            },
            textInputProps: {
              maxLength: 8
            },
            defaultValue: this.state.defVal,
            type: 'DELETE',
            isCorrect: this.state.commentErr == null
          }]}
          buttons={[
            {
              text: LocalizedStrings["action_cancle"], //"action_cancle": "取消"
              callback: (_) => {
                this.renameItem = null;
                this.setState({ commentDlg: false, isRename: false });
              }
            },
            {
              text: LocalizedStrings["csps_right"],  //"csps_right": "确定",
              callback: (result) => {
                let text = result.textInputArray[0].trim();
                // let text = result.textInputArray[0];
                console.log(TAG, "input changed", text, text.length);
                if (text.length > 0 && !this.containsEmoji(text)) {
                  let cmd = null;
                  //这个人有名字？
                  cmd = Util.commentFace(text, this.state.unMarkfaceId);
                  if (cmd) {
                    cmd.then((aRet) => {
                      this._onGetData()
                      this.setState({
                        commentDlg: false,
                        defVal: ''
                      });
                    })
                      .catch((aErr) => {
                        this.setState({ commentDlg: false });
                        LogUtil.logOnAll("FaceManagerNumber", "commentFace" +aErr);
                        let errCode = aErr.code;
                        // 400302 人物上限
                        let errMap = { 400302: "cloud_figure_limit", 400305: "cloud_face_limit" };
                        let err = errMap[errCode] || "action_fail";                                     //"操作失败",
                        Toast.success(err);
                        console.log(TAG, "comment failed", aErr);
                      });
                  } else {
                    console.log(TAG, "nothing changed");
                  }
                }else {
                  if (this.containsEmoji(text)) {
										this.setState({ commentErr: LocalizedStrings["no_emoij_tips"] });
									}
									else {
										this.setState({ commentErr: LocalizedStrings["cloud_comment_null"] });
									}
                }
              }
            }
          ]}
        />);
  }
  //点击添加后弹窗样式
  _renderChoiceAddMarkedFaceWayDialog() {
    return (
      <ChoiceDialog
        style={{ width: 100 }}
        dialogStyle={{ itemSubtitleNumberOfLines: 3 }}
        useNewType={false}
        visible={this.state.addMarkedFaceDialogVisible} //是否显示该组件
        title={LocalizedStrings["select_dialog_title"]}
        options={[
          { title: LocalizedStrings["select_dialog_camera"] },
          { title: LocalizedStrings["select_dialog_album"] },
          { title: LocalizedStrings["irs_left_text"] }
        ]}
        selectedIndexArray={[0]}
        onDismiss={(_) => this.setState({ addMarkedFaceDialogVisible: false })}
        onSelect={(result) => {
          this.selectedIndexArray = result;
          this._choiceAddMarkedFaceWay(result);
          this.setState({ addMarkedFaceDialogVisible: false });
        }}
      />
    );
  }
  //点击添加后弹窗功能
  _choiceAddMarkedFaceWay(result) {
    console.log(result, 'result')
    if (result ==0) {
      TrackUtil.reportClickEvent("Face_CameraInput_ClickNum");
    } else if (result == 1) {
      TrackUtil.reportClickEvent("Face_GalleryInput_ClickNum");
    }

    if (result == 0) {
      //0 是拍照录入
      // this.props.navigation.navigate('FaceCamera')
      this._startSelectPhoto(result);
    }
    if (result == 1) {
      //1 是从手机相册选择
      this._startSelectPhoto(result);
    }
    if (result == 2) {
      //2 是取消
      this.props.navigation.navigate('FaceManagerNumber')
    }
  }
  //拍照录入人脸
  _startSelectPhoto(result) {
    let permissionAndoid = null, permissionResult = result, permissionIOS
    if (result == 0) {
      permissionAndoid = PermissionsAndroid.PERMISSIONS.CAMERA;
      permissionIOS = 'camera';
    }
    else if (result == 1) {
      permissionAndoid = PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE;
      permissionIOS = 'photos';
    }
    if (Platform.OS === "android") {
      console.log('Platform.OS === "android"')
      this.isCheckingPermission = true;
      PermissionsAndroid.request(permissionAndoid, null)
        .then((granted) => {
          this.isCheckingPermission = false;
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            if (permissionResult == 0) {
              this.props.navigation.navigate('FaceCamera'
                , {
                  callback: (data) => {

                    if (data) {
                      this.setState({
                        commentDlg: true,
                        unMarkfaceUrl: data.faceUrl.uri,
                        unMarkfaceId: data.faceId
                      })
                    }

                  }
                }
              )
            }
            else if (permissionResult == 1) {
              StorageKeys.IS_AI_FACE_OPEN_TOAST
                .then((result) => {
                  // let ret = `$(JSON.stringify(ret))`
                  console.log('result', result)
                  // 当前的设备和用户和是否开启
                  if (result == true) {
                    this.setState({ showphotoTip: false });
                    this.selectPhotoTapped();
                  }
                  else {
                    this.setState({
                      showphotoTip: true
                    })
                    return;
                  }
                })
                .catch(err => {
                  console.log('errr', err)
                  this.setState({ showphotoTip: true, });
                })
            }
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            // Toast.success("camera_no_write_permission");
            this.setState({ showPermissionDialog: true, permissionRequestState: permissionResult, });
          } else {
            Toast.success("please_open_camera");
          }
        }).catch((error) => {
          this.isCheckingPermission = false;
          Toast.success("action_failed");
        });
    } else {
      // no ios's photos const use hardcode

      System.permission.request(permissionIOS).then((res) => {
        console.log('res', res)
        if (permissionResult == 0) {
          this.props.navigation.navigate('FaceCamera'
            , {
              callback: (data) => {

                if (data) {
                  this.setState({
                    commentDlg: true,
                    unMarkfaceUrl: data.faceUrl.uri,
                    unMarkfaceId: data.faceId
                  })
                }

              }
            }
          )
        }
        else if (permissionResult == 1) {
          StorageKeys.IS_AI_FACE_OPEN_TOAST
            .then((result) => {
              // let ret = `$(JSON.stringify(ret))`
              console.log('result', result)
              // 当前的设备和用户和是否开启
              if (result == true) {
                this.setState({ showphotoTip: false });
                this.selectPhotoTapped();
              }
              else {
                this.setState({
                  showphotoTip: true
                })
                return;
              }
            })
            .catch(err => {
              console.log('errr', err)
              this.setState({ showphotoTip: true, });
            })
        }

      }).catch((error) => {
        console.log('error', error)
        // Toast.success("camera_no_write_permission");
        this.setState({ showPermissionDialog: true, permissionRequestState: permissionResult });
      });

    }
  }
  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 camera
    // status == 1 存储卡/相册
    // 
    let message = "";
    if (this.state.permissionRequestState == 0) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_camera"]);
    } else {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);
    }
    return (
    // <AbstractDialog

      <MessageDialog
        title={LocalizedStrings["tips"]}
        message={message}
        messageStyle={{
          fontSize: 14,
        }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ showPermissionDialog: false });
            }
          },
          {
            text: LocalizedStrings["setting"],
            callback: () => {
              Host.ui.openTerminalDeviceSettingPage(1);
              this.setState({ showPermissionDialog: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog} />
    );

  }
  //点击添加后弹窗功能中从手机相册中选择照片
  selectPhotoTapped() {
    const options = {
      quality: 1.0,
      maxWidth: 500,
      maxHeight: 500,
      storageOptions: {
        skipBackup: true
      }
    };
    launchImageLibrary(options, (response) => {
      console.log(response, '相册照片response')
      if (response.didCancel) {
        console.log('User cancelled photo picker');
      }
      else if (response.error) {
        console.log('ImagePicker Error: ', response.error);
        this.setState({ showPermissionDialog: true, permissionRequestState: 1 });
      }
      else if (response.customButton) {
        console.log('User tapped custom button: ', response.customButton);
      }
      else {
        // this.setState({
        //   commentDlg: false,
        // });
        Toast.loading('c_setting');//请稍后
        let path = response.uri.slice(7)
        console.log(path,"path");

        Service.miotcamera.uploadImageToCameraServer(path).then((res) => {
          res = JSON.parse(res)
          console.log('res', res);
          
          if (res.result == "ok" && res.data !== null && res.data.faceInfoMetas[0] != null) {
            console.log("添加成功");
            this.setState({
              commentDlg: true,
              unMarkfaceId: res.data.faceInfoMetas[0].faceId,
              unMarkfaceUrl: response.uri,
            });
            //删除成功弹窗
          }
          else {
            this.setState({
              commentDlg: false,
            });
            Toast.fail("face_recognition_fail_tips")
            console.log("face_recognition_fail_tips");
          }
        }).catch((err) => {
          console.log('添加失败', err);
          Toast.fail('face_recognition_fail_tips', err); //"识别失败请选择清晰的人脸照片",
        })
      }
    })
  }
  //首次从手机相册中选择照片引导页·
  _renderTipsDialogView() {
		return (
			<AbstractDialog
				visible={this.state.showphotoTip}
				useNewTheme
				onDismiss={() => { this.setState({ showphotoTip: false, }) }}
				buttons={[
					{
						text: LocalizedStrings["csps_right"],
						style: { color: '#f0ac3d' },
						callback: (_) => {
							this.setState({
								showphotoTip: false,
							}, () => [
								this.selectPhotoTapped()
							]);

							StorageKeys.IS_AI_FACE_OPEN_TOAST = true
							StorageKeys.IS_AI_FACE_OPEN_TOAST = true
						}
					}
				]}
			>
				<View
					style={{
						flex: 1,
						flexDirection: "column",
						// height: 200,
						alignItems: 'center',
						justifyContent: 'center',
					}}
				>
					<View>
						<Image style={{ width: 280, height: 200 }} source={require('../../Resources/Images/photo_placeholder.webp')}>
						</Image>
					</View>
					<View style={{ marginVertical: 5, textAlign: 'center', marginHorizontal: (Dimensions.get("window").width - 280) / 2 }}>
						<Text style={{ fontSize: 12, color: '#000000' }}>
							{LocalizedStrings["pick_album_tips"]}
						</Text>
					</View>

				</View>
			</AbstractDialog>
		)
	}
  //这里是在选择后底部出现删除的时候
  _renderBottomSelectView() {
    let isDark = DarkMode.getColorScheme() == "dark";
    if (!this.state.isSelectMode) {
      return;
    }
    return (
      <View style={{ width: "100%", height: 69, bottom: 10, borderBottomColor: '#ffffff', borderTopColor: "#e5e5e5", borderTopWidth: 1, borderBottomWidth: 1, display: "flex", flexDirection: "row", justifyContent: "center", alignItems: "center", backgroundColor: '#ffffff' }}>
        <TouchableOpacity
          style={{ width: 50, display: "flex", flex: 1, alignItems: "center", }}
          onPress={() => { this.onPressDelete(); }} //到头像删除
        >
          <Image
            style={{ width: 25, height: 25 }}
            source={Util.isDark() ? require("../../resources2/images/icon_videorecord_delete_w.png") : require("../../resources2/images/icon_videorecord_delete_b.png")} />

          <Text style={{ color: "#000000", fontSize: 10 }}>
            {LocalizedStrings["delete_files"]}
          </Text>

        </TouchableOpacity>
      </View>
    );
  }
  //点击删除后的弹窗
  _renderDialog() {
    let title = LocalizedStrings["face_delete_dialog_title"]
    let message = LocalizedStrings["face_delete_dialog_message"]
    return (
      <MessageDialog
        title={title}
        message={message}
        cancelable={true}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ dialogVisible: false });
            }
          },
          {
            text: LocalizedStrings["action_confirm"],
            callback: () => {
              this.onConfirmDelete();
              this.setState({ dialogVisible: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ dialogVisible: false });
        }}
        visible={this.state.dialogVisible} />
    );
  }
  //头像删除功能
  onPressDelete() {
    //这里获取name根据name获取figureid 然后传给下面
    this.isallDelete = false
    let ids = [];
    for (let figureInfo of this.state.figureInfos) { // 遍历所有的正在展示的内容
      if (figureInfo.isSelected) {
        console.log('figureInfo', figureInfo,)
        Util.getExistFigure(figureInfo.name).then((res) => { ////根据名字获取人物的figureid
          console.log(res, 'figureInfo.name')
          figureInfo.figureId = res;
        }).catch((err) => {
          console.log(err, 'err1')
        })
        ids.push(figureInfo.figureId);
        console.log("this is ids", ids.push(figureInfo.figureId));
      }
    }
      //这里是判断一张都没有选择
      if (ids.length == 0) {
        Toast.show(LocalizedStrings["bottom_action_tip"]) //"bottom_action_tip": "请选择需要操作的文件",
      }
      else {
        this.setState({ dialogVisible: true });
      }
  
  }
  //删除功能
  onConfirmDelete() {
    //这里判断是删除一张还是所有 所有的话还要把这个人物删除
    console.log("准备删除");
    let ids = [];
    for (let figureInfo of this.state.figureInfos) { // 遍历所有的正在展示的内容
      if (figureInfo.isSelected) {
        ids.push(figureInfo.figureId)
        console.log("this is a ids", ids);
      }
    }
    // if(ids.length ==this.state.faceListLength || this.state.faceList.length == 2 ){
    // }
    Util.delFigures(ids).then((res) => {
      console.log('删除了之后的结果', res)
      if ("ok" == res.result.toLowerCase()) {
        this.setState((state) => {
          return {
            isSelectMode: false,
            isSelectAll: false
          };
        }, () => {
          //删除了所有之后返回
          this._onGetData()
        });
      }
      Toast.success("delete_success");
    })
      .catch((err) => {
        Toast.fail("delete_failed", err);
      });
  }
  // 加载提示loading......
  _renderLoadingView() {
    if (this.state.showLoading) {
      return (
        <View
          style={{ position: "absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center", backgroundColor: "white" }}
        >
          <ActivityIndicator
            style={{ width: 54, height: 54 }}
            color={DarkMode.getColorScheme() == "dark" ? "xm#ffffff" : "#000000"}
            size={"large"}
          />
          <Text
            style={{ marginTop: 10, fontSize: 12, color: "#000000" }}>
            {LocalizedStrings["camera_loading"]}
          </Text>
        </View>
      );
    }
  }
  // 这里是没有文件的时候的页面
  _renderEmptyLayout() {
    if (this.state.showLoading) {
      return;
    }
    if (this.state.isCurrentDayEmpty) {
      return (
        <View
          style={{ width: "100%", flexGrow: 1, display: "flex", justifyContent: "center", alignItems: "center" }}
        >
          <Image
            source={require("../../Resources/Images/icon_camera_empty_files.png")}
            style={{ width: 79, height: 79 }}
          />
          <Text
            style={{ fontSize: 14, color: "#808080" }}
          >
            {LocalizedStrings["no_files"]}
          </Text>
        </View>
      );
    } else {
      return null;
    }

  }
  // android返回键处理
  onBackHandler = () => {
    if (!this.isPageForeGround) {
      return false;
    }
    if (this.state.isSelectMode) {
      this.onSelectAllChanged(false);
      this.setNavigation(false, false, false);
      this.setState({ isSelectMode: false });
      return true;
    } else {
      return false;// 不接管
    }
  }
  //头部导航 选择了所有
  onSelectAllChanged(isSelectAll) {
    // this.setState({ index: isSelectAll ? 0 : 1 });
    for (let timeHourItem of this.state.figureInfos) {
      timeHourItem.faceUrl ? timeHourItem.isSelected = isSelectAll ? true : false : timeHourItem.isSelected = false


    }
    let selectedCount = this.state.figureInfos.length - 1;
    this.setNavigation(isSelectAll, this.state.isSelectMode, this.state.isCurrentDayEmpty, 
      isSelectAll ? LocalizedStrings["selected_count"].replace("%1$d", selectedCount) : LocalizedStrings["action_select"]);
    this.setState({ figureInfos: this.state.figureInfos, isSelectAll: isSelectAll });
  }
}
export const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    width: '100%',
    height: '100%',
    display: "flex",
    flexWrap: "nowrap"
  },
  whiteblank: {
    height: 0.5,
    marginHorizontal: 24,
    backgroundColor: "#e5e5e5",
    marginBottom: 30,
    marginTop: 20,
  }
});
