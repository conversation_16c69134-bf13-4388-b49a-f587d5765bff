'use strict';

import React from 'react';
import { ScrollView, View, BackHandler, Platform, Text } from 'react-native';

import { Service, Device, Package, Host } from "miot";
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import Separator from 'miot/ui/Separator';
import { strings } from 'miot/resources';

import MHPicker from '../ui/MHPicker';
import { ChoiceDialog } from 'miot/ui/Dialog';
import { MessageDialog } from 'miot/ui/Dialog';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import API from '../API';
import Toast from '../components/Toast';
import StorageKeys from '../StorageKeys';
import { PackageEvent } from 'miot/Package';
import CameraConfig from '../util/CameraConfig';
import AlarmUtil from '../util/AlarmUtil';
import VersionUtil from '../util/VersionUtil';
import { CAMERA_ALARM_SETTING, DescriptionConstants } from '../Constants';
import NavigationBar from "miot/ui/NavigationBar";
import TrackUtil from '../util/TrackUtil';
import Util from "../util2/Util";
import MHDatePicker from 'miot/ui/MHDatePicker';
import LoadingView from "../ui/LoadingView"
import I18n from "miot/resources/Strings";
import BaseSettingPage from "../BaseSettingPage";
// import MHDatePicker from 'miot/ui/MHDatePicker';

const ALL_DAY_START_TIME = "00:00";
const ALL_DAY_END_TIME = "23:59";
const ALL_DAY_END_TIME_MIN = "23:59";
const DAY_START_TIME = "08:00";
const DAY_END_TIME = "20:00";

const NIGHT_START_TIME = "20:00";
const NIGHT_END_TIME = "08:00";

const SENSITIVE_HIGH = 3;
const SENSITIVE_MED = 2;
const SENSITIVE_LOW = 1;
const SENSITIVE_NONE = 0;

export default class SurvelillanceSetting extends BaseSettingPage {

  constructor(props, context) {
    super(props, context);

    this.state = {
      detectionSwitch: false,
      interval: 5,
      alarmLevelDialogVisible: false,

      // not use
      startTime: '',
      endTime: '',
      repeatArray: [1,1,1,1,1,1,1],

      pushSwitch: false,

      wxpushSwitch: false,

      isPickerVisiable: false,

      showWechat: false,
      disableWechat: false,
      periodStr: "",
      alarmSensitivity: "",

      enableAlarmInterval: !CameraConfig.isNewChuangmi(Device.model) && this.props.navigation.state.params.vip ? false : true,

      confirmDialogVisible:false,

      showLoadingView: true,
      intervalIndex: 0
    };
    this.intervalData = [
      {title: `3${LocalizedStrings['tip_time_minute']}`, value: 3},
      {title: `5${LocalizedStrings['tip_time_minute']}`, value: 5},
      {title: `10${LocalizedStrings['tip_time_minute']}`, value: 10},
      {title: `30${LocalizedStrings['tip_time_minute']}`, value: 30}
    ];
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this._onResume();
      }
    );
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'didBlur',
      () => {
        this._onPause();
      }
    );
    // 监听从其他页面回来的callback


    if (!Host.isAndroid) { // ios监听原生页面回到插件页面的事件通知
      this.appearListener = PackageEvent.packageViewWillAppear.addListener(() => {
        this._onResume();
      });
    } else { // android监听activity onresume即可
      this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
        this._onResume();
      });
    }

    StorageKeys.IS_VIP_STATUS.then((res) => {
      if (res) { // 是vip
        if (!CameraConfig.isNewChuangmi(Device.model)) {
          this.setState({ enableAlarmInterval: false });// 是vip && 不使用v2协议的model  不展示报警时间间隔.
        }
      }
    });
    this.isFirstEnter = true;
    this.isGoAlarmList = false;
    this.is022 = VersionUtil.is022Model(Device.model);
    this.is051 = CameraConfig.Model_chuangmi_051a01 == Device.model;
  }

  // resumeListener = () => { // 从其他页面回来了 请求一下状态，
  //   if (this.state.showWechat) {
  //     this._getWxSetting();
  //   }
  // }
  getTitle() {
    return LocalizedStrings['s_survelillance_setting'];
  }

  // 获取vip的状态
  async fetchVipStatus() {
    try {
      let newVipD = await Util.fetchVipStatus();
      // StorageKeys.VIP_DETAIL = newVipD;
      console.log("我想拿到vip信息：", newVipD)
      this.setState({
        pacakgeType: newVipD.rollingSaveInterval / (1000 * 3600 * 24),
        isVip: newVipD.vip,
        vipEndTime: newVipD.endTime
      })
      // this.updateVipRelated(newVipD);
    } catch (aErr) {
      console.log(this.tag, "fetchVipStatus err", aErr);
    }
  }
  _onResume() {

    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }

    if (this.isGoAlarmList) {
      this.props.navigation.popToTop();
      return;
      // 看家设置跳看家列表  回来后，要主动退出回到首页。
    }

    if (this.state.showWechat) {
      this._getWxSetting();
    }
    if (!this.isFirstEnter) {
      this._getSetting();
    }
  }

  _onPause() {

  }
  _renderIntervalDialog() {
    return (
      <ChoiceDialog
        visible={this.state.isPickerVisiable}
        title={LocalizedStrings['ss_alarm_interval']}
        dialogStyle={{
          titleStyle: {
            fontSize: 18,
            fontWeight: '400'
          }}}
        useNewType={true}
        options={this.intervalData}
        selectedIndexArray={[this.state.intervalIndex]}
        itemStyleType={2}
        onDismiss={(_) => this.setState({ isPickerVisiable: false })}
        buttons={[{
          text: I18n.cancel,
          callback: () => {
            this.setState({ isPickerVisiable: false });
          }
        }, {
          text: I18n.ok,
          callback: (res) => {
            console.log("result1", res);
            let value = this.intervalData[res[0]].value;
            this._onIntervalChanged(value,res[0]);
          }
        }]}
      />
    );
  }

  // 渲染 跳转到用户反馈页面对话框
  renderDialog() {
    let title = '用户反馈';
    let message = '确认前往用户反馈页面';
    return (
      <MessageDialog
        visible={this.state.confirmDialogVisible}
        title={title}
        message={message}
        canDismiss={true}
        onDismiss={(_) => {
          this.setState({ confirmDialogVisible: false });
        }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            // style: { color: 'lightpink' },
            callback: (_) => {
              console.log('onCancel');
              this.setState({ confirmDialogVisible: false });
            }
          },
          {
            text: LocalizedStrings["action_confirm"],
            // style: { color: 'lightblue' },
            callback: (_) => {
              console.log('22222')
              this.setState({ confirmDialogVisible: false });
              this.props.navigation.navigate('customFeedbackMessage');
            }
          }
        ]}
          
      />
    );
  }
  //   renderDialog() {
  //     return (
  //         <MessageDialog
  //             visible={this.state.confirmDialogVisible}
  //             title={'确定前往用户反馈页面?'}
  //             message={''}
  //             cancel={LocalizedStrings["action_cancle"]}
  //             confirm={LocalizedStrings["delete_confirm"]}
  //             onCancel={(e) => {
  //                 console.log('onCancel', e);
  //             }}
  //             cancelable={true}
  //             onConfirm={(e) => {
  //                 console.log('onConfirm', e);
  //                 // this.props.navigation.navigate('customFeedbackMessage');
  //             }}
  //             onDismiss={() => {
  //                 console.log('onDismiss');
  //                 this.setState({ confirmDialogVisible: false });
  //             }}
  //         />
  //     );
  // }
  renderSettingContent() {
    let sensValue = this.state.alarmSensitivity ? this.state.alarmSensitivity < SENSITIVE_MED ? LocalizedStrings['alarm_level_low_title'] : LocalizedStrings['alarm_level_high_title'] : '';
    console.log('this.state.alarmSensitivity', this.state.alarmSensitivity, SENSITIVE_MED, sensValue, this.isPtz);
    let is022or051 = this.is022 ? true : this.is051 ? (this.isInExpireWindow || this.state.isVip) : false;
    let timeItem = (
      <View style={[styles.featureSetting, { marginTop: 0 }]}
        key={11}
      >
        {
          CameraConfig.isSupportSpanSensitivity(Device.model) ?
            <ListItem
              title={LocalizedStrings['ss_partition_sensitivity_settings']}
              showSeparator={false}
              value={this.isPtz ? sensValue : null}
              onPress={() =>
                this._onSensitivitySetting()
              }
              onLongPress={(e) => {
                this.setState({
                  confirmDialogVisible: true
                });
              }}
              titleStyle={{ fontWeight: 'bold' }}
              titleNumberOfLines={3}


            /> : null
        }

        {
          this.state.enableAlarmInterval &&
          <ListItem
            title={LocalizedStrings['ss_alarm_interval']}
            showSeparator={false}
            subtitle={LocalizedStrings['ss_alarm_interval_description']}
            value={this.state.interval + LocalizedStrings["tip_time_minute"]}
            onPress={() => {
              this.setState({ isPickerVisiable: true });

            }

            }
            unlimitedHeightEnable={true}

            titleStyle={{ fontWeight: 'bold' }}
            titleNumberOfLines={2}
            subtitleNumberOfLines={10}


          />
        }

        <ListItemWithSwitch
          title={LocalizedStrings['ss_push_alarm_notifications']}
          showSeparator={false}
          subtitle={LocalizedStrings['ss_push_alarm_notifications_description_new']}
          value={this.state.pushSwitch}
          onValueChange={(value) => this._onPushValueChange(value)}
          titleStyle={{ fontWeight: 'bold' }}
          titleNumberOfLines={2}
          subtitleNumberOfLines={3}
          unlimitedHeightEnable={true}
          accessibilitySwitch={{
            accessibilityLabel: DescriptionConstants.sz_5_12
          }}
          onPress={()=>{}}
          key={1}

        />
        {
          this.state.pushSwitch
            ? (<ListItem
              title={LocalizedStrings['ss_event_notification_type']}
              showSeparator={false}
              onPress={() => {
                TrackUtil.reportClickEvent("MonitoringSetting_PushStyle_ClickNum");
                this.props.navigation.navigate('NotificationTypeSetting');
              }
              }
              titleStyle={{ fontWeight: 'bold' }}
              titleNumberOfLines={3}
              subtitleNumberOfLines={3}
            />)
            : []
        }
        {/*<ListItem title={LocalizedStrings['ss_event_type_idm_cast_screen']}*/}
        {/*          key={7}*/}
        {/*          titleNumberOfLines={2}*/}
        {/*          showSeparator={false}*/}
        {/*          onPress={() => {*/}
        {/*            TrackUtil.reportClickEvent("Setting_Storage_ClickNum")*/}
        {/*            // this.props.navigation.navigate('IDMSettings')*/}
        {/*            // Service.miotcamera.showScreenLinkagePage(true, 3, Device.deviceID, ['push_enable_visit_linkage','push_enable_visit_staying']);*/}
        {/*            Service.miotcamera.showScreenLinkagePage(true, 3, Device.deviceID, [*/}
        {/*              {eventName: 'event.8.12', pushName: "push_enable_event.8.12", pushDescription: LocalizedStrings['people_move_desc']},*/}
        {/*              {eventName: 'event.8.14', pushName: "push_enable_event.8.14", pushDescription: LocalizedStrings['object_move_desc']},*/}
        {/*              {eventName: 'event.8.13', pushName: "push_enable_event.8.13", pushDescription: LocalizedStrings['loud_desc']}*/}
        {/*            ]);*/}
        {/*          }*/}
        {/*          }*/}
        {/*          titleStyle={{ fontWeight: 'bold' }}*/}
        {/*          accessibilityLabel={DescriptionConstants.sz_7}*/}
        {/*/>*/}
        {/* 022 不需要云存会员 所有用户均可访问每日故事  051区分会员 非会员用户*/}
        {/* 每日故事 */}
        {is022or051 ?
          <ListItem
            title={LocalizedStrings['ss_daily_story']}
            showSeparator={false}
            onPress={() => {
              // TrackUtil.reportClickEvent("MonitoringSetting_PushStyle_ClickNum");
              if (this.is022or051) {
                this.props.navigation.navigate('DailyStorySetting');
              } else {
                StorageKeys.IS_VIP_STATUS.then((res) => {
                  if (!res) { // 不是vip
                    this.props.navigation.navigate('DailyStoryFirstEnter');
                  } else {
                    this.props.navigation.navigate('DailyStorySetting', {
                      routeName: this.routeName
                    });
                  }
                });
              }
            }
            }
            titleStyle={{ fontWeight: 'bold' }}
          /> : null
        }

        {
          this.state.showWechat ?
            <ListItemWithSwitch
              title={LocalizedStrings['ss_recevive_alert_notification_on_wechat']}
              showSeparator={false}
              value={this.state.wxpushSwitch}
              onValueChange={(value) => this._onWechatPushValueChange(value)}
              disabled={this.state.disableWechat}
              titleStyle={{ fontWeight: 'bold' }}
              unlimitedHeightEnable={true}
              titleNumberOfLines={2}
              subtitleNumberOfLines={3}
              accessibilitySwitch={{
                accessibilityLabel: LocalizedStrings['ss_recevive_alert_notification_on_wechat']
              }}
              onPress={() => {}}
              key={2}
    
            /> : null
        }

      </View>
    );

    let timeItem022 = (
      <View style={[styles.featureSetting, { marginTop: 0 }]}
        key={22}
      >
        <ListItem
          title={LocalizedStrings['ss_event_type_idm_cast_screen']}
          showSeparator={false}
          titleStyle={{ fontWeight: 'bold' }}
          onPress={() => {
            // this.props.navigation.navigate('NotificationTypeSetting')
            // let data = {};
            // Service.miotcamera.openIDMSetting(JSON.stringify(data));
            this.props.navigation.navigate('IDMSettings');
          }
          }
        />
        {/* <ListItem
          title={LocalizedStrings['ss_daily_story']}
          showSeparator={false}
          onPress={() => {
            this.props.navigation.navigate('DailyStorySetting');
            // let data = {};
            // Service.miotcamera.openDailyStorySetting(JSON.stringify(data));
          }
          }
        /> */}
        { VersionUtil.Model_Chuangmi_022 == Device.model && VersionUtil.isVersionBiggerThanExpectedV2(Device.lastVersion, "4.1.6_0070") >= 0 ?
          <ListItem
            title={LocalizedStrings['ss_long_time_nobody']}
            showSeparator={false}
            onPress={() => {
              // this.props.navigation.navigate('NotificationTypeSetting')
              // let data = {};
              // Service.miotcamera.openLongTimeNobody(JSON.stringify(data));
              this.props.navigation.navigate('LongTimeAlarmList');
            }
            }
          /> : null
        }
        { VersionUtil.Model_Chuangmi_022 == Device.model && VersionUtil.isVersionBiggerThanExpectedV2(Device.lastVersion, "4.1.6_0073") > 0 ?
          <ListItem
            title={LocalizedStrings['ss_baby_sleep_setting']}
            showSeparator={false}
            onPress={() => {
              // this.props.navigation.navigate('NotificationTypeSetting')
              let path = `${ Host.file.storageBasePath }/${ VersionUtil.settingsImgPath }`;
              console.log(`VersionUtil.settingsImgPath=${ path }`);
              let data = { babySleepImgPath: path };
              // Service.miotcamera.openBabySleepSetting(JSON.stringify(data));
              this.props.navigation.navigate('BabySleepingSetting');
            }
            }
          /> : null
        }

      </View>
    );

    return (
      <View style={styles.container}>
          <View style={styles.featureSetting}
            key={2}
          >
            <ListItemWithSwitch
              title={LocalizedStrings['ss_home_survelillance']}
              showSeparator={false}
              value={this.state.detectionSwitch}
              onValueChange={(value) => this._onEnableValueChange(value)}
              titleStyle={{ fontWeight: 'bold' }}
              titleNumberOfLines={2}
              key={33}
              accessibilitySwitch={{ accessibilityLabel: DescriptionConstants.sz_5_3 }}
              subtitleNumberOfLines={3}
              unlimitedHeightEnable={true}
              onPress={()=>{}}
            />
            {
              this.state.detectionSwitch ?
                <ListItem
                  title={LocalizedStrings['ss_home_survelillance_period']}
                  showSeparator={false}
                  onPress={() =>
                    this.props.navigation.navigate('SurveillancePeriodSettingV2', { startTime: this.state.startTime, endTime: this.state.endTime,repeat: this.state.repeatArray, commitCallback: (startTime, endTime) => {
                      return new Promise((resolve, reject) => {
                        if (VersionUtil.isUsingSpec(Device.model)) {
                          let param = { startTime: startTime, endTime: endTime };
                          AlarmUtil.putSpecMotionDetectionPeriod(param).then((res) => {
                            console.log(`xxxxxx ${ JSON.stringify(res) } xxxxx${ JSON.stringify(param) }`);
                            if (res[0].code == 0) {
                              startTime = AlarmUtil.formatTimeString(startTime);
                              endTime = AlarmUtil.formatTimeString(endTime);
                              let periodStr = this.getPeriodStr(startTime, endTime);
                              this.setState({
                                startTime: startTime,
                                endTime: endTime,
                                periodStr: periodStr
                              });
                              resolve(res);
                            } else {
                              reject(res);
                            }
                          }).catch((err) => {
                            reject(err);
                          });
                        } else {
                          AlarmUtil.putMotionDetection({
                            open: this.state.detectionSwitch,
                            interval: this.state.interval,
                            startTime: startTime,
                            endTime: endTime
                          }).then((res) => {
                            if (res.code == 0) {
                              startTime = AlarmUtil.formatTimeString(startTime);
                              endTime = AlarmUtil.formatTimeString(endTime);
                              let periodStr = this.getPeriodStr(startTime, endTime);
                              this.setState({
                                startTime: startTime,
                                endTime: endTime,
                                periodStr: periodStr
                              });
                              resolve(res);
                            } else {
                              reject(res);
                            }
                          }).catch((err) => {
                            reject(err);
                          });
                        }
                      });
                    }
                    })
                  }
                  value={this.state.periodStr}
                  titleStyle={{ fontWeight: 'bold' }}
                  titleNumberOfLines={2}
                  subtitleNumberOfLines={2}
                />
                :
                null
            }

          </View>
          {this.state.detectionSwitch ? (VersionUtil.isAiCameraModel(Device.model) ? [timeItem, timeItem022] : [timeItem]) : []}
        <View style={{height: 600,width: 100}}></View>
        {/*{ this.state.isPickerVisiable ?*/}
        {/*  <MHPicker*/}
        {/*    key={5}*/}
        {/*    visible={this.state.isPickerVisiable}*/}
        {/*    title={LocalizedStrings['ss_alarm_interval']}*/}
        {/*    dataSource={['3', '5', '10', '30']}*/}
        {/*    unit={LocalizedStrings["tip_time_minute"]}*/}
        {/*    defaultValue={this.state.interval ? this.state.interval.toString() : "null"}*/}
        {/*    onConfirm={(value) => {*/}
        {/*      this._onIntervalChanged(value);*/}
        {/*    }}*/}
        {/*    onDismiss={() =>*/}
        {/*      this.setState({*/}
        {/*        isPickerVisiable: false*/}
        {/*      })*/}
        {/*    }*/}
        {/*  /> : null}*/}
        {this._renderSensitivityDialog()}
        {this.renderDialog()}
        {this._renderIntervalDialog()}
        {this._renderLoadingView()}
      </View>
    );
  }
  leftPress() {
    console.log("==========this is do")
    // 特殊机型按返回 响应慢，只响应一次点back的行为
    if (this.props.navigation.state.params.onGoBack) {
      this.handleGoAlarmList();
    } else {
      this.props.navigation.goBack();
    }
    return true;
  }

  componentDidMount() {
    super.componentDidMount();
    console.log("this.props.navigation.state.routeName:", this.props.navigation.state.routeName)
    this.routeName = this.props.navigation.state.routeName;
    this.fetchVipStatus();
    // this.props.navigation.setParams({
    //   title: LocalizedStrings['s_survelillance_setting'],
    //   titleNumberOfLines: 3,
    //   type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
    //   left: [
    //     {
    //       key: NavigationBar.ICON.BACK,
    //       onPress: () => {
    //         // 特殊机型按返回 响应慢，只响应一次点back的行为
    //         if (this.props.navigation.state.params.onGoBack) {
    //           this.handleGoAlarmList();
    //         } else {
    //           this.props.navigation.goBack();
    //         }
    //       }
    //     }
    //   ],
    //   titleStyle: {
    //     fontSize: 18,
    //     color: '#333333',
    //     fontWeight: 500
    //   }
    // });

    // this._getWxSetting();
    let isSupportWeChat = CameraConfig.isSupportWeChat(Device.model);
    if (isSupportWeChat) {
      let isInternational = CameraConfig.getInternationalServerStatus(); // 国际服不展示wechat
      let showWechat = !isInternational && Device.isOwner;
      if (showWechat) {
        this._getWxSetting();
      }
    }

    this._getSetting();

    this.isPtz = CameraConfig.isPTZCamera(Device.model);
    if (this.isPtz) {
      // this._getSensitivitySetting();
    }
    // 查看是否在云存期内
    StorageKeys.IS_VIP_STATUS.then((res) => {
      if (typeof (res) === "string" || res == null) {
        StorageKeys.IS_VIP_STATUS = false;
        this.isVip = false;
      } else {
        this.isVip = res;
      }
      StorageKeys.IN_CLOSE_WINDOW.then((res) => {
        if (typeof (res) === "string" || res == null) {
          StorageKeys.IS_VIP_STATUS = false;
          this.isInExpireWindow = true;
        } else {
          this.isInExpireWindow = res;
        }
        // this._initData();
        console.log("this.isInExpireWindow:", this.isInExpireWindow)
      });
    });
    AlarmUtil.loadAutomaticScenes(Device.deviceID).then(() => {
      console.log("SurvelillanceSetting loadAutomaticScenes res==", AlarmUtil.sceneSize);
    }).catch((err) => {
      console.log("SurvelillanceSetting loadAutomaticScenes err==", JSON.stringify(err));
    });
  }

  // android返回键处理
  onBackHandler = () => {
    if (this.props.navigation.state.params.onGoBack) {

      this.handleGoAlarmList();

      return true;// 挡住 
    } else {
    }
    return false;
  }

  handleGoAlarmList() {
    let shouldDisplayNewStorageManage = CameraConfig.shouldDisplayNewStorageManage(Device.model);
    if (shouldDisplayNewStorageManage) {
      // this.props.navigation.navigate("AlarmPage", {
      //   vip: this.props.navigation.state.params.vip,
      //   freeHomSurStatus: this.props.navigation.state.params.freeHomSurStatus,
      //   shouldPopTopWhenBack: true, freeHomeSurExpireTime: this.props.navigation.state.params.freeHomeSurExpireTime,
      //
      // });
      this.props.navigation.navigate("AlarmVideoUI", {
        shouldPopTopWhenBack: true,
        vip: this.props.navigation.state.params.vip,
        lstType: this.props.navigation.state.params.vip ? "list_vip" : "list",
        freeHomeSurExpireTime: this.props.navigation.state.params.freeHomeSurExpireTime,
        freeHomSurStatus: this.props.navigation.state.params.freeHomSurStatus,
        srcTip: "*********.27773", enterTime: Date.now() });

    } else {
      // setTimeout(() => {
      Service.miotcamera.showAlarmVideos(CameraConfig.getAlarmTypes(Device.model, this.props.navigation.state.params.vip), Device.did, true);
      // }, 800);
      this.isGoAlarmList = true;
    }
  }

  componentWillUnmount() {
    this.didFocusListener && this.didFocusListener.remove();
    this.didBlurListener && this.didBlurListener.remove();
    this.didResumeListener && this.didResumeListener.remove();
    this.appearListener && this.appearListener.remove();
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  _getSetting() {

    if (this.props.navigation.getParam("alarmConfig") != null && this.isFirstEnter) {// 已经有数据带过来了，不需要额外请求了。
      let alarmConfig = this.props.navigation.getParam("alarmConfig");
      if (VersionUtil.isUsingSpec(Device.model)) {
        this._specGetSettingHandler(alarmConfig);
      } else {
        this._profileSettingHandler(alarmConfig);
      }
      return;
    }

    if (VersionUtil.isUsingSpec(Device.model)) {
      AlarmUtil.getSpecAlarmConfig(2).then((result) => {
        console.log("getSpecAlarmConfig==", JSON.stringify(result));
        this._specGetSettingHandler(result);
      }).catch((err) => {
        this.setState({ showLoadingView: false });
        this.isFirstEnter = false;
        Toast.fail('c_get_fail', err);
        console.log(`getSpecAlarmConfig err=${ JSON.stringify(err) }`);
      });
    } else {
      AlarmUtil.getAlarmConfig().then((res) => {
        console.log(`getAlarmConfig:${ JSON.stringify(res) }`);
        this._profileSettingHandler(res)
      }).catch((err) => {
        this.setState({ showLoadingView: false });
        console.log("getSetting:", JSON.stringify(err));
        Toast.fail('c_get_fail', err);
        this.isFirstEnter = false;
      });
    }

  }

  _specGetSettingHandler(result) {
    if (result instanceof Array) {
      let detectionSwitch = result[0].value;
      let alarmInterval = result[1].value;
      let sensitivity = result[2].value;
      let startTime = result[3].value;
      let endTime = result[4].value;
      let repeat = result[5].code == 0?JSON.parse(result[5].value):[1,1,1,1,1,1,1];
      startTime = AlarmUtil.formatTimeString(startTime);
      endTime = AlarmUtil.formatTimeString(endTime);

      if (sensitivity == 0) { // 低灵敏度
        this.setState({ alarmSensitivity: SENSITIVE_LOW });
      } else {
        this.setState({ alarmSensitivity: SENSITIVE_HIGH });
      }

      let periodStr = this.getPeriodStr(startTime, endTime,repeat);
      let index = this.intervalData.findIndex((aItm) => {
        return aItm.value == alarmInterval;
      });
      this.setState({
        detectionSwitch: detectionSwitch,
        startTime: startTime,
        endTime: endTime,
        interval: alarmInterval,
        intervalIndex: index,
        periodStr: periodStr,
        showLoadingView: false,
        repeatArray: repeat
      });

      AlarmUtil.getAlarmConfig().then((res) => {
        this.isFirstEnter = false;
        if (res.code == 0) {
        } else {
          Toast.fail('c_get_fail');
          return;
        }
        this.setState({
          pushSwitch: res.data.pushSwitch

        });

      }).catch((err) => {
        this.isFirstEnter = false;
        Toast.fail('c_get_fail', err);
      });


    }
  }

  _profileSettingHandler(res) {
    this.isFirstEnter = false;
    if (res.code == 0) {
    } else {
      this.setState({ showLoadingView: false });
      console.log("getwxSetting:", JSON.stringify(-1));
      Toast.fail('c_get_fail');
      return;
    }
    let startTime = AlarmUtil.formatTimeString(res.data.motionDetectionSwitch.startTime);
    let endTime = AlarmUtil.formatTimeString(res.data.motionDetectionSwitch.endTime);
    let periodStr = this.getPeriodStr(startTime, endTime);
    let sensitivityValue = res.data.sensitive[0];

    this.setState({
      detectionSwitch: res.data.motionDetectionSwitch.detectionSwitch,
      startTime: startTime,
      endTime: endTime,
      interval: res.data.motionDetectionSwitch.interval,
      pushSwitch: res.data.pushSwitch,
      alarmSensitivity: sensitivityValue,
      periodStr: periodStr,
      showLoadingView: false
    });
  }
  _getWxSetting() {
    API.get('/wx/app/v1/get/pushSwitch', 'connect.camera').then((res) => {
      if (res.code == 0) {
      } else {
        console.log("getwxSetting:");
        Toast.fail('c_get_fail');
        return;
      }
      this.setState({
        showWechat: res.data.pushSwitch, // 已经打开的继续使用，关闭了的不在显示
        wxpushSwitch: res.data.pushSwitch,
        disableWechat: false
      });
    }).catch((err) => {
      console.log("getwxSetting:", JSON.stringify(err));
      Toast.fail('c_get_fail', err);
    });
  }

  _onEnableValueChange(value) {

    // 打点
    value ?
      TrackUtil.reportResultEvent("MonitoringSetting_OnOff_Status", "type", 1)
      :
      TrackUtil.reportResultEvent("MonitoringSetting_OnOff_Status", "type", 2);

    Toast.loading('c_setting');
    if (VersionUtil.isUsingSpec(Device.model)) {
      AlarmUtil.putSpecMotionDetectionSwitch(value).then((res) => {
        if (res[0].code == 0) {
          this.setState({ detectionSwitch: value });
          Toast.success('c_set_success');
        } else {
          Toast.fail('c_set_fail');
          this.setState({ detectionSwitch: !value });
        }
      }).catch((err) => {
        this.setState({ detectionSwitch: !value });
        Toast.fail('c_set_fail', err);
      });
    } else {
      // API.post('/miot/camera/app/v1/put/motionDetectionSwitch', 'business.smartcamera', {
      AlarmUtil.putMotionDetection({
        open: value,
        interval: this.state.interval,
        startTime: Util.addSecond(this.state.startTime),
        endTime: Util.addSecond(this.state.endTime)
      }).then((res) => {
        this.setState({ detectionSwitch: res.code == 0 ? value : !value });
        if (res.code == 0) {
          Toast.success('c_set_success');
        } else {
          Toast.fail('c_set_fail');
        }
      }).catch((err) => {
        this.setState({ detectionSwitch: !value });
        Toast.fail('c_set_fail', err);
      });
    }
  }
  // 报警时间间隔
  _onIntervalChanged(value,index = 0) {
    console.log("value", value);
    // console.log("Number.parseInt(value.rawArray[0])", Number.parseInt(value.rawArray[0]))
    // value = value.rawArray[0];
    switch (Number.parseInt(value)) {
      case 3:
        TrackUtil.reportResultEvent("MonitoringSetting_Interval_Status", "type", 1);
        break;
      case 5:
        TrackUtil.reportResultEvent("MonitoringSetting_Interval_Status", "type", 2);
        break;
      case 10:
        TrackUtil.reportResultEvent("MonitoringSetting_Interval_Status", "type", 3);
        break;
      case 30:
        TrackUtil.reportResultEvent("MonitoringSetting_Interval_Status", "type", 4);
        break;
    }

    try {
      let intValue = Number.parseInt(value);
      if (isNaN(intValue) || intValue == Infinity) {
        Toast.fail("c_set_fail", "value is Nan or infinity");
        return;
      }
    } catch (err) {
      Toast.fail("c_set_fail", err);
      return;
    }
    this.setState({ isPickerVisiable: false });

    Toast.loading('c_setting');
    if (VersionUtil.isUsingSpec(Device.model)) {
      AlarmUtil.putSpecMotionDetectionInterval(parseInt(value)).then((res) => {
        if (res[0].code == 0) {
          this.setState({ interval: parseInt(value), intervalIndex: index });
          Toast.success('c_set_success');
        } else {
          Toast.fail('c_set_fail');
        }
      }).catch((err) => {
        this.setState({ detectionSwitch: !value });
        Toast.fail('c_set_fail', err);
      });
    } else {
      // API.post('/miot/camera/app/v1/put/motionDetectionSwitch', 'business.smartcamera', {
      AlarmUtil.putMotionDetection({
        open: this.state.detectionSwitch,
        interval: parseInt(value),
        startTime: Util.addSecond(this.state.startTime),
        endTime: Util.addSecond(this.state.endTime)
      }).then((res) => {
        console.log(`why!, put modetion res: ${JSON.stringify(res)}`);
        if (res.code == 0) {
          this.setState({ interval: parseInt(value), intervalIndex: index });
          Toast.success('c_set_success');
        } else {
          Toast.fail('c_set_fail');
        }
      }).catch((err) => {
        this.setState({ interval: this.state.interval });
        Toast.fail('c_set_fail', err);
      });
    }
  }

  _onPushValueChange(value) {
    Toast.loading('c_setting');
    value ?
      TrackUtil.reportResultEvent("MonitoringSetting_DetectionPush_Status", "type", 1)
      :
      TrackUtil.reportResultEvent("MonitoringSetting_DetectionPush_Status", "type", 2);
    API.post('/miot/camera/app/v1/put/pushSwitch', 'business.smartcamera', {
      open: value
    }).then((res) => {
      this.setState({ pushSwitch: res.code == 0 ? value : !value });
      if (res.code == 0) {
        Toast.success('c_set_success');
      } else {
        Toast.fail('c_set_fail');
      }
    }).catch((err) => {
      this.setState({ pushSwitch: !value });
      Toast.fail('c_set_fail', err);
    });
  }

  _onWechatPushValueChange(value) {
    value ?
      TrackUtil.reportResultEvent("MonitoringSetting_PushWechat_Status", "type", 1)
      :
      TrackUtil.reportResultEvent("MonitoringSetting_PushWechat_Status", "type", 2);
    if (!value) { // 关闭直接调api就行了
      this.setWechatPush(false);// 直接返回
      this.setState({ showWechat: value });// 不再显示微信推送
      return;
    }
    // TODO: 获取小米账号与微信账号的绑定状态
    this.setState({ disableWechat: true });
    // API.post('/wx/app/v1/put/pushSwitch', 'connect.camera', {
    //   open: value,
    // }).then(res => {
    //   this.setState({ wxpushSwitch: res.code == 0 ? value : !value })
    //   if (res.code == 0) {
    //     Toast.success('c_set_success')
    //   } else {
    //     Toast.fail('c_set_fail')
    //   }
    // }).catch(err => {
    //   this.setState({ wxpushSwitch: !value })
    //   Toast.fail('c_set_fail', err)
    // })
    Service.miotcamera.tryBindAlarmNotifyWithWechatMijia()
      .then(() => {
        this.setState({ disableWechat: false, wxpushSwitch: true });// 失败了 允许重点
      })
      .catch(() => { // 绑定失败。
        // 对话框消失都会触发这里。
        this.setState({ disableWechat: false });// 失败了 允许重点
      });
  }

  _onSensitivitySetting() {
    TrackUtil.reportClickEvent("MonitoringSetting_AreaSensitivity_ClickNum");
    if (this.isPtz) {
      this.setState({ alarmLevelDialogVisible: true });
    } else {
      this.props.navigation.navigate('PartitionSensitivitySetting');
    }
  }

  _renderSensitivityDialog() {
    let selectedIndex = this.state.alarmSensitivity < SENSITIVE_MED ? 1 : 0;
    this.selectedIndexArray = [selectedIndex];
    console.log(this.selectedIndexArray, 'this.selectedIndexArray');

    return (
      <ChoiceDialog
        style={{ width: 100 }}
        dialogStyle={{ itemSubtitleNumberOfLines: 3 }}
        useNewType={false}
        visible={this.state.alarmLevelDialogVisible}
        title={LocalizedStrings["alarm_sensitivity"]}
        options={[
          { title: LocalizedStrings["alarm_level_high_title"], subtitle: LocalizedStrings["alarm_level_high_desc"] },
          { title: LocalizedStrings["alarm_level_low_title"], subtitle: LocalizedStrings["alarm_level_low_desc"] }
        ]}
        selectedIndexArray={this.selectedIndexArray}
        onDismiss={(_) => this.setState({ alarmLevelDialogVisible: false })}
        onSelect={(result) => {
          this.selectedIndexArray = result;
          this._setSensitivitySetting(result);
          this.setState({ alarmLevelDialogVisible: false });
        }}
        buttons={[
        ]}
      />
    );
  }

  _renderLoadingView() {
    console.log(this.state.showLoadingView);
    if (!this.state.showLoadingView) {
      return null;
    }

    let loadingViewStyle = {
      zIndex: 0,
      position: "absolute",
      width: "100%",
      height: '100%',
      bottom: 0,
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(0,0,0,0.1)"
    };

    return (
      <View
        style={loadingViewStyle}
      >
        <LoadingView
          style={{ width: 54, height: 54 }}
        />
        <Text
          style={{ marginTop: 10, color: "#ffffff" }}>
          {LocalizedStrings["camera_loading"]}
        </Text>
      </View>
    );
  }


  _setSensitivitySetting(selectIndex) {
    if (selectIndex instanceof Array) {
      selectIndex = selectIndex[0];
    }
    console.log(`_setSensitivitySetting: ${ selectIndex }`);
    selectIndex == 0 ?
      TrackUtil.reportResultEvent("MonitoringSetting_Sensitivity_Status", "type", 1)
      :
      TrackUtil.reportResultEvent("MonitoringSetting_Sensitivity_Status", "type", 2);
    let sensitiveValue = (selectIndex == 0) ? SENSITIVE_HIGH : SENSITIVE_LOW;
    let sensitiveArray = new Array(32);
    for (let i = 0; i < 32; ++i) {
      sensitiveArray[i] = sensitiveValue;
    }
    if (VersionUtil.isUsingSpec(Device.model)) {
      Service.spec.setPropertiesValue([{ ...CAMERA_ALARM_SETTING[0], value: (selectIndex == 0 ? 1 : 0) }])
        .then((result) => {
          if (result[0].code == 0) {
            console.log('设置了sensitivityValue5', sensitiveValue);
            this.setState({ alarmSensitivity: sensitiveValue });
            Toast.success('c_set_success');
          } else {
            Toast.fail('c_set_fail');
          }
        })
        .catch((error) => {
          Toast.fail("c_set_fail", error);
        });
    } else {
      API.post('/miot/camera/app/v1/put/sensitive', 'business.smartcamera', {
        'sensitive': JSON.stringify({ sensitive: sensitiveArray })
      }).then((res) => {
        if (res.code == 0) {
          console.log('设置了sensitivityValue6', sensitiveValue);
          this.setState({ alarmSensitivity: sensitiveValue });
          Toast.success('c_set_success');
        } else {
          Toast.fail('c_set_fail');
        }
      }).catch((err) => {
        Toast.fail('c_set_fail', err);
      });
    }

  }

  setWechatPush(value) {
    // TODO: 获取小米账号与微信账号的绑定状态
    Toast.loading('c_setting');
    API.post('/wx/app/v1/put/pushSwitch', 'connect.camera', {
      open: value
    }).then((res) => {
      this.setState({ wxpushSwitch: res.code == 0 ? value : !value });
      if (res.code == 0) {
        Toast.success('c_set_success');
      } else {
        Toast.fail('c_set_fail');
      }
    }).catch((err) => {
      this.setState({ wxpushSwitch: !value });
      Toast.fail('c_set_fail', err);
    });
  }

  getPeriodStr(startTime, endTime,repeatArray) {
    let periodString = ""
    startTime = Util.removeSecond(startTime);
    endTime = Util.removeSecond(endTime);
    let repeatArrayType = JSON.stringify(repeatArray) == "[1,1,1,1,1,1,1]" ? LocalizedStrings.daily : LocalizedStrings["alarm_time_user"];

    if ((startTime === (ALL_DAY_START_TIME)) && (endTime === (ALL_DAY_END_TIME) || endTime.includes(ALL_DAY_END_TIME_MIN))) {
      periodString = repeatArrayType+" "+LocalizedStrings["alarm_time_all"];
    }
      // else if (this.compareTimeValue(startTime, ALL_DAY_START_TIME) && this.compareTimeValue(endTime, ALL_DAY_END_TIME)) {
      //   return LocalizedStrings["alarm_time_all"];
    // }
    else if ((startTime === (DAY_START_TIME)) && (endTime === (DAY_END_TIME))) {
      periodString =repeatArrayType+" "+LocalizedStrings["alarm_time_day"];
    } else if ((this.compareTimeValue(startTime, DAY_START_TIME)) && this.compareTimeValue(endTime, DAY_END_TIME)) {
      periodString =repeatArrayType+" "+LocalizedStrings["alarm_time_day"];
    } else if ((startTime === (NIGHT_START_TIME)) && (endTime === (NIGHT_END_TIME))) {
      periodString =  repeatArrayType+" "+LocalizedStrings["alarm_time_night"];
    } else if ((this.compareTimeValue(startTime, NIGHT_START_TIME)) && (this.compareTimeValue(endTime, NIGHT_END_TIME))) {
      periodString =  repeatArrayType+" "+LocalizedStrings["alarm_time_night"];
    } else {
      periodString = repeatArrayType != LocalizedStrings["alarm_time_user"] ? repeatArrayType + " " + LocalizedStrings["alarm_time_user"] : LocalizedStrings["alarm_time_user"];
    }
    return periodString;
  }

  compareTimeValue(startTime, endTime) {
    let threeMinutes = 3 * 60 * 1000;

    let strArray1 = startTime.split(":");
    let strArray2 = endTime.split(":");
    let date = new Date();
    date.setHours(strArray1[0]);
    date.setMinutes(strArray1[1]);
    date.setSeconds(strArray1[2]);
    let timestamp1 = date.getTime();
    date.setHours(strArray2[0]);
    date.setMinutes(strArray2[1]);
    date.setSeconds(strArray2[2]);
    let timestamp2 = date.getTime();
    return Math.abs(timestamp1 - timestamp2) < threeMinutes;

  }
}
