import React from "react";
import { Animated, Easing } from 'react-native';
import MHLott<PERSON>BaseButton from "./base/MHLottieBaseButton";

/**
 * 创建自己的行为枚举值
 * @type  { enum } 
 */
export const MHLottieVoiceBtnDisplayState = {
  NORMAL: "NORMAL",
  CHATTING: "CHATTING"
};

export default class MHLottieVoiceButtonNew extends MHLottieBaseButton {

  /**
   * 创建自己的资源文件
   * @type  { object } 
   */
  static JSONFiles = {
    normal: {
      light: require("../lottie-json/btn_voice_new.json"),
      dark: require("../lottie-json/btn_voice_new_dark.json")
    }
  };

  /**
   * 创建自己的基于行为枚举值的链式动画
   * @param  { enum } displayState
   * @param  { bool } transition
   */
  switchDisplayState(displayState, transition) {

    if (displayState == MHLottieVoiceBtnDisplayState.NORMAL) {
      this.setState({
        file: M<PERSON>ottieVoiceButtonNew.JSONFiles.normal,
        loop: false
      });

      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();
    } else if (displayState == MHLottieVoiceBtnDisplayState.CHATTING) {
      this.setState({
        file: MHLottieVoiceButtonNew.JSONFiles.normal,
        loop: false
      });

      Animated.timing(
        this.progress,
        {
          from: 1,
          toValue: 0,
          duration: transition ? 1000 : 1
        }
      )
        .start();

    }
  }

  constructor(props) {
    super(props);

    // this.state = {
    //   file: MHLottieVoiceButton.JSONFiles.normal,
    // };
  }

  componentWillUnmount() {
    super.componentWillUnmount();
  }

  componentDidMount() {
    super.componentDidMount();
  }

  componentDidUpdate(prevProps) {
    super.componentDidUpdate(prevProps);
  }

}
