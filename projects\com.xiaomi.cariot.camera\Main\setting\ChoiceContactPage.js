import React from 'react';
import {
  ScrollView,
  View,
  BackHandler,
  FlatList,
  Platform,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity
} from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import StorageKeys from '../StorageKeys';
import Toast from '../components/Toast';
import CameraConfig from '../util/CameraConfig';
import { Service, Host, Device, Entrance, DarkMode } from 'miot';
import LogUtil from '../util/LogUtil';
import { PackageEvent } from 'miot/Package';
import API from '../API';
import Util from "../util2/Util";
import CallUtil, { CALL_TYPE } from "../util/CallUtil";
import DeviceSettingUtil from "../util/DeviceSettingUtil";
import BaseSettingPage from "../BaseSettingPage";
import VersionUtil from "../util/VersionUtil";
import { ChoiceDialog, ListItem } from "mhui-rn";
import { strings as I18n } from "miot/resources";

const TAG = "ChoiceContactPage";


/**
 * @Author: byh
 * @Date: 2024/6/11
 * @explanation:
 * 选择、更换联系人
 * 1、可多选
 * 2、已添加的联系人不可取消，不可选择
 * 3、最多选择五个联系人
 * 4、仅可查看联系人不可选择
 *********************************************************/
export default class ChoiceContactPage extends BaseSettingPage {

  constructor(props, context) {
    super(props, context);
    this.state = {
      isLoading: false,
      isVip: false,
      switchValue: false,
      sensitiveIndex: 0,
      sensitivityVisible: false,
      contactsData: [],
      shareContactsData: [],
      // 专门存储已经设置了的联系人的uid，不包含当前更好的联系人
      usedUidArr: [],
      canAdd: false,
      showMemberTypeDialog: false
    };
    this.isReadOnlyShared = Device.isReadonlyShared;
    this.callSettingData = null;
  }

  getTitle() {
    return  LocalizedStrings['select_contact'];
  }

  componentDidMount() {
    super.componentDidMount();
    // 获取vip的状态
    StorageKeys.IS_VIP_STATUS.then((res) => {
      StorageKeys.IN_CLOSE_WINDOW.then((res1) => {
        this.setState({ isVip: res || res1 });
      });
      console.log("看看vip的状态：", this.state.isVip);
    }).catch(() => {
      this.setState({ isVip: false });
    });

    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {
        // this._getSetting();
      }
    );
    this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {//// 原生页面关闭后回调
      // this.autoPlay();
      LogUtil.logOnAll(TAG, "did resume ios");
      this._onResume();
    });
    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      LogUtil.logOnAll(TAG, "did resume");
      this._onResume();
    });

    // let did = Device.extraObj?.split?.parentId;

    this.getContactData();


  }

  async getContactData() {
    let setContactData = await this.getSetContactData();
    let contactsData = await this.getHomeMemberContactData(setContactData);
    let shareContactsData = await this.getShareContactData(setContactData);

    let count = this.getAlreadyContact(contactsData,shareContactsData);

    this.setState({ contactsData: contactsData, usedUidArr: setContactData ? setContactData: [], shareContactsData: shareContactsData ? shareContactsData : [], isLimit: count === 10 });

  }

  async getSetContactData() {
   return await DeviceSettingUtil.getDeviceSettingByKey(DeviceSettingUtil.clickCallSetting).then(res => {
      console.log("=======prefix success", res);
      if (res.code == 0) {
        let settingsData = res.result.settings;
        if (settingsData && settingsData.call_setting) {
          let data = JSON.parse(settingsData.call_setting);
          this.callSettingData = data;
          let uidArr = [];
          // 手势：联系人都可以设置
          Object.keys(data).forEach((key) => {
            if (key.indexOf("key") != -1) {
              let key1Data = data[key];
              uidArr.push(key1Data.mijia);
            }
          });

          // this.setState({ usedUidArr: uidArr });
          return uidArr;
        }
      }
      return [];
    }).catch(error => {
      console.log("======= error: ", error);
      return [];
    });
  }
  async getHomeMemberContactData(setContactData = []) {
    return await Device.getHomeMemberList({}).then((res) => {
      console.log("++++++",res);
      let resData = [];
      if (res.code == 0) {
        resData = res.data;
        // let index
        resData.map(item => {
          item.nickname = item.nick_name;
          item.userid = item.uid;
          // 已经添加为联系人
          item.isAdded = setContactData ? (setContactData.includes(item.uid) || setContactData.includes(parseInt(item.uid))) : false;
          item.isSelect = setContactData ? (setContactData.includes(item.uid) || setContactData.includes(parseInt(item.uid))) : false;
          return item;
        });
        // 排序，把当前账号的排在第一个
        resData.map((item, index) => {
          if (item.userid == Service.account.ID) {
            resData.unshift(resData.splice(index, 1)[0]);
          }
        });


        // this.setState({ contactsData: resData, usedUidArr: setContactData });
        // ********** - ********** = **********
      }
      return resData;
    }).catch((error) => {
      console.log("++++++err",error);
      return [];
    });
  }
  async getShareContactData(setContactData) {
    return await Service.callSmartHomeAPI('/share/get_share_user', { did: Device.deviceID, pid: Device.pd_id })
      .then((res) => {
        // let shareList = res.list;
        let newArray = res.list.filter((item) => item.status == 1);

        //let newArray = res.list;
        newArray.map(item => {
          item.isAdded = setContactData ? (setContactData.includes(item.userid) || setContactData.includes(parseInt(item.userid))) : false;
          item.isSelect = setContactData ? (setContactData.includes(item.userid) || setContactData.includes(parseInt(item.userid))) : false;
          return item;
        });
        console.log("res=====", newArray);
        // this.setState({ shareContactsData: newArray });
        return newArray;
      })
      .catch((e) => {
        console.log("'''''", e)
        return [];
      });
  }

  getAlreadyContact(contactsData,shareContactsData) {
    let count = 0;
   contactsData.forEach((item) => {
      if (item.isSelect) {
        count += 1;
      }
    });

    shareContactsData.forEach((item) => {
      if (item.isSelect) {
        count += 1;
      }
    });

    return count;
  }
  componentWillUnmount() {
    console.log("DailyStoryFirstEnter componentWillUnmount.....");
    this.willFocusSubscription && this.willFocusSubscription.remove();
    this.didResumeListener && this.didResumeListener.remove();
    this.willAppearListener && this.willAppearListener.remove();
  }

  _onResume() {
    this._fetchVipStatus();
  }

  _fetchVipStatus() {
    console.log(TAG, "_fetchVipStatus start");
    // todo:需要框架端提供能够指定hostName前缀的api
    API.get("/miot/camera/app/v1/vip/status", "business.smartcamera")
      .then((result) => {
        if (result.code != 0) {
          return;
        }
        let data = result.data;
        if (data == null) {
          return;
        }
        console.log(TAG, "_fetchVipStatus", JSON.stringify(result));
        let vip = data["vip"];
        let status = data["status"];
        let inWindow = !data["closeWindow"]; // false表示在窗口内，true表示不在
        let endTime = data["endTime"];
        StorageKeys.IS_VIP_STATUS = vip;
        StorageKeys.IN_CLOSE_WINDOW = inWindow;
        StorageKeys.VIP_DETAIL = data;
        this.setState({ isVip: vip || inWindow });
      })
      .catch((err) => {
        console.log(err);
      });
  }
  renderSettingBottomContent() {
    let btnContainerStyle = {
      width: "85%",
      height: 46,
      backgroundColor: this.state.canAdd ? '#32BAC0' : '#C1EAEC',
      justifyContent: "center",
      borderRadius: 23
    };

    let btnTextStyle = {
      fontSize: 16,
      color: "#ffffff",
      fontWeight: "bold",
      textAlign: "center"
    };
    return(
      <View style={{ alignItems: 'center', marginBottom: 27, marginTop: 18 }}>

        <Text style={{marginBottom: 18,fontSize: 14,color: '#00000099'}}>{LocalizedStrings['add_contact_limit']}</Text>

        <TouchableOpacity
          style={btnContainerStyle}
          disabled={!this.state.canAdd}
          onPress={() => {
            this.uploadCallSetting();
          }}>
          <Text style={btnTextStyle}>{LocalizedStrings['add']}</Text>
        </TouchableOpacity>
      </View>
    )
  }
  renderSettingContent() {

    return (
      <View style={ {
        display: "flex",
        height: "100%",
        flex: 1,
        flexDirection: "column",
        backgroundColor: Util.isDark()? "#xm000000" : "#FFFFFF"
      } }>

          <View style={ styles.container } key={ 102 }>
            <Text
              style={ styles.title_group }>{ LocalizedStrings["family_contact"].replace("%d", this.state.contactsData.length) }</Text>

            <FlatList
              data={ this.state.contactsData }
              renderItem={ this._renderContactItem }
              ItemSeparatorComponent={ () => <View style={ { height: 12 } }/> }
              keyExtractor={ (item, index) => `key_${ index }_${ item.isMax }` }
            />
            {/*<ListItem*/}
            {/*  containerStyle={{display: CameraConfig.isSupportWDR(Device.model) ? "flex" : "none"}}*/}
            {/*  title={'邀请成员'}*/}
            {/*  value={''}*/}
            {/*  showSeparator={false}*/}
            {/*  onPress={() => {*/}
            {/*    this.setState({showMemberTypeDialog: true});*/}
            {/*  }}*/}
            {/*  titleStyle={{fontWeight: 'bold'}}*/}
            {/*  valueNumberOfLines={3}*/}
            {/*  titleNumberOfLines={2}*/}
            {/*/>*/}
            {/*<ListItem*/}
            {/*  containerStyle={{display: CameraConfig.isSupportWDR(Device.model) ? "flex" : "none"}}*/}
            {/*  title={'仅可通话'}*/}
            {/*  value={""}*/}
            {/*  showSeparator={false}*/}
            {/*  onPress={() => {*/}
            {/*    this.goWxInvite('call',LocalizedStrings['wx_invite_only_call']);*/}
            {/*  }}*/}
            {/*  titleStyle={{fontWeight: 'bold'}}*/}
            {/*  valueNumberOfLines={3}*/}
            {/*  titleNumberOfLines={2}*/}
            {/*/>*/}
            <Text
              style={ [styles.title_group,{ marginTop: 18}] }>{ LocalizedStrings["share_contact"].replace("%d", this.state.shareContactsData ? this.state.shareContactsData.length : 0) }</Text>
            {
              this.state.shareContactsData && this.state.shareContactsData.length > 0 ?
                <FlatList
                  data={ this.state.shareContactsData }
                  ItemSeparatorComponent={ () => <View style={ { height: 12 } }/> }
                  renderItem={ this._renderContactItem }
                  keyExtractor={ (item, index) => `key_${ index }_${ item.isMax }` }
                /> :
                <View style={ { alignItems: 'center', marginBottom: 60, marginTop: 50 } }>
                  <Image
                    style={ { height: 60, width: 92 } }
                    source={ require('../../Resources/Images/icon_share_no.webp') }/>
                  <Text style={ styles.empty_share_title }>{ LocalizedStrings["no_share_user"] }</Text>
                  <Text style={ styles.empty_share_subtitle }>{ LocalizedStrings["no_share_user_desc"] }</Text>
                  <TouchableOpacity
                    style={ {
                    marginTop: 12,
                    paddingHorizontal: 20,
                    paddingVertical: 8,
                    backgroundColor: 'rgba(0, 0, 0, 0.04)',
                    minHeight: 34,
                    borderRadius: 17 } }
                    onPress={()=>{
                      let newData = { url: "https://home.mi.com/views/article.html?articleId=577935712000000001" };
                      this.props.navigation.navigate("NativeWebPage", newData);
                    } }>
                    <Text
                      style={ { color: 'rgba(0, 0, 0, 0.80)', fontSize: 13, fontWeight: 'bold' } }>{ LocalizedStrings["know_more"] }</Text>
                  </TouchableOpacity>
                </View>
            }


          </View>
        {this.memberTypeDialog()}
      </View>
    );
  }

  memberTypeDialog() {
    return (
      <ChoiceDialog
        visible={this.state.showMemberTypeDialog}
        title={'邀请成员'}
        useNewType={true}
        dialogStyle={{
          allowFontScaling: true,
          unlimitedHeightEnable: false,
          titleStyle: {
            fontSize: 18
          },
          itemSubtitleNumberOfLines: 0,
          itemSubtitleStyle: {
            marginRight: 10
          }
        }}
        buttons={[{
          text: I18n.cancel,
          callback: () => {
            this.setState({ showMemberTypeDialog: false });
          }
        }, {
          text: I18n.ok,
          callback: (res) => {
            console.log("ssssss", res);
            // "call" "homeMemberAndCall" "homeAdminAndCall"
            this.setState({ showMemberTypeDialog: false });
            // let inviteType = res[0] === 0 ? 'homeAdminAndCall' : 'homeMemberAndCall';
            let inviteType = res[0] === 0 ? 'homeAdmin' : 'homeMember';
            this.goWxInvite(inviteType);

          }
        }]}
        onDismiss={() => {this.setState({ showMemberTypeDialog: false });}}
        options={[
          { title: '家庭管理员', value: LocalizedStrings['pss_m'] },
          { title: '家庭成员', value: LocalizedStrings['pss_h'] },
        ]}
        itemStyleType={2}
        selectedIndexArray={[0]}
      />
    )
  }

  goWxInvite(inviteType,msg = LocalizedStrings['wx_invite_member']) {
    let params = {
      inviteType: inviteType,
      // inviteType: "homeAdmin",
      did: Device.deviceID.toString(),
      userId: Service.account.ID.toString(),
      deviceName: Device.name,
      model: Device.model,
      wxMessageTitle: msg
    }
    console.log("==============params:",params);
    Host.ui.shareWxForInviteFriends(params);
  }
  _renderContactItem = ({ item, index }) => {
    let isSelected = item.isSelect;
    // let itemBgColor = isSelected ? "rgba(50, 186, 192, 0.10)" : Util.isDark() ? "#FFFFFFB2" : "#F0F0F0";
    let itemBgColor = "#F0F0F0";
    let disabled = (this.state.usedUidArr.includes(item.userid) || this.state.usedUidArr.includes(parseInt(item.userid))) || (!item.isSelect && this.state.isLimit) || (item.isMax && this.state.isLimit);
    let opacity = disabled ? { opacity: 0.5 } : { opacity: 1 };
    let nickname = item.nickname ? item.nickname : "";
    if (item.userid == Service.account.ID) {
      nickname = `${nickname}${LocalizedStrings["call_me"]}`
    }
    return (
      <TouchableOpacity
        style={ [{
          height: 70,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          borderRadius: 12,
          marginHorizontal: 12,
          backgroundColor: itemBgColor
        }, opacity] }
        // disabled={ disabled }
        onPress={ () => {
          this.userPress(item);
        } }>
        <View
          style={ { display: "flex", flexDirection: "row", alignItems: "center", paddingRight: 20, width: "100%" } }>

          <Image
            key={`img_${index}`}
            style={ { width: 40, height: 40, marginLeft: 12, borderRadius: 20 } }
            source={ item.icon ? { uri: item.icon } : require('../../Resources/Images/icon_user.png') }/>

          <View style={ {
            display: "flex",
            flexDirection: "row",
            flexGrow: 1,
            paddingLeft: 12,
            paddingRight: 15,
            flex: 1,
            alignItems: "center"
          } }>
            <View style={ { display: "flex", flexDirection: "column" } }>
              <Text style={ [{
                fontSize: 16,
                fontWeight: 'bold'
              }, { color: "#000000" }] }>{ nickname }</Text>
              <Text
                style={ [{ fontSize: 13 }, { color: "rgba(0, 0, 0, 0.60)" }] }>{ item.userid ? item.userid : "" }</Text>
            </View>
          </View>
          {
            isSelected ?
              <Image
                style={ { width: 24, height: 24, marginStart: 15 } }
                source={ require('../../Resources/Images/icon_some_checked.png') }/> :
              <Image
                style={ { width: 24, height: 24, marginStart: 15 } }
                source={ DarkMode.getColorScheme() == "dark" ? require("../../Resources/Images/icon_single_unchecked_d.png") : require('../../Resources/Images/icon_single_unchecked.png') }/>
          }
        </View>
      </TouchableOpacity>
    );
  };

  userPress(item) {
    if (item.isAdded) {
      console.log("=========");
      Toast.show(LocalizedStrings['add_contact_already_choose']);
      return;
    }
    if (!item.isSelect && this.state.isLimit) {
      Toast.show(LocalizedStrings['add_contact_already_max']);
      return;
    }
    // 临时选中
    item.isSelect = !item.isSelect;

    // 已添加的联系人数量
    let count = this.getAlreadyContact(this.state.contactsData,this.state.shareContactsData);

    console.log("================================count",count);
    if (count === 10) {
      this.setState({
        canAdd: count !== 0,
        isLimit: count === 10,
        contactsData: this.state.contactsData.map((item,idx) => {
          if (!item.isSelect) {
            return {...item, isMax: true};
          } else {
            return item;
          }
        }),
        shareContactsData: this.state.shareContactsData.map((item,idx) => {
          if (!item.isSelect) {
            return {...item, isMax: true};
          } else {
            return item;
          }
        }),
      });
    } else {
      this.setState({
        canAdd: count !== 0,
        isLimit: count === 10,
        contactsData: this.state.contactsData.map((item,idx) => {
          if (!item.isSelect) {
            return {...item, isMax: false};
          } else {
            return item;
          }
        }),
        shareContactsData: this.state.shareContactsData.map((item,idx) => {
          if (!item.isSelect) {
            return {...item, isMax: false};
          } else {
            return item;
          }
        }),
      });
    }


    // CallUtil.getOpenIdByUid(item.userid).then(openId=>{
    //   this.uploadCallSetting(item,openId);
    // }).catch(error => {
    //   console.log("{{{{{{{{error",error);
    //   this.uploadCallSetting(item)
    // });

  }

  async uploadCallSetting() {
    // console.log("--------",item.userid, typeof (item.userid))
    if (!Device.isOnline) {
      Toast.fail("c_set_fail");
      return;
    }
    let upContactList = [];
    this.state.contactsData.forEach((item) => {
      if (item.isSelect && !item.isAdded) {
        upContactList.push(item);
      }
    });
    this.state.shareContactsData.forEach((item) => {
      if (item.isSelect && !item.isAdded) {
        upContactList.push(item);
      }
    });
    // if (upContactList.length == 0) {
    //   Toast.success('');
    //   return;
    // }
    let uploadData = JSON.parse(JSON.stringify(this.callSettingData));
    if (uploadData == null || JSON.stringify(uploadData) === '{}') {
      uploadData = {"switch": {"hand": 0, "mijia": 1, "wx": 1}};
    }
    // upContactList新增的联系人
    let callIconSetting = {}
    let callIconSettingForUpdate = {}
    for (const item1 of upContactList) {
      console.log("-----------------",item1,uploadData);
      // 需要计算出这个联系人的key
      let key = this.getItemKey(uploadData);
      await CallUtil.getOpenIdByUid(item1.userid).then((openId) => {
        this.addUploadData(uploadData,item1,openId,key);
        this.addUploadIconData(callIconSetting,callIconSettingForUpdate,item1,key);
      }).catch((error) => {
        this.addUploadData(uploadData,item1,"",key);
        this.addUploadIconData(callIconSetting,callIconSettingForUpdate,item1,key);
      });
    }
    console.log("======================callSettingData",uploadData);
    DeviceSettingUtil.setDeviceSetting(`${ DeviceSettingUtil.clickCallSetting }`, JSON.stringify(uploadData)).then((res) => {
      // 成功后 基础信息
      // 添加头像数据
      DeviceSettingUtil.setDeviceSettingArray(callIconSetting).then((res) =>{
        // 头像数据也添加成功
        // 文案提示改为添加联系人成功
        Toast.success('add_contact_success');
        uploadData['method'] = "0";
        let paramsStr = JSON.stringify(uploadData);
        console.log("update device setting base:",paramsStr);
        CallUtil.updateSettingToDevice([paramsStr]);
        // 下发头像数据
        let iconKeys = Object.keys(callIconSettingForUpdate);
        if (iconKeys.length < 6) {
          callIconSettingForUpdate['method'] = "1";
          let paramsIcon = JSON.stringify(callIconSettingForUpdate);
          console.log("update device setting:",paramsIcon);
          CallUtil.updateSettingToDevice([paramsIcon]);
        } else {
          //
          let groupOne = {};
          iconKeys.slice(0, 5).map(key => {
            groupOne[key] = callIconSettingForUpdate[key];
          });
          groupOne['method'] = "1";
          console.log("++++++++++++++1",groupOne);
          let groupTwo = {}
          iconKeys.slice(5).map(key => {
            groupTwo[key] = callIconSettingForUpdate[key];
          });
          groupTwo['method'] = "1";
          console.log("++++++++++++++2",groupOne);
          // 分两次下发
          let paramsGroupIconOne = JSON.stringify(groupOne);
          CallUtil.updateSettingToDevice([paramsGroupIconOne]);

          let paramsGroupIconTwo = JSON.stringify(groupTwo);
          CallUtil.updateSettingToDevice([paramsGroupIconTwo]);
        }
        this.props.navigation.getParam("callback")();
        this.props.navigation.goBack();
      }).catch((error) => {
        console.log("=========error",error);
        Toast.success('c_set_fail');
      });
      // Toast.success('c_set_success');
      // let paramsStr = JSON.stringify(uploadData);
      // CallUtil.updateSettingToDevice([paramsStr]);
      // this.props.navigation.getParam("callback")();
      // this.props.navigation.goBack();
    }).catch(error => {
      Toast.success('c_set_fail');
    });
  }

  getItemKey(uploadData) {
    let keys = Object.keys(uploadData);
    let keyFree = 'key10';
    for (let i = 1; i < 11; i++) {
      let newKey = `key${i}`;
      let index = keys.findIndex((v) => v == newKey);
      if (index == -1) {
        keyFree = newKey;
        break;
      }
    }
    return keyFree;
  }



  addUploadData(uploadData,item1,openId,key) {
    console.log("======================openId",openId);
    let uid = item1.userid;
    if (typeof (item1.userid) != "number") {
      uid = parseInt(item1.userid);
    }
    let params = {
      mijia: uid,
      nickname: Util.replaceEmojis(item1.nickname),
      callName: Util.replaceEmojis(item1.nickname),
      // icon: item1.icon ? item1.icon : "",
      wx: openId ? openId : ""
    };
    uploadData[key] = params;
  }

  addUploadIconData(callIconSetting,callIconSettingForUpdate,item,key) {
    let params = {
      icon: item.icon ? item.icon : "",
    };
    callIconSetting[`call_${key}`] = JSON.stringify(params);
    callIconSettingForUpdate[`call_${key}`] = params;
  }

}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Util.isDark() ? "#xm000000" : 'white'
  },
  title_group: {
    color: '#8C93B0',
    fontSize: 12,
    paddingVertical: 10,
    marginHorizontal: 27,
    marginBottom: 6
  },
  empty_share_title: {
    color: 'rgba(0, 0, 0, 0.40)',
    fontSize: 14,
    marginTop: 12,
    marginHorizontal: 27
  },
  empty_share_subtitle: {
    color: 'rgba(0, 0, 0, 0.38)',
    fontSize: 12,
    paddingVertical: 5,
    marginHorizontal: 27
  },
  white_blank: {
    height: 0.5,
    marginTop: 20
  },
  whiteblank: {
    height: 0.5,
    marginHorizontal: 24,
    backgroundColor: "#e5e5e5",
    marginBottom: 30,
    marginTop: 20

  },
  algorithm_title: {
    color: "#999",
    fontSize: 18,
    paddingHorizontal: 24
  },
  algorithm_subtitle: {
    color: "#999",
    fontSize: 12,
    marginTop: 10,
    paddingHorizontal: 24
  },
  optionsPage: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 20,
    paddingHorizontal: 24
  },
  optionContainer: {
    minHeight: 80,
    alignItems: 'center'
  },
  icon: {
    width: 48,
    height: 48
  },
  optionText: {
    marginTop: 4,
    marginBottom: 10,
    width: 48,
    textAlign: 'center',
    fontSize: 12
  }

});