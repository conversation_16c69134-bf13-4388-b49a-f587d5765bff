import React from 'react';
import { View } from 'react-native';
import { Constants, styles, Opacity } from '../../common/styles/Styles';
import PropTypes from 'prop-types';

/**
 * @export public
 * @module Dividers
 * @description Dividers for CarIoT
 * @property {bool} disabled - 是否禁用，默认值 false
 * @property {object} style - 自定义style
 */
class Dividers extends React.PureComponent {
  static propTypes = {
    disabled: PropTypes.bool,
    style: PropTypes.object
  };

  render() {
    const opacity = this.props.disabled ? Opacity.Disabled : Opacity.Normal;

    return (
      <View style={[
        styles.dividersStyle, 
        { 
          marginTop: Constants.DEFAULT_MARGIN_TOP_BOTTOM, 
          marginBottom: Constants.DEFAULT_MARGIN_TOP_BOTTOM,
          opacity
        }, this.props.style]}/>
    );
  }

}

export default Dividers;
export { Dividers };
