'use strict';
import React from 'react';
import {
  Dimensions,
  Platform
} from 'react-native';

const { width, height } = Dimensions.get('window');
// const isIPX = MHPluginSDK.systemInfo.mobileModel === "iPhone10,3" || MHPluginSDK.systemInfo.mobileModel === "iPhone10,6";
const APPBAR_HEIGHT = Platform.OS === 'ios' ? 44 : 56;
const isIphoneX = (Platform.OS === 'ios' && (Number((`${ height / width }`).substr(0, 4)) * 100) === 216);

let ConstDefine = {
  /* navigator 高度 */
  APPBAR_HEIGHT: APPBAR_HEIGHT,
  /* 导航头 高度 */
  NavigatorBarHeight: (isIphoneX == true) ? (88) : (APPBAR_HEIGHT + 20),
  /* iphone X 底部安全区域高度 */
  SafeBottomHeight: (isIphoneX == true) ? (34) : (0),
  /* 是否是iphone X */
  isIPX: isIphoneX,
  /* 屏幕宽 */
  screenWidth: width,
  /* 屏幕高 */
  screenHeight: height
};

module.exports = ConstDefine;