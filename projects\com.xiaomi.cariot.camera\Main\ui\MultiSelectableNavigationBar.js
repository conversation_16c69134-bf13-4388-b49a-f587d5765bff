
import PropTypes from 'prop-types';
import React, { Component } from 'react';
import { Dimensions, Image, Platform, StatusBar, StyleSheet, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native';
import ImageButton from "miot/ui/ImageButton";
import { Properties } from 'miot/native';
import { DarkMode } from 'miot/Device';
import Util from "../util2/Util";


// const { width } = Dimensions.get('window'); // 屏幕宽度
const width = Math.min(Dimensions.get('window').width, Dimensions.get('window').height); // 屏幕宽度
const navigationBarHeightThin = 53; // 导航栏高度，无副标题
const navigationBarHeightFat = 65; // 导航栏高度，有副标题
const paddingHorizontal = 9; // 导航栏左右内边距
const iconSize = 40; // 图标尺寸
const lightTitleColor = '#000000'; // 浅色背景下标题颜色


export default class MultiSelectableNavigationBar extends Component {

  static propTypes = {
    title: PropTypes.string,
    isSelectAll: PropTypes.bool,
    isSelectMode: PropTypes.bool,
    onEnterSelectMode: PropTypes.func,
    onExitSelectMode: PropTypes.func,
    onSelectAll: PropTypes.func,
    onUnselectAll: PropTypes.func,
    onBackPress: PropTypes.func
  }

  static defaultProps = {
    isSelectAll: false
  }

  constructor(props, context) {
    super(props, context);
    this.state = {
      isSelectMode: props.isSelectMode
    };
  }


  renderIcons(icon) {

    // 如果没有找过图标资源，则显示占位空白
    if (!icon) {
      return null;
    }
    return (
      <View
        style={{ width: iconSize, height: iconSize }}
      >
        <ImageButton
          onPress={icon.onPress}
          style={styles.icon}
          source={icon.source}
          highlightedSource={null}
        />
      </View>
    );

  }

  /**
   * 中间标题部分
   */
  renderTitle() {
    const title = this.props.title;
    const titleColor = {
      color: lightTitleColor
    };

    return (
      <View style={[styles.titleContainer]}>
        <Text
          numberOfLines={1}
          style={[styles.title, titleColor]}
        >
          {title || ''}
        </Text>
      </View>
    );
  }

  onLeftPress = () => {
    if (this.props.isSelectMode) {
      this.setState({ isSelectMode: false });
      this.props.onExitSelectMode();
      return;
    }
    this.props.onBackPress();
  }

  renderEmptyIcon() {
    return (
      <View
        style={{ width: iconSize, height: iconSize, position: "relative" }}
      >
      </View>
    );
  }

  /**
   * 导航栏在进入插件的时候就已经生成，并且常驻，所以样式判断逻辑不能写在 constructor 中
   */
  render() {
    const iconBack = Util.isDark() ? require("../../Resources/Images/icon_back_black_nor_dark.png") : require("../../Resources/Images/icon_back_black.png");
    const iconCancle = Util.isDark() ? require("../../Resources/Images/icon_cancle_white.png") : require("../../Resources/Images/icon_cancle_black.png");
    const iconEdit = Util.isDark() ? require("../../Resources/Images/icon_edit_white.png") : require("../../Resources/Images/icon_edit_black.png");
    let iconSelect = this.props.isSelectAll ? require("../../resources2/images/icon_select_active.png") : (Util.isDark() ? require("../../Resources/Images/icon_select_white.png") : require("../../Resources/Images/icon_select_black.png"));
    !Util.isDark() ? StatusBar.setBarStyle('dark-content') : StatusBar.setBarStyle('light-content')// 白底黑字 测试过的机型都有效：华为荣耀V9，红米Note4X，小米Mix2
    if (Platform.OS == 'android') {
      // StatusBar.setTranslucent(true); // 测试过的机型几乎都无效：华为荣耀V9，红米Note4X，小米Mix2
    }
    let isDisable = this.props.disableSelect;

    let isSelectMode = isDisable ? false : this.props.isSelectMode;

    const leftIcon = isSelectMode ? iconCancle : iconBack;
    const rightIcon = isSelectMode ? iconSelect : iconEdit;

    const leftIconData = {
      source: leftIcon,
      onPress: this.onLeftPress
    };

    const rightIconData = {
      source: rightIcon,
      onPress: !isSelectMode ?
        (_) => { this.props.onEnterSelectMode(); } :
        (this.props.isSelectAll ? this.props.onUnselectAll : this.props.onSelectAll)
    };
    let containerHeight = this.props.subtitle ? navigationBarHeightFat : navigationBarHeightThin;
    const backgroundColor = DarkMode.getColorScheme() == 'dark' ? 'xm#000' : 'xm#fff';
    // StatusBar.setBackgroundColor(backgroundColor); // 仅对某些机型有效：华为荣耀V9
    const containerStyle = {
      backgroundColor
    };
    console.log("cheng statusBar render");

    return <SafeAreaView style={[containerStyle, {
      paddingTop: StatusBar.currentHeight
    }]}>
      <View style={[styles.container, {
        minHeight: containerHeight
      }]}>
        {this.renderIcons(leftIconData)}
        {this.renderTitle()}
        {this.renderIcons(rightIconData)}
      </View>
    </SafeAreaView>;
  }
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    paddingHorizontal,
    flexDirection: 'row',
    alignItems: 'center'
  },
  titleContainer: {
    flex: 1,
    alignSelf: 'stretch',
    justifyContent: 'center',
    alignItems: 'stretch',
    marginHorizontal: 5
  },
  title: {
    fontSize: 18,
    // lineHeight: 22,
    textAlignVertical: 'center',
    textAlign: 'center',
    fontWeight: 'bold'
  },
  titleView: {
    fontSize: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center'
  },
  subtitle: {
    fontSize: 12,
    lineHeight: 17,
    textAlignVertical: 'center',
    textAlign: 'center'
  },
  icon: {
    width: iconSize,
    height: iconSize
  },
  dot: {
    width: iconSize,
    height: iconSize
  }
});
