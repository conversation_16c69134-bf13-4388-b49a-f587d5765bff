import { DeviceEventEmitter, Platform } from 'react-native';
import { MI<PERSON><PERSON>om<PERSON>, AlarmEventType, MISSError, MISSConnectState } from "miot/service/miotcamera";
import { API_LEVEL, Package, Device, Service, DeviceEvent } from 'miot';
import { HostEvent } from "miot/Host";
import NetInfo from "@react-native-community/netinfo";
import StorageKeys from '../StorageKeys';
import { PackageEvent } from 'miot/Package';
import VersionUtil from './VersionUtil';
import PushHandler from './PushHandler';
import RPC from './RPC';
import Toast from '../components/Toast';
import LogUtil from './LogUtil';
import { getStack } from '..';
import { DirectionViewConstant } from '../ui/DirectionView';
import Singletons from '../framework/Singletons';
import UriPlayer from '../framework/UriPlayer';
import Alarm<PERSON>tilV2, {
  CAMERA_CONTROL_RECORD_MODE_PIID,
  SIID_CAMERA_CONTROL,
  CAMERA_SDCARD_FREE_SPACE_PIID,
  CAMERA_SDCARD_SIID,
  CAMERA_SDCARD_STATUS_PIID,
  CAMERA_SDCARD_TOTAL_SPACE_PIID,
  CAMERA_SDCARD_USED_SPACE_PIID, SPEC_SIID_KEY_SD_STATUS, SPEC_PIID_KEY_SD_DURATION
} from "./AlarmUtilV2";
import {
  MEMORY_CARD_MANAGEMENT_DURATION_PIID,
  MEMORY_CARD_MANAGEMENT_FREE_PIID,
  MEMORY_CARD_MANAGEMENT_SIID,
  MEMORY_CARD_MANAGEMENT_STATUS_PIID,
  MEMORY_CARD_MANAGEMENT_TOTAL_PIID, MEMORY_CARD_MANAGEMENT_USED_PIID
} from "./CarSpecConstant";

export const MISSCommand_ECO = {
  MISS_CMD_NETWORK_STATUS: 0x114,
  MISS_CMD_HANGUP_CALL: 0x301,
  MISS_CMD_PLUGIN_CALL: 0x302,
  MISS_CMD_DEVICE_SEND: 0x303,
  MISS_CMD_HUMAN_POSITION: 0x304,
  MISS_CMD_RESET_STATUS: 0x202,
  MISS_CMD_MIC_OPEN: 0x2000,
  MISS_CMD_MIC_CLOSE: 0x2001,
};
Object.freeze(MISSCommand);

const kConnectionCallBackName = 'connectionCallBack';
const kCommandReceiveCallBackName = 'commandReceiveCallBack';
const TAG = "CameraPlayer";
export default class CameraPlayer {
  static SD_CARD_NEED_FORMAT_CODE = 8;
  static SD_CARD_TOO_SMALL_CODE = 9;
  static SD_CARD_FILE_ERROR_CODE = 10;
  static SD_CARD_INCOMPATIBLE_CODE = 11;
  static getInstance() {
    if (this.instance == null) {
      this.instance = new CameraPlayer();
    }
    return this.instance;
  }
  static oneKeyCallEvent_051a01 = "12.1";
  //
  static callEventRequirePermission_086ac1 = "18.1";

  constructor() {
    let that = this;
    this.connectionListener = DeviceEventEmitter.addListener(kConnectionCallBackName, this.connectionHandler);
    this.cmdReceiveListener = DeviceEventEmitter.addListener(kCommandReceiveCallBackName, this.p2pCommandHandler);
    this.connectionState = { state: 0, error: 0 };
    this.oneKeyCallReceived = null;
    this.callStateReceived = null;
    this.connectionCallback = null;
    this.p2pCommandCallback = null;
    this.timestampCallback = null;
    this.networkInfoCallback = null;
    this.powerOffCallback = null;
    this.workModeStateCallback = null;
    this.temperatureStateCallback = null;
    this.waterTimeCallback = null;
    this.sdCallback = null;
    this.remoteViewStateCallback = null;
    this.hdrStateCallback = null;
    this.playbackStateCallback = null;
    this.privacyStateCallback = null;
    this.speed = 1;
    this.lastSessionId = 0;
    this.isPowerOn = true;
    this.networkState = 0;

    this.isMobileNetwork = false;

    this.videoCallState = -1;
    this.lastNeedStorageInfo = false;
    Service.miotcamera.setCurrentDeviceIsMissFirmware(VersionUtil.judgeIsMiss(Device));// 每次连接的时候都更新。
    Service.miotcamera.bindP2PCommandReceiveCallback(kCommandReceiveCallBackName);

    this.isForeground = true;
    // 监听手机网络状态变化
    this.cellPhoneNetworkStateChanged = HostEvent.cellPhoneNetworkStateChanged.addListener((networkInfo) => {
      LogUtil.logOnAll(TAG, "network change", this.networkState, networkInfo.networkState);
      if (this.networkState == networkInfo.networkState) {
        return;//状态一样，不要通知出去。
      }
      //网络发生了变化，要重置connection state
      this.connectionState.state = MISSConnectState.MISS_Connection_Disconnected;
      if (networkInfo.networkState == 0 || networkInfo.networkState == -1) {
        this.pauseCallback && this.pauseCallback();
        // 有些操作依赖于连接状态，需要在断连之前就调用stopAll
        if (this.connectionCallbackArray.length > 0) {
          this.connectionCallbackArray.forEach((connectionCallback) => {
            connectionCallback(this.connectionState);
          });
        }
        if (Package.entryInfo.mobileType != "car") {
          // 调用这个方法后会导致车机直播无法正常加载，车机不关心网络条件，走车机局域网热点
          this.disconnectToDevice();
        }
      }

      this.networkState = networkInfo.networkState;

      if (this.networkInfoCallbackArray.length > 0) {
        this.networkInfoCallbackArray.forEach((networkCallback) => {
          networkCallback(this.connectionState);
        });
      }

      if (this.networkInfoCallback != null) {
        if (networkInfo == 1) {
          this.isMobileNetwork = true;
        } else {
          this.isMobileNetwork = false;
        }
        // Service.smarthome.reportLog(Device.model, "networkchanged handler:" + this.networkInfoCallback);
        LogUtil.logOnAll(TAG, "currentNetwork:" + networkInfo.networkState);
        this.networkInfoCallback(networkInfo.networkState);
      }
    });

    this._deviceStatusListener = DeviceEvent.deviceReceivedMessages
      .addListener(
        (device, map, res) => {
          LogUtil.logOnAll("deviceReceivedMessages", JSON.stringify(res), " isPowerOn:" + this.isPowerOn);// 收到了服务器下发的消息
          try {
            LogUtil.logOnAll("CameraPlay", "receive device change message:" + JSON.stringify(res) + " using spec:" + VersionUtil.isUsingSpec(Device.model) + " this.isPowerOn:" + this.isPowerOn);
            if (VersionUtil.isUsingSpec(Device.model)) {
              let key = res[0].key;
              let value = res[0].value[0];
              if (key == "prop.2.1") {
                this.isPowerOn = value;
                console.log("===========0",this.isPowerOn);
                StorageKeys.IS_POWER_ON
                  .then((result) => {
                    if (typeof (result) === "string") {
                      console.log("===========2",this.isPowerOn, result);
                      result = true;// 默认时有电的
                    }
                    console.log("===========1",this.isPowerOn, result);
                    if (result != this.isPowerOn && this.powerOffCallback != null) { // 如果状态与本地不一致，就要通知刷新
                      StorageKeys.IS_POWER_ON = this.isPowerOn;
                      this.powerOffCallback(this.isPowerOn, false, true);// ispoweron
                    }
                  })
                  .catch((err) => {

                  });
              } else if (key == "prop.2.2") {
                let isFlipOn = value;
                StorageKeys.IS_IMAGE_FLIP
                  .then((result) => {
                    if (typeof (result) === "string") {
                      result = false;
                    }
                    if (result != isFlipOn) {
                      StorageKeys.IS_IMAGE_FLIP = isFlipOn;
                    }
                  })
                  .catch((error) => {
                  });
              } else if (key == "event.12.3") {
                console.log("=================通话属性event监听",value)
                this.oneKeyCallReceived && this.oneKeyCallReceived(3);
              } else if (key == "prop.12.4") {
                console.log("=================通话属性prop监听",value)
                if (value != undefined || value != '') {
                  this.videoCallState = value;
                }
                LogUtil.logOnAll("callState is change");
                if (this.callStateReceived == null) {
                  LogUtil.logOnAll("callStateReceived is null");
                }
                this.callStateReceived && this.callStateReceived(value);
              } else if (key == "prop.2.5") {
                if (value != undefined || value != '') {
                  this.waterTimeCallback && this.waterTimeCallback(value);
                  StorageKeys.IS_WATERMARK_OPEN.then((res) => {
                    if (res === "" || res == null) {
                      res = true;
                    }
                    if (res != value) {
                      StorageKeys.IS_WATERMARK_OPEN = value;
                    }
                  }).catch(() => {

                  });
                }
              } else if (key == "prop.20.2") {
                if (value != undefined || value != '') {
                  this.workModeStateCallback && this.workModeStateCallback(value);
                }
              } else if (key == "prop.20.14") {
                this.temperatureStateCallback && this.temperatureStateCallback(value);
              } else if (key == "prop.4.1") {
                this.sdCallback && this.sdCallback(value);
              } else if (key == "prop.20.3") {
                this.remoteViewStateCallback && this.remoteViewStateCallback(value);
              } else if (key == "prop.2.17") {
                this.hdrStateCallback && this.hdrStateCallback(value);
              } else if (key == "prop.20.18") {
                this.playbackStateCallback && this.playbackStateCallback(value);
              } else if (key == "prop.20.21") {
                this.privacyStateCallback && this.privacyStateCallback(value);
              }
            } else {
              let value = res[0].value;
            
              let prop = value[0];
              
              if (prop === "off") {
                this.isPowerOn = false;
              } else {
                this.isPowerOn = true;
              }
              // compare with local state;
              StorageKeys.IS_POWER_ON
                .then((result) => {
                  if (typeof (result) === "string") {
                    result = true;// 默认时有电的
                  }
                  if (result != this.isPowerOn && this.powerOffCallback != null) { // 如果状态与本地不一致，就要通知刷新
                    StorageKeys.IS_POWER_ON = this.isPowerOn;
                    this.powerOffCallback(this.isPowerOn, false, true);// ispoweron
                  }
                })

                .catch((err) => {

                });


              value = res[1].value;

              let isFlipOn = false;
              if (VersionUtil.isUsingSpec(Device.model)) {
                isFlipOn = value;
              } else {
                if (value == "on") {
                  isFlipOn = true;
                } else {
                  isFlipOn = false;
                }
              }

              StorageKeys.IS_IMAGE_FLIP
                .then((result) => {
                  if (typeof (result) === "string") {
                    result = false;
                  }
                  if (result != isFlipOn) {
                    StorageKeys.IS_IMAGE_FLIP = isFlipOn;
                  }
                })
                .catch((error) => {

                });
              if (!VersionUtil.judgeIsV1(Device.model)) {
                return;
              }
              value = res[2].value[0];
              if (this.bandNearbyCallback != null) {
                this.bandNearbyCallback(value);
              }


            }
           
          } catch (exception) {
            return;// 不管
          }
          // let status = map.get("prop.on") || {};
          // let sRGB = "#" + this.getNewRGB(status.rgb >> 16, (status.rgb >> 8) & 0x00ff, (status.rgb & 0x0000ff));
          // this.setState({ "resultViewColor": sRGB });
        });

    if (VersionUtil.judgeIsV1(Device.model)) {
      Device.getDeviceWifi().subscribeMessages("prop.power", "prop.flip", "prop.band_nearby")
        .then((listener) => {
          this.propListener = listener;
        })
        .catch((error) => {
          // ignore
        });
    } else {
      let powerProp = "prop.power";
      let flipProp = "prop.flip";
      let isCallProp = "prop.12.4";
      let callEvent = "event.12.3";
      let waterProp = "prop.2.5";
      let workModeProp = "prop.20.2";
      let temperatureProp = "prop.20.14";
      let sdProp = "prop.4.1";
      let remoteProp = "prop.20.3";
      let hdrProp = "prop.2.17";
      let playbackProp = "prop.20.18";
      // 一键隐私
      let privacyProp = "prop.20.21";
      if (VersionUtil.isUsingSpec(Device.model)) {
        powerProp = "prop.2.1";
        flipProp = "prop.2.2";
      }
      Device.getDeviceWifi().subscribeMessages(powerProp,isCallProp,callEvent,waterProp,workModeProp,temperatureProp,sdProp,remoteProp,hdrProp,playbackProp,privacyProp)
        .then((listener) => {
          this.propListener = listener;
        })
        .catch((error) => {
          // ignore
          console.log("===============订阅属性",error);
        });
    }

    this.notifyListener = PackageEvent.packageReceivedInformation.addListener((result) => {
      let did = result.did;
      let event = result.event;
      let extra = result.extra;
      let isNotified = result.isNotified;
      let time = result.time;
      LogUtil.logOnAll("packageReceivedInformation====", JSON.stringify(result));
      // console.log('push', result);
      if (Platform.OS == "ios") {
        extra = result.value;
        did = extra.did;
        isNotified = extra.isNotified == "0" ? true : false;// 通知栏来的消息的值是0， 直接收到的应该是1
      }
      if (VersionUtil.Model_chuangmi_051a01 == Device.model || VersionUtil.Model_chuangmi_086ac1 == Device.model) {
        // result={"open_plugin_main_process_send_message_proxy":"1650024185958","open_plugin_main_process_send_message_inner":"1650024185958","open_plugin_api_send_message_type":"11","ensure_service_start_to_ensure_service_success_cost":"0","rev_message_to_core_ready_cost":"1","plugin_cache_ready_to_work_thread_cost":"0","work_thread_to_ensure_service_start_cost":"1","isNotified":"false","extra":"[]","open_plugin_downloaded":"true","event":"12.1","mj_dialog_theme_pad":"1","type":"ScenePush","time":"1650024185","did":"1070202740","extra_start_rnplugin_activity":"false","open_plugin_api_get_available_cost":"0","open_plugin_api_sdk_check_background":"false","open_plugin_plugin_process_rev_message":"1650024185959","open_plugin_api_send_message_internal":"1650024185953","open_plugin_click_start_time":"1650024185952","core_ready_to_plugin_cache_ready_cost":"0","open_plugin_api_plugin_ready":"1650024185954","process_reuse_enter_type":"0","open_plugin_plugin_process_load_rn":"1650024185961","open_plugin_cached":"true","open_plugin_api_std_try_check":"1650024185953","ensure_service_success_to_ensure_service_main_thread_cost":"0"}
        LogUtil.logOnAll("packageReceivedInformation====Model_chuangmi_051a01===event=", event);
        if (CameraPlayer.oneKeyCallEvent_051a01 == event) {
          if (time && Date.now() / 1000 - time > 30) {
            this._toAlarmPage(extra);
            return;
          }
          // 20240907 m300不存在接听页
          return;
          if (Platform.OS == "ios") {
            if (extra.isNotified != undefined) {
              LogUtil.logOnAll("one key call has extra is alarm message!!");
              return;
            }
          }
          try {
            let rootStack = getStack();
            const routes = rootStack._navigation.state.routes;
            LogUtil.logOnAll("packageReceivedInformation===rootStack._navigation.state.routes=", JSON.stringify(routes), " size=", routes.length);
            // if (routes.length > 1) {
            if (Device.model == "chuangmi.camera.086ac1") {
              rootStack._navigation.navigate("OneKeyCallPageV2", { privacyed: true });

            } else {
              rootStack._navigation.navigate("OneKeyCallPage", { privacyed: true });
            }
            // setTimeout(() => {
            //   this.receivedOneKeyCall();
            // }, 100);
            // } else {
            //   this.receivedOneKeyCall();
            // }
          } catch (e) {
            LogUtil.logOnAll("packageReceivedInformation===rootStack._navigation.state.routes error=", JSON.stringify(e));
            this.receivedOneKeyCall();
          }
          return;
        }

        if (CameraPlayer.callEventRequirePermission_086ac1 == event) {
          // 微信视频通话，需要请求权限 首页时需要弹框提示
          this.permissionParam = {
            showPermission: true
          }

          const navigationRouter = getStack()._navigation;
          let rootStack = getStack();
          const routes = rootStack._navigation.state.routes;
          console.log("插件内点击授权push消息",routes.length,JSON.stringify(routes));
          if (routes.length > 1) {
            if (Platform.OS == "android") {
              getStack()._navigation.navigate('LiveVideoPageV2',this.permissionParam);
              DeviceEventEmitter.emit("SHOW_AUTH_PUSH_WX", {});
            }
          } else {
            getStack()._navigation.replace('LiveVideoPageV2',this.permissionParam);
            DeviceEventEmitter.emit("SHOW_AUTH_PUSH_WX", {});
          }

          return;
        }
      }
      if (!isNotified) {
        return;
      }
      if (extra == null || event == null) {
        return;
      }
      new PushHandler().handlePushFromInnerPlugin(did, event, extra, time);
    });

    this.connectionCallbackArray = [];
    this.networkInfoCallbackArray = [];

    // this.handleIOSBackground();
    this.lastRecordTime = new Date().getTime();

    this.sdcardCode = -1;
    
  }

  _toAlarmPage(extra) {
    let data = extra;
    if (typeof (data) == "string") {
      data = JSON.parse(extra);
    }
    let videoFileId = data.fileId;
    if (!videoFileId) {
      LogUtil.logOnAll("CameraPlayer", "!videoFileId and _gotoAlarmList");
      this._gotoAlarmList();
      return;
    }
    let createTime = data.createTime;
    let isAlarm = true;
    if (data.hasOwnProperty("isAlarm")) {
      isAlarm = data.isAlarm;
    }
    let offset = 0;
    if (data.offset) {
      offset = data.offset;
    }
    this._gotoAlarmVideoUI({
      item: {
        fileId: videoFileId, createTime: createTime, offset: offset, isAlarm: isAlarm,
        playCfg: { loader: "_CloudEventLoader" }
      }, cfg: { loader: Singletons.CloudEventLoader, player: UriPlayer }, event: 'Default',
      lstType: "push",
      items: null,
      loaderArgs: { startDate: createTime ? new Date(createTime) : new Date(), filter: "Default", nextDate: null },
      pushType: "inner"
    });
  }
  _gotoAlarmVideoUI(params) {
    let rootStack = getStack();
    rootStack._navigation.navigate("AlarmVideoUI", params);
  }

  _gotoAlarmList() {
    let rootStack = getStack();
    let alarmUIParam = {
      lstType: "push",
      pushType: "inner"
    };
    rootStack._navigation.navigate("AlarmPage", alarmUIParam);
  }

  getNetworkType() {
    return this.networkState;
  }

  isMobile() {
    return this.isMobileNetwork;
  }

  
  cancleIOSBackground() { // 跳转到native页面时，cancle通知
    if (Platform.OS === "android") {
      return;
    }
    if (this.onresumeListener) {
      this.onresumeListener.remove();
    }
    if (this.willpauseListener) {
      this.willpauseListener.remove();
    }
  }

  getPowerState() {
    return this.isPowerOn;
  }

  setPowerState(isPowerOn) {
    this.isPowerOn = isPowerOn;
    StorageKeys.IS_POWER_ON = isPowerOn;
  }

  getConnectionState() {
    return this.connectionState;
  }

  startConnect() { // 这里负责连接
    console.log("++++++startConnect");
    LogUtil.logOnAll("start connect to device isDisconnected:" + this.isDisconnected());
    // Service.miotcamera.setCurrentDeviceIsMissFirmware(VersionUtil.judgeIsMiss(Device));// 每次连接的时候都更新。
    Service.miotcamera.setCurrentDeviceIsMissFirmware(true);// 每次连接的时候都更新。
    if (!this.isDisconnected()) { // 即使是连接中，也不要重新发起了，避免多次发起连接导致的问题； 连接状态会分发给其他人的
      console.log("已经处于连接过程中");
      return;// 已经处于连接状态
    }
    Service.miotcamera.connectToDeviceWithStateChangeCallBack(kConnectionCallBackName);
  }

  connectionHandler = (connectionState) => {
    console.log('reach connectionCallback get callback');
    LogUtil.logOnAll(TAG, "connectionHandler", JSON.stringify(connectionState));
    this.connectionState = connectionState;

    if (this.connectionCallbackArray.length > 0) {
      this.connectionCallbackArray.forEach((connectionCallback) => {
        connectionCallback(connectionState);
      });
    }

    if (this.connectionState.state >= 2) {

      Service.miotcamera.setCurrentDeviceIsMissFirmware(VersionUtil.judgeIsMiss(Device));// 每次连接的时候都更新。
      Service.miotcamera.bindP2PCommandReceiveCallback(kCommandReceiveCallBackName);
    }

    if (this.connectionCallback == null) {
      LogUtil.logOnAll("CameraPlayer connectionCallback is null return");
      return;
    }

    this.connectionCallback(connectionState);


  };

  p2pCommandHandler = ({ command, data }) => {
    if (this.p2pCommandCallback == null) {
      // LogUtil.logOnAll("p2pCommandHandler", "commandCallback == null",command,data);// 收到p2p回复，回调为空
      return;
    }
    this.p2pCommandCallback({ command, data });
  };

  bindP2pCommandCallback(p2pCommandCallback) {
    LogUtil.logOnAll("bindP2pCommandCallback",p2pCommandCallback == null);
    this.p2pCommandCallback = p2pCommandCallback;
  }

  bindOneKeyCallReceived(oneKeyCallReceived) {
    this.oneKeyCallReceived = oneKeyCallReceived;
  }

  bindCallStateReceived(callStateReceived) {
    LogUtil.logOnAll("bindCallStateReceived",callStateReceived == null);

    this.callStateReceived = callStateReceived;
  }
  receivedOneKeyCall() {
    if (this.oneKeyCallReceived == null) {
      LogUtil.logOnAll("this.oneKeyCallReceived == null");
      return;
    }
    this.oneKeyCallReceived();
  }

  bindConnectionCallback(connectionCallback) {
    this.connectionCallback = connectionCallback;
  }

  addConnectionListener(callback) {
    let index = this.connectionCallbackArray.indexOf(callback);
    if (index != -1) {
      throw "already added";// 避免重复添加。
    } else {
      this.connectionCallbackArray.push(callback);
      return {
        remove: () => {
          this.connectionCallbackArray = this.connectionCallbackArray.filter((value) => {
            return value != callback;
          });
        }
      };
    }
  }

  addNetworkListener(callback) {
    let index = this.networkInfoCallbackArray.indexOf(callback);
    if (index != -1) {
      throw "already added";// 避免重复添加。
    } else {
      this.networkInfoCallbackArray.push(callback);
      return {
        remove: () => {
          this.networkInfoCallbackArray = this.networkInfoCallbackArray.filter((value) => {
            return value != callback;
          });
        }
      };
    }
  }

  bindWaterTimeCallback(waterTimeCallback) {
    this.waterTimeCallback = waterTimeCallback;
  }

  bindWorkModeStateCallback(callback) {
    this.workModeStateCallback = callback;
  }

  bindTemperatureModeStateCallback(callback) {
    this.temperatureStateCallback = callback;
  }

  bindSDCallback(callback) {
    this.sdCallback = callback;
  }

  bindRemoteViewStateCallback(callback) {
    this.remoteViewStateCallback = callback;
  }

  bindHdrStateCallback(callback) {
    this.hdrStateCallback = callback;
  }

  bindPlaybackStateCallback(callback) {
    this.playbackStateCallback = callback;
  }

  bindPrivacyStateCallback(callback) {
    this.privacyStateCallback = callback;
  }

  bindPowerOffCallback(powerOffCallback) {
    this.powerOffCallback = powerOffCallback;
  }

  bindPauseAllCallback(pauseCallback) {
    this.pauseCallback = pauseCallback;
  }

  bindBandNearbyCallback(bandNearbyCallback) {
    this.bandNearbyCallback = bandNearbyCallback;
  }

  isConnected() {
    return this.connectionState.state >= 2;// 已经连接了
  }
  isDisconnected() {
    return this.connectionState.state == MISSConnectState.MISS_Connection_Disconnected;
  }
  isConnecting() {
    return this.connectionState.state == MISSConnectState.MISS_Connection_Connecting;
  }

  startPlayBack(startTime, offset, endTime, speed) {
    // this.connectionCallback = connectionCallback;
    return new Promise((resolve, reject) => {
      if (startTime == 0) {
        reject("参数错误");
        return;
      }
      if (!this.isConnected()) {
        reject("p2p连接已经断开");// 没有连接 把状态丢出去
        // //这里不负责重连， 让外面的人去做检测。
        // this.startConnect();
        return;
      }
      // 如果已经连接成功
      this.lastSessionId = startTime;
      let keepPlaying = endTime == 0 ? 1 : 0;
      let params = {};
      params.sessionid = startTime;
      params.starttime = startTime;
      params.endtime = endTime;
      params.autoswitchtolive = 0;
      params.offset = offset;
      params.speed = speed;
      params.avchannelmerge = 1;
      params.channel = 0;
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_PLAYBACK_REQ, params)
        .then((retCode) => {
          console.log("playback ok");
          resolve(retCode);
        })
        .catch((err) => {
          console.log(err);
          reject(err);
        });

    });
  }

  changeSpeed(speed) {
    return new Promise((resolve, reject) => {
      let params = {};
      params.sessionid = this.lastSessionId;
      params.speed = speed;
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_PLAYBACK_SET_SPEED, params)
        .then((retCode) => {
          console.log("playback ok changeSpeed");
          resolve(retCode);
          this.speed = speed;
        })
        .catch((err) => {
          console.log(err);
          reject(err);
        });
    });

  }

  getPlaybackTimetampInterval() {
    clearInterval(this.interval);
    this.interval = setInterval(() => {
      Service.miotcamera.getCurrentFrameInfo()
        .then((res) => {
          let timestamp = res["timestamp"];// res frameInfo
          if (timestamp > 0) {
            this.timestamp = timestamp;
            if (this.timestampCallback != null) {
              this.timestampCallback(this.timestamp);// 回调出去
            }
          } else {
            LogUtil.logOnAll("CameraPlayer getCurrentFrameInfo timestamp error", timestamp);
          }
        })
        .catch((exception) => {
          // ignore
          LogUtil.logOnAll("CameraPlayer getCurrentFrameInfo error", exception);
        });
    }, 1000);// 单位 1000

  }

  stopPlaybackTimestampInterval() {
    clearInterval(this.interval);
  }

  bindPlaybackTimestampCallback(timestampCallback) {
    this.timestampCallback = timestampCallback;
  }

  bindNetworkInfoCallback(networkInfoCallback) {
    this.networkInfoCallback = networkInfoCallback;
  }

  stopVideoPlay() {
    Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_VIDEO_STOP, {})
      .then((retCode) => {
        console.log("stopVideo success");
        console.log(retCode);
      })
      .catch((err) => console.log(err));
  }

  queryShouldPauseOn4G() {
    return new Promise((resolve, reject) => {
      console.log("start fetch network info");
      NetInfo.fetch()
        .then((networkInfo) => {
          console.log(`networkInfo:${ networkInfo }`);
          if (Platform.OS != 'android') { // ios返回obj  android返回type str
            networkInfo = networkInfo.type.toUpperCase();
          }
          
          if (networkInfo == "CELLULAR") {
            this.isMobileNetwork = true;
            this.networkState = 1;
          } else {
            this.isMobileNetwork = false;
            if (networkInfo == "NONE" || networkInfo == "UNKNOWN") {
              this.networkState = 0;
            } else {
              this.networkState = 2;
            }
          }
          return this.queryLocal4g(networkInfo);
        })
        .then((result) => {
          console.log(`get status:${ result }`);
          resolve(result);
        })
        .catch((err) => {
          reject(err);
        });
    });



  }


  queryLocal4g(state) {
    return new Promise((resolve, reject) => {
      StorageKeys.IS_DATA_USAGEE_WARNING.then((res) => { // 是否使用流量保护
        let pauseOnCellular = true;
        if (typeof (res) === "string") {
          StorageKeys.IS_DATA_USAGEE_WARNING = false;
          pauseOnCellular = false;
        } else {
          pauseOnCellular = res;
        }
        resolve({ state, pauseOnCellular });
      }).catch((error) => {
        console.log(error);
        let pauseOnCellular = false;
        StorageKeys.IS_DATA_USAGEE_WARNING = false;
        resolve({ state, pauseOnCellular });
      });
    });
  }

  isIOSPluginForeGround() {
    if (Platform.OS != "android") {
      return this.isForeground;
    }
    return false;
  }


  sendDirectionCmd(type, needToast = false) {
    let obj = {};
    obj["operation"] = type;

    // 每隔2s记录一次吧，避免太快了 影响性能
    this.lastRecordTime = new Date().getTime();
    LogUtil.logOnAll("sending direction cmd", type, obj);// 打印发送方向转盘的trace，定位一直发送转动方向的原因
    

    Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_MOTOR_REQ, obj)
      .then((result) => {
        console.log("+++++++++++success",result);
        if (type == DirectionViewConstant.CMD_CHECK) {
          Toast.success("camera_celibrating");
        }

      })
      .catch((error) => {
        console.log("+++++++++++error",error);
        if (needToast) {
          Toast.fail("action_failed");
        }
      });
  }

  isLocalSdcardStatusNormal() { // storageUI页面，由于卡状态和卡数据分离，获取卡数据的时候，又依赖卡状态，特意在这里开一个接口出来，以便拦截卡数据请求
    return this.sdcardCode == 0 || this.sdcardCode == 2 || this.sdcardCode == -1 || this.sdcardCode == CameraPlayer.SD_CARD_FILE_ERROR_CODE; // -1的时候，卡状态没有获取到，有可能是点击太快了，也让请求数据
  }

  getSdcardStatusDirect(needStorageInfo = false) {

    return new Promise((resolve, reject) => {
      let timestamp = new Date().getTime();
      console.log('走了这里0', this.sdcardCode,timestamp , this.lastRequestSdcardStatusTime )

      if (timestamp - this.lastRequestSdcardStatusTime < 1000) {
        if (this.sdcardCode == null || this.sdcardCode == -1) {
          LogUtil.logOnAll("CameraPlayer", "reqeust too fast && sdcardCode null");
          reject({ sdcardCode: -1 });
          return;
        }
        LogUtil.logOnAll("CameraPlayer", "reqeust too fast && sdcardCode normal");
        if (needStorageInfo && (this.totalSize == null || this.videoSize == null || this.idleSize == null) && this.lastNeedStorageInfo === needStorageInfo) {
          LogUtil.logOnAll("CameraPlayer", "request too fast && sdcardCode normal but size info is empty, return reject");
          reject({ sdcardCode: this.sdcardCode, recordMode: this.recordMode, duration: this.duration });
          return;
        }
        if ((needStorageInfo && !(this.totalSize == null || this.videoSize == null || this.idleSize == null)) || !needStorageInfo) {
          resolve({ recordMode: this.recordMode, sdcardCode: this.sdcardCode, totalSize: this.totalSize, videoSize: this.videoSize, idleSize: this.idleSize, duration: this.duration });
          return;
        }
        LogUtil.logOnAll("CameraPlayer", "should request new sdcard info");
      }
      this.lastRequestSdcardStatusTime = timestamp;
      this.lastNeedStorageInfo = needStorageInfo;
      let params = needStorageInfo ? [
          { did: Device.deviceID, siid: MEMORY_CARD_MANAGEMENT_SIID, piid: MEMORY_CARD_MANAGEMENT_STATUS_PIID },
          { did: Device.deviceID, siid: MEMORY_CARD_MANAGEMENT_SIID, piid: MEMORY_CARD_MANAGEMENT_TOTAL_PIID },
          { did: Device.deviceID, siid: MEMORY_CARD_MANAGEMENT_SIID, piid: MEMORY_CARD_MANAGEMENT_FREE_PIID },
          { did: Device.deviceID, siid: MEMORY_CARD_MANAGEMENT_SIID, piid: MEMORY_CARD_MANAGEMENT_USED_PIID },
          { did: Device.deviceID, siid: MEMORY_CARD_MANAGEMENT_SIID, piid: MEMORY_CARD_MANAGEMENT_DURATION_PIID }] :
        [{ did: Device.deviceID, siid: MEMORY_CARD_MANAGEMENT_SIID, piid: MEMORY_CARD_MANAGEMENT_STATUS_PIID }];
      Service.spec.getPropertiesValue(params, 2)
        .then((result) => {
          console.log("CameraPlayer", JSON.stringify(result));
          LogUtil.logOnAll("CameraPlayer", "sdcard status:" + JSON.stringify(result));
          let model = Device.model;
          let isResLen = result.length;
          console.log('getPropertiesValue', isResLen);
          if (result[0].code == 0) {
            // this.recordMode = result[0].value || 0;
            this.sdcardCode = result[0].value;
            if (this.sdcardCode == null) {
              this.sdcardCode = -1;
            }
            if (this.sdcardCode == 7) {
              this.sdcardCode = 5;
            }
            if (this.sdcardCode == 6) {
              this.sdcardCode = 4;
            }
            if (this.sdcardCode < 0 || this.sdcardCode > 11) {
              this.sdcardCode = -1;
            }
            // this.sdcardCode = 3;
            // CHUANGMI-12763
            // 029a02增加256G大容量功能 固件版本从固件版本从4.3.4_0360 开始返回的数据以M为单位传输 插件对大于4.3.4_0360 与 小于4.3.4_0360返回的数据的计算处理方式不同
            // 通过判断卡容量判断是否是大于4.3.4_0360的固件
            // // model为029a02
            if (needStorageInfo  && model == 'chuangmi.camera.029a02' && isResLen >= 4 ) {
              console.log('result[2].value',result[2].value)
              if (result[1].value <= 262144) { // 固件版本大于4.3.4_0360
                this.totalSize = (result[1].value || 0) * 1024 * 1024;
                this.idleSize = (result[2].value || 0) * 1024 * 1024;
                this.videoSize = (result[3].value || 0) * 1024 * 1024;
              } else { // 固件版本小于4.3.4_0360
                this.totalSize = (result[1].value || 0) * 1024;
                this.idleSize = (result[2].value || 0) * 1024;
                this.videoSize = (result[3].value || 0) * 1024;
              }
            } else if (needStorageInfo) { // model为非029a02
              this.totalSize = (result[1].value || 0) * 1024;
              this.idleSize = (result[2].value || 0) * 1024;
              this.videoSize = (result[3].value || 0) * 1024;
              // this.duration  = (result[4].value || 0) / 60.0 / 24.0;
              this.duration = ((result[4].value || 0) / 60.0 / 24.0).toFixed(1);
            }
            let res = { recordMode: this.recordMode, sdcardCode: this.sdcardCode, totalSize: this.totalSize, videoSize: this.videoSize, idleSize: this.idleSize, duration: this.duration };
            resolve(res);
          } else {
            this.sdcardCode = -1;
            this.recordMode = 2;
            this.totalSize = 0;
            this.idleSize = 0;
            this.videoSize = 0;
            this.duration = 0;
            LogUtil.logOnAll("CameraPlayer", "sdcard error");
            let res = { error: "get spec error", recordMode: this.recordMode, sdcardCode: this.sdcardCode, totalSize: this.totalSize, videoSize: this.videoSize, idleSize: this.idleSize, duration: this.duration };
            reject(res);
          }

        })
        .catch((error) => {
          this.sdcardCode = -1;
          this.recordMode = 2;
          this.totalSize = 0;
          this.idleSize = 0;
          this.videoSize = 0;
          this.duration = 0;
          LogUtil.logOnAll("CameraPlayer", "sdcard error" + JSON.stringify(error));

          let res = { error: "get spec error", recordMode: this.recordMode, sdcardCode: this.sdcardCode, totalSize: this.totalSize, videoSize: this.videoSize, idleSize: this.idleSize, duration: this.duration };
          reject(res);
        });

    });
  }

  getSdcardStatus(needStorageInfo = false) {

    return new Promise((resolve, reject) => {
      let timestamp = new Date().getTime();
      console.log('走了这里0', this.sdcardCode,timestamp , this.lastRequestSdcardStatusTime )

      if (timestamp - this.lastRequestSdcardStatusTime < 1000) {
        if (this.sdcardCode == null || this.sdcardCode == -1) {
          LogUtil.logOnAll("CameraPlayer", "reqeust too fast && sdcardCode null");
          reject({ sdcardCode: -1 });
          return;
        }
        LogUtil.logOnAll("CameraPlayer", "reqeust too fast && sdcardCode normal");
        if (needStorageInfo && (this.totalSize == null || this.videoSize == null || this.idleSize == null)) {
          LogUtil.logOnAll("CameraPlayer", "request too fast && sdcardCode normal but size info is empty, return reject");
          reject({ sdcardCode: this.sdcardCode, recordMode: this.recordMode, duration: this.duration });
          return;
        }
        resolve({ recordMode: this.recordMode, sdcardCode: this.sdcardCode, totalSize: this.totalSize, videoSize: this.videoSize, idleSize: this.idleSize, duration: this.duration });
        return;
      }
      this.lastRequestSdcardStatusTime = timestamp;

      let params = needStorageInfo ? [
        { sname: CAMERA_SDCARD_SIID, pname: CAMERA_SDCARD_STATUS_PIID },
        { sname: CAMERA_SDCARD_SIID, pname: CAMERA_SDCARD_TOTAL_SPACE_PIID },
        { sname: CAMERA_SDCARD_SIID, pname: CAMERA_SDCARD_FREE_SPACE_PIID },
        { sname: CAMERA_SDCARD_SIID, pname: CAMERA_SDCARD_USED_SPACE_PIID },
        { sname: CAMERA_SDCARD_SIID, pname: SPEC_PIID_KEY_SD_DURATION }] :
        [{ sname: CAMERA_SDCARD_SIID, pname: CAMERA_SDCARD_STATUS_PIID }];
      AlarmUtilV2.getSpecPValue(params, 2)
        .then((result) => {
          console.log("CameraPlayer", JSON.stringify(result));
          LogUtil.logOnAll("CameraPlayer", "sdcard status:" + JSON.stringify(result));
          let version = Device.lastVersion;
          let isNeedVersion = VersionUtil.isVersionBiggerThanExpected(version, '4.3.4_0360');
          let model = Device.model;
          let isResLen = result.length;
          console.log('getPropertiesValue', isResLen);
          if (result[0].code == 0) {
            // this.recordMode = result[0].value || 0;
            this.sdcardCode = result[0].value;
            if (this.sdcardCode == null) {
              this.sdcardCode = -1;
            }
            if (this.sdcardCode == 7) {
              this.sdcardCode = 5;
            }
            if (this.sdcardCode == 6) {
              this.sdcardCode = 4;
            }
            if (this.sdcardCode < 0 || this.sdcardCode > 11) {
              this.sdcardCode = -1;
            }
            // this.sdcardCode = 3;
            // CHUANGMI-12763
            // 029a02增加256G大容量功能 固件版本从固件版本从4.3.4_0360 开始返回的数据以M为单位传输 插件对大于4.3.4_0360 与 小于4.3.4_0360返回的数据的计算处理方式不同
            // 通过判断卡容量判断是否是大于4.3.4_0360的固件
            // // model为029a02
            if (needStorageInfo  && model == 'chuangmi.camera.029a02' && isResLen >= 4 ) {
              console.log('result[2].value',result[2].value)
              if (result[1].value <= 262144) { // 固件版本大于4.3.4_0360
                this.totalSize = (result[1].value || 0) * 1024 * 1024;
                this.idleSize = (result[2].value || 0) * 1024 * 1024;
                this.videoSize = (result[3].value || 0) * 1024 * 1024;
              } else { // 固件版本小于4.3.4_0360
                this.totalSize = (result[1].value || 0) * 1024;
                this.idleSize = (result[2].value || 0) * 1024;
                this.videoSize = (result[3].value || 0) * 1024;
              }
            } else if (needStorageInfo) { // model为非029a02
              this.totalSize = (result[1].value || 0) * 1024;
              this.idleSize = (result[2].value || 0) * 1024;
              this.videoSize = (result[3].value || 0) * 1024;
              this.duration  = ((result[4].value || 0) / 60.0 / 24.0).toFixed(1);

            }
            let res = { recordMode: this.recordMode, sdcardCode: this.sdcardCode, totalSize: this.totalSize, videoSize: this.videoSize, idleSize: this.idleSize, duration: this.duration };
            resolve(res);
          } else {
            this.sdcardCode = -1;
            this.recordMode = 2;
            this.totalSize = 0;
            this.idleSize = 0;
            this.videoSize = 0;
            this.duration = 0;
            LogUtil.logOnAll("CameraPlayer", "sdcard error");
            let res = { error: "get spec error", recordMode: this.recordMode, sdcardCode: this.sdcardCode, totalSize: this.totalSize, videoSize: this.videoSize, idleSize: this.idleSize, duration: this.duration };
            reject(res);
          }

        })
        .catch((error) => {
          this.sdcardCode = -1;
          this.recordMode = 2;
          this.totalSize = 0;
          this.idleSize = 0;
          this.videoSize = 0;
          this.duration = 0;
          LogUtil.logOnAll("CameraPlayer", "sdcard error" + JSON.stringify(error));

          let res = { error: "get spec error", recordMode: this.recordMode, sdcardCode: this.sdcardCode, totalSize: this.totalSize, videoSize: this.videoSize, idleSize: this.idleSize, duration: this.duration };
          reject(res);
        });
      
    });    
  }

  getNasConfig() {
    return new Promise((resolve, reject) => {
      let timestamp = new Date().getTime();
      if (timestamp - this.lastRequestNasConfigTime < 1000) {
        if (this.nasConfigValue == null) {
          reject({});
          return;
        }
        resolve(this.nasConfigValue);
        return;
      }
      this.lastRequestNasConfigTime = timestamp;
      RPC.callMethod("nas_get_config", {})
        .then((res) => {
          if (res) {
            this.nasConfigValue = res;
            resolve(res);
          } else {
            reject({});
          }
        }).catch((err) => {
          LogUtil.logOnAll("StorageSetting", "nas_get_config failed" + JSON.stringify(err));
          reject(err);
      });
    });

  }



  disconnectToDevice() {
    LogUtil.logOnAll("CameraPlayer", "disconnect to device;" + new Error().stack);
    Service.miotcamera.disconnectToDevice();
    this.connectionState = { state: 0, error: MISSError.MISS_ERR_CLOSE_BY_LOCAL };
  }

  resetConnectionState() {
    this.connectionState = { state: 0, error: MISSError.MISS_ERR_CLOSE_BY_LOCAL };
  }

  destroy() {
    this.cellPhoneNetworkStateChanged && this.cellPhoneNetworkStateChanged.remove();// 避免内存泄漏
    this.connectionListener && this.connectionListener.remove();
    this.cmdReceiveListener && this.cmdReceiveListener.remove();
    this._deviceStatusListener && this._deviceStatusListener.remove();
    this.propListener && this.propListener.remove();
    this.notifyListener && this.notifyListener.remove();
  }

  static destroyInstance() {
    this.instance.destroy();
    this.instance = null;
  }





  
}