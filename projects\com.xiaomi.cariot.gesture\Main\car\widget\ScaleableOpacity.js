import React from 'react';
import { Animated, TouchableWithoutFeedback, Easing } from 'react-native';
import PropTypes from 'prop-types';
import { handlerOnceTap } from "../../util/HandlerOnceTap";

class ScaleableOpacity extends React.PureComponent {
    static propTypes = {
      style: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
      onPress: PropTypes.func,
      disabled: PropTypes.bool,
      selected: PropTypes.bool,
      children: PropTypes.node,
      scaleValue: PropTypes.number, // 自定义缩放值，默认为20
    };

    constructor(props) {
      super(props);
      this.isPressed = false;
      this.scaleAnim = new Animated.Value(1);
      this.layout = { width: 0, height: 0 };
    }

    calculateScale = () => {
      const { width, height } = this.layout;
      const maxSize = Math.max(width, height);
      const scaleValue = this.props.scaleValue || 20;
      // 计算缩放比例
      let scale = (maxSize - scaleValue) / maxSize;
      // 限制最大缩放比例为0.96
      return Math.min(scale, 0.96);
    }

    onLayout = (event) => {
      this.layout = event.nativeEvent.layout;
    }

    // 创建贝塞尔曲线动画
    createBezierEasing = (x1, y1, x2, y2) => {
      return (t) => {
        // 三次贝塞尔曲线公式
        const cx = 3 * x1;
        const bx = 3 * (x2 - x1) - cx;
        const ax = 1 - cx - bx;

        const cy = 3 * y1;
        const by = 3 * (y2 - y1) - cy;
        const ay = 1 - cy - by;

        const sampleCurveX = (t) => ((ax * t + bx) * t + cx) * t;
        const sampleCurveY = (t) => ((ay * t + by) * t + cy) * t;

        return sampleCurveY(t);
      };
    }

    handlePressIn = () => {
      if (this.isPressed) {
        return;
      }
      this.isPressed = true;
      Animated.timing(this.scaleAnim, {
        toValue: this.calculateScale(),
        duration: 200,
        useNativeDriver: true,
        easing: this.createBezierEasing(0.33, 1, 0.68, 1)
      }).start();
    }

    handlePressOut = () => {
      Animated.timing(this.scaleAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
        easing: this.createBezierEasing(0.11, 0, 0.5, 0)
      }).start(() => {
        this.isPressed = false;
        // if (this.props.onPress) {
        //   this.props.onPress();
        // }
      });
    }

    render() {
      const { style, children, disabled } = this.props;

      return (
        <TouchableWithoutFeedback
          onPressIn={!disabled ? this.handlePressIn : undefined}
          onPressOut={!disabled ? this.handlePressOut : undefined}
          onPress={() => this.props.onPress()}
        >
          <Animated.View
            onLayout={this.onLayout}
            style={[
              style,
              {
                transform: [{ scale: this.scaleAnim }]
              },
              {
                borderWidth: 4,
                borderColor: this.props.selected ? '#1F8FFF' : 'transparent',
                borderRadius: 26
              }
            ]}
          >
            {children}
          </Animated.View>
        </TouchableWithoutFeedback>
      );
    }
}

export default ScaleableOpacity; 