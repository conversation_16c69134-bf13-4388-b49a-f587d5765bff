import React from 'react';
import { View, TouchableOpacity, Text, Image, StyleSheet, PermissionsAndroid, Platform, Dimensions } from 'react-native';
import { StackNavigator } from 'react-navigation';
import ImagePicker, { launchCamera } from 'react-native-image-picker';
import Util from "../util2/Util";
import InputDlgEx from '../widget/InputDlgEx';
import TrackUtil from '../util/TrackUtil';
import { System, Service } from 'miot';
import PermissionUtil from '../util/PermissionUtil';
import { AbstractDialog } from 'miot/ui/Dialog';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import LogUtil from '../util/LogUtil';
import { NavigationBar } from "mhui-rn";
import StorageKeys from '../StorageKeys';
import Toast from '../components/Toast';

export default class InformNoFace extends React.Component {

  state = {
    commentDlg: false
  }
  
    // 拍照录入人脸
    takePhoto = () => {
      // 上传点击信息
      TrackUtil.reportClickEvent("Face_CameraInput_ClickNum");
      // this._startSelectPhoto(0);
      PermissionUtil.checkCameraPermission().then((res) => {
        console.log(res);
        this.props.navigation.navigate('FaceCamera'
          , {
            callback: (data) => {

              if (data) {
                this.setState({
                  commentDlg: true,
                  unMarkfaceUrl: data.faceUrl.uri,
                  unMarkfaceId: data.faceId
                });
              }

            }
          }
        ); 
      }).catch((err) => {
        // 没有相机存储权限
        this.isCheckingPermission = false;
        Toast.success("action_failed");
      });

    }
  
    // 从手机相册选相片
    choosePhoto = () => {
      TrackUtil.reportClickEvent("Face_GalleryInput_ClickNum");
      StorageKeys.IS_AI_FACE_OPEN_TOAST.
        then((res) => {
          this.setState({
            isAIFrame: res
          });
          console.log('不是为第一次进入inform', res);
          if (res) {
            this.setState({
              showTips: false
            });
            PermissionUtil.checkCameraPermission().then((res) => {
              PermissionUtil.checkStoragePermission().then((res) => {
                console.log(res);
                this.selectPhotoTapped();
              }).catch((err) => {
              // 没有读写手机存储权限
                if (err == PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
                  this.setState({ showPermissionDialog: true, permissionRequestState: 1 });
                }
              });
            }
            ).catch((err) => {
            // 没有相机权限
              if (err == PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
                this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
              }
            })
          }
          else {
            this.setState({
              showTips: true
            })
          }
        })
        .catch((err) => {
          console.log('errr', err)
          this.setState({
            showTips: true
          })
        });

    }

    // 在相册中选择相片
    selectPhotoTapped() {
      const options = {
        quality: 1.0,
        maxWidth: 500,
        maxHeight: 500,
        storageOptions: {
          skipBackup: true
        }
      };
      setTimeout(() => {
        // 到相册中选择
        ImagePicker.launchImageLibrary(options, (response) => {
          console.log('response', response);
          // this.setState({
          //     showLoading: true
          // })
          if (response.didCancel) {
            console.log('User cancelled photo picker');
            // Toast.fail("bind_error");
            return;
          } else if (response.error) {
            console.log('ImagePicker Error: ', response.error);
            // 这其实就是一个提示信息
            Toast.fail("bind_error");
            return;
          } else if (response.customButton) { // 用户点击自定义按钮
            console.log('User tapped custom button: ', response.customButton);
            Toast.fail("bind_error");
            return;
          } else { // 选择成功
            Toast.loading('c_setting');
            let path = response.uri.slice(7);
            Service.miotcamera.uploadImageToCameraServer(path).then((result) => {
              LogUtil.logOnAll("FaceManager", `uploadImageToCameraServer success${ JSON.stringify(result) }`);
  
              let res = JSON.parse(result);
              let data = res.data;
              // 如果照片成功，则跳转到FaceManager
              if (res.result == "ok" && data != null && data.faceInfoMetas[0] != null) {

                this.setState(
                  {
                    commentDlg: true,
                    unMarkfaceId: data.faceInfoMetas[0].faceId,
                    unMarkfaceUrl: response.uri
                  }
                );
              } else {
                Toast.fail("face_recognition_fail_tips");
  
              }
            }).catch((err) => {
              LogUtil.logOnAll("FaceManager", `uploadImageToCameraServer failed${ JSON.stringify(err) }`);
  
              let errCode = err.code;
              // 501103 系统错误
              let errMap = { 501103: "cloud_figure_limit", 400305: "cloud_face_limit" };
              // let err = errMap[errCode] || "action_fail";
              Toast.fail('face_recognition_fail_tips', err);
            });
            // You can also display the image using data:
            // let source = { uri: 'data:image/jpeg;base64,' + response.data };
          }
        });
      }, 100);
    }

    // 没显示过人脸管理弹窗就显示提示
    _renderTipsDialogView() {

      return (
        <AbstractDialog
          visible={this.state.showTips}
          useNewTheme
          onDismiss={() => { this.setState({ showTips: false }); }}
          buttons={[
            {
              text: LocalizedStrings["csps_right"],
              style: { color: '#f0ac3d' },
              callback: () => {
                // this.setState({ showTips: false, addMarkedFaceDialogVisible:true });
                this.setState({
                  showTips: false
                }, () => {
                  this.selectPhotoTapped();
                });
                StorageKeys.IS_AI_FACE_OPEN_TOAST = true;
              }
            }
          ]}
        >
          <View
            style={{
              flex: 1,
              flexDirection: "column",
              // height: 200,
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <View>
              <Image style={{ width: 280, height: 200 }} source={require('../../Resources/Images/photo_placeholder.jpg')}>
              </Image>
            </View>
            <View style={{ marginVertical: 5, textAlign: 'center', marginHorizontal: (Dimensions.get("window").width - 280) / 2 }}>
              <Text style={{ fontSize: 12, color: '#000000' }}>
                {LocalizedStrings["pick_album_tips"]}
              </Text>
            </View>
  
          </View>
        </AbstractDialog>
      )
    }
  
    componentDidMount() {
      let title = LocalizedStrings["type_in_face"];
      // LogUtil.logOnAll("setting page title:" + title);
      this.props.navigation.setParams({
        title: title,
        left: [
          {
            key: NavigationBar.ICON.BACK,
            onPress: () => { this.props.navigation.goBack(); }
          }
        ],
        titleStyle: {
          fontSize: 18,
          color: '#333333',
          fontWeight: 500
        }
      });
    }

    // 备注框
    _renderCommentDlg() {
      let obj = {}
      obj.uri = this.state.unMarkfaceUrl
      if (this.state.commentDlg)
        return (
          <InputDlgEx
            title={LocalizedStrings["cloud_comment_dlg_title"]}
            visible={this.state.commentDlg}
            icon={obj}
            listData={this.state.mAllFigureInf}
            onPressed={(aDat) => {
              this.setState({ defVal: aDat.name });
            }}
            onDismiss={(_) => {
              this.renameItem = null;
              this.setState({ commentDlg: false, isRename: false, defVal: '', commentErr: null });
            }}
            inputWarnText={this.state.commentErr}
            inputs={[{
              onChangeText: (text) => {
                console.log(this.tag, "onChangeText", text);
                if (this.state.commentErr != null) {
                  this.setState({ commentErr: null });
                }
              },
              textInputProps: {
                maxLength: 8,
                returnKeyType: "done",
                autoFocus: Util.isHeightPt() ? true : false
              },
              defaultValue: this.state.defVal,
              type: 'DELETE',
              isCorrect: this.state.commentErr == null
            }]}
            buttons={[
              {
                text: LocalizedStrings["action_cancle"],
                callback: (result) => {
                  this.renameItem = null;
                  this.setState({ commentDlg: false, isRename: false, defVal: '', commentErr: null });
                }
                // ignore
              },
              {
                text: LocalizedStrings["csps_right"],
                callback: (result) => {
                  this.setState({ isRename: false, defVal: '', commentErr: null });
                  // console.log(this.state.figureInfos.length, 'this.state.figureInfos')
                  let text = result.textInputArray[0].trim();
  
                  if (text.length > 0 && !this.containsEmoji(text)) {
                    let cmd = null;
                    // 这个人有名字？
  
                    cmd = Util.commentFace(text, this.state.unMarkfaceId);
  
                    if (cmd) {
                      cmd.then((aRet) => {
  
                        this.props.navigation.navigate('FaceManager');
  
                        Toast.success('save_success');
                        this.setState({
                          commentDlg: false,
                          defVal: ''
                        });
                      })
                        .catch((aErr) => {
                          LogUtil.logOnAll("FaceManager", `commentFace failed${ JSON.stringify(aErr) }`);
  
                          this.setState({ commentDlg: false });
                          let errCode = aErr.code;
                          // 400302 人物上限
                          let errMap = { 400302: "figure_max_tips", 400305: "face_max_tips" };
                          let err = errMap[errCode] || "action_fail";
                          Toast.fail(err, err, true);
                          console.log(this.tag, "comment failed", aErr);
                        });
                    } else {
                      console.log(this.tag, "nothing changed");
                    }
  
                  } else {
                    if (this.containsEmoji(text)) {
                      this.setState({ commentErr: LocalizedStrings["no_emoij_tips"] });
                    }
                    else {
                      this.setState({ commentErr: LocalizedStrings["cloud_comment_null"] });
                    }
                  }
                }
              }
            ]}
          />);
    }

    // 暂不支持表情输入
    isEmojiCharacterV2(codePoint) {
      return !((codePoint == 0x0) ||
        (codePoint == 0x9) ||
        (codePoint == 0xA) ||
        (codePoint == 0xD) ||
        ((codePoint >= 0x20) && (codePoint <= 0xD7FF)) ||
        ((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) ||
        ((codePoint >= 0x10000))) ||
        (codePoint == 0xa9 || codePoint == 0xae || codePoint == 0x2122 ||
          codePoint == 0x3030 || (codePoint >= 0x25b6 && codePoint <= 0x27bf) ||
          codePoint == 0x2328 || (codePoint >= 0x23e9 && codePoint <= 0x23fa))
        || ((codePoint >= 0x1F000 && codePoint <= 0x1FFFF))
        || ((codePoint >= 0x2702) && (codePoint <= 0x27B0))
        || ((codePoint >= 0x1F601) && (codePoint <= 0x1F64F));
    }
    containsEmoji(str) {
      let length = str.length;
      for (let i = 0; i < length; ++i) {
        let c = str.charCodeAt(i);
        if (this.isEmojiCharacterV2(c)) {
          return true;
        }
      }
      return false;
    }

    render() {

      let NoFaceView = (<View style={styles.container}>
  
        <View style={styles.content}>
          <Image style={styles.logo} source={require('../../resources2/images/icon_inform_noface.png')} />
          <Text style={styles.title}>
            {LocalizedStrings["no_face"]}
          </Text>
          <Text style={styles.paragraph}>
            {LocalizedStrings["no_face_tips"]}
          </Text>
        </View>

        <View style={{ alignItems: "center" }}>
          <TouchableOpacity style={styles.btn} onPress={this.takePhoto}>
            <Text style={styles.tip}>{LocalizedStrings["select_dialog_camera"]}</Text>
          </TouchableOpacity >
          <TouchableOpacity style={styles.btn} onPress={this.choosePhoto}>
            <Text style={styles.tip}>{LocalizedStrings["select_dialog_album"]}</Text>
          </TouchableOpacity >
        </View>
        {this.state.commentDlg ? this._renderCommentDlg() : null}
        {this._renderTipsDialogView()}
      </View>);

      return NoFaceView;
    }
}
  
const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
    padding: 8
  },
  content: {
    marginTop: 115,
    justifyContent: 'center',
    alignItems: 'center'
  },
  logo: {
    width: 138,
    height: 138
  },
  title: {
    fontFamily: 'MI-LANTING_GB-OUTSIDE-YS',
    fontSize: 15.3,
    color: '#999999',
    width: 274,
    marginBottom: 10,
    textAlign: 'center',
    marginTop: 19
  },
  paragraph: {
    fontFamily: 'MI-LANTING_GB-OUTSIDE-YS',
    fontSize: 12.6,
    color: '#B2B2B2',
    textAlign: 'center',
    width: 280
  },
  btn: {
    backgroundColor: '#32BAC0',
    borderRadius: 23,
    width: 312,
    height: 46,
    margin: 24,
    marginTop: 0
  },
  tip: {
    color: 'white',
    fontFamily: 'MI-LANTING--GBK1-Bold',
    fontSize: 15.3,
    lineHeight: 46,
    textAlign: 'center'
  }
});