import { localStrings as LocalizedStrings } from '../../MHLocalizableString';
import { Event } from "../../config/base/CfgConst";
import EventTypeConfig, { EVENT_TYPE, TIMELINE_EVENT_MAP, TIMELINE_EVENT_TYPE } from "./EventTypeConfig";

export default class EventTypeUtil {
  static getEventType(eventTypeFlags, eventType, eventTypeIndex) {
    let lastEventType = 0;
    let lastEventTypeIndex = -1;
    if (EVENT_TYPE[eventType] && (eventTypeFlags & EVENT_TYPE[eventType]) && eventTypeIndex == TIMELINE_EVENT_TYPE[eventType]) {
      lastEventType = eventType;
      lastEventTypeIndex = eventTypeIndex;
      return lastEventTypeIndex;
    }
    return lastEventTypeIndex;
  }

  static _shouldDrawEvent(eventTypeFlags, sdcardEventType) {
    let eventType = TIMELINE_EVENT_MAP[sdcardEventType];
    if (EVENT_TYPE[eventType] && (eventTypeFlags & EVENT_TYPE[eventType])) {
      return true;
    }
    return false;
  }

  static getEventEmptyTip(eventType) {
    let mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.all'];
    switch (eventType) {
      case Event.BabyCry:
        mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.baby.cry'];
        break;
      case Event.PeopleMotion:
        mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.people.motion'];
        break;
      case Event.Face:
        mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.face'];
        break;
      case Event.KnownFace:
        mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.face'];
        break;
      case Event.ObjectMotion:
        mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.object.motion'];
        break;
      case Event.AI:
        mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.ai'];
        break;
      case Event.Pet:
        mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.pet'];
        break;
      case Event.CameraCalling:
        mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.cameracall'];
        break;
      default:
        break;
    }
    return mEmptyDes;
  }
}