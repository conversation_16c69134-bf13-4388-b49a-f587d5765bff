import React from 'react';
import { Text, View, TouchableOpacity, Image, ProgressBarAndroid } from 'react-native';
import PropTypes from 'prop-types';
import { ConfigContext } from "mhui-rn/dist/components/configProvider";
import { styles } from '../../common/styles/Styles';
import PluginStrings from '../../resources/strings';


export const LOAD_STATE = {
  LOADING: 0,
  LOAD_COMPLETED: 1,
  LOAD_FAILED: 2
};

export const LOAD_TIMEOUT = 2000; // 2000ms

const DEFAULT_TEXT_MARGIN_TOP = 40;
const DEFAULT_RETRY_TEXT_WIDTH = 248;
const DEFAULT_RETRY_TEXT_HEIGHT = 96;

/**
 * @export public
 * @module LoadingPage
 * @description LoadingPage for CarIoT
 * @property {number} loadState - 加载状态
 * @property {function} onRetry - 点击重试按钮的回调
 * @property {object} style - style
 */
class LoadingPage extends React.PureComponent {
  static contextType = ConfigContext;
  static propTypes = {
    loadState: PropTypes.number,
    onRetry: PropTypes.func,
    style: PropTypes.object
  };
  constructor(props) {
    super(props);
  }

  componentDidMount() {
  }

  render() {
    const {
      colorScheme
    } = this.context;
    return (
      <View style={[styles.loadingPageContainer, this.props.style]}>
        {this.props.loadState == LOAD_STATE.LOADING ?
          <View style={{ alignItems: 'center', justifyContent: 'center' }}>
            <ProgressBarAndroid style={styles.progressStyle}/>
            <Text style={[styles.loadingFailedTextStyle, { marginTop: 17 }]}>{PluginStrings.loadingTips}</Text>
          </View> : null}
        {this.props.loadState == LOAD_STATE.LOAD_FAILED ?
          <View style={{ alignItems: 'center', justifyContent: 'center' }}>
            <Image source={colorScheme == 'dark' ? require('../../resources/images/empty_icon.png') : require('../../resources/images/empty_icon_light.png')} />
            <Text style={[styles.loadingFailedTextStyle, { marginTop: DEFAULT_TEXT_MARGIN_TOP }]}>{PluginStrings.loadFailedTips}</Text>
            <TouchableOpacity
              style={[
                styles.retryBtnStyle, 
                styles.buttonBaseStyle, 
                { 
                  marginTop: DEFAULT_TEXT_MARGIN_TOP, 
                  width: DEFAULT_RETRY_TEXT_WIDTH, 
                  height: DEFAULT_RETRY_TEXT_HEIGHT, 
                  justifyContent: 'center' 
                }]}
              onPress={() => {
                this.props.onRetry();
              }}>
              <Text style={styles.retryTextStyle}>{PluginStrings.retry}</Text>
            </TouchableOpacity>
          </View> : null}
      </View>
    );
  }

}

export default LoadingPage;
export { LoadingPage };