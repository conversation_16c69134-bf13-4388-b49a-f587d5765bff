import React from "react";
import Host from 'miot/Host';
import { ScrollView } from "react-native";
const LayoutContainer = ({ children, isFullScreen, onScroll }) => {
  if (Host.isPad && Host.isAndroid) {
    return (
      <ScrollView
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        scrollEnabled={true}
        scrollEventThrottle={10}
        style={{ width: '100%', height: '100%', flex: 1 }}
        contentContainerStyle={isFullScreen ? { height: '100%', width: '100%' } : {flexGrow: 1}}
        onScroll={onScroll}
      >
        {children}
      </ScrollView>
    );
  } else {
    return <>{children}</>;
  }
};
export default LayoutContainer;