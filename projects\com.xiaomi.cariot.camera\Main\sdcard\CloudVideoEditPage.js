'use strict';

import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import Separator from 'miot/ui/Separator';
import React from 'react';
import {
  ScrollView,
  View,
  Text,
  Image,
  StyleSheet,
  Dimensions,
  TouchableHighlight,
  Platform,
  TouchableOpacity, FlatList, PermissionsAndroid, ActivityIndicator
} from 'react-native';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';

import { AbstractDialog, Card, NavigationBar, Radio, StringSpinner, Switch } from 'mhui-rn';

import { Locale } from "mhui-rn/dist/locale";
import EventList from "../widget/EventList";
import EventCardV3, { CardHeight, CardMB } from "../widget/EventCardV3";
import { DefFilter, LoadStatus } from "../widget/EventListV3";
import { CldDldTypes } from "../framework/CloudEventLoader";
import { DldStatus, Order } from "../framework/EventLoaderInf";
import { Device, Host, Service, System } from "miot";
import Singletons from "../framework/Singletons";
import Util from "../util2/Util";
import { BaseStyles } from "../BasePage";
import EventEditSingleCard from "../widget/EventEditSingleCard";
import EventEditMultipleCard from "../widget/EventEditMultipleCard";
import LinearGradient from "react-native-linear-gradient";
import { DescriptionConstants } from "../Constants";
import Toast from "../components/Toast";
import LogUtil from "../util/LogUtil";
import { MessageDialog } from "miot/ui/Dialog";
import DldMgr from "../framework/DldMgr";
import { DarkMode } from "miot";
import CameraConfig from "../util/CameraConfig";
const { width: screenWidth, height: screenHeight } = Dimensions.get("screen");
const hours24 = constructArray(24, true, true);
const minutes = constructArray(60, true, true);
const ListFooterH = Platform.OS == "ios" ? 20 : 10;
const TAG = "CloudVideoEditPage";
function constructArray(length, zeroPrefix = true, fromZero = false) {
  const maxLength = (length - (fromZero ? 1 : 0)).toString().length;
  return Array.from({
    length
  }, (v, i) => ((zeroPrefix ? '0000000000000' : '') + (i + (fromZero ? 0 : 1))).slice(-maxLength));
}
export default class CloudVideoEditPage extends React.Component {

  constructor(props, context) {
    super(props, context);

    this.state = {
      selectCount: 0,
      isSelectAll: false,
      startDate: null,
      nextDate: null,
      loadingStatus: LoadStatus.Idle,
      events: [],
      eventGroupList: [],
      deleteDialogVisible: false,
      showLoading: false
    };
    this.mLoader = Singletons.CloudEventLoader;
    this.filter = null;
    this.selectEvents = []
    this.tempHour = this.state.hour;
    this.tempMinute = this.state.minute;
  }
  componentDidMount() {
    if (this.props.navigation != null && this.props.navigation.state != null && this.props.navigation.state.params != null) {
      console.log("==========", this.props.navigation.state.params);
      let params = this.props.navigation.state.params;
      let events = params.aExtra?.events;
      //下次请求数据时间点
      let nextDate = params.aExtra?.nextDate;
      let curItem = params.curItem;
      let startDate = params.startDate;
      this.filter = params.filter;
      let loadingStatus = params.loadingStatus;
      this.selectEvents.push(curItem);
      let groupList = this.doFirstInGroupData(events, this.selectEvents);
      this.getSelectCount(groupList);
      this.setState({ nextDate: nextDate, startDate: startDate, loadingStatus: loadingStatus, events: events, eventGroupList: groupList });
    }
    this.setNavigationBar()
  }

  setNavigationBar() {
    this.props.navigation.setParams({
      // show:true
      title: LocalizedStrings["selected_cloud_count"].replace("%1$d", this.state.selectCount),
      titleNumberOfLines: 2,
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => {
            this.props.navigation.goBack();
          }
        }
      ],
      right: [
        {
          key: this.state.isSelectAll ? NavigationBar.ICON.SELECTED_ALL : NavigationBar.ICON.SELECT_ALL,
          onPress: () => {
            this.onRightBtnPress();
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
  }

  doFirstInGroupData(events, selectEvents = []) {
    let newArr = []
    events.forEach((item, i) => {
      let index = -1;
      //选中某个事件，表明选中这一组

      let alreadyExists = newArr.some((newItem, j) => {
        if (item.fileId === newItem.fileId) {
          index = j;
          return true;
        }
        return false
      });
      let isSelected = selectEvents.some((newItem, j) => {
        if (item.fileId === newItem.fileId) {
          return true;
        }
        return false;
      })
      //数据处理成这样是为了后面进行展示
      if (!alreadyExists) {
        newArr.push({
          selected: isSelected,
          fileId: item.fileId,
          data: [item]
        });
      } else {
        if (newArr[index] && newArr[index].data) {
          newArr[index].data.push(item);
        }
      }
    })
    return newArr;
  }

  getSelectCount(list = []) {
    let count = 0;
    for (let i = 0; i < list.length; i++) {
      if (list[i].selected) {
        count++;
      }
    }
    let isSelectAll = false;
    if (count === list.length) {
      isSelectAll = true;
    }
    console.log("select count =======", count);
    this.setState({ selectCount: count, isSelectAll: isSelectAll }, () => {
      this.setNavigationBar()
    });
  }

  onRightBtnPress() {
    //全选  取消全选
    // let groupData = JSON.parse(JSON.stringify(this.state.eventGroupList))
    let groupData = this.state.eventGroupList;
    this.selectEvents = [];
    for (let i = 0; i < groupData.length; i++) {
      groupData[i].selected = !this.state.isSelectAll;
      if (!this.state.isSelectAll) {
        this.selectEvents.push(groupData[i].data[0])
      }
    }
    let count = this.state.isSelectAll ? 0 : this.state.eventGroupList.length;

    this.setState({ selectCount: count, isSelectAll: !this.state.isSelectAll }, () => {
      this.setNavigationBar();
    });
  }

  render() {
    let hvf = this.props.eventHeaderView;
    let hh = hvf != null ? this.props.eventHeaderHeight : 1;
    let cardH = CardHeight + CardMB;
    let mShowLoading = this.state.showLoading;

    return (
      <View style={{ backgroundColor: Util.isDark() ? this.context.theme?.colorWhite : BaseStyles.mainBg.backgroundColor, flex: 1 }}>
        <FlatList
          data={this.state.eventGroupList}
          style={this.props.style}
          ref={(ref) => { this.mLst = ref; }}
          contentContainerStyle={[{ marginTop: 12, paddingBottom: 60, flexGrow: 1, paddingHorizontal: 12 }]}
          ListEmptyComponent={this.mEmptyView()}
          showsVerticalScrollIndicator={false}
          renderItem={
            ({ item, index }) => {
              let mkey = `c_${index}_${item.fileId}`;

              if (item.data.length == 1) {
                return (
                  <EventEditSingleCard
                    style={styles.item}
                    key={mkey}
                    item={item}
                    cardPressed={(aItm) => {
                      //刷新选中数据
                      this.onItemPress(aItm);
                    }} />
                )
              } else {
                return (
                  <EventEditMultipleCard
                    item={item}
                    key={mkey}
                    cardPressed={(aItm) => {
                      //刷新选中数据
                      this.onItemPress(aItm);
                    }} />
                );
              }
            }
          }

          // ListEmptyComponent={this.mEmptyV()}
          // ListHeaderComponent={hvf ? () => { return hvf(); } : null}
          ListFooterComponent={this.mFooter}
          keyExtractor={(item, index) => `${item.fileId}_${index}`}
          refreshing={LoadStatus.Loading === this.state.loadingStatus}
          onRefresh={this.mRefresh}
          onEndReached={this.mOnEnd}
          onEndReachedThreshold={0.1}
          // getItemLayout={this._onItemLayout}
          // getItemLayout={(data, index) => {
          //
          //   let ret = { length: cardH, offset: cardH * index + hh, index };
          //   // console.log(TAG, "getItemLayout", data.length, index, ret);
          //   return ret;
          // }}
        />
        {this.bottomOperationView()}
        {this.renderDialog()}
        {this._renderDownloadHint()}
        {mShowLoading ? this.renderLoadingView() : null}
      </View>
    );
  }

  mEmptyView = () => {
    return (
      <View
        accessibilityLabel={DescriptionConstants.kj_1_17}
        style={{ height: "100%",  alignItems:"center" }}>
        <View style={{flex: 0.38}}></View>
        <Image
          style={{ alignSelf: "center", width: 92, height: 60 }} source={Util.isDark() ? require("../../Resources/Images/icon_home_empty_d.webp") : require("../../Resources/Images/icon_home_empty.webp")} />
        <Text
          style={{ color: 'gray', textAlign: "center", paddingHorizontal: 40, marginTop: 12 }}
          numberOfLines={2}> { LocalizedStrings['sdcard_page_desc_empty'] }
        </Text>
        <View style={{flex: 0.5}}></View>
      </View>
    );
  }

  _onItemLayout = (data, index) => {
    // 计算高度 列表展示会有问题
    console.log("===============",data,index);
    let length = CardHeight * data[index].data.length + CardMB;
    console.log("===============",length,data[index].data.length);
    let offset = 0;
    for (let i = 0; i < index; i++) {
      offset += CardHeight * data[i].data.length + CardMB
    }
    let ret = { length, offset, index };
    return ret;
  }
  bottomOperationView() {
    let imgStyle = {
      width: 25,
      height: 25
    };
    let opacity = this.state.selectCount === 0 ? { opacity: 0.3 } : null
    return (
      <View style={{ width: "100%", height: 69, display: "flex", flexDirection: "row", justifyContent: "center", alignItems: "center", backgroundColor: "#ffffff", borderTopWidth: 1, borderTopColor: "rgba(0,0,0,0.15)" }}>
        <LinearGradient
          colors={["#00000000", "#00000000"]}
          style={{ width: "100%", height: "100%", position: "absolute" }} />
        <TouchableOpacity
          style={[{ width: 50, display: "flex", alignItems: "center", marginTop: 0 }, opacity]}
          disabled={this.state.selectCount === 0}
          onPress={() => this.deleteImgClick()} >
          <Image
            style={imgStyle}
            disabled={true}
            source={!Util.isDark() ? require("../../Resources/Images/camera_icon_loc_pic_delete.png") : require("../../Resources/Images/camera_icon_loc_pic_delete_white.png")}
            accessibilityLabel={DescriptionConstants.lc_14}
          />
          {
            this.state.isFullscreen ? null :
              <Text style={{ fontSize: 11, color: 'black', marginTop: 3 }}>{LocalizedStrings['delete_files']}</Text>
          }
        </TouchableOpacity>

        <TouchableOpacity
          style={[{ marginLeft: 30, width: 60, display: "flex", alignItems: "center", marginTop: 0 }, opacity]}
          disabled={this.state.selectCount === 0}
          onPress={() => this.downLoadClick(false)} >
          <Image
            style={imgStyle}
            source={Util.isDark() ? require("../../resources2/images/icon_videorecord_download_w.png") : require("../../resources2/images/icon_videorecord_download_b.png")}
            accessibilityLabel={DescriptionConstants.lc_14}
          />
          {
            this.state.isFullscreen ? null :
              <Text style={{ fontSize: 11, color: 'black', marginTop: 3 }}>{LocalizedStrings['f_download']}</Text>

          }
        </TouchableOpacity>

        {/*<TouchableOpacity*/}
        {/*  style={[{ marginLeft: 30, width: 50, display: "flex", alignItems: "center", marginTop: 0},opacity]}*/}
        {/*  disabled={this.state.selectCount === 0}*/}
        {/*  onPress={() => { this.downLoadClick(true) }}>*/}
        {/*  <Image*/}
        {/*    style={imgStyle}*/}
        {/*    source={this.state.isFullscreen ?*/}
        {/*      require("../../Resources/Images/camera_icon_loc_pic_share.png") :*/}
        {/*      require("../../Resources/Images/camera_icon_loc_pic_share.png")}*/}
        {/*    accessibilityLabel={DescriptionConstants.lc_13}*/}
        {/*  />*/}
        {/*  {*/}
        {/*    this.state.isFullscreen ? null :*/}
        {/*      <Text style={{ fontSize: 11, color: 'black', marginTop: 3 }}>{LocalizedStrings['share_files']}</Text>*/}
        {/*  }*/}
        {/*</TouchableOpacity>*/}

      </View>
    )
  }
  // 点击删除时弹出对话框
  renderDialog() {
    return (
      <MessageDialog
        visible={this.state.deleteDialogVisible}
        message={LocalizedStrings['delete_cloud_warning'].replace("%1$d", this.state.selectCount)}
        messageStyle={[BaseStyles.text17, { alignSelf: 'center', color: '#333333', fontWeight: '400' }]}
        canDismiss={false}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: (_) => this.setState({ deleteDialogVisible: false })
            // ignore
          },
          {
            text: LocalizedStrings["delete_confirm"],
            callback: (_) => {
              this.setState({ deleteDialogVisible: false });
              this.confirmDelete();
            }
            // ignore
          }
        ]}
        onDismiss={() => {
          console.log('onDismiss');
          this.setState({ deleteDialogVisible: false });
        }}
      />
    );
  }

  // 查看下载进度弹框
  _renderDownloadHint() {
    if (!this.state.showDownloadHint) {
      return null;
    }
    return (
      <View style={{
        position: "absolute",
        bottom: 0, height: 90, width: "100%", display: "flex", flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        backgroundColor: "#ffffff",
        borderTopWidth: 1,
        borderTopColor: "rgba(0,0,0,0.15)"
      }}
        onPress={() => {

        }}
      >
        <Text
          style={[BaseStyles.text16, { paddingHorizontal: 10, flex: 1, marginLeft: 28, marginRight: 10 }]}
        >
          {LocalizedStrings["video_already_download"]}
        </Text>

        <TouchableOpacity style={{ backgroundColor: 'rgba(50, 186, 192, 0.1)', borderRadius: 17, minHeight: 34, paddingHorizontal: 10, marginRight: 28,  justifyContent: 'center' }}
          onPress={() => {
            this.props.navigation.navigate("DldPage", { preOri: "portrait" });
          }}
        >
          <Text style={[BaseStyles.text13, { color: "#32BAC0", fontWeight: '400', minWidth: 60, textAlign: 'center' }]}>{LocalizedStrings["check_view"]}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  renderLoadingView() {
    let isDark = DarkMode.getColorScheme() == "dark";
    return (
      <View style={{ position: "absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center", zIndex: 9 }}>
        <ActivityIndicator
          style={{ width: 54, height: 54 }}
          color={isDark ? "xm#ffffff" : "xm#000000"}
          size={"large"}
        />
        <Text style={[BaseStyles.text12, { marginTop: 10, color: "#000000" }]}>
          {LocalizedStrings["camera_loading"]}
        </Text>
      </View>
    );
  }
  //读取权限弹框
  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 存储卡/相册
    // status == 1 麦克风
    //
    let message = "";
    if (this.state.permissionRequestState == 0) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);
    } else {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_microphone"]);
    }
    return (
      // <AbstractDialog
      <MessageDialog
        title={LocalizedStrings["tips"]}
        message={message}
        messageStyle={{
          fontSize: 14,
        }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ showPermissionDialog: false });
            }
          },
          {
            text: LocalizedStrings["setting"],
            callback: () => {
              Host.ui.openTerminalDeviceSettingPage(1);
              this.setState({ showPermissionDialog: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog} />
    );
  }

  // 删除
  deleteImgClick() {
    this.setState({
      deleteDialogVisible: true
    });
  }

  //执行删除
  confirmDelete() {

    let selectedItems = [];
    for (let i = 0; i < this.state.eventGroupList.length; i++) {
      let selEv = this.state.eventGroupList[i];
      if (selEv.selected) {
        let item = selEv.data[0]
        selectedItems.push(item);
      }
    }
    this.setState({ showLoading: true });

    this.mLoader.delete(selectedItems)
      .then((res) => {
        Toast.show("delete_success");
        // this.state.events.filter(aEv=>{return })
        //删除了视频数据，需要重新拉取下数据列表
        CameraConfig.isToUpdateEventList = true;
        let newArr = this.state.events.filter((v) =>
          selectedItems.every((val) => val.fileId != v.fileId)
        )
        let resList = this.state.eventGroupList.filter((aEv) => { return !aEv.selected });
        this.setState({ eventGroupList: resList, isSelectAll: false, events: newArr, selectCount: 0, showLoading: false }, () => {
          this.setNavigationBar();
          this.selectEvents = [];
        });
        this.mOnEnd();
      }).catch((aErr) => {
        console.log(this.tag, "del error", aErr);
        Toast.fail('delete_failed');
        this.setState({ showLoading: false });
      });
  }
  // 下载
  downLoadClick(aForShare = false) {

    this.checkStoragePerm()
      .then(() => {
        let fileIds = [];
        for (let i = 0; i < this.state.eventGroupList.length; i++) {
          let selEv = this.state.eventGroupList[i];
          if (selEv.selected) {
            let item = selEv.data[0]
            fileIds.push(item);
          }
        }
        this.setState({ showDownloadHint: true });
        setTimeout(() => {
          this.setState({ showDownloadHint: false });
        }, 5000);

        DldMgr.addDld(fileIds, this.mLoader);
        const resList = this.state.eventGroupList.map((item) => ({
          ...item,
          selected: false
        }));
        this.setState({ eventGroupList: resList, isSelectAll: false, selectCount: 0, showLoading: false }, () => {
          this.setNavigationBar();
          this.selectEvents = [];
        })
      })
      .catch((err) => {
        Toast.success(err);
      });
  }

  checkStoragePerm() {
    this.mPermReq = true;
    return new Promise((resolve, reject) => {
      if (Platform.OS === "android") {
        PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
          .then((granted) => {
            if (granted === PermissionsAndroid.RESULTS.GRANTED) {
              this.mPermReq = false;
              resolve();
            } else {
              this.mPermReq = false;
              reject("camera_no_write_permission");
            }
          }).catch((error) => {
            this.mPermReq = false;
            reject("action_failed");
          });
      } else {
        System.permission.request("photos").then((res) => {
          this.mPermReq = false;
          resolve();
        }).catch((error) => {
          this.mPermReq = false;
          reject("unauthorized_album_permission");
        });
      }
    });
  }

  onItemPress(aItm) {
    let index = this.state.eventGroupList.indexOf(aItm);
    console.log("=============index===========", index)


    let groupData = this.state.eventGroupList;
    let isSelect = aItm.selected;
    groupData[index].selected = !isSelect;
    this.getSelectCount(groupData);
    let hasEvents = false;
    for (let i = 0; i < this.selectEvents.length; i++) {
      if (this.selectEvents[i].fileId === aItm.fileId) {
        this.selectEvents.splice(i, 1);
        hasEvents = true;
      }
    }
    if (!isSelect) {
      this.selectEvents.push(aItm.data[0]);
    }
  }

  mRefresh = () => {
    console.log("landing7");
    this.getEventList(this.state.startDate, this.filter, false);
  }

  mFooter = () => {
    let text = null;

    switch (this.state.loadingStatus) {
      case LoadStatus.Finish:
        if (!(this.state.events == null || 0 == this.state.events.length)) {
          text = LocalizedStrings.alarm_none_data;
        }
        break;
      case LoadStatus.Loading:
        // text = LocalizedStrings.alarm_loading_data;
        break;
      case LoadStatus.Failed:
        // text = LocalizedStrings.alarm_loading_failed;
        break;
      default:
        break;
    }
    return text ?
      <View style={{ height: ListFooterH, alignItems: 'center', marginTop: 10 }}>
        <Text style={{ color: 'gray' }}>{text}</Text>
      </View>
      : null;
  }


  mOnEnd = () => {
    if (this.localSelectDays) { //判断是本地视频，同时是用户点击某一天，不再多次调用接口，否则会出现视频重复（历史逻辑原因，用面向过程思想为最低成本改动）
      return false;
    }

    console.log(TAG, "mOnEnd");
    if (LoadStatus.Finish === this.state.loadingStatus || LoadStatus.Loading === this.state.loadingStatus) {
      console.log(TAG, "onEndReached skip loading for status", this.state.loadingStatus);
      if (this.props.onGetDataDone) {
        this.props.onGetDataDone(this.state.events.length);
      }
      if (this.props.isSltDay && this.props.onSwitchSltDayMore && !this.mSltDayMore) {
        console.log("landing6 2");
        this.props.onSwitchSltDayMore();
        this.mSltDayMore = true;
        console.log("landing6 1");
        this.setState({ loadingStatus: LoadStatus.Loading });
        setTimeout(() => {
          this.localSelectDays = false;
          let date = new Date(this.state.startDate);
          console.log('mNextDate: ', date, 'startDate: ', this.state.startDate);
          this.getEventList(date, this.filter, true);
        }, 100);
      }
    } else {
      console.log("landing6");
      this.getEventList(this.state.nextDate, this.filter, true);
      this.setState({ loadingStatus: LoadStatus.Loading });
    }
  }

  async getEventList(date, event, isMore = false, aOrder = Order.Desc) {
    let now = Date.now();
    let reNonce = `${now}${Math.random(now)}`;
    this.mReNonce = reNonce;
    let events = this.state.events;
    // console.log("events1111111111111111111111111111111111:",this.state.events);
    if (!isMore) {
      events = [];
    }
    let loadingStatus = this.state.loadingStatus;
    let nextDate = this.state.nextTime;// ????? this.state.nextState
    let keepRet = false;
    let status = DldStatus.Complete;
    try {

      console.log("landing2");

      let data = await this.getData(date, event, isMore, aOrder);
      Service.smarthome.reportLog(Device.model, "getEventListData:" + data);
      // console.log("data:", data);
      console.log("landing3");
      console.log(TAG, "getData ", date, "with", event, "datas", data.items ? data.items.length : 0, "nextDate", data.nextTime);
      keepRet = this.mReNonce === reNonce || !isMore;

      if (keepRet) {
        nextDate = data.nextTime;
        loadingStatus = data.hasMore ? LoadStatus.Idel : LoadStatus.Finish;
        if (data.items && data.items.length > 0) {
          console.log("landing301");
          events = this.appendEvents(events, data.items, aOrder);
          console.log("landing302");
          this.downloadFileThump(data.items);
        } else {
          loadingStatus = LoadStatus.Finish;
          status = data.status;
        }
      } else {
        console.log(TAG, "drop ret");
      }
    } catch (err) {
      console.log(TAG, "got error", err);
      keepRet = this.mReNonce === reNonce || !isMore;
      loadingStatus = LoadStatus.Failed;
    } finally {
      if (keepRet) {
        this.setState({
          loadingStatus,
          events,
          nextDate,
          isP2pLostEmpty: status == DldStatus.p2pLost
        }, () => {
          let group = [];
          if (events.length > 0) {

            group = this.doFirstInGroupData(events, this.selectEvents);
          }
          this.setState({ eventGroupList: group })
        });

      }
      console.log(TAG, "getEventList finish with keepRet", keepRet, "nextDate", nextDate);
      // this._getPlayingOffset();
    }
    return keepRet;
  }

  async getData(date, event, isMore = false, aOrder = Order.Desc, type = CldDldTypes.Events) {

    console.log("landing4");
    let data = await this.mLoader.getEventList(date, event, isMore, type);
    return data;
  }

  appendEvents(aOldEv, aNewEv, aOrder = Order.Desc) {
    if (Order.Desc == aOrder) {
      return aOldEv.concat(aNewEv);
    } else {
      return aNewEv.concat(aOldEv);
    }
  }

  async downloadFileThump(items) {
    // load from new to old
    //如果图片返回太快，就慢一些刷新，避免频繁刷新导致的UI卡顿问题，如果
    let lastNotifyTime = Date.now();
    for (let i = 0; i < items.length; ++i) {
      try {
        let item = items[i];
        // console.log(this.tag, "get thumb", item.createTime, i, item);
        item.imgStoreUrl = await this.mLoader.getThumb(item);
        // 3 thumb per refresh
        if (Date.now() - lastNotifyTime < 1000) {//距离上次全局刷新，不超过1s,return
          continue;
        }
        lastNotifyTime = Date.now();
        // if (i % 4 == 3 || i == items.length - 1) {
        this.setState({});
        // }
      } catch (err) {
        console.log(this.tag, "getthumb", err);
      }
    }
    this.setState({});
  }

}
const styles = StyleSheet.create({

});
