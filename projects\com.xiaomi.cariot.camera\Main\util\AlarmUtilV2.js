import { Service, Device } from "miot";
import CameraConfig from "./CameraConfig";
import VersionUtil from '../util/VersionUtil';
import API from '../API';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import Toast from "../components/Toast";
import LogUtil from "./LogUtil";
import StorageKeys from "../StorageKeys";
import VipUtil from "./VipUtil";
import { Event } from "../config/base/CfgConst";
import Util from "../util2/Util";
import AlarmUtil from "./AlarmUtil";

// 摄像机控制
export const SIID_CAMERA_CONTROL = "camera-control";
export const PIID_ON = "on";
export const PIID_IMAGE_ROLLOVER = "image-rollover";

export const PIID_CAMERA_WATERMARK = "time-watermark";
export const PIID_CAMERA_CORRECTION = "image-distortion-correction";
export const CAMERA_CONTROL_RECORD_MODE_PIID = "recording-mode";
export const PIID_NIGHT_VISION = "night-shot"; //夜视功能
export const PIID_GLIMMER_FULL_COLOR = "glimmer-full-color";//微光全彩
// 移动侦测
export const SIID_MOTION_DETECTION = "motion-detection";
export const PIID_MOTION_DETECTION = "motion-detection"; // 移动侦测 bool
export const PIID_ALARM_INTERVAL = "alarm-interval"; // 报警时间间隔 uint8 1-30 step:1
export const PIID_DETECTION_SENSITIVITY = "detection-sensitivity"; // 侦测灵敏度 uint8 0:Low 2:High
export const PIID_MOTION_DETECTION_START_TIME = "motion-detection-start-time"; // 移动侦测开始时间 string
export const PIID_MOTION_DETECTION_END_TIME = "motion-detection-end-time"; // 移动侦测结束时间 string
export const PIID_RECORD_DELAY = "record-delay"; // 延迟录像 uint8 0: None 1: 2 Seconds 2: 4 Seconds
export const PIID_MAX_RECORD_TIME = "max-record-time"; // 最大拍摄时长	uint8 0: 10 Seconds 1: 20 Seconds 2: 30 Seconds
export const PIID_device_warn_time = "device-warn-time"; // 设备语音警告时长	uint8 0: Off 1: 6 Seconds 2: 10 Seconds
export const PIID_DETECTION_REPEAT = "motion-detection-repetition"; // 重复周期

//AI功能
export const SIID_AI_DETECTION = "ai-detection";
export const SIID_AI_SENSITIVITY = "ai-sensitivity";
export const SIID_AI_CUSTOM = "ai-self";
//AI拓展功能管理
export const SIID_AI_EXTENSION = "ai-extension-manager";
export const PIID_AI_EXTENSION_HOUR_SWITCH = "hour-alarm-switch";
// -1表示全天
export const PIID_AI_EXTENSION_HOUR_TIME = "schedule-time";

//暂时不知道spec如何定义
//隐私区域
export const PIID_PRIVATE_AREA_SWITCH = "private-area-switch";//开关
export const PIID_PRIVATE_AREA_POINTS = "private-area-pos";//区域（坐标点）
export const PIID_PRIVATE_AREA_HIDE = "private-area-display";//隐私区域隐藏-开关
export const PIID_PRIVATE_AREA_STYLE = "private-area-style";//隐私区域样式
//虚拟围栏
export const SIID_FENCE_DETECTION = "virtual-fence";
export const PIID_FENCE_SWITCH = "virtual-fence-switch";//虚拟围
export const PIID_FENCE_AREA = "virtual-fence-area";//虚拟围栏区域
export const PIID_FENCE_DIRECTION = "virtual-fence-dir";//虚拟围栏方向
//人形
export const SIID_PEOPLE_DETECTION = "motion-detection";
export const PIID_PEOPLE_SWITCH = "human-detection";//人形
export const PIID_PEOPLE_SENSITIVITY = "detection-sensitivity";//灵敏度
//咳嗽
export const SIID_COUGH_DETECTION = "motion-detection";
export const PIID_COUGH_SWITCH = "cough-detection-switch";//咳嗽
export const PIID_COUGH_SENSITIVITY = "cough-det-sens";//灵敏度
//哭声
export const SIID_CRY_DETECTION = "motion-detection";
export const PIID_CRY_SWITCH = "babycry-detection-switch";//哭声
export const PIID_CRY_SENSITIVITY = "babycry-det-sens";//灵敏度
//人脸
export const PIID_FACE_SWITCH = "face-detection-switch";//人脸
export const PIID_FACE_SENSITIVITY = "sound-sensitivity";//灵敏度
//响声
export const SIID_SOUND_DETECTION = "motion-detection";
export const PIID_SOUND_SWITCH = "sound-detection-switch";//响声
export const PIID_SOUND_SENSITIVITY = "sound-det-sens";//灵敏度
//画面变动
export const PIID_MOVE_SWITCH = "move-detection";
export const PIID_MOVE_SENSITIVITY = "move-det-sens";

//看护屏模块
//连接绑定看护屏
export const SIID_SCREEN_CONTROL = "screen-control";
export const PIID_SCREEN_STATUS = "scrn-status";//看护屏状态
// 0: StartBind
// 1: ResetBind
// 解除绑定/开始连接
export const PIID_SCREEN_BIND_CMD = "scrn-bind-cmd";
export const PIID_SCREEN_CONNECT_STATUS = "scrn-con-status";//状态：搜索中、搜索失败、搜索成功-连接中、连接成功、连接失败
//看护屏相关设置
export const PIID_SCREEN_NOTICE_SWITCH = "scrn-alarm-switch";//允许通知总开关
export const PIID_SCREEN_NOTICE_MOVE = "scrn-alarm-motion";
export const PIID_SCREEN_NOTICE_PEOPLE = "scrn-alarm-people";
export const PIID_SCREEN_NOTICE_COUGH = "scrn-alarm-cough";
export const PIID_SCREEN_NOTICE_SOUND = "scrn-alarm-loud";
export const PIID_SCREEN_NOTICE_FENCE = "scrn-alarm-fence";
export const PIID_SCREEN_NOTICE_BABYCRY = "scrn-alarm-bbcry";
export const PIID_SCREEN_NOTICE_FACE = "scrn-alarm-face";
//息屏设置
export const PIID_SCREEN_REST_TIME = "scrn-sleep-time";
export const PIID_SCREEN_REST_SWITCH = "scrn-sleep-display";
//语言
export const PIID_SCREEN_LANG = "scrn-language";
//device信息
export const PIID_SCREEN_DEVICE_SN = "scrn-sn";
export const PIID_SCREEN_DEVICE_VERSION = "scrn-fw-version";

export const PIID_SCREEN__UNBIND_DEVICE = "motion-detection";

//高动态
export const PIID_CAMERA_WDR = "wdr-mode";
export const PIID_CAMERA_HDR = "hdr-mode";
//对讲音量
export const SIID_CALL_VOLUME = "call-volume";
export const PIID_CALL_VOLUME = "volume";
//扬声器
// 音量调节
export const SPEC_SIID_KEY_SPEAKER = "speaker";
export const SPEC_PIID_KEY_VOLUME = "volume";
// 记录SD卡的状态
// export const SPEC_SIID_KEY_SD_STATUS = "sd-status";
// export const SPEC_PIID_KEY_SD_EJECTED = "ejected";
// export const SPEC_PIID_KEY_SD_DURATION = "record-duration";
export const SPEC_SIID_KEY_SD_STATUS = "memory-card-management";
export const SPEC_PIID_KEY_SD_EJECTED = "dialog-ejected";
export const SPEC_PIID_KEY_SD_DURATION = "remaining-recording-duration";
// SD卡相关
export const CAMERA_SDCARD_SIID = "memory-card-management";
export const CAMERA_SDCARD_STATUS_PIID = "status";
export const CAMERA_SDCARD_TOTAL_SPACE_PIID = "storage-total-space";
export const CAMERA_SDCARD_FREE_SPACE_PIID = "storage-free-space";
export const CAMERA_SDCARD_USED_SPACE_PIID = "storage-used-space";
export const CAMERA_SDCARD_FORMAT_ACTION = "format";
export const CAMERA_SDCARD_EJECT_ACTION = "pop-up";
export const CAMERA_SDCARD_STORAGE_BEGIN_TIME_PIID = "storage-begin-time";

// 指示灯
export const STATUS_LIGHT_SIID = "indicator-light";
export const STATUS_LIGTH_PIID = "on";
// 夜间模式
export const CAMERA_NIGHT_MODE_SIID = "night-mode";
export const CAMERA_NIGHT_MODE_SWITCH_PIID = "night-mode-switch";
export const CAMERA_NIGHT_MODE_PERIOD_PIID = "night-mode-period";
export const CAMERA_NIGHT_MODE_CLOSE_SCREEN_PIID = "night-mode-screen";
// 人脸特写
export const CAMERA_FACE_SHOT_SIID = "face-close-up";
export const CAMERA_FACE_SHOT_SWITCH_PIID = "face-close-up-switch";
// m300 屏幕
export const SCREEN_SETTING_SIID = "screen";
export const SCREEN_SETTING_SWITCH_PIID = "on";
export const SCREEN_SETTING_BRIGHTNESS_PIID = "brightness";
export const SCREEN_SETTING_DISPLAY_STATE_PIID = "display-state"; // 0自动  1自定义
// 按键呼叫
export const KEY_CALL_SIID = "key-call";
// 按键呼叫-051
export const KEY_CALL_051SIID = "one-key-call";
export const KEY_CALL_STATE_PIID = "key-call-status";

// 家人守护
export const KEY_FAMILY_CARE_SIIP = "family-guard";
export const KEY_FAMILY_CARE_TIME_PERIOD_PIIP = "family-guard-config";

// 闹钟提醒
export const KEY_ALARM_REMIND_SIID = 'alarm-reminder';
export const KEY_ALARM_REMIND_LIST_PIID = 'alarm-list';
export const KEY_ALARM_REMIND_DURATION_PIID = 'ring-duration';
export const KEY_ALARM_REMIND_INTERVAL_PIID = 'snap-interval-time';
export const KEY_ALARM_REMIND_REPEAT_PIID = 'number-of-repeats';

// 主题设置
export const SIID_THEME_SETTINGS = "theme-settings";
export const PIID_THEME = "theme";
export const PIID_TIME_SHOW = "time-show";
export const PIID_CALENDER_SETTINGS = "calendar-settings";
export const PIID_DEVICE_LOCATION = "device-locaton"

// voip voip-call
export const SIID_VOIP = "voip-call";
export const AIID_SCENE_CALL_USER = "call-user";


// 隐私下发，用于相机协同
export const SIID_OTHER_FUNCTIONS = 'other-functions';
export const PIID_OTHER_FUNCTION_PRIVACY = "privacy-agree";

// 车机服务SIID
export const SIID_COCKPIT_SERVICE = 'cockpit-service';
// 存储开关
export const PIID_COCKPIT_STORAGE_SWITCH = "storage-switch";
// 0: Normal
// 1: Limited
export const PIID_COCKPIT_WORK_MODE = "work-mode";

export const PIID_COCKPIT_CROSS_SWITCH = "playback-switch";
export const PIID_COCKPIT_PRIVACY = "privacy-mode";
// 是否允许直播查看开关
export const PIID_COCKPIT_LIVE_SWITCH = "live-switch";
export const PIID_COCKPIT_TEMPERATURE_STATUS = "thermal-status";
export const PIID_COCKPIT_LIVE_INFO = "live-info";
export const PIID_COCKPIT_IP_INFO = "ip-info";
export const PIID_COCKPIT_BIND_INFO = "bind-info";



const TAG = "AlarmUtilV2";
export default class AlarmUtilV2 {

  static async getSpecPiidParams(skey, pkey) {
    let res = await Service.spec.getSpecByKey(Device.deviceID, { mkey: null, skey: skey, pkey: pkey });
    console.log("getSpecPiidParams====", JSON.stringify(res));
    return res;
  }

  static async getSpecAiidParams(skey, akey) {
    let res = await Service.spec.getSpecByKey(Device.deviceID, { mkey: null, skey: skey, akey: akey });
    // console.log("getSpecAiidParams====", JSON.stringify(res));
    return res;
  }

  /**
   * params : [{"sname": "device-information", "pname": "manufacturer"},
   *          {"sname": "device-information", "pname": "model"},
   *          {"sname": "camera-control", "pname": "on"},
   *          ...]
   * 返回相应的siid 和piid
   */
  static async buildGetSpecPiidParams(params) {
    let getParams = [];
    for (let element of params) {
      let res = await this.getSpecPiidParams(element.sname, element.pname);
      getParams.push({ did: Device.deviceID, siid: res[0].siid, piid: res[0].piid });
    }
    return getParams;
  }

  /**
   * params : {"sname": "camera-control", "aname": "on"}
   * 返回相应的siid 和aiid
   */
  static async buildActionSpecAiidParams(params) {
    let res = await this.getSpecAiidParams(params.sname, params.aname);
    return { did: Device.deviceID, siid: res[0].siid, aiid: res[0].aiid, in: [] };
  }

  /**
   * params : [{"sname": "motion-detection", "pname": "alarm-interval", value: 30},
   *          {"sname": "motion-detection", "pname": "motion-detection", value: false},
   *          {"sname": "indicator-light", "pname": "on", value: false},
   *          ...]
   * 返回相应的siid 和piid 以及设置的value
   */
  static async buildSetSpecPiidParams(params) {
    let setParams = [];
    for (let element of params) {
      let res = await this.getSpecPiidParams(element.sname, element.pname);
      setParams.push({ did: Device.deviceID, siid: res[0].siid, piid: res[0].piid, value: element.value });
    }
    return setParams;
  }

  /**
    使用示例：
    根据spec 属性name获取一些spec属性值：（可以随意组合）
     AlarmUtilV2.getSpecPValue([{ "sname": "device-information", "pname": "manufacturer" }, 
      { "sname": "device-information", "pname": "model" }, 
      { "sname": "camera-control", "pname": "on" }]).then((res) => {
      console.log("getSpecPValue res=", JSON.stringify(res));
    }).catch((error)=> {
      console.log("getSpecPValue err=", JSON.stringify(error));
    });

    tag: 可以自定义，方便日志查询哪个请求
  */
  static async getSpecPValue(params, datasource = 2, tag = "tag") {
    let requestTime = Date.now();
    LogUtil.logOnAll(tag, "getSpecPValue params=", params, " taskID=", requestTime);
    let requestParams = await this.buildGetSpecPiidParams(params);
    LogUtil.logOnAll(tag, "getSpecPValue buildGetSpecPiidParams=", requestParams, " taskID=", requestTime);
    return new Promise((resolve, reject) => {
      Service.spec.getPropertiesValue(requestParams, datasource).then((res) => {
        LogUtil.logOnAll(tag, "getSpecPValue res==", JSON.stringify(res), " taskID=", requestTime);
        resolve(res);
      }).catch((err) => {
        LogUtil.logOnAll(tag, "getSpecPValue err==", JSON.stringify(err), " taskID=", requestTime);
        reject(err);
      });
    });
  }

  /**
    使用示例：
    根据spec 属性name设置一些spec属性值：（可以随意组合）
    AlarmUtilV2.setSpecPValue([{ "sname": "motion-detection", "pname": "alarm-interval", value: 30 },
      { "sname": "motion-detection", "pname": "motion-detection", value: false },
      { "sname": "indicator-light", "pname": "on", value: false }]).then((res) => {
      console.log("setSpecPValue res=", JSON.stringify(res));
    }).catch((error)=> {
      console.log("setSpecPValue err=", JSON.stringify(error));
    });

    tag: 可以自定义，方便日志查询哪个请求
  */
  static async setSpecPValue(params, tag = "tag") {
    let requestTime = Date.now();
    LogUtil.logOnAll(tag, "setSpecPValue params=", params, " taskID=", requestTime);
    let requestParams = await this.buildSetSpecPiidParams(params);
    LogUtil.logOnAll(tag, "setSpecPValue buildSetSpecPiidParams=", requestParams, " taskID=", requestTime);
    return new Promise((resolve, reject) => {
      Service.spec.setPropertiesValue(requestParams).then((res) => {
        LogUtil.logOnAll(tag, "setSpecPValue res==", JSON.stringify(res), " taskID=", requestTime);
        resolve(res);
      }).catch((err) => {
        LogUtil.logOnAll(tag, "setSpecPValue err==", JSON.stringify(err), " taskID=", requestTime);
        reject(err);
      });
    });
  }

  static async doSpecAction(params,tag = "SpecAction") {
    let requestTime = Date.now();
    let requestParams = await this.buildActionSpecAiidParams(params);
    console.log("doSpecAction",requestParams);
    return new Promise((resolve, reject) => {
      Service.spec.doAction(requestParams).then((res) => {
        LogUtil.logOnAll(tag, "doAction res==", JSON.stringify(res), " taskID=", requestTime);
        resolve(res);
      }).catch((err) => {
        LogUtil.logOnAll(tag, "doAction err==", JSON.stringify(err), " taskID=", requestTime);
        reject(err);
      });
    });

  }

  static AI_EVENT_SETTING_MAP = {};
  static getALLAISettings(tag = "getALLAISettings") {
    let params = [{ "sname": SIID_AI_DETECTION, "pname": PIID_PEOPLE_SWITCH },
      { "sname": SIID_AI_DETECTION, "pname": PIID_MOVE_SWITCH },
      { "sname": SIID_AI_DETECTION, "pname": PIID_CRY_SWITCH },
      { "sname": SIID_AI_DETECTION, "pname": PIID_FACE_SWITCH },
      { "sname": SIID_AI_DETECTION, "pname": PIID_COUGH_SWITCH },
      { "sname": SIID_AI_DETECTION, "pname": PIID_SOUND_SWITCH },
      { "sname": SIID_FENCE_DETECTION, "pname": PIID_FENCE_SWITCH },
      { "sname": SIID_MOTION_DETECTION, "pname": PIID_MOTION_DETECTION }];

    return new Promise((resolve, reject) => {
      AlarmUtilV2.getSpecPValue(params, 2, tag).then((res) => {
        if (res[0].code != 0) {
          reject(res);
          return;
        }
        AlarmUtilV2.AI_EVENT_SETTING_MAP[Event.PeopleMotion] = res[0].value;
        AlarmUtilV2.AI_EVENT_SETTING_MAP[Event.ObjectMotion] = res[1].value;
        AlarmUtilV2.AI_EVENT_SETTING_MAP[Event.BabyCry] = res[2].value;
        AlarmUtilV2.AI_EVENT_SETTING_MAP[Event.Face] = res[3].value;
        AlarmUtilV2.AI_EVENT_SETTING_MAP[Event.PeopleCough] = res[4].value;
        AlarmUtilV2.AI_EVENT_SETTING_MAP[Event.LouderSound] = res[5].value;
        AlarmUtilV2.AI_EVENT_SETTING_MAP[Event.FencePass] = res[6].value;
        if (res[7].value) {
          // FIX CHUANGMI-17542 尽量规避
          // 1、一个手机领取过云存，使用另一部手机，此时设备离线
          // 2、领取免费看家 清除手机缓存
          StorageKeys.FREE_CLOUD = true;
        }
        resolve(AlarmUtilV2.AI_EVENT_SETTING_MAP);
      }).catch((err) => {
        reject(err);
      });
    });
  }

  // ===== 下面是老办法，先保持原样使用，忽略就好 =====
  static getSpecByKey(skey, pkey) {
    return Service.spec.getSpecByKey(Device.deviceID, { mkey: null, skey: skey, pkey: pkey });
  }

  static getSpeakerVolume(datasource = 2) {
    return this.getSpecByKey(SPEC_SIID_KEY_SPEAKER, SPEC_PIID_KEY_VOLUME).then((res) => {
      console.log("getSpeakerVolume getSpecByKey ", res[0].siid, " piid=", res[0].piid);
      return Service.spec.getPropertiesValue([{ did: Device.deviceID, siid: res[0].siid, piid: res[0].piid }], datasource);
    }).catch((err) => {
      console.log("getSpeakerVolume getSpecByKey err=", JSON.stringify(err));
      return new Promise((resolve, reject) => {
        reject(err);
      });
    });
  }
  static setSpeakerVolume(value) {
    return this.getSpecByKey(SPEC_SIID_KEY_SPEAKER, SPEC_PIID_KEY_VOLUME).then((res) => {
      console.log("setSpeakerVolume getSpecByKey ", res[0].siid, " piid=", res[0].piid);
      let params = [
        Object.assign({ did: Device.deviceID, siid: res[0].siid, piid: res[0].piid }, { value: value })
      ];
      return Service.spec.setPropertiesValue(params);
    }).catch((err) => {
      console.log("setSpeakerVolume getSpecByKey err=", JSON.stringify(err));
      return new Promise((resolve, reject) => {
        reject(err);
      });
    });
  }

  // ---------------------------- http ----------------------------
  static buildEventSortValue(attentions, normals, closeds) {
    let value = [];
    for (let item of attentions) {
      item.attention = 1;
      value.push(item);
    }
    for (let item of normals) {
      item.attention = 0;
      value.push(item);
    }
    for (let item of closeds) {
      item.attention = 0;
      value.push(item);
    }
    return value;
  }

  static setEventSortO(attentions, normals, closeds) {
    let value = this.buildEventSortValue(attentions, normals, closeds);
    let params = { "did": Device.deviceID, "eventTypeList": JSON.stringify(value) };
    LogUtil.logOnAll("setEventSortO", "params = ", JSON.stringify(params));
    let url = '/common/app/set/event/sort';
    return new Promise((resolve, reject) => {
      Service.callSmartHomeCameraAPI(url, "business.smartcamera", true, params).then((res) => {
        LogUtil.logOnAll(url, "Ores = ", JSON.stringify(res));
        resolve(res);
      }).catch((err) => {
        LogUtil.logOnAll(url, "Oerr = ", JSON.stringify(err));
        reject(err);
      });
    });
  }
  static setEventSort(value, tag = "default") {
    let params = { "did": Device.deviceID, "eventTypeList": JSON.stringify(value) };
    LogUtil.logOnAll(tag, "setEventSort", "params = ", JSON.stringify(params));
    let url = '/common/app/set/event/sort';
    return new Promise((resolve, reject) => {
      Service.callSmartHomeCameraAPI(url, "business.smartcamera", true, params).then((res) => {
        LogUtil.logOnAll(tag, url, "res = ", JSON.stringify(res));
        resolve(res);
      }).catch((err) => {
        LogUtil.logOnAll(tag, url, "err = ", JSON.stringify(err));
        reject(err);
      });
    });
  }
  static getEventSort() {
    let url = '/common/app/get/event/sort';
    let params = { "did": Device.deviceID, "model": Device.model };
    LogUtil.logOnAll(url, "params = ", JSON.stringify(params));
    return new Promise((resolve, reject) => {
      Service.callSmartHomeCameraAPI(url, "business.smartcamera", false, params).then((res) => {
        LogUtil.logOnAll(url, "res = ", JSON.stringify(res));
        resolve(res);
      }).catch((err) => {
        LogUtil.logOnAll(url, "err = ", JSON.stringify(err));
        reject(err);
      });
    });
  }

  static setAISettingEventClose(eventType) { // 取消关注
    if (!eventType) {
      LogUtil.logOnAll("setAISettingEventClose err eventType is null");
      return;
    }
    return new Promise((resolve, reject) => {
      AlarmUtilV2.getEventSort().then((res) => {
        let data = res.data;
        let eventTypeAttentionList = data.eventTypeAttentionList;
        let attentions = [];
        let normals = [];
        let insertItem = null;
        eventTypeAttentionList.forEach((item) => {
          if (eventType != item.eventType) {
            if (item.attention > 0) {
              attentions.push(item);
            } else {
              normals.push(item);
            }
          } else {
            insertItem = item;
          }
        });
        Util.insertEventItem(insertItem, normals);
        AlarmUtilV2.setEventSortO(attentions, normals, []).then((res) => {
          resolve(res);
        }).catch((err) => {
          LogUtil.logOnAll(TAG, "setAISettingEventClose setEventSortO err=", JSON.stringify(err));
          reject(err);
        });
      }).catch((err) => {
        LogUtil.logOnAll(TAG, "setAISettingEventClose getEventSort err=", JSON.stringify(err));
        reject(err);
      });
    });
  }

  static getEventStatistics() {
    let url = '/common/app/get/event/statistics';
    let params = { "did": Device.deviceID, "model": Device.model, "timestamp": Date.now() };
    LogUtil.logOnAll(url, "params = ", JSON.stringify(params));
    return new Promise((resolve, reject) => {
      Service.callSmartHomeCameraAPI(url, "business.smartcamera", false, params).then((res) => {
        LogUtil.logOnAll(url, "res = ", JSON.stringify(res));
        resolve(res);
      }).catch((err) => {
        LogUtil.logOnAll(url, "err = ", JSON.stringify(err));
        reject(err);
      });
    });
  }

  static getSpecMotionDetectionSwitch() {
    let params  = [{ "sname": SIID_MOTION_DETECTION, "pname": PIID_MOTION_DETECTION }]
    return new Promise((resolve, reject)=>{
        this.getSpecPValue(params,2).then(res=>{
         if (res[0].code !== 0){
           resolve(false);
         }else {
           resolve(res[0].value);
         }
        }).catch(error => {
          resolve(false);
        });
    });
  }

  static openMotionDetectionSwitch(value = true) {
    let params  = [{ "sname": SIID_MOTION_DETECTION, "pname": PIID_MOTION_DETECTION, value: value}]
    return new Promise((resolve, reject)=>{
      this.setSpecPValue(params).then(res=>{
        if (res[0].code !== 0){
          reject(false);
        }else {
          resolve(true);
        }
      }).catch(error => {
        reject(false);
      });
    });
  }

  static showAlarmCloudBuy = false;
  static lastRequestTime = 0;
  static async getHideAlarmSetting() {
    let timestamp = new Date().getTime();
    if (AlarmUtilV2.lastRequestTime != 0 && timestamp - AlarmUtilV2.lastRequestTime < 2000) {
      return AlarmUtilV2.showAlarmCloudBuy;
    }
    AlarmUtilV2.lastRequestTime = timestamp;
    // 获取VIP状态
    // 本地是否领取过免费云存
    const res = await StorageKeys.FREE_CLOUD;
    let isReceiveCloud = false;
    typeof (res) === 'boolean' ? isReceiveCloud = res : isReceiveCloud = false;
    // VIP状态
    let isVip = await VipUtil.getVipValue();
    // 看家spec
    let detectMotion = await AlarmUtilV2.getSpecMotionDetectionSwitch();
    console.log("====------=++++=", isReceiveCloud, isVip, detectMotion);
    AlarmUtilV2.showAlarmCloudBuy = !isVip && !isReceiveCloud && !detectMotion;
    return !isVip && !isReceiveCloud && !detectMotion;
  }

  static FACE_ACCEPT = "accept";
  static FACE_CANCEL = "cancel";
  static setFacePrivacyConfirmation(type, needSetSwitch = true) {
    let param = {
      privacyVersion: "",
      type: type,
      privacyType: 3,
      pluginPrivacyId: 302
    };
    return new Promise((resolve, reject) => {
      if (type == this.FACE_CANCEL) {
        this.putFaceSwitch(false).then(() => {
          Device.setPrivacyConfirmation(param).then((res) => {
            if (needSetSwitch) {
              Toast.success('c_set_success');
            }
            resolve(res);
          }).catch((err) => {
            if (needSetSwitch) {
              Toast.fail('c_set_fail');
            }
            reject(err);
          });
        }).catch((err) => {
          if (needSetSwitch) {
            Toast.fail('c_set_fail');
          }
          reject(err);
        });
      } else {
        Device.setPrivacyConfirmation(param).then((res) => {
          if (needSetSwitch) {
            this.putFaceSwitch(true).then(() => {
              Toast.success('c_set_success');
              resolve(res);
            }).catch((err) => {
              Toast.fail('c_set_fail');
              reject(err);
            });
          } else {
            resolve(res);
          }
        }).catch((err) => {
          Toast.fail('c_set_fail');
          reject(err);
        });
      }
    });
  }

  static putFaceSwitch(value) {
    let params = [{ sname: SIID_AI_DETECTION, pname: PIID_FACE_SWITCH, value: value }];
    return this.setSpecPValue(params);
  }

  static getSD_STATUS_EJECTED() {
    let params = [{ sname: SPEC_SIID_KEY_SD_STATUS, pname: SPEC_PIID_KEY_SD_EJECTED }];
    return this.getSpecPValue(params);
  }

  static putSD_STATUS_EJECTED(value) {
    let params = [{ sname: SPEC_SIID_KEY_SD_STATUS, pname: SPEC_PIID_KEY_SD_EJECTED, value: value }];
    return this.setSpecPValue(params);
  }

  static getSD_SD_CARD_RECORD_DURATION() {
    let params = [{ sname: SPEC_SIID_KEY_SD_STATUS, pname: SPEC_PIID_KEY_SD_DURATION }];
    return this.getSpecPValue(params);
  }
  static putOneKeyCallStatus(value) {

    let params = [{ sname: KEY_CALL_SIID, pname: KEY_CALL_STATE_PIID, value: value }];
    if (CameraConfig.Model_chuangmi_051a01 == Device.model) {
      params = [{ sname: KEY_CALL_051SIID, pname: KEY_CALL_STATE_PIID, value: value }];
    }
    return this.setSpecPValue(params);
  }
  static getOneKeyCallStatus() {
    let params = [{ sname: KEY_CALL_SIID, pname: KEY_CALL_STATE_PIID }];
    if (CameraConfig.Model_chuangmi_051a01 == Device.model) {
      params = [{ sname: KEY_CALL_051SIID, pname: KEY_CALL_STATE_PIID }];
    }
    return this.getSpecPValue(params);
  }

  static checkPushNotice() {
    // 检测云端默认状态值，咳嗽、口鼻需要默认开启，未定义时，主动设置值为true
    AlarmUtil.getAlarmConfig().then((res) => {
      if (res.code == 0) {
        if (res.data.genericSwitchMap !== undefined && res.data.genericSwitchMap.LouderSound === undefined) {
          // 大声
          this.openGenericPush("LouderSound");
        }
      }
    }).catch((err) => {
    });
  }

  // 只执行，不考虑执行结果
  static openGenericPush(eventType,value = true) {
    API.post("/miot/camera/app/v1/put/genericEventPushSwitch", 'business.smartcamera', { eventType: eventType, open: value })
      .then((res) => {
      })
      .catch((err) => {
      });
  }

  static SD_EARLIEST_TIME = 0;
  static getSDEarliestTime() {
    return new Promise((resolve, reject) => {
      let params = [{ sname: CAMERA_SDCARD_SIID, pname: CAMERA_SDCARD_STORAGE_BEGIN_TIME_PIID }];
      this.getSpecPValue(params).then((res) => {
        if (res[0].code == 0){
          AlarmUtilV2.SD_EARLIEST_TIME = res[0].value;
          resolve(res[0].value);
        } else {
          AlarmUtilV2.SD_EARLIEST_TIME = 0;
          resolve(0);
        }
      }).catch((error) => {
        AlarmUtilV2.SD_EARLIEST_TIME = 0;
        reject(0);
      });
    });
  }
}