// import DynamicColor from './DynamicColor';
// import { dynamicStyleSheet } from './DynamicStyleSheet';

import { DynamicColor, dynamicStyleSheet } from "miot/ui";
import { Radius, PluginWindow, Color, Font, Constants } from 'micariot-ui-sdk';

export const CarConstants = {
  VIDEO_CONTENT_WIDTH: 1152,
  VIDEO_CONTENT_HEIGHT: 648
};
// 插件窗口参数
export const CarPluginWindow = {
  MediumLarge: {
    Width: 1200,
    Height: 904,
    MarginLeft_Right: 72
  }
};

// 抽取字体style
export const CarFont = {
  Size: {
    _22: 22
  },
  Color: {
    Light: {
      TextWhite: '#FFFFFF',
      TextCancel: '#121A2E',
      blue: '#1F8FFF'

    },
    Dark: {
      TextWhite: '#FFFFFFE5',
      TextCancel: '#FFFFFFE5',
      blue: '#1F8FFF'
    }
  }
};

export const CarColor = {
  Border: '#FFFFFFCC',
  ShadeCover: '#00000033',
  Light: {
    innerPrimary: '#EFF3FB',
    innerVideoBg: '#EFF3FB',
    selectBg: '#FFFFFF',
    handColorHint: '#121B2E',
    handTitleColor: '#121B2E99',
    nearHandBg: '#D6DDEB',
    nearHandPress: '#2A3A5C14',
    lineContent: '#FDFDFF',
    progress: '#11E881',
    progressBg: '#121B2E1F',
    black_30: '#60697A4C',
    line: '#313742',
    homeAbnormal: '#B9C0CE',
    loadingBg: '#CED5E5'

  },
  Dark: {
    innerPrimary: '#484C56',
    innerVideoBg: '#383B43',
    selectBg: '#FFFFFF33',
    handColorHint: '#FFFFFF',
    handTitleColor: '#FFFFFF7A',
    nearHandBg: '#25272D',
    nearHandPress: '#FFFFFF14',
    lineContent: '#484C56',
    progressBg: '#C1CCE21F',
    white_30: '#FFFFFF4C',
    homeAbnormal: '#2E3138',
    loadingBg: '#2E3138'
  }
};

export const carStyles = dynamicStyleSheet({
  dialogStyle: {
    width: 640,
    alignItems: 'center',
    backgroundColor: new DynamicColor(Color.Light.SurfaceDialog, Color.Dark.SurfaceDialog),
    paddingLeft: 56,
    paddingRight: 56,
    paddingBottom: 56,
    borderRadius: Radius.PanelLevel,
    marginTop: 84
  },
  modalStyle: {
    backgroundColor: new DynamicColor(Color.Light.Shade, Color.Dark.Shade)
  },

  mediumLargeContainerStyle: {
    width: CarPluginWindow.MediumLarge.Width,
    height: CarPluginWindow.MediumLarge.Height,
    paddingLeft: CarPluginWindow.MediumLarge.MarginLeft_Right,
    paddingRight: CarPluginWindow.MediumLarge.MarginLeft_Right,
    backgroundColor: new DynamicColor(Color.Light.SurfaceDialog, Color.Dark.SurfaceDialog)
  },
  mediumLargeContainerStyle2: {
    width: CarPluginWindow.MediumLarge.Width,
    height: CarPluginWindow.MediumLarge.Height,
    backgroundColor: new DynamicColor(Color.Light.SurfaceDialog, Color.Dark.SurfaceDialog),
    borderRadius: Radius.WindowLevel
  },

  videoContainerStyle: {
    width: 1152,
    height: 648
  },
  buttonUnselected: {
    backgroundColor: new DynamicColor(CarColor.Light.innerPrimary, CarColor.Dark.innerPrimary)
  },

  innerVideoContainerStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.innerPrimary, CarColor.Dark.innerPrimary),
    borderRadius: Radius.WidgetLevel
  },
  daySelectStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.selectBg, CarColor.Dark.selectBg),
    borderRadius: 72,
    width: 72,
    height: 72,
    justifyContent: 'center',
    alignItems: 'center'
  },

  dayUnselectStyle: {
    width: 72,
    height: 72,
    justifyContent: 'center',
    alignItems: 'center'
  },

  nearHandStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.nearHandBg, CarColor.Dark.nearHandBg)
  },
  nearHandItemStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.nearHandPress, CarColor.Dark.nearHandPress)
  },
  lineDayBg: {
    backgroundColor: new DynamicColor(CarColor.Light.lineContent, CarColor.Dark.lineContent)
  },
  progressStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.progress, CarColor.Light.progress)
  },
  progressBgStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.progressBg, CarColor.Dark.progressBg)
  },

  dialogButtonSureStyle: {
    color: new DynamicColor(CarFont.Color.Light.TextWhite, CarFont.Color.Dark.TextWhite),
    fontSize: Font.Size._26
  },

  dialogButtonCancelStyle: {
    color: new DynamicColor(CarFont.Color.Light.TextCancel, CarFont.Color.Dark.TextCancel),
    fontSize: Font.Size._26
  },

  videoStorageNotOpenImage: {
    width: 200,
    height: 128,
    marginTop: 200,
    marginBottom: Constants.DEFAULT_TEXT_MARGIN_BOTTOM
  },

  storageTitle: {
    color: new DynamicColor(Font.Color.Light.TextPrimary, Font.Color.Dark.TextPrimary),
    fontSize: Font.Size._28
  },
  homeAbnormalStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.homeAbnormal, CarColor.Dark.homeAbnormal)
  },
  selectAllStyle: {
    color: new DynamicColor(CarFont.Color.Light.blue, CarFont.Color.Dark.blue),
    fontSize: Font.Size._28,
    paddingHorizontal: 15
  },

  videoErrorImageStyle: {
    width: 200,
    height: 128
  },

  centerLineContentStyle: {
    position: "absolute",
    justifyContent: "center",
    alignItems: 'center',
    backgroundColor: new DynamicColor(CarColor.Light.black_30, CarColor.Dark.white_30),
    width: 24,
    height: 88
  },

  centerLineStyle: {
    position: "absolute",
    backgroundColor: new DynamicColor(CarColor.Light.line, CarColor.Light.selectBg),
    width: 4,
    height: 88
  },

  videoLoadingStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.loadingBg, CarColor.Dark.loadingBg)
  }


});

export const Opacity = {
  // 正常
  Normal: 1,
  // 置灰
  Disabled: 0.35
};
