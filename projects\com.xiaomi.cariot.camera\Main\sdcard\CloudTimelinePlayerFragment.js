'use strict';

import React from 'react';
import { isIphoneX, getStatusBarHeight } from 'react-native-iphone-x-helper';
import { ScrollView, ActivityIndicator, SafeAreaView, StatusBar, BackHandler, View, Text, Image, StyleSheet, TouchableOpacity, TouchableWithoutFeedback, PermissionsAndroid, Platform, DeviceEventEmitter, Dimensions, NativeModules,PanResponder } from 'react-native';

import LinearGradient from 'react-native-linear-gradient';

import { Device, Service, PackageEvent, Host, System, API_LEVEL, DarkMode } from 'miot';
import AlbumHelper from "../util/AlbumHelper";

import ImageButton from "miot/ui/ImageButton";

import Toast from '../components/Toast';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';

import Orientation from 'react-native-orientation';

import StorageKeys from '../StorageKeys';
import CenterTimeView from '../ui/CenterTImeView';
import TimeScaleView2 from '../ui/TimeScaleView2';
import { EVENT_TYPE, EVENT_TYPE_COLOR } from './util/EventTypeConfig';
import Video from 'react-native-video';
import CloudVideoUtil, { CLOUD_VIDEO_STATUS } from './util/CloudVideoUtil';
import StackNavigationInstance, { SD_CLOUD_STACK_NAVIGATION_ONBACK, SD_CLOUD_STACK_NAVIGATION_ONPAUSE, SD_CLOUD_STACK_NAVIGATION_ONRESUME } from '../StackNavigationInstance';
import CameraConfig from '../util/CameraConfig';
import { ChoiceDialog } from 'mhui-rn';
import { MessageDialog } from "mhui-rn";
import { PixelRatio } from 'react-native';
import LoadingView from '../ui/LoadingView';
import CommonMsgDialog from '../ui/CommonMsgDialog';
import CameraPlayer from '../util/CameraPlayer';
import { HostEvent } from 'miot/Host';
import VersionUtil from '../util/VersionUtil';

import MHLottieSnapToolButton, { MHLottieSnapToolBtnDisplayState } from '../ui/animation/lottie-view/MHLottieSnapToolButton';
import MHLottieAudioToolButton, { MHLottieAudioToolBtnDisplayState } from '../ui/animation/lottie-view/MHLottieAudioToolButton';
import MHLottieSpeedToolButton, { MHLottieSpeedToolBtnDisplayState } from '../ui/animation/lottie-view/MHLottieSpeedToolButton';
import MHLottieFullScreenToolButton, { MHLottieFullScreenToolBtnDisplayState } from '../ui/animation/lottie-view/MHLottieFullScreenToolButton';

import MHLottieSnapLandscapeButton, { MHLottieSnapLandscapeBtnDisplayState } from '../ui/animation/lottie-view/MHLottieSnapLandscapeButton';
import StatusBarUtil from '../util/StatusBarUtil';

import { DescriptionConstants } from '../Constants';
import TrackUtil from '../util/TrackUtil';
import API from '../API';
import LogUtil from '../util/LogUtil';
import Util from '../util2/Util'
import VipUtil from '../util/VipUtil';
import NetInfo from "@react-native-community/netinfo";
import loudSoundSelectedDrawable from "../../Resources/Images/time_line_event_icon_loud_sound_sel.png";
import loudSoundUnselectedDrawable from "../../Resources/Images/time_line_event_icon_loud_sound_nor.png";
import { BaseStyles } from "../BasePage";


const kIsCN = Util.isLanguageCN();
const TAG = "CloudTimelinePlayerFragment";
const kWindowHeight = Math.max(Dimensions.get("window").height, Dimensions.get("window").width);
const kWindowWidth = Math.min(Dimensions.get("window").height, Dimensions.get("window").width);
const portraitVideoHeight = Number.parseInt(kWindowWidth / 1.78);

let isDark = DarkMode.getColorScheme() == "dark";
const kPxScale = Math.min(kWindowHeight / 896, 1);

export default class CloudTimelinePlayerFragment extends React.Component {

  static navigationOptions = ({ navigation }) => {
    // if (true) {//不要导航条
    //   return null;
    // }

    let tabBarVisible = true;
    let param = navigation.state.params || {};
    if (param.isFullScreen) {
      tabBarVisible = false;
    }
    return {
      tabBarVisible
    };
  }

  state = {
    isEmpty: true,
    isVip: false,
    touchMovement: false,
    sdcardFiles: [],
    showPlayToolBar: false,

    fullScreen: false,
    isMute: CameraConfig.getUnitMute(),
    isSleep: false,
    resolution: 0,
    speed: 1, // 倍速
    isPlaying: true,

    screenshotVisiblity: false, // 截图是否可见

    showErrorView: false,
    showLoadingView: false,
    showPauseView: false,
    errTextString: "", // 错误提示文案

    screenshotPath: null,
    recordTimeSeconds: 0,

    eventTypeFlags: (EVENT_TYPE.EVENT_TYPE_DEFAULT | EVENT_TYPE.EVENT_TYPE_PEOPLE_MOTION | EVENT_TYPE.EVENT_TYPE_AREA_MOTION | EVENT_TYPE.EVENT_TYPE_FACE | EVENT_TYPE.EVENT_TYPE_BABY_CRY | EVENT_TYPE.EVENT_TYPE_AI | EVENT_TYPE.EVENT_TYPE_PET_MOTION | EVENT_TYPE.EVENT_TYPE_CAMERA_CALLING | EVENT_TYPE.EVENT_TYPE_LOUDER_SOUND),

    videoPath: null,

    videoViewWidth: 0,
    videoViewHeight: 0,
    dialogVisibility: false,

    permissionRequestState: 0,
    showPermissionDialog: false,
    displayCloudList: StackNavigationInstance.getStackNavigationInstance().getParam("isVip"),
    showEmptyHint: false
  };

  loadEvents() {
    // (EVENT_TYPE.EVENT_TYPE_DEFAULT | EVENT_TYPE.EVENT_TYPE_PEOPLE_MOTION | EVENT_TYPE.EVENT_TYPE_AREA_MOTION | EVENT_TYPE.EVENT_TYPE_FACE | EVENT_TYPE.EVENT_TYPE_BABY_CRY | EVENT_TYPE.EVENT_TYPE_AI | EVENT_TYPE.EVENT_TYPE_PET_MOTION | EVENT_TYPE.EVENT_TYPE_CAMERA_CALLING)
    let eventFlags = EVENT_TYPE.EVENT_TYPE_DEFAULT;
    this.supportEvts = [];
    this.supportEvtsInt = [];
    if (this.supportEvent(Event.ObjectMotion)) {
      eventFlags = eventFlags | EVENT_TYPE.EVENT_TYPE_AREA_MOTION;
      this.supportEvts.push(Event.ObjectMotion);
      this.supportEvtsInt.push(EVENT_TYPE.EVENT_TYPE_AREA_MOTION);
    }
    if (this.supportEvent(Event.PeopleMotion)) {
      eventFlags = eventFlags | EVENT_TYPE.EVENT_TYPE_PEOPLE_MOTION;
      this.supportEvts.push(Event.PeopleMotion);
      this.supportEvtsInt.push(EVENT_TYPE.EVENT_TYPE_PEOPLE_MOTION);
    }
    if (this.supportEvent(Event.BabyCry)) {
      eventFlags = eventFlags | EVENT_TYPE.EVENT_TYPE_BABY_CRY;
      this.supportEvts.push(Event.BabyCry);
      this.supportEvtsInt.push(EVENT_TYPE.EVENT_TYPE_BABY_CRY);
    }
    if (this.supportEvent(Event.Pet)) {
      eventFlags = eventFlags | EVENT_TYPE.EVENT_TYPE_PET_MOTION;
      this.supportEvts.push(Event.Pet);
      this.supportEvtsInt.push(EVENT_TYPE.EVENT_TYPE_PET_MOTION);
    }
    if (this.supportEvent(Event.KnownFace)) {
      eventFlags = eventFlags | EVENT_TYPE.EVENT_TYPE_FACE;
      if (!this.isInternationalServer) {
        this.supportEvts.push(Event.KnownFace);
        this.supportEvtsInt.push(EVENT_TYPE.EVENT_TYPE_FACE);
      }
    }
    if (this.supportEvent(Event.AI)) {
      eventFlags = eventFlags | EVENT_TYPE.EVENT_TYPE_AI;
      this.supportEvts.push(Event.AI);
      this.supportEvtsInt.push(EVENT_TYPE.EVENT_TYPE_AI);
    }
    if (this.supportEvent(Event.LOUD)) {
      eventFlags = eventFlags | EVENT_TYPE.EVENT_TYPE_LOUD;
      this.supportEvts.push(Event.LOUD);
      this.supportEvtsInt.push(EVENT_TYPE.EVENT_TYPE_LOUD);
    }

    if (this.supportEvent(Event.CALL)) {
      eventFlags = eventFlags | EVENT_TYPE.EVENT_TYPE_CAMERA_CALLING;
      this.supportEvts.push(Event.CALL);
      this.supportEvtsInt.push(EVENT_TYPE.EVENT_TYPE_CAMERA_CALLING);
    }

    if (this.supportEvent(Event.FenceIn)) {
      eventFlags = eventFlags | EVENT_TYPE.EVENT_TYPE_FENCE;
      this.supportEvts.push(Event.FenceIn);
      this.supportEvtsInt.push(EVENT_TYPE.EVENT_TYPE_FENCE);
    }

    LogUtil.logOnAll("cloudTimeline", `supportEvts: ${ JSON.stringify(this.supportEvts) }`);
    return eventFlags;
  }

  constructor(props) {
    super(props);

    this.timeIndicatorView = null;
    this.dateTime = new Date();
    this.isFirstReceiveFiles = true;
    this.isUserPause = false;

    this.isEuropeServer = CameraConfig.getIsEuropeServer();
    this.isInternationalServer = CameraConfig.getInternationalServerStatus();
    this.isCloudServer = CameraConfig.getIsCloudServer();

    this.isPluginForeGround = true;// sdcard页面不存在跳到其他页面的情况
    this.isPageForeGround = true;
    this.isAppForeground = true; // 米家App是否在前台。

    // see https://blog.csdn.net/u011068702/article/details/83218639
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        console.log("will focus");

        this.isPageForeGround = true;
        this.isPluginForeGround = true;
        this.isAppForeground = true;
        this.restoreOri();
        this._onResume();
      }
    );
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'didBlur',
      () => {
        console.log("did blur");

        this.isPageForeGround = false;
        this._onPause();
      }
    );


    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }
      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      this.isAppForeground = true;// rnactivity调用了onresume
      this.restoreOri();
      this._onResume();
    });

    this.willPauseListener = PackageEvent.packageWillPause.addListener(() => {
      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }
      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      this.isAppForeground = false;// rnactivity调用了onpause
      this._onPause();
    });

    if (Platform.OS == "ios") {
      this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {
        if (!this.isPageForeGround) { // 如果当前插件不在前台
          return;
        }
        this.isPluginForeGround = true;// rnactivity调用了onresume
        this._onResume();
      });

      this.willDisappearListener = PackageEvent.packageViewWillDisappearIOS.addListener(() => {
        if (!this.isPageForeGround) { // 如果当前插件不在前台
          return;
        }
        this.toPortrait();// ios强制切换到竖屏去
        setTimeout(() => {
          this.isPluginForeGround = false;// rnactivity调用了onpause
          // console.log('testaaa', 'willDisappearListener', 'page: ', this.isPageForeGround, ' plugin: ', this.isPluginForeGround);
          this._onPause();
          
        }, 500);
      });
    }

    this.isDataUsageWarning = true;

    this.currentNetworkState = -1;
    this.isChangingNetwork = false;

    this.toStartTime = 0;// 暂存要播放的view的时间戳

    this.startTime = 0;
    this.endTime = 0;
    this.offset = 0;
    this.sessionId = 0;

    this.lastTimeItemEndTime = 0;// 从rdt捞过来的数据 的最后一个item的endtime

    this.isSetPlayTime = false;// 是否正在设置播放时间
    this.setPlayTimeMillis = 0;// 记录本次设置playback命令的时间
    this.lastTimestamp = 0;// 记录上次返回回调的时间戳
    // todo notify native side  whether in continue playback mode
    this.timelineView = null;
    this.scrollTimeout = null;
    this.scrollTimeout1 = null;
    this.mOri = "PORTRAIT";
    this.connRetry = 2;

    this.videoItem = null;


    this.stackNavigationOnPauseListener = null;
    this.stackNavigationOnResumeListener = null;
    this.isParentPageBackground = false;

    if (this.stackNavigationOnPauseListener == null) {
      this.stackNavigationOnPauseListener = DeviceEventEmitter.addListener(SD_CLOUD_STACK_NAVIGATION_ONPAUSE, () => {
        // 父类收到了pause事件，传给了子控件。
        if (!this.isPageForeGround) { // 只有页面处于前台的时候，这个属性才奏效
          return;
        }
        this.isParentPageBackground = true;
        this.isPageForeGround = false;
        this._onPause();// 暂停咯
      });
    }
    if (this.stackNavigationOnResumeListener == null) {
      this.stackNavigationOnResumeListener = DeviceEventEmitter.addListener(SD_CLOUD_STACK_NAVIGATION_ONRESUME, () => {
        console.log("onresume");

        if (!this.isParentPageBackground) {
          return;
        }
        this.restoreOri();
        this.isParentPageBackground = false;
        this.isPageForeGround = true;
        this.isPluginForeGround = true;
        this._onResume();
      });
    }
    if (this.stacknavigationOnRebackListener == null) {
      this.stacknavigationOnRebackListener = DeviceEventEmitter.addListener(SD_CLOUD_STACK_NAVIGATION_ONBACK, () => {
        // 父类收到了pause事件，传给了子控件。
        // if (!this.isPageForeGround) {//只有页面处于前台的时候，这个属性才奏效
        //   return;
        // }
        console.log(TAG, "receive parents back event");
        this.isParentPageBackground = true;
        this.isPageForeGround = false;
        this._onPause();// 暂停咯
      });
    }

    this.selectedIndexArray = [0];

    this.displayCutoutTop = 0;

    this.showNetworkDisconnectTimeout = null;
  }

  _networkChangeHandler = (networkState) => {

    if (!this.isPageForeGround) {
      return;
    }
    if (!this.state.displayCloudList) {
      return;
    }
    // if (this.currentNetworkState != networkState) { // 网络状态不一样
    //   this.isChangingNetwork = true;
    // }
    // if (this.currentNetworkState != networkState) { // 网络状态不一样
    //   this.isChangingNetwork = true;
    // }
    console.log("处理网络变化" + new Date().getTime());
    this.networkState = networkState;
    clearTimeout(this.showNetworkDisconnectTimeout);
    if (networkState == 0) { // 网络断开了连接 showError?
      this.showNetworkDisconnectTimeout = setTimeout(() => {
        this.setState({ showErrorView: true, showLoadingView: false, showPlayToolBar: false, showPauseView: false });
      }, 1300);
      return;
    }
    this.isMobileNetwork = networkState == 1;
    if (this.isPageForeGround) { // 有网络来了  发起重连吧
      this.setState({ showErrorView: false });
      setTimeout(() => {
        this._startQueryNetwork();
      }, 500);// 过一会再查询 ，那个查询网络类型的api有问题。
    }
  }

  _onResume() {
    if (CameraConfig.isToUpdateVipStatue) {
      VipUtil.getVipStatus().then((res) => {
        this.isVip = res;
        this.setState({ isVip: this.isVip });
        this._onResume();
      }).catch((err) => {

      });
    }

    this.cellPhoneNetworkStateChanged = HostEvent.cellPhoneNetworkStateChanged.addListener((networkInfo) => {
      this._networkChangeHandler(networkInfo.networkState);
    });
    DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');
    if (CameraConfig.isToUpdateVipStatue) {
      VipUtil.getVipStatus().then(({ isVip, inCloseWindow }) => {
        this.isVip = isVip;
        this.isInExpireWindow = inCloseWindow;
        this.refreshContents();
      }).catch((err) => {

      });
    }

    if (!this.state.displayCloudList) {
      this.fetchVipStatus();//
      return;// 不是vip 直接返回，不应该走剩下的路线。
    }

    Host.getPhoneScreenInfo()
      .then((result) => {
        this.displayCutoutTop = PixelRatio.roundToNearestPixel(result.displayCutoutTop / PixelRatio.get() || 0);
        if (isNaN(this.displayCutoutTop)) {
          this.displayCutoutTop = 0;
        }
        console.log(TAG, "result:", result);
      })
      .catch((error) => {

      });
    this.destroyed = false;



    if (this.isUserPause) {
      return;
    }
    // 重新进来 要绑定一遍这些事件。

    this.onGetFiles();// 从其他页面回来 要刷新一遍数据，避免出现其他页面删了  这个页面还没有同步数据的情况。
    this._startQueryNetwork();
  }

  _onPause() {

    this.cellPhoneNetworkStateChanged && this.cellPhoneNetworkStateChanged.remove();

    if (!this.state.displayCloudList) {
      return;
    }


    if (this.state.showErrorView) {
      return;
    }

    this._startPlay(false);
  }

  restoreOri() {
    console.log(TAG, "restoreOri");
    if ("PORTRAIT" === this.mOri) {
      this.toPortrait();
    } else {
      this.toLandscape();
    }
  }


  toPortrait() {
    StatusBar.setHidden(false); // false 状态栏显示
    this._hidePlayToolBarLater();
    console.log(TAG, "toPortrait");
    // 解决第三方手机状态栏隐藏问题，如有深色模式可修改
    this.mOri = "PORTRAIT";
    if (Host.isPad && Platform.OS == "android") { // for android for the special page
      Service.miotcamera.enterFullscreenForPad(false);
    } else {
      // CameraConfig.lockToPortrait();
    }
    CameraConfig.lockToPortrait();

  }

  toLandscape() {
    StatusBar.setHidden(true); // true 状态栏隐藏
    this._hidePlayToolBarLater();
    console.log(TAG, "toLandscape");
    this.mOri = "LANDSCAPE";
    if (Platform.OS === "android") {
      Orientation.lockToLandscape();
      Service.miotcamera.enterFullscreenForPad(true);
    } else {
      if (Platform.OS == 'ios' && Host.isPad) {
        Service.miotcamera.enterFullscreenForPad(true);
      } else {
        Orientation.lockToLandscapeRight();
      }
    }
  }

  _hidePlayToolBarLater(ignoreFull = false) {
    let tTimer = 5000;
    clearTimeout(this.showPlayToolBarTimer);
    this.showPlayToolBarTimer = setTimeout(() => {
      if (this.timelineView && !this.timelineView.isTimelineIdle() && this.state.fullScreen) {
        this._hidePlayToolBarLater();
        return;
      }
      this.setState({ showPlayToolBar: false, showPauseView: false });
    }, tTimer);
  }
  componentWillMount() {
    this._panResponder = PanResponder.create({
      onStartShouldSetPanResponderCapture: (evt, gestureState) => {
        if (Platform.OS == "ios") {
          this.setState({ touchMovement: true }) ;//手指移动，用来监听IOS
        }
      },
    });
  }

  componentWillUnmount() {
    this.destroyed = true;
    console.log("CloudTimelinePlayerFragment", "unmount");

    try {
      this._onPause();
    } catch (exception) {
      console.log(TAG, "unmount error", exception);
    }

    this.stackNavigationOnPauseListener && this.stackNavigationOnPauseListener.remove();
    this.stackNavigationOnPauseListener = null;
    this.stackNavigationOnResumeListener && this.stackNavigationOnResumeListener.remove();
    this.stackNavigationOnResumeListener = null;
    this.stacknavigationOnRebackListener && this.stacknavigationOnRebackListener.remove();

    CloudVideoUtil.setCloudFilesReceivedCallback(null);
    CloudVideoUtil.setCloudDateListCallback(null);
    CloudVideoUtil.removeAllDatas();

    this.toPortrait();
    clearTimeout(this.scrollTimeout);
    clearTimeout(this.scrollTimeout1);
    clearTimeout(this.snapshotTimeout);


    this.didResumeListener && this.didResumeListener.remove();
    this.willPauseListener && this.willPauseListener.remove();
    if (this.willAppearListener) {
      this.willAppearListener.remove();
    }
    if (this.willDisappearListener) {
      this.willDisappearListener.remove();
    }
    this.willStopListener && this.willStopListener.remove();
    this.didFocusListener && this.didFocusListener.remove();
    this.didBlurListener && this.didBlurListener.remove();
    Orientation.removeOrientationListener(this._orientationListener);
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  render() {

    return (
      <View {...this._panResponder.panHandlers} style={styles.container}>
        <SafeAreaView style={{ backgroundColor: "#ffffff" }}></SafeAreaView>
        {this.state.displayCloudList ? this._renderVideoLayout() : null}
        {this.state.displayCloudList ? this._renderTimeLineView() : null}
        {this.state.displayCloudList ? this._renderEventTypes() : null}
        {this.state.displayCloudList ? this._renderBottomSelectView() : null}
        {this.renderCloudBuyViewNew()}
        {this._renderResolutionDialog()}
        {this._renderPermissionDialog()}
        <SafeAreaView></SafeAreaView>
      </View>
    );
  }

  renderCloudBuyViewNew() {
    if (this.state.displayCloudList) {
      return null;
    }
    let item1 = {
      source: require("../../Resources/Images/cloud_intro_part3.png"),
      title: LocalizedStrings["cloud_banner_storage_title1"],
      text: LocalizedStrings["cloud_banner_storage_text11"]
    };
    let item2 = {
      source: require("../../Resources/Images/cloud_intro_part2.png"),
      title: LocalizedStrings["cloud_banner_storage_title2"],
      text: LocalizedStrings["cloud_banner_storage_text21"]
    };
    let item3 = {
      source: require("../../Resources/Images/cloud_intro_part4.png"),
      title: LocalizedStrings["cloud_banner_storage_title3"],
      text: LocalizedStrings["cloud_banner_storage_text31"]
    };
    let stateCover = {
      position: 'absolute',
      width: '100%',
      height: '100%',
      display: "flex",
      // alignItems: "flex-start",
      // justifyContent: 'space-evenly',
      marginTop: (this.isEuropeServer ? 30 : 55) * kPxScale,
      marginLeft: 10
    };
    let stateCoverTitle = {
      textAlign: "left",
      textAlignVertical: "center",
      marginLeft: 20
    };
    let isIOS = Platform.OS === 'ios';
    let isLanCN = Util.isLanguageCN();
    let textWidth = isLanCN ? 160 : 250;
    let titleFontsize = this.isEuropeServer ? 31 * kPxScale : isLanCN ? 33 : 31; // 海外都是英文xiaomi home secure, 所以都随尺寸变化
    let titleLineHeight = this.isEuropeServer ? 37 * kPxScale : 37;
    return (
      <SafeAreaView style={{ flex: 1, flexDirection: 'column', width: '100%', height: '100%', alignItems: "center", backgroundColor: "xm#F0F0F0" }}>
        <View style={{ width: '100%', height: '40%', marginBottom: -57, marginTop: 0 }}>
          {/* <Image source={titleBkImg} style={{ display: "flex", flex: 1, width: "100%", height: "100%" }} resizeMode="stretch" /> */}
          <Image source={{ uri: 'https://cdn.cnbj0.fds.api.mi-img.com/miio.files/resource_package/20240117101102_mj-std-camera-cloud_intro_title_bk_cloud_intro_title_bk.png' }} style={{ display: "flex", flex: 1, width: "100%", height: "100%" }} resizeMode="stretch" />
          <View style={stateCover}>
            {/* 上面的存储暂停 */}
            <View style={{ height: this.isEuropeServer ? 155 : 150, width: textWidth, backgroundColor: "#00000000", paddingBottom: 30 }}>
              <View style={{ marginLeft: isIOS ? 0 : (isLanCN ? -5 : -1), width: this.isEuropeServer ? 160 : (isLanCN ? 170 : 220) }}>
                <Text
                  style={[stateCoverTitle, { fontSize: titleFontsize, color: "#283537", fontWeight: 'bold', marginBottom: 10, letterSpacing: isLanCN ? 3 : 1, lineHeight: titleLineHeight }]}>
                  {this.isEuropeServer ? LocalizedStrings["eu_cloud_seting"] : LocalizedStrings["s_cloud_setting"]}
                </Text>
              </View>

              <Text numberOfLines={3}
                    style={[BaseStyles.text17, stateCoverTitle, { color: "xm#000000CC", marginBottom: 5, letterSpacing: isLanCN ? 2 : 1, fontWeight: "300", lineHeight: 22 }]}>
                {LocalizedStrings["cloud_banner_storage_text1"]}
              </Text>

            </View>
          </View>
        </View>

        <ScrollView style={{ display: "flex", width: "100%", height: "100%", flexGrow: 1 }}>
          <View style={{ alignItems: 'center' }}>
            {[item1, item2, item3].map((item, idx) => this._renderBuyViewItem(item, idx))}
          </View>
          { Platform.OS == 'android' ? <View style={{ height: 200 }}></View> : null }
        </ScrollView>

        <View style={{ position: 'absolute', width: '86%', height: 46, borderRadius: 23, bottom: 20, backgroundColor: '#32BAC0', marginBottom: Util.getBottomMarginWithoutSafeArea() }}>
          <TouchableOpacity style={{ width: '100%', height: 46, justifyContent: 'center', alignItems: 'center' }}
                            onPress={() => { // cancel
                             if (!Device.isOwner) {
                               Toast.success("share_user_permission_hint");
                               return;
                             }
                             // super.statView(true); // 携带duration
                             console.log('click_buy_cloud clicked, Storage_CloudStorage_Purchase_ClickNum');
                             TrackUtil.reportClickEvent("Storage_CloudStorage_Purchase_ClickNum"); // Storage_CloudStorage_Purchase_ClickNum
                             TrackUtil.newOneTrack(['click'], { item_name: 'cloud_new_link_button', item_type: 'button' });
                             Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: "stroagemgt_button_v1" });
                             CameraConfig.isToUpdateVipStatue = true;
                           }}>
            <Text style={[BaseStyles.text17, { color: Util.isDark() ? 'xm#ffffffe7' : '#ffffff', fontWeight: '500' }]} > {this.isEuropeServer ? LocalizedStrings['eu_click_buy_cloud'] : LocalizedStrings['cloud_banner_buy_button_title']} </ Text >
          </TouchableOpacity >
        </View>
      </SafeAreaView>
    );
  }

  _renderBuyViewItem(item, idx) {
    let panelOptionItemLayout = {
      display: "flex",
      position: "relative",
      width: "86%",
      height: Util.isLanguageCN() ? 110 : 155,
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 12,
      paddingLeft: 30,
      paddingRight: 22,
      borderRadius: 16,
      backgroundColor: "xm#FFFFFF"
    };
    return (
      <View key={idx}>
        <View
          style={panelOptionItemLayout}
        >
          <Image
            style={{ width: 40, height: 40, position: "relative" }}
            source={item.source}
          />

          <View style={{ display: "flex", height: "100%", flex: 1, position: "relative", flexDirection: "column", justifyContent: "center" }}>
            <Text numberOfLines={5}
                  style={[BaseStyles.text16, { marginLeft: 15, marginTop: 0, color: "xm#000000", fontWeight: "bold", lineHeight: 20 }]}
            >
              {item.title}
            </Text>

            <Text
              style={[BaseStyles.text13, { marginLeft: 15, marginTop: 4, color: "#999999", fontWeight: "400", lineHeight: 16 }]}
              numberOfLines={5}
              ellipsizeMode={"tail"}
            >
              {item.text}
            </Text>
          </View>
        </View>
      </View>
    );
  }


  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 存储卡/相册
    // status == 1 麦克风
    // 
    let message = "";
    if (this.state.permissionRequestState == 0) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);
    } else {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_microphone"]);
    }
    return (
      <MessageDialog
      title={LocalizedStrings["tips"]}
      message={message}
      messageStyle={{
        fontSize:14,
    }}
      buttons={[
        {
          text: LocalizedStrings["action_cancle"],
          callback: () => {
            this.setState({ showPermissionDialog: false });
          }
        },
        {
          text: LocalizedStrings["setting"],
          callback: () => {
            Host.ui.openTerminalDeviceSettingPage(1);
            this.setState({ showPermissionDialog: false });
          }
        }
      ]}
      onDismiss={() => {
        this.setState({ showPermissionDialog: false });
      }}
      visible={this.state.showPermissionDialog} />
    );

  }

  _renderEventTypes() {
    if (this.state.fullScreen) {
      return null;
    }
    if (this.state.isEmpty) {
      return (
        <View style={{ flex: 1 }}>
        </View>
      );
    }
    // here judge whether to show baby cry or others   currently 021 support all feature
    let isMotionSelected = this.state.eventTypeFlags & EVENT_TYPE.EVENT_TYPE_AREA_MOTION;
    let isBabyCrySelected = this.state.eventTypeFlags & EVENT_TYPE.EVENT_TYPE_BABY_CRY;
    let isCameraCallingSelected = this.state.eventTypeFlags & EVENT_TYPE.EVENT_TYPE_CAMERA_CALLING;
    let isPeopleSelected = this.state.eventTypeFlags & EVENT_TYPE.EVENT_TYPE_PEOPLE_MOTION;
    let isFaceSelected = this.state.eventTypeFlags & EVENT_TYPE.EVENT_TYPE_FACE;
    let isPetSelected = this.state.eventTypeFlags & EVENT_TYPE.EVENT_TYPE_PET_MOTION;
    let isLoudSoundSelected = this.state.eventTypeFlags & EVENT_TYPE.EVENT_TYPE_LOUDER_SOUND;
    let isAISelected = this.state.eventTypeFlags & EVENT_TYPE.EVENT_TYPE_AI;

    // let cameraCallingUnselectedDrawable = require("../../Resources/Images/time_line_event_icon_camera_calling_sel.png");
    // let cameraCallingSelectedDrawable = require("../../Resources/Images/time_line_event_icon_camera_calling_nor.png");

    let cameraCallingUnselectedDrawable = require("../../Resources/Images/time_line_event_icon_camera_video_calling_sel.png");
    let cameraCallingSelectedDrawable = require("../../Resources/Images/time_line_event_icon_camera_video_calling_nor.png");

    let babyCryUnselectedDrawable = require("../../Resources/Images/time_line_event_icon_baby_cry_sel.png");
    let babyCrySelectedDrawable = require("../../Resources/Images/time_line_event_icon_baby_cry_nor.png");

    let peopleUnselectedDrawable = require("../../Resources/Images/time_line_event_icon_people_selected.png");
    let peopleSelectedDrawable = require("../../Resources/Images/time_line_event_icon_people_move_nor.png");

    let motionUnselectedDrawable = require("../../Resources/Images/time_line_event_icon_erea_move_sel.png");
    let motionSelectedDrawable = require("../../Resources/Images/time_line_event_icon_erea_move_nor.png");

    let faceUnselectedDrawable = require("../../Resources/Images/time_line_event_icon_face_sel.png");
    let faceSelectedDrawable = require("../../Resources/Images/time_line_event_icon_face_nor.png");

    let petUnselectedDrawable = require("../../Resources/Images/time_line_event_icon_pet_move_sel.png");
    let petSelectedDrawable = require("../../Resources/Images/time_line_event_icon_pet_move_nor.png");

    let aiUnselectedDrawable = require("../../Resources/Images/time_line_event_icon_ai_sel.png");
    let aiSelectedDrawable = require("../../Resources/Images/time_line_event_icon_ai_nor.png");

    let textColorSelected = "#FFFFFF";
    let textColorUnselected = "#666666";

    let containerPadding = 15;
    let eventPadding = 10;
    let eventMargin = 13.5;

    if (kWindowHeight < 700) {
      containerPadding = 10;
      eventPadding = 7;
      eventMargin = 13.5;
    }
    let containerWidth = (kWindowWidth - 40 - eventMargin * 4) / 3;
    let eventMarginLeft = (kWindowWidth - containerWidth * 3 - eventMargin * 4) / 2;
    return (
      <ScrollView
        contentContainerStyle={{
          flexDirection: "row",
          flexWrap: "wrap",
          justifyContent: 'flex-start'

        }}
        style={{
          display: "flex",
          flex: 1,
          paddingHorizontal: containerPadding,
          paddingTop: 15,
          marginTop: 20
          // backgroundColor: 'blue'
        }}
      >
        <TouchableOpacity
        // style={{width:containerWidth,height:36, padding: eventPadding,marginHorizontal: eventMargin/2, marginBottom: 10, backgroundColor: (isFaceSelected ? EVENT_TYPE_COLOR.faceSelectedColor : EVENT_TYPE_COLOR.unselectedColor), borderRadius: 360, display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center" }}
          style={{ height: 36, padding: eventPadding, marginHorizontal: eventMargin / 2, marginBottom: 15, backgroundColor: (isMotionSelected ? EVENT_TYPE_COLOR.motionSelectedColor : EVENT_TYPE_COLOR.unselectedColor), borderRadius: 360, display: "flex", flexDirection: "row", alignItems: "center", justifyContent: 'center' }}
          onPress={() => {
            this.changeEventType(EVENT_TYPE.EVENT_TYPE_AREA_MOTION);
          }}
        >
          <Image
            style={{ width: 15, height: 15, marginRight: 5 }}
            source={isMotionSelected ? motionSelectedDrawable : motionUnselectedDrawable}
          />

          <Text style={{ color: (isMotionSelected ? textColorSelected : textColorUnselected), fontSize: kIsCN ? 12 : 10 }}>
            {LocalizedStrings["object_move_desc"] + " "}
          </Text>

        </TouchableOpacity>
        <TouchableOpacity
          style={{ height: 36, padding: eventPadding, marginHorizontal: eventMargin / 2, marginBottom: 15, backgroundColor: (isPeopleSelected ? EVENT_TYPE_COLOR.peopleSelectedColor : EVENT_TYPE_COLOR.unselectedColor), borderRadius: 360, display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center" }}
          onPress={() => {
            this.changeEventType(EVENT_TYPE.EVENT_TYPE_PEOPLE_MOTION);
          }}
        >
          <Image
            style={{ width: 15, height: 15, marginRight: 5 }}
            source={isPeopleSelected ? peopleSelectedDrawable : peopleUnselectedDrawable}
          />

          <Text style={{ color: (isPeopleSelected ? textColorSelected : textColorUnselected), fontSize: kIsCN ? 12 : 10 }}>
            {LocalizedStrings["people_move_desc"] + " "}
          </Text>

        </TouchableOpacity>

        {
          !CameraConfig.displayLOUDER_SOUND_Timeline(Device.model) ? null :
            <TouchableOpacity
              style={{ height: 36, padding: eventPadding, marginHorizontal: eventMargin / 2, marginBottom: 15, backgroundColor: (isLoudSoundSelected ? EVENT_TYPE_COLOR.loudSoundSelectedColor : EVENT_TYPE_COLOR.unselectedColor), borderRadius: 360, display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center" }}
              onPress={() => {
                this.changeEventType(EVENT_TYPE.EVENT_TYPE_LOUDER_SOUND);
              }}


            >
              <Image
                style={{ width: 15, height: 15, marginRight: 5 }}
                source={isLoudSoundSelected ? loudSoundSelectedDrawable : loudSoundUnselectedDrawable}
              />

              <Text style={{ color: (isLoudSoundSelected ? textColorSelected : textColorUnselected), fontSize: kIsCN ? 12 : 10 }}
              >
                {LocalizedStrings["loud_desc"] + " "}
              </Text>

            </TouchableOpacity>
        }
        {!VersionUtil.judgeIsV1(Device.model)&&CameraConfig.CloudBabyCry() ? <TouchableOpacity
          style={{ height: 36, padding: eventPadding, marginHorizontal: eventMargin / 2, marginBottom: 15, backgroundColor: (isBabyCrySelected ? EVENT_TYPE_COLOR.babyCrySelectedColor : EVENT_TYPE_COLOR.unselectedColor), borderRadius: 360, display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center" }}
          onPress={() => {
            this.changeEventType(EVENT_TYPE.EVENT_TYPE_BABY_CRY);
          }}
        >
          <Image
            style={{ width: 15, height: 15, marginRight: 5 }}
            source={isBabyCrySelected ? babyCrySelectedDrawable : babyCryUnselectedDrawable}
          />

          <Text style={{ color: (isBabyCrySelected ? textColorSelected : textColorUnselected), fontSize: kIsCN ? 12 : 10 }}>
            {LocalizedStrings["baby_cry_desc"] + " "}
          </Text>

        </TouchableOpacity> : null}

        {!VersionUtil.judgeIsV1(Device.model) && !this.isInternationalServer ? <TouchableOpacity
          style={{ height: 36, padding: eventPadding, marginHorizontal: eventMargin / 2, marginBottom: 10, backgroundColor: (isFaceSelected ? EVENT_TYPE_COLOR.faceSelectedColor : EVENT_TYPE_COLOR.unselectedColor), borderRadius: 360, display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center" }}
          onPress={() => {
            this.changeEventType(EVENT_TYPE.EVENT_TYPE_FACE);
          }}
        >
          <Image
            style={{ width: 15, height: 15, marginRight: 5 }}
            source={isFaceSelected ? faceSelectedDrawable : faceUnselectedDrawable}
          />

          <Text style={{ color: (isFaceSelected ? textColorSelected : textColorUnselected), fontSize: kIsCN ? 12 : 10 }}>
            {LocalizedStrings["face_desc"] + " "}
          </Text>

        </TouchableOpacity> : null}

        {
          CameraConfig.intelligentScenePush() ? <TouchableOpacity
          style={{ height: 36, padding: eventPadding, marginHorizontal: eventMargin / 2, marginBottom: 15, backgroundColor: (isAISelected ? EVENT_TYPE_COLOR.aiSelectedColor : EVENT_TYPE_COLOR.unselectedColor), borderRadius: 360, display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center" }}
          onPress={() => {
            this.changeEventType(EVENT_TYPE.EVENT_TYPE_AI);
          }}
        >
          <Image
            style={{ width: 15, height: 15, marginRight: 5 }}
            source={isAISelected ? aiSelectedDrawable : aiUnselectedDrawable}
          />

          <Text style={{ color: (isAISelected ? textColorSelected : textColorUnselected), fontSize: kIsCN ? 12 : 10 }}>
            {LocalizedStrings["ai_desc"] + " "}
          </Text>

        </TouchableOpacity> : null
        }
        {
          !CameraConfig.displayPetInTimeline(Device.model)? null:
            <TouchableOpacity
              style={{ width: 95, height: 36, padding: eventPadding, marginHorizontal: eventMargin / 2, marginBottom: 15, backgroundColor: (isPetSelected ? EVENT_TYPE_COLOR.petSelectedColor : EVENT_TYPE_COLOR.unselectedColor), borderRadius: 360, display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center" }}
              onPress={() => {
                this.changeEventType(EVENT_TYPE.EVENT_TYPE_PET_MOTION);
              }}
            >
              <Image
                style={{ width: 15, height: 15, marginRight: 5 }}
                source={isPetSelected ? petSelectedDrawable : petUnselectedDrawable}
              />

              <Text style={{ color: (isPetSelected ? textColorSelected : textColorUnselected), fontSize: 12 }}>
                {LocalizedStrings["pet_desc"]}
              </Text>

            </TouchableOpacity>
        }
        {
          !CameraConfig.displayCameraCallingTimeline(Device.model) ? null :
            <TouchableOpacity
              style={{ height: 36, padding: eventPadding, marginHorizontal: eventMargin / 2, marginBottom: 10, backgroundColor: (isCameraCallingSelected ? EVENT_TYPE_COLOR.cameraCallingSelectedColor : EVENT_TYPE_COLOR.unselectedColor), borderRadius: 360, display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center" }}
              onPress={() => {
                this.changeEventType(EVENT_TYPE.EVENT_TYPE_CAMERA_CALLING);
              }}
            >
              <Image
                style={{ width: 15, height: 15, marginRight: 5 }}
                source={isCameraCallingSelected ? cameraCallingSelectedDrawable : cameraCallingUnselectedDrawable}
              />
              <Text style={{ color: (isCameraCallingSelected ? textColorSelected : textColorUnselected), fontSize: 12 }}>
                {CameraConfig.isSupportVideoCall(Device.model) ? LocalizedStrings['voice_video_call'] : LocalizedStrings["one_key_call_event_name"]}
              </Text>
            </TouchableOpacity>
        }
      </ScrollView>

    );
  }


  _renderVideoLayout() {
    return (
      // <View style={this.state.fullScreen ? styles.videoContainerFull : [styles.videoContainerNormal, kWindowWidth <= 390 && Platform.OS !== "ios" ? { height: portraitVideoHeight - 7 } : { height: portraitVideoHeight }]}
      <View style={this.state.fullScreen ? styles.videoContainerFull : [styles.videoContainerNormal, { height: portraitVideoHeight }]}
        onLayout={(event) => {
          let width = event.nativeEvent.layout.width;
          let height = event.nativeEvent.layout.height;
          this.setState({ videoViewWidth: width, videoViewHeight: height });
        }}
      >
        {this._renderVideoView()}
        {this._renderClickableView()}
        {this._renderPauseView()}
        {this._renderErrorRetryView()}
        {this._renderLoadingView()}

        {/* {this._renderTitleView()} */}
        {this._renderVideoControlView()}
        {this._renderSnapshotView()}

        {this._renderTimeIndicatorView()}

        {this._renderLandscapeTopButtons()}
        {this._renderLandscapeRightButtons()}
      </View>
    );
  }

  _renderClickableView() {
    return (
      <TouchableOpacity
        style={{ width: "100%", height: "100%", backgroundColor: "#00000000", position: "absolute", zIndex: 0 }}
        accessibilityLabel={DescriptionConstants.rp_62}
        onPress={() => {
          this._onVideoClick();
        }}
      >
      </TouchableOpacity>
    );
  }
  // 这里是滑动时产生的时间条
  _renderTimeIndicatorView() {
    return (
      <View
        style={{ width: "100%", height: "100%", position: "absolute", display: "flex", justifyContent: "center", alignItems: "center", marginTop: -50 }}
        pointerEvents={"none"}
      >
        <CenterTimeView
          ref={(ref) => {
            this.timeIndicatorView = ref;
          }}
        >

        </CenterTimeView>
      </View>
    );
  }

  _renderLandscapeRightButtons() {
    if (!this.state.fullScreen) {
      return null;
    }

    if (!this.state.showPlayToolBar) {
      return null;
    }

    let screenHeight = Dimensions.get("window").height;
    let screenWidth = Dimensions.get("window").width;
    if (screenWidth < screenHeight) { // mi 9 se上 第一次切换回来，获取的高度是竖屏下的高度，需要修改以下。
      screenHeight = screenWidth;
    }
    let viewHeight = screenHeight - 120;
    let StatusBarheight = null, haveStatus = false;
    if (Platform.OS === "android") {
      StatusBarheight = StatusBar.currentHeight - 16;
      if (StatusBarheight <= 0) {
        haveStatus = true;
        StatusBarheight = 15;
      }
      // -16 因为返回按钮的图片留白太大
    } else {
      StatusBarheight = StatusBarUtil._getInset("top") - 30;
    }
    return (
      <View style={{ position: "absolute", justifyContent: "space-around", bottom: 0, right: this.displayCutoutTop - 16, top: Host.isPad ? 80 : 50, height: viewHeight - 50, width: 80, alignItems: "center" }}>


        {/* 目前的 - Lottie动画按钮 */}
        {
          this.showSnapShot ?
            <MHLottieSnapLandscapeButton
              style={{ width: 50, height: 50 }}
              onPress={() => {
                this._startSnapshot();
                this._hidePlayToolBarLater();
              }}
              displayState={MHLottieSnapLandscapeBtnDisplayState.NORMAL}
              accessibilityLabel={DescriptionConstants.rp_26}
            /> : null
        }


      </View>
    );
  }

  _renderLandscapeTopButtons() {

    if (!this.state.fullScreen) {
      return null;
    }

    if (!this.state.showPlayToolBar) {
      return null;
    }

    let screenHeight = Dimensions.get("window").height;
    let screenWidth = Dimensions.get("window").width;
    if (screenWidth < screenHeight) { // mi 9 se上 第一次切换回来，获取的高度是竖屏下的高度，需要修改以下。
      screenHeight = screenWidth;
    }

    let iconBack = require("../../Resources/Images/icon_back_black_nor_dark.png");
    let iconBackPre = require("../../Resources/Images/icon_back_black_nor_dark.png");


    let audioIndex = this.state.speed > 1 ? 2 : (this.state.isMute ? 1 : 0);
    let speedIndex = this.state.speed == 1 ? 0 : (this.state.speed == 2 ? 1 : (this.state.speed == 4 ? 2 : 3));

    let StatusBarheight = null, haveStatus = false;
    if (Platform.OS === "android") {
      StatusBarheight = StatusBar.currentHeight - 16;
      // -16 因为返回按钮的图片留白太大
      if (StatusBarheight <= 0) {
        haveStatus = true;
        StatusBarheight = 15;
      }
    } else {
      StatusBarheight = StatusBarUtil._getInset("top") - 16;
    }

    let speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
    switch (this.state.speed) {
      case 1:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
        break;
      case 2:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X2;
        break;
      case 4:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X4;
        break;
      case 16:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X16;
        break;
      default:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
        break;
    }
    // 这里是全屏的时候 返回键改这里
    return (
      <LinearGradient colors={['#00000077', '#00000000']} pointerEvents={"box-none"}>
        <View style={{ width: "100%", paddingHorizontal: this.displayCutoutTop, display: "flex", flexDirection: "row" }}>
          <View style={{ flexGrow: 1, display: "flex", flexDirection: "row", justifyContent: "space-between", paddingLeft: 25, paddingRight: 15, paddingTop: Host.isPad ? 35 : 0 }}>
            <ImageButton
              source={iconBack}
              highlightedSource={iconBackPre}
              style={styles.videoControlBarItemImg}
              onPress={() => {
                console.log("back To 竖屏");
                this.toPortrait();// 切换到竖屏
                this._hidePlayToolBarLater(true);
              }}
              accessibilityLabel={DescriptionConstants.rp_22}
            >
            </ImageButton>
          </View>

          <View>

            <View style={{ display: "flex", flexDirection: "row", height: 50, justifyContent: 'flex-end' }}>

              {/* 目前的 - Lottie动画按钮 */}
              <MHLottieAudioToolButton
                style={{ width: 50, height: 50, marginRight: 20 }}
                onPress={() => {
                  this._hidePlayToolBarLater();
                  if (this.state.isMute) {
                    // 默认是这个状态，去开启声音
                    if (this.state.isCalling) {
                      this.isAudioMuteTmp = false;
                    }
                    this._toggleAudio(false);
                  } else {

                    if (this.state.isCalling) {
                      this.isAudioMuteTmp = true;
                    }
                    this._toggleAudio(true);
                  }
                }}

                displayState={this.state.isMute ? MHLottieAudioToolBtnDisplayState.MUTED : MHLottieAudioToolBtnDisplayState.NORMAL}
                landscape={true}

                disabled={this.state.speed > 1}
                accessibilityLabel={this.state.speed <= 1 ? 
                  this.state.isMute ? DescriptionConstants.rp_3 : DescriptionConstants.rp_24 : 
                  DescriptionConstants.rp_3}
              />

              <MHLottieSpeedToolButton
                style={styles.videoControlBarItemImg}

                onPress={() => {
                  this._hidePlayToolBarLater();
                  this.setState({ dialogVisibility: true });
                }}

                displayState={speedDisplayState}
                landscape={true}

                disabled={this.state.isRecording}
                accessible={true}
                accessibilityLabel={DescriptionConstants.rp_25.replace('1',this.state.speed)}
                accessibilityState={{
                  disabled:this.state.isRecording
                }}
                testId={speedDisplayState}
              />

            </View>
          </View>
        </View>

      </LinearGradient>

    );
  }

  _renderPauseView() {
    if (!this.state.showPauseView) {
      return null;
    }

    if (this.state.videoViewWidth == 0 || this.state.videoViewHeight == 0) {
      return null;
    }


    if (this.state.showLoadingView) {
      return null;
    }

    let pauseIcons = [
      {
        source: require("../../Resources/Images/camera_icon_center_pause_nor.png"),
        highlightedSource: require("../../Resources/Images/camera_icon_center_pause_press.png"),
        onPress: () => {
          TrackUtil.reportClickEvent("TimeSlider_PlayPause_ClickNum");

          // todo pause
          if (!this.state.displayCloudList) {
            return;
          }
          this.isUserPause = true;
          this._startPlay(false);
        }
      },
      {
        source: require("../../Resources/Images/camera_icon_center_play_nor.png"),
        highlightedSource: require("../../Resources/Images/camera_icon_center_play_nor.png"),
        onPress: () => {
          TrackUtil.reportClickEvent("TimeSlider_PlayPause_ClickNum");

          if (!this.state.displayCloudList) {
            return;
          }
          if (this.isMobileNetwork) {
            this.isDataUsageWarning = false;
          }
          this._hidePlayToolBarLater();
          this.isUserPause = false;
          this._onResume();
        }
      }
    ];
    let index = this.state.isPlaying ? 0 : 1;

    return (
      <View style={{ position: "absolute", width: 64, height: 64, top: "50%", left: "50%", marginTop: -32, marginLeft: -32 }}
      >
        <ImageButton
          style={{ width: 64, height: 64 }}
          source={pauseIcons[index].source}
          highlightedSource={pauseIcons[index].highlightedSource}
          onPress={pauseIcons[index].onPress}
          accessibilityLabel={this.state.isPlaying ? 
            !this.state.fullScreen ? DescriptionConstants.rp_5 : DescriptionConstants.rp_23 :
            !this.state.fullScreen ? DescriptionConstants.hk_3_6 : DescriptionConstants.yc_16}

        />
      </View>
    );
  }


  _renderVideoView() {
    if (this.state.videoPath == null) {
      return null;// 没有的时候 就这么滴
    }
    return (
      <Video
        ref={(ref) => { this.video = ref; }}
        style={{ position: 'absolute', top: 0, left: 0, bottom: 0, right: 0 }}
        source={{ uri: this.state.videoPath }}
        muted={this.state.isMute}
        paused={!this.state.isPlaying}
        resizeMode="contain"
        onEnd={this.onEnd}
        onLoad={this.onLoad}
        onError={this.onError}
        onBuffer={this.onBuffer}
        playInBackground={false}
        playWhenInactive={false}
        repeat={false}
        onProgress={this.onProgress}
        onSeek={this.onSeek}
        controls={false}
        onPress={this._onVideoClick}
        rate={this.state.speed}
        ignoreSilentSwitch={"ignore"}
      />
    );
  }

  _renderErrorRetryView() {
    if (!this.state.showErrorView) {
      return null;
    }
    let errorDesc = "";
    let errorButton = "";
    if (this.state.displayCloudList) {
      // 播放失败了
      errorDesc = this.state.showEmptyHint ? LocalizedStrings["sdcard_page_desc_empty"] : LocalizedStrings["common_net_error"];
      errorButton = this.state.showEmptyHint ? LocalizedStrings["camera_connect_retry"] : LocalizedStrings["reconnect_button_text"];
    } else {
      errorButton = this.isCloudServer ? LocalizedStrings["eu_click_buy_cloud"] : LocalizedStrings["go_buy_cloud"];
      if (this.isInExpireWindow) { // 過期窗口期內
        errorDesc = this.isCloudServer ? LocalizedStrings["eu_cloud_vip_end"] : LocalizedStrings["cloud_vip_end"];
      } else {
        errorDesc = this.isCloudServer ? LocalizedStrings["eu_cloud_vip_no_open"] : LocalizedStrings["cloud_vip_no_open"];
      }
    }

    let buttonReConnectItem = (
      <View
        style={{
          paddingTop: 6,
          paddingBottom: 6,
          paddingLeft: 17,
          paddingRight: 17,
          backgroundColor: "#249A9F",
          borderRadius: 20,
          marginTop: 10
        }}>
        <Text style={{
          color: "#fff",
          fontSize: kIsCN ? 12 : 10
        }}
        >{errorButton}</Text>
      </View>
    );

    let noNetworkItem = (
      <View style={{ display: "flex", flexDirection: "row" }}>
        <TouchableOpacity
          style={{ display: "flex", alignItems: "center" }}
          onPress={() => {
            // this.setState({ showErrorView: false });
            if (this.state.displayCloudList) {
              this.setState({ showErrorView: false });
              Service.smarthome.reportLog(Device.model, "on error Retry");
              if (!this.isFileReceived) {
                this._initData();//
                return;
              }
              this._startPlay(false);
            } else {
              if (!Device.isOwner) {
                Toast.success("share_user_permission_hint");
                return;
              }
              LogUtil.logOnAll(TAG, "云存储回看点击了购买云存按钮");
              API_LEVEL > 10068 ? Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: 'playback_button' }) : Service.miotcamera.showCloudStorage(true, false);
              CameraConfig.isToUpdateVipStatue = true;
            }
          }}>
          {buttonReConnectItem}
        </TouchableOpacity>

      </View>

    );

    const errIcons = [
      require("../../Resources/Images/icon_connection_failure.png"),
      require("../../Resources/Images/icon_camera_offline.png"),
      require("../../Resources/Images/icon_camera_fail.png")
    ];

    let errIconIndex = 0;
    if (!Device.isOnline) {
      errIconIndex = 1;
    }

    return (
      <View
        style={{ zIndex: 7, position: "absolute", bottom: 0, backgroundColor: "black", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <View
          style={{ display: "flex", alignItems: "center" }}
        >
          <Image
            style={{ width: 54, height: 54 }}
            source={errIcons[errIconIndex]} />
          <Text
            style={{ marginTop: 10, fontSize: kIsCN ? 12 : 10, color: "#999", paddingHorizontal: 10, textAlign: "center", width: kWindowWidth - 60 }}>
            {errorDesc}
          </Text>
          {/* {Device.isOnline ? null : powerOfflineText} */}
        </View>
        {noNetworkItem}
      </View>
    );
    // todo render errorRetryView not
  }


  _renderLoadingView() {
    // todo render loading view 
    if (!this.state.showLoadingView) {
      return null;
    }

    let bgColor = "transparent";
    let loadingViewStyle = {
      zIndex: 0,
      position: "absolute",
      width: "100%",
      height: "100%",
      bottom: 0,
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: bgColor
    };

    return (
      <View
        style={loadingViewStyle}
      >
        <LoadingView
          style={{ width: 54, height: 54 }}
        />
        <Text
          style={{ marginTop: 10, fontSize: kIsCN ? 12 : 10, color: "#ffffff" }}>
          {LocalizedStrings["camera_loading"]}
        </Text>
      </View>
    );
  }


  onBackPress = () => {
    this.props.navigation.goBack();
    this._startPlay(false);// 测试
  }

  _renderVideoControlView() {
    if (this.state.fullScreen) {
      return null;
    }

    let speedIndex = this.state.speed == 1 ? 0 : (this.state.speed == 2 ? 1 : (this.state.speed == 4 ? 2 : 3));


    let speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
    switch (this.state.speed) {
      case 1:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
        break;
      case 2:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X2;
        break;
      case 4:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X4;
        break;
      case 16:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X16;
        break;
      default:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
        break;
    }

    if (this.state.showPlayToolBar) {
      return (

        <LinearGradient pointerEvents={"box-none"} colors={['#00000000', '#00000077']} style={[{ position: "absolute", width: "100%" }, this.state.fullScreen ? { top: 0 } : { bottom: 0 }]}>

          <View style={this.state.fullScreen ? styles.videoControlBarFull : styles.videoControlBar}>
            {/* 目前的 - Lottie动画按钮 */}
            {
              this.showSnapShot ?
                <View style={styles.videoControlBarItem}>

                  <MHLottieSnapToolButton
                    style={styles.videoControlBarItemImg}
                    onPress={() => {
                      TrackUtil.reportClickEvent("TimeSlider_ScreenShot_ClickNum");
                      this._startSnapshot();
                      this._hidePlayToolBarLater();
                    }}
                    displayState={MHLottieSnapToolBtnDisplayState.NORMAL}
                    landscape={this.state.fullScreen}
                    accessibilityLabel={this.state.speed <= 1 ? DescriptionConstants.rp_1 : DescriptionConstants.rp_1_1}

                  />
                </View>

                : null
            }

            <View style={styles.videoControlBarItem}>
              <MHLottieSpeedToolButton
                style={styles.videoControlBarItemImg}

                onPress={() => {
                  TrackUtil.reportClickEvent("TimeSlider_PlaySpeed_ClickNum");
                  this.setState({ dialogVisibility: true });
                  this._hidePlayToolBarLater();
                }}
                displayState={speedDisplayState}
                landscape={false}

                disabled={this.state.isRecording}
                accessible={true}
                accessibilityLabel={!this.state.isRecording ? DescriptionConstants.rp_2.replace('1',this.state.speed) : DescriptionConstants.rp_2_1}
                testId={
                  speedDisplayState
                }
              />
            </View>

            <View style={styles.videoControlBarItem}>

              <MHLottieAudioToolButton
                style={styles.videoControlBarItemImg}

                onPress={() => {
                  this._hidePlayToolBarLater();
                  if (this.state.isMute) {

                    // 默认是这个状态，去开启声音
                    if (this.state.isCalling) {
                      this.isAudioMuteTmp = false;
                    }
                    this._toggleAudio(false);
                  } else {

                    if (this.state.isCalling) {
                      this.isAudioMuteTmp = true;
                    }
                    this._toggleAudio(true);
                  }
                }}

                displayState={this.state.isMute ? MHLottieAudioToolBtnDisplayState.MUTED : MHLottieAudioToolBtnDisplayState.NORMAL}
                landscape={this.state.fullScreen}
                disabled={this.state.speed > 1}
                accessibilityLabel={this.state.isMute ? DescriptionConstants.rp_3 : DescriptionConstants.rp_24}

              />
            </View>

            <View style={styles.videoControlBarItem}>

              <MHLottieFullScreenToolButton
                style={styles.videoControlBarItemImg}

                onPress={() => {
                  this._hidePlayToolBarLater();
                  if (!this.state.fullScreen) {
                    this.setState({
                      isWhiteVideoBackground: false
                    });
                    this.toLandscape();
                  } else {
                    this.setState({
                      isWhiteVideoBackground: true
                    });
                    this.toPortrait();
                  }
                }}

                displayState={MHLottieFullScreenToolBtnDisplayState.NORMAL}
                accessibilityLabel={DescriptionConstants.rp_4}

              />
            </View>


          </View>
        </LinearGradient>
      );

    } else {
      return (null);
    }
  }

  _renderSnapshotView() {
    if (!this.state.screenshotVisiblity) {
      return null;
    }

    let recordItem = (
      <View style={{
        display: "flex",
        flexDirection: "row",
        bottom: 0,
        position: "absolute",
        alignItems: "center"
      }}>
        <Image style={{ width: 12, height: 12, marginLeft: 10 }} source={require("../../Resources/Images/icon_snapshot_camera_play.png")}></Image>
        <Text style={{ fontSize: kIsCN ? 12 : 10, fontWeight: "bold", color: "#ffffff", marginLeft: 5 }}>{this.lastRecordTime}</Text>
      </View>
    );

    let sWidth = 90;
    let sHeight = 55;
    let sPadding = 20;
    let leftPading = 15;
    if (this.state.fullScreen) {
      sPadding = 90;
      leftPading = leftPading + StatusBarUtil._getInset("top");

    } else {
    }

    let containerStyle;

    containerStyle = {
      position: "absolute",
      left: leftPading,
      top: sPadding,
      width: sWidth,
      height: sHeight,
      borderRadius: 4,
      borderWidth: 1.5,
      borderColor: "xm#ffffff",
      zIndex: 4
    };

    if (this.state.fullScreen) {
      if (Platform.OS == "ios" && isIphoneX()) {
        containerStyle.left = 60;
      }

      if (Host.isPad) {
        containerStyle.top = "50%";
        containerStyle.marginTop = -1 * sHeight / 2;
      }

    }

    return (
      <View style={containerStyle}>
        <ImageButton
          style={{ width: "100%", height: "100%", borderRadius: 4 }}
          source={(this.state.screenshotPath == "") ? null : ({ uri: `file://${Host.file.storageBasePath}/${this.state.screenshotPath}` })}
          fadeDuration={0}
          accessible={true}
          accessibilityLabel={!this.state.fullScreen ? DescriptionConstants.rp_42 : DescriptionConstants.rp_46}
          onPress={() => {
            if (!this.canStepOut()) {
              return;
            }
            clearTimeout(this.snapshotTimeout);
            this.setState({ screenshotVisiblity: false, screenshotPath: "", isWhiteVideoBackground: true });// 点击后就消失。
            if (this.isForVideoSnapshot) {
              console.log("点击了缩略图，跳转到视频页面");
              this.showLastVideo();
              // this.props.navigation.navigate("AlbumVideoViewPage");
            } else {
              console.log("点击了缩略图，跳转到图片页面");
              this.showLastImage();
              // this.props.navigation.navigate("AlbumPhotoViewPage");
            }

            this.isForVideoSnapshot = false;
            // todo jump to album activity
          }}
        />
        {this.isForVideoSnapshot ? recordItem : null}

      </View>
    );
  }


  showLastImage() {
    StackNavigationInstance.jumpToStackNavigationPage("AlbumPhotoViewPage", { preOri: this.state.fullScreen ? "landscape" : "portrait" });
  }

  showLastVideo() {
    StackNavigationInstance.jumpToStackNavigationPage("AlbumVideoViewPage", { preOri: this.state.fullScreen ? "landscape" : "portrait" });
  }

  canStepOut() {

    if (this.state.isSleep) {
      !this.destroyed && Toast.success("camera_power_off");
      return false;
    }
    return true;
  }

  _renderTimeLineView() {
    // 如果这里设置为null，等切屏回来后，就生成了一个新的view。原先绑定的数据就没了。
    // if (this.state.fullScreen) {
    // return null;
    // }

    let containerStyle;
    if (this.state.fullScreen) {
      let padding = 0;
      if (Platform.OS == "ios") {
        if (isIphoneX()) {
          padding = this.statusBarHeight + 5;
        }
        padding = this.statusBarHeight + 15;
      }
      containerStyle = {
        position: "absolute",
        bottom: 0,
        paddingLeft: padding,
        paddingRight: padding,
        width: "100%"
      };
    } else {
      containerStyle = { width: "100%" };
    }
    const {touchMovement} =this.state;

    return (// scaletimelineView自己处理宽高。
      <View style={[(this.state.fullScreen && !this.state.showPlayToolBar) ? { display: "none" } : containerStyle]}>
        <TimeScaleView2
          ref={(ref) => { this.timelineView = ref; }}
          // onCenterValueChanged={this._onCenterValueChanged}
          onScrolling={this._onScrolling}
          onScrollEnd={this._onCenterValueChanged}
          landscape={this.state.fullScreen}
          isCloud={true}
          eventTypeFlags={this.state.eventTypeFlags}
          isDisabled={this.state.isEmpty ? true : false}
          touchMovement={touchMovement}
        />

      </View>




    );
  }


  // 这里代表时间轴滚动了
  _onCenterValueChanged = (timestamp) => {
    if (!this.state.displayCloudList) {
      return;
    }
    TrackUtil.reportClickEvent("TimeSlider_Drop_ClickNum");

    console.log("滑动结束");
    this.setState({ touchMovement: false });
    this.dateTime.setTime(timestamp);
    // console.log(`timestamp:${timestamp}`);
    console.log(`${this.dateTime.getHours()} ${this.dateTime.getMinutes()} ${this.dateTime.getSeconds()}`);

    if (this.timeIndicatorView == null) {
      return;
    }

    this.timeIndicatorView.setState({ centerTimestamp: 0 });

    this.toStartTime = timestamp;
    if (this.state.isPlaying) {
      this.blockOnProgress = true;// 如果开始播放的过程中，滚动了时间轴，要暂时屏蔽onProgress的回调。
      // 1.5s后再处理progress的回调。避免拖拽失败。
      if (this.blockOnProgressTimeout) {
        clearTimeout(this.blockOnProgressTimeout);
      }
      this.blockOnProgressTimeout = setTimeout(() => {
        this.blockOnProgress = false;
      }, 1500);

      this._startQueryNetwork();
    }
    // 如果没有播放  就不用管了

  }

  _renderBottomSelectView() {
    if (this.state.fullScreen) {
      return;
    }


    return (
      <View style={[this.state.isEmpty && !this.state.displayCloudList ? { display: "none" } : null, { width: "100%", position: "relative", height: 46, marginTop: 25 }]}>
        <TouchableOpacity
          style={{ position: "absolute", bottom: 0, width: "100%", height: 46, marginBottom: 20, marginTop: 8, paddingHorizontal: 24 }}
          onPress={() => {
            TrackUtil.reportClickEvent("TimeSlider_AllVedio_ClickNum");
            this._onPressSeeAllVideo();
          }}
        >
          <View
            style={{ width: "100%", height: "100%", backgroundColor: isDark ? "#474747" : "#F5F5F5", borderRadius: 23, display: "flex", alignItems: "center", justifyContent: "center" }}
          >
            <Text
              style={{ color: "#4C4C4C", fontSize: kIsCN ? 16 : 14, fontWeight: 'bold' , marginHorizontal: 40, textAlignVertical: "center", textAlign: 'center' }}
            >
              {LocalizedStrings["all_playback_video"] + " "}
            </Text>
          </View>

        </TouchableOpacity>

      </View>
    );
  }

  _orientationListener = (orientation) => {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    if (!this.isPageForeGround || !this.isPluginForeGround || this.isParentPageBackground || !this.isAppForeground) {
      return;
    }
    console.log(TAG, `device orientation changed :${orientation} want ${this.mOri}`);
    if ((Platform.OS == "ios" || (Host.isAndroid && Host.isPad)) && ((this.mOri === 'LANDSCAPE' && this.state.fullScreen) || (this.mOri === 'PORTRAIT' && !this.state.fullScreen))) {
      return;
    }
    if (this.mOri === orientation) {
      if (orientation === 'LANDSCAPE') {
        this._setNavigation(true);
        if (Host.isPad) {
          Service.miotcamera.enterFullscreenForPad(true);
        }
      } else {
        StatusBar.setHidden(false);
        this._setNavigation(false);
        if (Host.isPad) {
          Service.miotcamera.enterFullscreenForPad(false);
        }
      }
    } else {
      // ios need restore for next lock
      this.restoreOri();
    }
  };

  _setNavigation(isFull) {
    this.props.navigation.setParams({ isFullScreen: isFull });
    this.setState({ fullScreen: isFull });
  }

  componentDidMount() {
    TrackUtil.reportClickEvent("TimeSlider_CloudStorage_Num");
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
    DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');

    this.restoreOri();
    Orientation.addOrientationListener(this._orientationListener);

    StorageKeys.IS_DATA_USAGEE_WARNING.then((res) => { // 是否使用流量保护  这个页面只需要进来一次就行了
      if (typeof (res) === "string" || res == null) {
        StorageKeys.IS_DATA_USAGEE_WARNING = false;
        this.isDataUsageWarning = false;
      } else {
        this.isDataUsageWarning = res;
      }

    }).catch((error) => {
      console.log(error);
      StorageKeys.IS_DATA_USAGEE_WARNING = false;
      this.isDataUsageWarning = false;
    });
    StorageKeys.IS_VIP_STATUS.then((res) => {
      this.isVip = res;
      StorageKeys.IN_CLOSE_WINDOW.then((res) => {
        this.isInExpireWindow = res;
        this.refreshContents();
      });
    });
    if (Platform.OS == "ios") {
      this.statusBarHeight = getStatusBarHeight();
    }

    this.showSnapShot = Util.isShowSnapShop();
  }

  refreshContents() {
  
    this.setState(() => {
      return { displayCloudList: this.isVip || this.isInExpireWindow }
    }, () => {
      this._initData();
    });
  }


  _initData() {
    if (this.state.displayCloudList) {
      this.setState({ showLoadingView: true });
    } else {
      this.setState({ isPlaying: false, showLoadingView: false, showErrorView: true, showPlayToolBar: false });// here hide 
      return;
    }
    CloudVideoUtil.setCloudFilesReceivedCallback(this._bindFilesHandler);// 收到了数据  通知刷新
    CloudVideoUtil.fetchCloudVideoDataSerials(Device.deviceID, Device.model, this.state.displayCloudList);
  }

  _startSnapshot() {
    if (Platform.OS === "android") {
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this._realStartSnapshot(false);
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
          } else {
            !this.destroyed && Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
          !this.destroyed && Toast.success("action_failed");
        });
    } else {
      // no ios's photos const use hardcode
      System.permission.request("photos").then((res) => {
        this._realStartSnapshot(false);
      }).catch((error) => {
        this.setState({ showPermissionDialog: true, permissionRequestState: 0 });

      });
    }
  }

  _realStartSnapshot(isFromVideo) {
    AlbumHelper.reactNativeSnapShot(this.video)
      .then((path) => {
        console.log(path);
        this.isForVideoSnapshot = isFromVideo;
        this.setState({ screenshotVisiblity: true, screenshotPath: path });// show snapshotview
        this.snapshotTimeout = setTimeout(() => {
          this.isForVideoSnapshot = false;
          this.setState({ screenshotVisiblity: false, screenshotPath: null });
        }, 3000);
        // 文件路径。
      })
      .catch((error) => {
        console.log(JSON.stringify(error));
        !this.destroyed && Toast.success("action_failed");
      });
  }

  _toggleAudio(isMute, changeUnitMute = true) {
    //false
    if (!isMute) {
      TrackUtil.reportClickEvent("TimeSlider_OpenVolume_ClickNum");
    }
    console.log('_toggleAudio isMute', isMute);// isisMute true 静音/ isisMute false 开启声音
    if ( isMute ) {
      this.setState({ isMute: true });
      if (changeUnitMute) {
        CameraConfig.setUnitMute(true);
      }
      return;
    }
    if (this.state.speed > 1) {
      console.log('倍速模式下 进来了吗 this.state.speed', this.state.speed);
      this.setState({ isMute: true });
      return;// 倍速模式下 不要播放声音
    }
    this.setState({ isMute: false });
    if (changeUnitMute) {
      CameraConfig.setUnitMute(false);
    }
  }

  _startQueryNetwork() {
    CameraPlayer.getInstance().queryShouldPauseOn4G()
      .then(({ state, pauseOnCellular }) => {
        if (state === "CELLULAR") {
          this.isMobileNetwork = true;
          this.networkState = 1;
        } else {
          if (state === "NONE" || state === "UNKNOWN") {
            this.networkState = 0;
          } else {
            this.networkState = 2;
          }
          this.isMobileNetwork = false;
        }
        if (state === "CELLULAR" && pauseOnCellular && this.isDataUsageWarning) { // 普通网络 && 数据流量提醒
          if (this.state.isPlaying) {
            !this.destroyed && Toast.success("nowifi_pause");
          }
          this._startPlay(false);
          return;
        }
        if (state === "NONE" || state === "UNKNOWN") {
          this._networkChangeHandler(0);
          return;
        }

        if (!this.isFileReceived) {
          this.fetchVipStatus();//
          return;
        }
        // 其他网络条件 走连接的步骤吧
        this._startPlay(true);// 开始连接
      })
      .catch(() => { // 获取网络状态失败 也直接走开始连接的流程
        this._startPlay(true);// 开始连接
      });
  }

  // to be continued
  _startPlay(isPlay) {
    // handle流量保护。

    if (isPlay) {
      let selectedItem = null;
      let offset = 0;
      let lastestItem = CloudVideoUtil.getLastestVideo(this.toStartTime);
      if (lastestItem == null) { // 压根都没有最后一条数据；
        return;
      }
      if (lastestItem.endTime < this.toStartTime) {
        selectedItem = lastestItem;
        if (lastestItem.endTime - lastestItem.startTime > 20000) {
          offset = (lastestItem.endTime - lastestItem.startTime - 20000) / 1000;
        } else {
          offset = 0;
        }
      } else {
        selectedItem = CloudVideoUtil.searchNeareastVideoItem(this.toStartTime);
        if (selectedItem == null) {
          return;
        }
        if (selectedItem.startTime > this.toStartTime) {
          offset = 0;
        } else {
          offset = (this.toStartTime - selectedItem.startTime) / 1000;
        }
      }
      if (!this.state.showLoadingView) {
        this.setState({ showLoadingView: true });
      }
      if (this.videoItem != null && this.videoItem.fileId == selectedItem.fileId) {
        console.log(`state:${JSON.stringify(this.state)} state.isPlaying:${this.state.isPlaying} isMute:${CameraConfig.getUnitMute()}`);
        this._toggleAudio(this.state.speed > 1 || CameraConfig.getUnitMute());
        if (this.state.isPlaying) {
          this._toggleAudio(this.state.speed > 1 || CameraConfig.getUnitMute());
          !this.destroyed && this.video && this.video.seek(offset);
          return;
        } else {
          this._toggleAudio(this.state.speed > 1 || CameraConfig.getUnitMute());
          this.setState({ isPlaying: true, showPlayToolBar: true, showPauseView: true, showLoadingView:false });
          !this.destroyed && this.video && this.video.seek(offset);
          return;
        }
      }
      this.videoItem = selectedItem;
      this.offset = offset;

      let fileId = selectedItem.fileId;
      Service.miotcamera.getVideoFileUrl(fileId, false)
        .then((url) => {
          this.setState(() => { return { showErrorView: false, videoPath: url, isPlaying: true, showPlayToolBar: true, showPauseView: false }; }, () => {
            this._toggleAudio(this.state.speed > 1 || CameraConfig.getUnitMute());
            // this.updateSpeed(this.state.speed);
          });
          this._hidePlayToolBarLater();

        })
        .catch((error) => {
          !this.destroyed && Toast.fail("action_failed", error);
        });
    } else {
      if (this.state.showErrorView) {
        return;
      }
      // stop播放
      this.setState({ isPlaying: false, showPauseView: true, showPlayToolBar: true, showLoadingView: false });
      clearTimeout(this.showPlayToolBarTimer);
    }
  }

  changeEventType(eventType) {
    TrackUtil.reportClickEvent("TimeSlider_Motion_ClickNum");
    let eventTypeFlag = this.state.eventTypeFlags;
    let temp = eventTypeFlag & eventType;
    if (temp !== 0) { // 已经有了这个值  取反
      eventTypeFlag = eventTypeFlag & ~eventType;
    } else {
      eventTypeFlag = eventTypeFlag | eventType;
    }
    this.setState({ eventTypeFlags: eventTypeFlag });
  }

  updateSpeed(position) {
    let speed = 0;
    switch (position) {
      case 0:
        speed = 1;
        break;
      case 1:
        speed = 2;
        break;
      default:
        speed = 0;
        break;
    }
    this.selectedIndexArray = [position];
    this.setState(() =>{return { speed: speed }}, () => {
      if (speed > 1) {
        this._toggleAudio(true, false);
      } else {
        this._toggleAudio(CameraConfig.getUnitMute(), false);
      }
    });
    
  }

  // todo 需要使用一个view盖在videoview上。
  _onVideoClick() {

    if (!this.state.displayCloudList) {
      return;// 不是vip 直接返回，不应该显示这些页面。
    }

    this.setState((state) => {
      return {
        showPlayToolBar: !this.state.showPlayToolBar,
        showPauseView: !this.state.showPlayToolBar
      };
    }, () => {
      this._hidePlayToolBarLater();
    });

    console.log("click video view");
  }

  // android返回键处理
  onBackHandler = () => {
    if (!this.isPageForeGround) {
      return false;
    }
    if (this.state.fullScreen) {
      this.toPortrait();
      this._hidePlayToolBarLater(true);
      return true;
    }


    this._startPlay(false);
    return false;// 不接管
  }

  _onPressSeeAllVideo() {
    if (this.state.displayCloudList) { // inexpireWindow== closeWindow= true 代表已经彻底过期了。
      if (CameraConfig.shouldDisplayNewStorageManage(Device.model)) {
        StackNavigationInstance.jumpToStackNavigationPage("AllStorage", { initPageIndex: 0, vip: this.isVip, isSupportCloud: CameraConfig.isSupportCloud(Device.model) });
      } else {
        LogUtil.logOnAll(TAG, "云存储回看点击了全部视频按钮跳云存列表");
        Service.miotcamera.showCloudStorage(true, false);// 这里负责跳转， 跳购买页面还是普通页面，某种程度来说，并不对。
        CameraConfig.isToUpdateVipStatue = true;
      }
    } else {

      // here nothing 
    }
  }

  _onScrolling = (timestamp) => {
    console.log("滑动中");
    this.dateTime.setTime(timestamp);
    // console.log(`${this.dateTime.getHours()} ${this.dateTime.getMinutes()} ${this.dateTime.getSeconds()}`);
    if (this.timeIndicatorView == null) {
      console.log('');
      return;
    }
    this.timeIndicatorView.setState({ centerTimestamp: timestamp });
  }

  _bindFilesHandler = (status) => { // 收到文件列表的回调
    if (CLOUD_VIDEO_STATUS.FAIL == status || CLOUD_VIDEO_STATUS.EMPTY == status) {
      this.isFileReceived = false;
      // 渲染 common_net_error；
      CameraPlayer.getInstance().queryShouldPauseOn4G()
        .then(({ state, pauseOnCellular }) => {
          if (state === "NONE" || state === "UNKNOWN") {
            this.networkState = 0;
            this.isMobileNetwork = false;
            this.setState({ showErrorView: true, showPlayToolBar: false, showLoadingView: false, showEmptyHint: false });
          } else {
            this.setState({ showErrorView: true, showPlayToolBar: false, showLoadingView: false, showEmptyHint: status == CLOUD_VIDEO_STATUS.EMPTY });
          }
        });
      return;
    }
    this.setState({ showEmptyHint: false});// 避免后面没有刷新
    this.isFileReceived = true;
    this.onGetFiles();
    if (this.state.displayCloudList) {
      return;
    }
    //不是vip，每次回来都要检测一遍是否是vip
  }



  // 收到了 云存的数据。
  onGetFiles() {
    if (this.timelineView == null) {
      return;
    }
    let video = CloudVideoUtil.getLastestVideo();
    if (video == null) {
      console.log("find cloud items failed");
      return;
    }
    this.setState({ isEmpty: false });
    console.log("收到了视频数据");
    this.lastTimeItemEndTime = video.endTime;
    !this.destroyed && this.timelineView && this.timelineView.onReceiveCloudDatas();
    if (!this.isFirstReceiveFiles) {
      return;
    }

    this.isFirstReceiveFiles = false;
    setTimeout(() => {
      let video = CloudVideoUtil.getLastestVideo();
      if (video == null) {
        console.log("find cloud items failed");
        return;
      }
      !this.destroyed && this.timelineView && this.timelineView.scrollToTimestamp(video.startTime);
      this.toStartTime = video.startTime;
      this._startQueryNetwork();
    });
    // here startToPlay

  }

  toSdcardEnd() {
    if (!this.state.displayCloudList) {
      return;
    }
    // todo jump to sdcard file end
    // todo  pauseCamera
    !this.destroyed && this.timelineView && this.timelineView.scrollToTimestamp(this.lastTimeItemEndTime);
    this.videoItem = null;
    this._startPlay(false);
  }


  onProgress = (data) => {
    if (this.videoItem == null) {
      return;
    }
    if (!this.isPageForeGround) {
      return;
    }
    if (this.blockOnProgress) { // 拖动了时间轴，1.5s内不响应onProgress
      return;
    }
    this.progress = data.currentTime;
    if (data.currentTime > this.offset) {
      let currentTime = data.currentTime * 1000;
      let isReachCurrentFileEnd = this.videoItem.startTime + currentTime >= this.videoItem.endTime; //特定视频播放器返回的进度条信息不太对，导致直接跳文件末尾了，这里不这么弄；
      if (isReachCurrentFileEnd) {
        // this.toSdcardEnd();
        // 特定视频不太对，在这里直接跳过去。
        LogUtil.logOnAll("CloudTimeline", "onProgress endReached");
        this.reachCurrentFileEnd();
      } else {
        // console.log("onProgress:", currentTime);
        // scrollToPositionByTimestamp
        this.toStartTime = this.videoItem.startTime + currentTime;
        !this.destroyed && this.timelineView && this.timelineView.scrollToTimestamp(this.toStartTime);// 受到进度提示了。
      }

    } else {
      console.log("on seeking");
    }


  };

  // todo check 原生代码。
  onError = (error) => { // 修改进度条
    console.log("播放失败");
    if (!this.state.displayCloudList) {
      return;
    }
    if (this.networkState == 0) {
      this._networkChangeHandler(0);
      return;// 断网了
    }
    this._startPlay(false);
    this.setState({ showErrorView: true, showPlayToolBar: false });
    // 设置错误文案 以及retry button。
  }

  // todo  check原生代码
  onEnd = () => {
    // this._startPlay(false);//播放结束了

    LogUtil.logOnAll("CloudTimeline", "onEnd");
    this.reachCurrentFileEnd();
  }

  reachCurrentFileEnd() {
    if (!this.state.displayCloudList) {
      return;
    }
    if (this.videoItem != null) {
      console.log(`Video onEnd:${this.videoItem.endTime}`);
      this.toStartTime = this.videoItem.endTime + 1000; // 播放完成后，+1000 让下一次寻找的时候找到下一个。
      let video = CloudVideoUtil.getLastestVideo();
      if (video == null) {
        return;
      }
      if (this.toStartTime > video.endTime) {
        this.toSdcardEnd();
        return;
      }
      this._startQueryNetwork();// 播放下一个。
    }
  }

  onLoad = (info) => {
    LogUtil.logOnAll("CloudTimeline", "onLoad" + info.duration);
    // 获取到duration后，需要刷新UI，避免动态增长的云存视频，由于本地没有刷新导致的问题
    let duration = info.duration;// seconds
    this.duration = duration;
    // this.updateTimeStr();
    // this.setState({ showLoadingView: false, showPauseView: true });// 移除loading
    // if (this.offset != null && this.offset >= 0) {
    //   !this.destroyed && this.video && this.video.seek(this.offset);
    // }


    // android端需要先暂停播放，再开始播放
    if (Platform.OS == "android") {
      this.setState(() => {
        return { isPlaying: false };
      }, () => {
        this.setState(() => { return { isPlaying: true }; }, () => {
          if (this.offset == null || this.offset == 0) {
            this.setState({ showLoadingView: false, showPauseView: this.state.showPlayToolBar });// 移除loading
            // 不需要seek
          } else {
            LogUtil.logOnAll("CloudTimeline", "seek:" + this.offset);
            !this.destroyed && this.video && this.video.seek(this.offset);
          }
        });
      });
    } else {
      if (this.offset == null || this.offset == 0) {
        this.setState({ showLoadingView: false, showPauseView: this.state.showPlayToolBar });// 移除loading
        // 不需要seek
      } else {
        !this.destroyed && this.video && this.video.seek(this.offset);
      }
    }
  }

  onBuffer = (info) => {
    console.log(info);
  }

  // 拖拽完毕； 这里要移除loading
  onSeek = (info) => {
    this.setState({
      showLoadingView: false, showPauseView: this.state.showPlayToolBar
    });

  }


  _renderResolutionDialog() {
    return (
      <ChoiceDialog
        modalStyle={{ marginLeft: this.state.fullScreen ? 100 : 0, width: this.state.fullScreen ? (this._getWindowPortraitHeight() - 100 * 2) : "100%" }}

        visible={this.state.dialogVisibility}
        title={LocalizedStrings["ptzc_play_back_speed_selection_title"]}
        options={[
          { "title": "x1", accessibilityLabel: DescriptionConstants.rp_13 },
          { "title": "x2", accessibilityLabel: DescriptionConstants.rp_13.replace('1', 2) }
        ]}
        selectedIndexArray={this.selectedIndexArray}
        onDismiss={(_) => this.setState({ dialogVisibility: false })}
        onSelect={(result) => {
          this.selectedIndexArray = result;
          this.updateSpeed(result[0]);
          this.setState({ dialogVisibility: false });
        }}
      />
    );
  }

  _getWindowPortraitHeight() {
    let winWidth = Dimensions.get('window').width;
    let winHeight = Dimensions.get('window').height;
    let width = Math.min(winWidth, winHeight);
    let height = Math.max(winWidth, winHeight);
    return height;
  }

  fetchVipStatus() {
    // todo:需要框架端提供能够指定hostName前缀的api
    API.get("/miot/camera/app/v1/vip/status", "business.smartcamera")
      .then((result) => {
        if (result.code != 0) {
          return;
        }
        let data = result.data;
        if (data == null) {
          return;
        }

        let vip = data["vip"];
        StorageKeys.IS_VIP_STATUS = vip;
        StorageKeys.IN_CLOSE_WINDOW = !data["closeWindow"];
        this.isVip = vip;
        this.isInExpireWindow = !data["closeWindow"];
        this.refreshContents();
      })
      .catch((err) => {
        LogUtil.logOnAll("fetchVipStatus err=", JSON.stringify(err));
      });
  }

}


const styles = StyleSheet.create({

  container: {
    backgroundColor: "#ffffff",
    width: '100%',
    height: '100%',
    display: "flex",
    flexWrap: "nowrap"
  },

  main: {
    display: 'flex',
    flexDirection: 'column',
    flexWrap: 'nowrap',
    backgroundColor: 'white',
    height: "100%"
  },

  videoContainerNormal: {
    backgroundColor: 'black',
    width: kWindowWidth,
    position: "relative"// absolute以第一个非static的父类变量作为定位标准
  },

  videoContainerFull: {
    backgroundColor: 'black',
    width: "100%",
    height: "100%",
    position: "relative"// absolute以第一个非static的父类变量作为定位标准
  },

  videoView: {
    position: "absolute",
    width: "100%",
    height: "100%"
  },

  videoControl: {
    position: "absolute",
    bottom: 0,
    width: "100%",
    height: 80
  },

  videoControlBar: {// 横向
    display: "flex",
    flexWrap: "nowrap",
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-around"
    // backgroundColor: '#FFF1'
  },
  videoControlBarFull: {
    // backgroundColor: '#FFF1',
    display: "flex",
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end"

  },
  videoControlBarItem: {// 内容居中排列
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    flexGrow: 1
  },

  videoControlBarItemImg: {
    width: 50,
    height: 50
  },

  landscapeCallViewLayout: {
    width: "100%",
    paddingBottom: 10,
    paddingTop: 20,
    position: "absolute",
    bottom: 0
  },
  landscapeCallViewLayoutImg: {
    display: "flex",
    margin: "auto",
    width: "100%",
    flexDirection: "row",
    justifyContent: "center"
    // textAlign:"center"
  },

  callViewLayout: {
    flexGrow: 1,
    width: "100%",
    flexDirection: "column",
    display: "flex",
    alignItems: "center",
    justifyContent: "center"
  },

  bottomLayout: {
    display: "flex",
    width: "100%",
    height: 60,
    flexDirection: "row",
    flexWrap: 'nowrap'
  },

  bottomLayoutItem: {
    flexGrow: 1,
    height: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center"
  },

  whiteText: {
    fontSize: kIsCN ? 10 : 8,
    textAlign: "center",
    padding: 4,
    color: "#ffffff",
    borderColor: "#FFFFFFCC",
    borderRadius: 3,
    borderWidth: 1
  },
  snapShot: {
    position: "absolute",
    bottom: 40,
    left: 5,
    margin: 6,
    width: 100,
    height: 66,
    borderRadius: 5
  },
  snapShotFull: {
    position: "absolute",
    bottom: 84,
    left: 35,
    margin: 6,
    width: 100,
    height: 66,
    borderRadius: 5
  }
});
