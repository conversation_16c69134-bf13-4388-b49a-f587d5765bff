import Host from 'miot/Host';
import React, { Component } from 'react';
import { Image, Dimensions, PermissionsAndroid, StyleSheet, Text, TouchableOpacity, View, Button, ScrollView } from 'react-native';
import Camera, { RNCamera } from 'react-native-camera';
import Canvas from 'react-native-canvas';
import NavigationBar from "miot/ui/NavigationBar";
import { Platform } from 'react-native';
import { Package, Device, Service, PackageEvent, System, DarkMode } from 'miot';
import { RkButton } from 'react-native-ui-kitten';
import Util from "../util2/Util";
import Toast from '../components/Toast';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import LogUtil from '../util/LogUtil';
import Orientation from 'react-native-orientation';

let containerWidth = Dimensions.get("window").width
let containerHeight = Dimensions.get("window").height

export default class FaceCamera extends Component {
  constructor(props, context) {
    super(props, context);
    this.state = {
      aiPush: false,
      areaPush: false,
      babyCrySwitch: false,
      babyPush: false,
      isVip: false,
      facePush: false,
      faceSwitch: false,
      pedestrianDetectionPushSwitch: false,
      uploadUri: '',
      canvasFinish: false
    };


  }

  state = {
    displayVideoView: false,
    cameraType: RNCamera.Constants.Type.front
  }

  componentDidMount() {
    this.props.navigation.setParams({
      // show:true
      title: '',
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.CLOSE,
          onPress: () => {
            if (this.state.uploadUri) {
              // this.camera.pausePreview();
              this.setState({
                canvasFinish:false,
                uploadUri: ''
              })
            }
            else {
              this.setState({
                displayVideoView:false
              })
              this.props.navigation.goBack()
            }

          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      if (!this.isPageForeGround) { // app进入前台，ios/android都会调用。对android，从native页面返回也会调用这个页面
        return;
      }
      this.isPluginForeGround = true;// rnactivity调用了onresume
      this.checkPreConditions();
    });
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.checkPreConditions();
      }
    );
    this.didBlurListener = this.props.navigation.addListener(
      'didBlur',
      () => {
        this.setState({
          displayVideoView: false
        });
      }
    );

    Orientation.lockToPortrait();
  }
  componentWillUnmount() {
    this.didFocusListener.remove()
    this.didBlurListener.remove()
    this.didResumeListener.remove()
    if (Host.isPad && Host.isAndroid) {
      Orientation.unlockAllOrientations();
    }
  }

  checkPreConditions() {
    this.checkCameraPermission()
      .then(() => {
        if (this.props.navigation.isFocused()) {
          this.setState({ displayVideoView: true })
          // setTimeout(() => {
          //   this.setState({
          //     cameraType: RNCamera.Constants.Type.front
          //   })
          // }, 10);
        } else {

        }

      })
      .catch((err) => {
        console.log('err', err)
        Toast.fail("auth_fail", err)
      });

  }

  checkCameraPermission() {
    return new Promise((resolve, reject) => {
      if (Platform.OS == "android") {

        PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.CAMERA)
          .then((result) => {
            if (result === PermissionsAndroid.RESULTS.GRANTED) {
              resolve();
            }
            else if (result === PermissionsAndroid.RESULTS.REJECTED) {
              Toast.fail('please_open_camera')
              reject();////
            }
          })
          .catch((err) => {
            reject();
          })
      } else {
        System.permission.request("camera").then((res) => {
          // alert(`requestPermission,result:${ res }`);
          if (res) {
            resolve();//xxxx
          }
          else {
            Toast.fail('auth_fail')
          }

        }).catch((error) => {
          // alert(`requestPermission,error:${ JSON.parse(error) }`);
          reject();
        });
      }
    });
  }



  handleCanvas = (canvas) => {
    console.log('containerWidth', containerWidth, Host.isPad)

    if (canvas == null) {
      return;
    }
    const ctx = canvas.getContext('2d');
    canvas.width = containerWidth;
    canvas.height = containerWidth //这里的高度要和下面的那个对应
    ctx.fillStyle = "dark" == DarkMode.getColorScheme() ? "xm#000" : "#fff";
    ctx.fillRect(0, 0, containerWidth, containerWidth);
    ctx.fillStyle = "dark" == DarkMode.getColorScheme() ? "xm#000" : "#fff";
    ctx.arc(containerWidth / 2, containerWidth / 2, (containerWidth / 2) - 26, 0, 2 * Math.PI);//x是中间，y是200
    ctx.globalCompositeOperation = 'xor';
    ctx.fill();
    setTimeout(() => {
      this.setState({
        canvasFinish: true
      })
    }, 500)


  }
  takePicture = async function () {
    //这里等待
    // if (Host.isIOS) {
    //   if (!await Camera.checkDeviceAuthorizationStatus()) {
    //     alert('相机权限未开启')
    //     return
    //   }
    // }
    if (Host.isAndroid) {
      if (!await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.CAMERA)) {
        let str = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_camera"])
        console.log(str, 'str')
        Toast._showToast(str)
        return
      } else {
        console.log('相机权限已开启');
      }
    }
    if (this.camera) {
      const options = {
        quality: 0.5,
        mirrorImage: true,
        doNotSave: false,
        pauseAfterCapture: true,
        fixOrientation: true
        // writeExif:true,
        // path:'/手机存储'
      }

      const data = await this.camera.takePictureAsync(options);
      // console.log(data, 'data')
      let source = { uri: data.uri };
      //这里是开始预览
      this.camera.resumePreview();
      this.setState({
        uploadUri: source
      })
      // console.log(this.state.uploadUri);
    }


  };
  _uploadFaceimg() {
    // Toast.show('人脸识别中')
    Toast.success("face_recognitioning");
    let path = this.state.uploadUri.uri.slice(7)
    console.log('path', path, this.state.uploadUri.uri)

    //'61551016841576832'
    //这里是从detail页面过来判断一下

    //这里弹一个请等待
    Service.miotcamera.uploadImageToCameraServer(path).then((result) => {
      let res = JSON.parse(result)
      LogUtil.logOnAll("FaceCamera", "uploadImageToCameraServer success" + JSON.stringify(res));

      let data = res.data;
      if (res.result == "ok" && data != null && data.faceInfoMetas != null && data.faceInfoMetas[0] != null) {
        console.log('data', data.faceInfoMetas[0].faceId)

        let state = this.props.navigation.state;
        if (state.params.figureId) {//如果是从人脸detail页面过来的
          let cmd = Util.addFaceToExisting(state.params.figureId, data.faceInfoMetas[0].faceId);
          if (cmd) {
            cmd
              .then(() => {
                this.props.navigation.goBack()
              })
              .catch((aErr) => {
                LogUtil.logOnAll("FaceCmaera", "addFaceToExisting failed" + JSON.stringify(aErr));

                this.setState({ commentDlg: false });
                let errCode = aErr.code;
                // 400302 人物上限
                let errMap = { 400302: "figure_max_tips", 400305: "face_max_tips" };
                let err = errMap[errCode] || "face_recognition_fail_tips";
                Toast.fail(err, err, true);
                console.log(this.tag, "comment failed", aErr);
                this.setState({
                  uploadUri: ""
                })
              });
          } else {
            // 上传人脸成功，添加人脸出错
            Toast.fail("face_recognition_fail_tips")
            this.setState({
              uploadUri: ""
            })
          }
        } else {
          this.props.navigation.goBack();
          // 上传并识别成功人脸，需要通知到外面
          this.props.navigation.goBack();
          state.params.callback(
            {
              faceId: data.faceInfoMetas[0].faceId,
              faceUrl: this.state.uploadUri
            }
          );
        }
      

      } else {
        Toast.fail("face_recognition_fail_tips")
        // this.props.navigation.goBack();

        if (this.state.uploadUri) {
          // this.camera.pausePreview();
          this.setState({
            canvasFinish:false,
            uploadUri: ''
          })
        }
        else {
          this.setState({
            displayVideoView:false
          })
          this.props.navigation.goBack()
        }
      }

    }).catch((err) => {
      LogUtil.logOnAll("FaceCmaera", "uploadImageToCameraServer failed" + JSON.stringify(err));

      //这里设置错误类型
      let errCode = err.code;
      // 501103 系统错误
      let errMap = { 400302: "cloud_figure_limit", 400305: "cloud_face_limit" };
      // let err = errMap[errCode] || "action_fail";
      Toast.fail("face_recognition_fail_tips", err);

      this.setState({
        uploadUri: ''
      });
      console.log('err1', err);
    });
  }

  render() {
    let isFocused = this.props.navigation.isFocused();//是否聚焦显示了
    let imgWidth = (Math.sqrt(Math.pow(containerWidth - 52, 2))) / Math.sqrt(2)
    if (!this.state.uploadUri) {
      return (
        <View style={styles.container}>
          {/* 上面照相部分 */}
          <View style={{ width: containerWidth - 52, aspectRatio: 3 / 4, }}>
            {
              this.state.displayVideoView && this.state.canvasFinish ?
                <RNCamera
                  ref={(ref) => {
                    this.camera = ref;
                  }}
                  style={styles.preview}
                  type={RNCamera.Constants.Type.front}
                  ratio="4:3"
                  flashMode={RNCamera.Constants.FlashMode.off}
                  cameraViewDimensions={{
                    width: containerWidth - 52,
                    height: (containerWidth - 52) * 4 / 3
                  }}
                  onFacesDetected={null}
                  onGoogleVisionBarcodesDetected={null}
                  onTextRecognized={null}
                  onBarCodeRead={null}
                  captureAudio={false}
                  onCameraReady={async (result) => {//also will fired by changing type or cameraId
                    let supportRatio = await this.camera.getSupportedRatiosAsync()
                    let supportPictureSize = await this.camera.getAvailablePictureSizes();
                    console.log(JSON.stringify(supportRatio), JSON.stringify(supportPictureSize));
                    // ["2:1","3:2","4:3","11:9","16:9"] ["320x240","640x480","800x600","1280x960","1920x1440","2048x1536"]
                    //2592-1940
                  }}
                  onMountError={(error) => {//打开摄像头失败了
                    Toast.fail('camera_connect_error', error)
                    console.log("mount camera error" + JSON.stringify(error));
                  }}
                  onStatusChange={({ cameraStatus }) => {
                    console.log("camera status changed:" + cameraStatus);
                  }}
                  onPictureTaken={() => {
                  }}
                /> :
                null
            }

          </View>

          <View style={{ position: 'absolute', width: containerWidth, height:'100%' }}>
            {/* 这里是扣得圆 */}
            <ScrollView scrollEnabled={Platform.OS == 'ios' && !Host.isPad ? false : true}>
              <Canvas ref={this.handleCanvas} />

              <View style={{ backgroundColor: "dark" == DarkMode.getColorScheme() ? "xm#000" : "#fff", height: containerHeight / 2, alignItems: "center", marginTop: -5 }}>
                <View style={{ flexGrow: 1, marginTop: 20 }}>
                  <Text>{LocalizedStrings["camera_take_photo_tips"]}</Text>
                </View>
                <View style={{ flexGrow: 1 }}>
                  <TouchableOpacity
                    onPress={this.takePicture.bind(this)}
                    style={{ borderColor: '#32BAC0', borderWidth: 2, width: 80, height: 80, borderRadius: 40, justifyContent: 'center', alignItems: "center", marginTop: containerHeight <= 736 ? -60 : 0 }}>
                    <View style={{ width: 70, height: 70, borderRadius: 35, backgroundColor: '#32BAC0' }}>
                    </View>
                  </TouchableOpacity>
                  <Text style={{ textAlign: "center", marginTop: 5, color: "dark" == DarkMode.getColorScheme() ? "xm#fff" : "#000" }}>
                    {LocalizedStrings["takePhoto"]}
                  </Text>
                </View>

              </View>
            </ScrollView>

          </View>
          {/* 四角边框 */}
          <View
            style={{
              position: 'absolute', marginLeft: 0,
              marginTop: containerWidth / 2 - imgWidth / 2 //这里的margintop要根据圆的位置计算一下
            }}>
            <Image
              style={{ width: imgWidth, height: imgWidth }}
              source={require('../../Resources/Images/four-corner.png')}>

            </Image>
          </View>
        </View >
      );
    }
    else {
      return (
        <View style={styles.container}>
          <View style={{ flexGrow: 1, marginTop: 26 }}>
            {/* 预览图片 */}
            <Image
              style={{ width: containerWidth - 52, height: containerWidth - 52, borderRadius: (containerWidth - 52) / 2 }}
              source={this.state.uploadUri}
            />
          </View>


          <View style={{ flexDirection: 'row', bottom: 27 }}
          >
            <RkButton
              style={{ margin: 16, flexGrow: 1, height: 46, borderRadius: 23, backgroundColor: '#32BAC0', display: 'flex', bottom: -20 }}
              onPress={() => { this._uploadFaceimg() }}
              activeOpacity={0.8}
            >
              <Text style={{ color: 'white', fontSize: 16, fontWeight: 'bold', textAlign: 'center' }}>
                {LocalizedStrings["csps_right"]}
              </Text>
            </RkButton>
          </View>


        </View>
      )
    }

  }

}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    // justifyContent:"center"
    // flexDirection: 'column',
    backgroundColor: "dark" == DarkMode.getColorScheme() ? "xm#000" : "#fff",

  },
  preview: {

    flex: 1,
    // justifyContent: 'center',
    // alignItems: 'center',
  },
  capture: {
    flex: 0,
    // backgroundColor:"white",
    borderRadius: 5,
    padding: 15,
    paddingHorizontal: 20,
    alignSelf: 'center',
    margin: 20,
  },
});