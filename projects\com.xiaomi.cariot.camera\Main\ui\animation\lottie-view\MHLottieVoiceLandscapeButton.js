import React from "react";
import { Animated, Platform } from 'react-native';
import MHLottieBaseButton from "./base/MHLottieBaseButton";

/**
 * 创建自己的行为枚举值
 * @type  { enum } 
 */
export const MHLottieVoiceLandscapeBtnDisplayState = {
  NORMAL: "NORMAL",
  CHATTING: "CHATTING"
};

export default class MHLottieVoiceLandscapeButton extends MHLottieBaseButton {

  /**
   * 创建自己的资源文件
   * @type  { object } 
   */
  static JSONFiles = {
    normal: {
      light: require("../lottie-json/btn_voice_landscape.json")
    },
    chatting: {
      light: require("../lottie-json/btn_voice_chatting_landscape.json")
    }
  };

  /**
   * 创建自己的基于行为枚举值的链式动画
   * @param  { enum } displayState
   * @param  { bool } transition
   */
  switchDisplayState(displayState, transition) {

    if (displayState == MHLottieVoiceLandscapeBtnDisplayState.NORMAL) {
      if (transition) {
        this.setState({
          file: MHLottieVoiceLandscapeButton.JSONFiles.normal,
          loop: false
        });
      
        this.progress.setValue(1);
        Animated.timing(
          this.progress,
          {
            toValue: 0,
            duration: 700
          }
        )
          .start(
            
          );
      } else {
        this.setState({
          file: MHLottieVoiceLandscapeButton.JSONFiles.normal,
          loop: false
        });
      }
    } else if (displayState == MHLottieVoiceLandscapeBtnDisplayState.CHATTING) {
      if (transition || Platform.OS == "ios") {
        this.setState({
          file: MHLottieVoiceLandscapeButton.JSONFiles.normal,
          loop: false
        });
        
        this.progress.setValue(0);
        Animated.timing(
          this.progress,
          {
            toValue: 1,
            duration: 700
          }
        )
          .start(
            () => {
              this.setState({
                file: MHLottieVoiceLandscapeButton.JSONFiles.chatting,
                loop: true
              });
              
              this.lottieButton && this.lottieButton.playAnimation();
            }
          );
      } else {
        this.setState({
          file: MHLottieVoiceLandscapeButton.JSONFiles.chatting,
          loop: true
        });
    
        this.lottieButton && this.lottieButton.playAnimation();
      }
    }
  }

  constructor(props) {
    super(props);

    // this.state = {
    //   file: MHLottieVoiceLandscapeButton.JSONFiles.normal,
    // };
  }

  componentWillUnmount() {
    super.componentWillUnmount();
  }

  componentDidMount() {
    super.componentDidMount();
  }

  componentDidUpdate(prevProps) {
    super.componentDidUpdate(prevProps);
  }

}
