'use strict';
import React from 'react';
import { Device, DarkMode } from "miot";
import { ActivityIndicator, ScrollView, StyleSheet, View, Image, Text, SafeAreaView, Platform, Button } from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import NavigationBar from "miot/ui/NavigationBar";
import RPC from "../util/RPC";
import { ListItemWithSwitch, ListItem } from 'miot/ui/ListItem';
import Toast from '../components/Toast';
import LogUtil from '../util/LogUtil';
import LoadingView from "../ui/LoadingView";
export default class ChangeNAS extends React.Component {

  constructor(props, context) {
    super(props, context);
    //就是一进来要获取信息 看看有没有
    this.state = {
      showLoading: true
    };
  }

  render() {
    return (
        <View style={{
          backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : '#fff',
          height: '100%'
        }}>
          <ScrollView
              showsVerticalScrollIndicator={false}
              scrollEventThrottle={1}
              onScroll={this.scrollViewScroll}>
            <View style={{flexDirection: "row", flexWrap: "wrap"}} key={'0'}>
              <Text style={{fontSize: 30, color: "rgba(0, 0, 0, 0.80)", fontWeight: "300",
                position: "relative", marginLeft: 25, marginTop: 3, marginBottom: 23, fontFamily: 'MI-LANTING--GBK1-Light'
              }}>
                {LocalizedStrings['set_nas_storage']}</Text>
            </View>
            {this._renderChangeView()}
          </ScrollView>
          {this._renderLoadingView()}
        </View>
    )
  }

  // 监听列表滚动事件
  scrollViewScroll = (event) => {
    const y = event.nativeEvent.contentOffset.y;
    let flag = y > 28;
    if (this.showTitle == flag) {
      return;
    }
    if (flag) {
      this.showTitle = true;
      this.props.navigation.setParams({ title: LocalizedStrings['set_nas_storage'] });
    } else {
      this.showTitle = false;
      this.props.navigation.setParams({ title: "" });
    }
  };

  componentDidMount() {
    this.props.navigation.setParams({
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => {
            this.props.navigation.goBack()
            // this.props.navigation.push('NASNetworkLocation', { deviceName: this.state.deviceName, NASLocation: this.state.NASLocation })
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        fontWeight: 500
      }
    });
    // //刷新的时候
    this.willFocusSubscription = this.props.navigation.addListener(
      'didFocus', () => {
        this.getStorageTimer = setTimeout(() => {
          this._getStorage()
        }, 1000);
        // console.log('路由改变了',this.props.navigation.state.params)
        // if(!this.props.navigation.state.params.deviceMessage.share.dir){
        //     //这里是从修改跳回来重新获取一下
        //     this._getStorage()
        // }
        // this.setState({
        //     deviceName: deviceMessage.share.name,
        //     NASLocation: deviceMessage.share.dir,
        //     deviceMessage:deviceMessage
        // })
      }
    );
    this.didBlurListener = this.props.navigation.addListener(
      'didBlur',
      () => {
        clearTimeout(this.getStorageTimer);
        LogUtil.logOnAll("ChangeNAS", "didBlurListener==========");
      }
    );
  }
  componentWillUnmount() {
    this.willFocusSubscription.remove()
  }
  _getStorage() {
    RPC.callMethod("nas_get_config", {})
      .then((res) => {
        LogUtil.logOnAll("nas_get_config2-=-=-=-=", JSON.stringify(res));
        this.setState({
          deviceName: res.result.share.name,
          NASLocation: res.result.share.dir,
          deviceMessage: res.result,
          showLoading: false,
          showChangeView: true
        })
      }).catch((err) => {
        LogUtil.logOnAll("changenas", "nas_get_config failed" + JSON.stringify(err));

        this.setState({ showLoading: false });
        this.props.navigation.goBack()
        Toast.fail('c_get_fail', err);
      });

  }
  _renderChangeView() {
    if (this.state.showChangeView) {
      return (
        <View style={styles.container}>
          <View>
            <ListItem
              title={LocalizedStrings['change_nas_storage']}
              value={this.state.deviceName}
              containerStyle={{paddingVertical:5,minHeight:60}}
              key={1}
              showSeparator={false}
              onPress={() => {
                this.props.navigation.push('ChangeNASDinfo', { deviceMessage: this.state.deviceMessage })
              }
              }
              titleStyle={{ fontWeight: 'bold' }}
              titleNumberOfLines={3}
              unlimitedHeightEnable={true}
            />
          </View>
          <View>
            <ListItem
              title={LocalizedStrings['change_nas_location']}
              value={this.state.NASLocation}
              containerStyle={{paddingVertical:5,minHeight:60}}
              key={2}
              showSeparator={false}
              onPress={() => {
                this.props.navigation.navigate('ChangeNASDirectory', { deviceMessage: this.state.deviceMessage })
              }
              }
              titleStyle={{ fontWeight: 'bold' }}
              titleNumberOfLines={3}
            />
          </View>

        </View>
      );
    }

  }
  _renderLoadingView() {
    if (this.state.showLoading) {
      let isDark = DarkMode.getColorScheme() == "dark";
      return (
          <View
              style={{ position:"absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
          >
            <LoadingView
                type={isDark ? LoadingView.TYPE.LIGHT : LoadingView.TYPE.DARK}
                style={{ width: 54, height: 54 }}
            />
            <Text
                style={{ marginTop: 10, fontSize: 12, color: "#000000" }}>
              {LocalizedStrings["camera_loading"]}
            </Text>
          </View>
      );

    }

  }


}

export const styles = StyleSheet.create({
  container: {
    backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : '#fff',
    height: '100%'
  },

});
