import DateFormatter from "../../util2/DateFormater";
import EventLoaderInf, { DldStatus, Order } from "../EventLoaderInf";
import CameraPlayer from "../../util/CameraPlayer";
import SdFileManager from "../../sdcard/util/SdFileManager";
import { Host } from "miot";
import { DeviceEventEmitter } from "react-native";
import dayjs from 'dayjs';
import { MISSConnectState } from "miot/service/miotcamera";
import LogUtil from "../../util/LogUtil";


const TAG = "SdcardEventLoader";
// 保证在 存储管理页面存活 其他地方都不存活即可。
export default class SdcardEventLoader extends EventLoaderInf {
  constructor() {
    super(false, true);
    this.mDate = (new Date()).getTime();
    this.mPlayCfg = { loader: this, player: null };

    this.mSaveL = DeviceEventEmitter.addListener(SdFileManager.sDowningProgress, ({ timestamp, progress }) => {
      if (timestamp != this.downloadingFileId) {
        return;
      }
      this.downloadListener && this.downloadListener.onDldProgress(progress);
    });

    
    // SdFileManager.getInstance().bindDownloadVideoFileListener(this._videoFileReceived);
    // SdFileManager.getInstance().bindOnFileDownloadedListener(this._imageFileReceived);
    // SdFileManager.getInstance().bindReceiveFilesListener(this._fileListReceived);
    this.videoFileResolve = null;
    this.videoFileReject = null;
    this.imageFileReject = null;
    this.imageFileResolve = null;
    this.fileListReject = null;
    this.fileListResolve = null;

    this.downloadingFileId = null;
    this.downloadListener = null;
    this.downloadingItem = null;

    this.conectionListener = CameraPlayer.getInstance().addConnectionListener((state) => {
      if (state.state === MISSConnectState.MISS_Connection_Connected) {
        if (this.downloadingItem != null) {
          this.downloadDirect(this.downloadingItem, this.downloadListener);
          this.downloadingItem = null;// 保证连接发起成功后，只触发一次下载
        }
      } else if (state.state === MISSConnectState.MISS_Connection_Disconnected) { // 重连失败了，告知外面失败
        if (this.downloadingItem != null) {
          this.downloadListener && this.downloadListener.onDldProgress(DldStatus.p2pLost);//
          this.downloadingItem = null;// 保证连接发起失败后，只触发一次通知
          this.downloadListener = null;// 下载失败
        }
      }
    });
  }

  _videoFileReceived = (timestamp, isDownloadSuccess) => {
    if (timestamp != this.downloadingFileId) { // 返回下载成功的视频，跟预期的不一样，就删除这个视频？？？还是保留？？？？
      return;
    }
    // 只有一样 才通知外面 下载成功。
    if (isDownloadSuccess) {
      this.videoFileResolve && this.videoFileResolve(SdFileManager.getSdcardFilePath(timestamp, true));
    } else {
      this.videoFileReject && this.videoFileReject();
    }
    this.videoFileReject = null;
    this.videoFileResolve = null;
    this.downloadListener = null;
    this.downloadingItem = null;
  }

  _imageFileReceived = (timestamp, isDownloadSuccess) => {
    // here should do check timestamp

    if (isDownloadSuccess) {
      this.imageFileResolve && this.imageFileResolve(Host.file.storageBasePath + SdFileManager.getSdcardFilePath(timestamp));
    } else {
      this.imageFileReject && this.imageFileReject();
    }
    this.imageFileReject = null;
    this.imageFileResolve = null;
  }

  _fileListReceived = (status, important = true) => { // 以后拓展需要sdcard列表事件类型怎么办
    // ignore
    console.log("_fileListReceived", status, important, this.fileListResolve == null);
    if (status) {
      let items = this._getExpectedSdcardTabData();
      if (items == null && !important) {
        // 事件数据返回了，列表数据并没有正常返回，忽略
        LogUtil.logOnAll(TAG, "not the video list received");
        return;
      }
      if (this.fileListResolve == null) {
        this.notifyDataChanged(important);// 刷新了数据。
        return;
      }
      // 这里暂时拿sdcard hour && day数据
      this.fileListResolve && this.fileListResolve({ hasMore: false, nextTime: null, items });
    } else {
      this.fileListResolve && this.fileListResolve({ hasMore: false, nextTime: null, items: [] });
    }
    this.fileListResolve = null;
    this.fileListReject = null;
  }

  _getExpectedSdcardTabData() {
    let sdcardDays = SdFileManager.getInstance().getTimeItemDays();
    let items = [];
    if (sdcardDays == null) {
      return null;
    }
    for (let i = 0; i < sdcardDays.length; i++) {
      let sdcardDay = sdcardDays[i];
      if (sdcardDay.timeItemHourList == null) {
        console.log(" get sdcard timeItemDays ERROR:" + JSON.stringify(sdcardDay));
        continue;
      }
      for (let j = 0; j < sdcardDay.timeItemHourList.length; j++) { // 对于所有的sdcard
        let sdcardHour = sdcardDay.timeItemHourList[j];
        let timeInMs = sdcardHour.startTime;
        let subTimestamps = sdcardHour.timeItemList.map((item) => {
          return { startTime: item.startTime, save: item.save };
        });
        // let sdcardMinute = sdcardHour.timeItemList && sdcardHour.timeItemList[0];
        let mapped = {
          createTime: timeInMs,
          startTime: timeInMs,
          eventTime: DateFormatter.instance().format(timeInMs),
          type: "Default", // 暂时全部都是default
          desc: "",
          imgStoreId: timeInMs,
          duration: 0,
          fileId: timeInMs, /* `${itm.url}${i}${j}` */
          isRead: true,
          offset: 0,
          playCfg: this.mPlayCfg,
          mediaType: "sdcard",
          extraData: { tag: sdcardDay.tag, hour: sdcardHour.hour, dayBeginTime: sdcardDay.startTime },
          subTimestamps: subTimestamps
          
        };
        items.push(mapped);
      }
    }
    items.reverse();
    return items;
  }

  _getExpectedSdcardTabDataCar() {
    // let sdcardDays = SdFileManager.getInstance().getTimeItemDays();
    let sdcardDays = SdFileManager.getInstance().getTimeItemContinuous(false);
    let items = [];
    if (sdcardDays == null) {
      return null;
    }
    for (let i = 0; i < sdcardDays.length; i++) {
      let sdcardDay = sdcardDays[i];
      if (sdcardDay.timeItemContinuousList == null) {
        console.log(" get sdcard timeItemDays ERROR:" + JSON.stringify(sdcardDay));
        continue;
      }
      for (let j = 0; j < sdcardDay.timeItemContinuousList.length; j++) { // 对于所有的sdcard
        let sdcardHour = sdcardDay.timeItemContinuousList[j];
        let timeInMs = sdcardHour.startTime;
        let duration = sdcardHour.duration;
        let subTimestamps = sdcardHour.timeItemList.map((item) => {
          return { startTime: item.startTime, save: item.save };
        });
        // let sdcardMinute = sdcardHour.timeItemList && sdcardHour.timeItemList[0];
        let mapped = {
          createTime: timeInMs,
          startTime: timeInMs,
          eventTime: DateFormatter.instance().format(timeInMs),
          type: "Default", // 暂时全部都是default
          desc: "",
          imgStoreId: timeInMs,
          duration: duration / 1000,
          fileId: timeInMs, /* `${itm.url}${i}${j}` */
          isRead: true,
          offset: 0,
          playCfg: this.mPlayCfg,
          mediaType: "sdcard",
          extraData: { tag: sdcardDay.tag, hour: sdcardHour.hour, dayBeginTime: sdcardDay.startTime },
          subTimestamps: subTimestamps

        };
        items.push(mapped);
      }
    }
    items.reverse();
    return items;
  }


  async getAllEvent(aDate, aEvent, aIsMore) {
    // return await this.getEventList(aDate, aEvent, aIsMore);

    let sdcardEvents = await this.getEventList(aDate, aEvent, aIsMore);
    let end = dayjs(aDate).hour(0).minute(0).second(0).add(1, "days").subtract(1, "seconds").valueOf();
    let oneDayEvents = [];
    //逆序的
    let count = 20;
    let finalIndex = 0;
    for (let i = 0; i < sdcardEvents?.items?.length; i++) {
      let sdcardEvent = sdcardEvents.items[i];
      let timestamp = sdcardEvent.createTime;
      if (timestamp <= aDate) {
        oneDayEvents.push(sdcardEvent);
        count--;
      }
      if (count <= 0) {
        finalIndex = i;
        break;
      }
    }
    let hasMore = true;
    let nextTime = undefined;
    // 遍历oneDayEvent 找到从aDate起的20个item，如果遍历到尾巴了，就hasMore == false
    if (oneDayEvents.length < 20) {
      hasMore = false;
      nextTime = undefined;
    } else if (finalIndex < (sdcardEvents?.items?.length - 1)) { // 还没有到最后一个
      nextTime = sdcardEvents.items[finalIndex + 1].createTime;
      hasMore = true;
    } else { // 找到了最后面
      nextTime = undefined;
      hasMore = false;
    }

    let data = {
      hasMore: hasMore,
      items: oneDayEvents,
      nextTime: new Date(nextTime),
      status: sdcardEvents.status
    }



    return data;
  }

  async getAllEventCar(aDate, aEvent, aIsMore) {
    // return await this.getEventList(aDate, aEvent, aIsMore);
    let sdcardEvents = await this.getEventListCar(aDate, aEvent, aIsMore);
    let end = dayjs(aDate).hour(0).minute(0).second(0).add(1, "days").subtract(1, "seconds").valueOf();
    let oneDayEvents = [];
    //逆序的
    let count = 20;
    let finalIndex = 0;
    for (let i = 0; i < sdcardEvents?.items?.length; i++) {
      let sdcardEvent = sdcardEvents.items[i];
      let timestamp = sdcardEvent.createTime;
      if (timestamp <= aDate) {
        oneDayEvents.push(sdcardEvent);
        count--;
      }
      if (count <= 0) {
        finalIndex = i;
        break;
      }
    }
    let hasMore = true;
    let nextTime = undefined;
    // 遍历oneDayEvent 找到从aDate起的20个item，如果遍历到尾巴了，就hasMore == false
    if (oneDayEvents.length < 20) {
      hasMore = false;
      nextTime = undefined;
    } else if (finalIndex < (sdcardEvents?.items?.length - 1)) { // 还没有到最后一个
      nextTime = sdcardEvents.items[finalIndex + 1].createTime;
      hasMore = true;
    } else { // 找到了最后面
      nextTime = undefined;
      hasMore = false;
    }

    let data = {
      hasMore: hasMore,
      items: oneDayEvents,
      nextTime: new Date(nextTime),
      status: sdcardEvents.status
    }



    return data;
  }


  // 获取当前这一天的数据合集。
  async getOneDayAllEvent(aDate, aEvent, aIsMore, aOrder = Order.Desc) {
    let now = Date.now();
    let sdcardEvents = await this.getEventList(aDate, aEvent, aIsMore);
    let start = dayjs(aDate).hour(0).minute(0).second(0).subtract(0, "day").valueOf();
    let end = dayjs(aDate).hour(0).minute(0).second(0).add(1, "days").subtract(1, "seconds").valueOf();
    let oneDayEvents = [];
    for (let i = 0; i < sdcardEvents.items.length; i++) {
      let sdcardEvent = sdcardEvents.items[i];
      let timestamp = sdcardEvent.createTime;
      if (timestamp >= start && timestamp <= end) {
        oneDayEvents.push(sdcardEvent);
      }
    }
    return { hasMore: false, nextTime: null, items: oneDayEvents, status: sdcardEvents.status };
  }

  // sdcard here 忽略aDate。 
  getEventList(aDate, aEvent, aIsMore) {
    console.log(TAG, "getEventList");
    return new Promise((resolve, reject) => {
      LogUtil.logOnAll("SD StorageUI getEventList: isConnected?" + CameraPlayer.getInstance().isConnected() + " lasterItem:" + SdFileManager.getInstance().getLastestItemStartTime());
      if (!CameraPlayer.getInstance().isConnected()) {
        resolve({ hasMore: false, nextTime: null, items: [], status: DldStatus.p2pLost });
        return;
      }
      if (!CameraPlayer.getInstance().isLocalSdcardStatusNormal()) {
        // 卡状态不正常，也直接返回空数据；
        resolve({ hasMore: false, nextTime: null, items: [], status: DldStatus.Err });
        return;
      }
      // 如果sd卡已经有内容了，就不用再请求了
      if (SdFileManager.getInstance().getLastestItemStartTime() != 0) {
        let items = this._getExpectedSdcardTabData();
        // 这里暂时拿sdcard hour && day数据
        resolve({ hasMore: false, nextTime: null, items });
        return;
      }
      this.fileListResolve = resolve;
      this.fileListReject = reject;// 这里接收数据 把数据抛出去。
      this.lastRequestSDListTime = new Date().getTime();
      SdFileManager.getInstance().bindReceiveFilesListener(this._fileListReceived);// 防止SdFileManager被注销，再重建，这里的监听没有新调用。
      SdFileManager.getInstance().startRequestSdcardFilesRegularly();// 发了一个消息出去 这里会多次请求拉取。

      // 事实上部分model需要区分sd卡事件类型，这里需要做一下处理。
    });

  }

  getEventListCar(aDate, aEvent, aIsMore) {
    console.log(TAG, "getEventListCar");
    return new Promise((resolve, reject) => {
      LogUtil.logOnAll("SD StorageUI getEventListCar: isConnected?" + CameraPlayer.getInstance().isConnected() + " lasterItem:" + SdFileManager.getInstance().getLastestItemStartTime());
      if (!CameraPlayer.getInstance().isConnected()) {
        resolve({ hasMore: false, nextTime: null, items: [], status: DldStatus.p2pLost });
        return;
      }
      if (!CameraPlayer.getInstance().isLocalSdcardStatusNormal()) {
        // 卡状态不正常，也直接返回空数据；
        resolve({ hasMore: false, nextTime: null, items: [], status: DldStatus.Err });
        return;
      }
      // 如果sd卡已经有内容了，就不用再请求了
      if (SdFileManager.getInstance().getLastestItemStartTime() != 0) {
        let items = this._getExpectedSdcardTabDataCar();
        // 这里暂时拿sdcard hour && day数据
        resolve({ hasMore: false, nextTime: null, items });
        return;
      }
      this.fileListResolve = resolve;
      this.fileListReject = reject;// 这里接收数据 把数据抛出去。
      this.lastRequestSDListTime = new Date().getTime();
      SdFileManager.getInstance().bindReceiveFilesListener(this._fileListReceived);// 防止SdFileManager被注销，再重建，这里的监听没有新调用。
      SdFileManager.getInstance().startRequestSdcardFilesRegularly();// 发了一个消息出去 这里会多次请求拉取。

      // 事实上部分model需要区分sd卡事件类型，这里需要做一下处理。
    });

  }


  getThumb(aRec) {
    return new Promise((resolve, reject) => {
      if (!CameraPlayer.getInstance().isConnected()) {
        reject("device not connected");
        return;
      }
      this.imageFileResolve = resolve;
      this.imageFileReject = reject;
      SdFileManager.getInstance().bindOnFileDownloadedListener(this._imageFileReceived);// 提前注册
      SdFileManager.getInstance().startDownloadVideoThumbs([aRec.imgStoreId]);
    });
  }

  getSummary() {
    return new Promise((resolve, reject) => {
      CameraPlayer.getInstance().getSdcardStatus(true)
        .then((result) => {
          console.log(TAG, JSON.stringify(result));
          resolve({ type: "sdcard", info: result });
        })
        .catch((error) => {
          resolve({ type: "sdcard", info: error });
        });
    });
    // return Promise.resolve({ type: "local", info: { dldCnt: DldMgr.mDld.list.length } });

  }

  canDownload() {
    return false;
  }

  supportImage() {
    return true;
  }

  download(aRec, aPath, aListener) {


    //下载的时候，如果p2p连接断开了，尝试一次重连吧
    

    // nothing to do
    this.downloadingFileId = aRec.fileId;
    this.downloadListener = aListener;
    this.downloadingItem = aRec;
    this.downloadDirect(aRec, aListener);
  }

  downloadDirect(aRec, aListener) {
    if (!CameraPlayer.getInstance().isConnected()) { // 连接成功后，会再次走到这里的
      // 如果已经断开连接了，尝试重连一次
      LogUtil.logOnAll(TAG, "downloadDirect disconnect");
      CameraPlayer.getInstance().startConnect();
      // 把下载状态通知出去 未连接的情况下
      aListener.onDldProgress(DldStatus.Err);
      return;
    } 
    this.downloadWithPromise(aRec)
      .then(() => {
        aListener.onDldProgress(DldStatus.Complete);
      })
      .catch(() => {
        aListener.onDldProgress(DldStatus.Err);
      });
  }

  cancelDownload(aRec, aListener) {
    if (aRec.fileId == this.downloadingFileId) { // 两个相等
      // 要取消的fileId 跟正在下载的是一样的，才调用接口。
      this.downloadingFileId = null;
      this.videoFileReject = null;
      this.videoFileResolve = null;// 都置空
      this.downloadListener = null;
      this.downloadingItem = null;
      aListener.onDldCancelResult(aRec.fileId, DldStatus.Complete);
    }
  }

  downloadWithPromise(aRec) {
    return new Promise((resolve, reject) => {

      if (CameraPlayer.getInstance().isConnecting()) {// 之前处于连接过程中，又跑来下载了，抛错吧，不允许一个没给通知，又来一个
        reject("device not connected");
        return;
      }
      // 连接成功，才直接走下载的地方

      this.videoFileResolve = resolve;
      this.videoFileReject = reject;
      SdFileManager.getInstance().bindDownloadVideoFileListener(this._videoFileReceived);
      SdFileManager.getInstance().addDownloadSdcardVideo([aRec.fileId]);
    });
  }

  delete(aRecs = [], useSpec = false) {
    // let urls = aRecs.map((aRec) => { return aRec.localUrl; });
    // return AlbumHelper.deleteAlbumFilesByUrl(urls);

    return SdFileManager.getInstance().startDeleteFiles(aRecs, useSpec);
  }

  async getVideoDates() {
    return new Promise((resolve, reject) => {
      let sdcardDays = SdFileManager.getInstance().getTimeItemDays();
      if (sdcardDays == null || sdcardDays.length == 0) {
        resolve(null);
      }
      resolve(sdcardDays.map((item) => {
        return item.startTime;
      }));
    });


  }

  // 清空资源。
  clear() {
    
    // 这里退出插件了，除了各种监听，还需要notify外面 下载任务失败了  因为p2p已经断开了连接
    if (this.videoFileReject != null) {
      this.videoFileReject("destroyed");
    }
    if (this.imageFileReject != null) {
      this.imageFileReject("destroyed");
    }
    if (this.fileListReject != null) {// 全部抛错
      this.fileListReject("destroyed");
    }

    SdFileManager.getInstance().bindDownloadVideoFileListener(null);
    SdFileManager.getInstance().bindOnFileDownloadedListener(null);
    SdFileManager.getInstance().bindReceiveFilesListener(null);
    SdFileManager.getInstance().clearAllDownloadTask();
    SdFileManager.getInstance().destroyInstance();
    this.mSaveL.remove();
    this.videoFileResolve = null;
    this.videoFileReject = null;
    this.imageFileReject = null;
    this.imageFileResolve = null;
    this.fileListReject = null;
    this.fileListResolve = null;
    this.downloadingItem = null;
    this.conectionListener && this.conectionListener.remove();
    this.downloadListener && this.downloadListener.remove();
  }

  getThumbUrlByImgStoreId(timestamp) {
    return {
      thumbUrl: SdFileManager.getSdcardFilePath(timestamp),
      videoUrl: SdFileManager.getSdcardFilePath(timestamp, true)
    };
  }

  clearSdcardFileList() {
    SdFileManager.getInstance().clearSdcardFileList();
  }

  static destroyInstance() {
    this._instance && this._instance.clear();
    this._instance = null;
  }

  static getInstance() {
    if (this._instance == null) {
      this._instance = new SdcardEventLoader();
    }
    return this._instance;
  }
}