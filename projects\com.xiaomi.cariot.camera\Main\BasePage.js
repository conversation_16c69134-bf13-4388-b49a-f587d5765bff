import React from 'react';
import Toast from './components/Toast';
import StorageKeys from './StorageKeys';
import { Permissions } from "miot/system/permission";
import { Service, PackageEvent, System } from 'miot';
import Orientation from 'react-native-orientation';
import Util from "./util2/Util";
import CameraConfig from './util/CameraConfig';
import { Platform, StatusBar, BackHandler, PermissionsAndroid, DeviceEventEmitter, StyleSheet, Dimensions } from 'react-native';
import { getStack } from '.';
import Package from 'miot/Package';

const kIsCN = Util.isLanguageCN();

export const AllNativePage = {
  FaceRec: "NatFaceRec",
  ScreenLink: "NatScreenLink"
};

function checkVip(aCb) {
  StorageKeys.VIP_DETAIL
    .then((aRet) => {
      aCb(aRet.vip);
    })
    .catch((aErr) => {
      Toast.fail("c_get_fail", aErr);
    });
}

const NativePage = {
  [AllNativePage.FaceRec]: () => { checkVip((aVip) => { Service.miotcamera.showFaceRecognize(aVip); }); },
  [AllNativePage.ScreenLink]: (aParam) => {
    // Stat.reportEvent(StatEV.PUSH_CALL_SWITCH, null);
    Service.miotcamera.showScreenLinkagePage(aParam.isMultiChoice, aParam.screenCount);
  }
};

export default class BasePage extends React.Component {
  constructor(props, aOri = "UNDEF") {
    super(props);
    this.mPluginCfg = this.props.navigation.state.params != null ? this.props.navigation.state.params.pluginCfg : null;
    this.mActive = false;
    // see https://blog.csdn.net/u011068702/article/details/83218639
    // 回到当前页面 或者第一次进来
    this.mWillFocusL = this.props.navigation.addListener('willFocus', this.mPreloader);
    this.mFocusL = this.props.navigation.addListener('didFocus', this.mResumeL);
    // 去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
    this.mBlurL = this.props.navigation.addListener('willBlur', this.mPauseL);
    this.mResL1 = PackageEvent.packageDidResume.addListener(this.mResumeL);
    this.mPauL1 = PackageEvent.packageWillPause.addListener(this.mPauseL);
    this.mResL2 = PackageEvent.packageViewWillAppear.addListener(this.mResumeL);
    this.mPauL2 = PackageEvent.packageViewWillDisappearIOS.addListener(this.mPauseL);
    this.mOri = aOri;
    this.mPermReq = false;
    Dimensions.addEventListener('change', (args) => {
      if (!this.props.navigation.isFocused()) {
        return;
      }
      this.setState({ screenSize: Dimensions.get('screen') });
      if (!this.props.navigation.isFocused()) {
        return;
      }
      if (Platform.OS === "ios") {
        console.log(this.tag, 'setDimensionsIos: ', args);
        if (args && args.screen && args.window) {
          if (args.screen.width !== args.window.width || args.screen.height !== args.window.height) {
            setTimeout(() => Dimensions.set({ 'window': args.screen }), 10);
          }
        }
      }
    });
    this.state = { fullScreen: "LANDSCAPE" == this.mOri ? true : false, screenSize: Dimensions.get('screen') };
  }



  get tag() {
    return `BasePage_${this.constructor.name}`;
  }

  initState(aState) {
    this.state = Object.assign({}, aState, this.state);
  }

  mPreloader = () => {
    this.onPreload();
  }

  mResumeL = () => {
    if (this.props.navigation.isFocused()) {
      this.resume();
    }
  }

  mPauseL = (willFinish = false) => {
    if (this.mPermReq && this.isAndroid()) {
      console.log(this.tag, "perm req defer pause to stop");
    } else {
      this.pause(willFinish);
    }
  }

  mStopL = () => {
    this.mPermReq = false;
    console.log(this.tag, "host stopped");
    this.mPauseL();
  }

  // android返回键处理
  mBackL = () => {
    let ret = false;
    if (this.props.navigation.isFocused()) {
      ret = this.onBack();
    }
    console.log(this.tag, "mBackL invoke", ret);
    return ret;
  }

  mOriL = (orientation) => {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    console.log(this.tag, `device orientation changed :${ orientation } want ${ this.mOri } active ${ this.mActive }`);
    if (this.mOri === orientation) {
      if (orientation === 'LANDSCAPE') {
        // do something with landscape layout
        this.setState({ fullScreen: true });
      } else {
        // do something with portrait layout
        this.setState({ fullScreen: false });
      }
    } else {
      // ios need restore for next lock
      if (!this.isAndroid()) {
        this.restoreOri();
      }
    }
  };

  resume() {
    if (!this.mActive) {
      console.log(this.tag, "doResume");
      Orientation.addOrientationListener(this.mOriL);
      this.restoreOri();
      this.mActive = true;
      this.onResume();
    }
  }

  pause(willFinish) {
    if (this.mActive) {
      console.log(this.tag, "doPause focused", this.props.navigation.isFocused());
      Orientation.removeOrientationListener(this.mOriL);
      this.mActive = false;
      this.onPause(willFinish);
    }
  }

  componentDidMount() {
    console.log(this.tag, "componentDidMount focused", this.props.navigation.isFocused());
    this.restoreOri();
    if (this.isAndroid) {
      this.mStopEL = DeviceEventEmitter.addListener("packageWillStop", this.mStopL);
      BackHandler.addEventListener("hardwareBackPress", this.mBackL);
    }
  }

  componentWillUnmount() {
    this.mPauseL(true);
    console.log(this.tag, "componentWillUnmount");
    this.mFocusL.remove();
    this.mBlurL.remove();
    this.mResL1.remove();
    this.mPauL1.remove();
    this.mResL2.remove();
    this.mPauL2.remove();
    if (this.isAndroid) {
      BackHandler.removeEventListener("hardwareBackPress", this.mBackL);
      this.mStopEL.remove();
    }

    Dimensions.removeEventListener('change', this.dimensionListener);
  }

  onResume() {

  }

  onPause(willFinish) {

  }

  // use for setting preload
  onPreload() {

  }

  onBack() {
    return false;
  }

  isAndroid() {
    return Platform.OS == "android";
  }

  restoreOri() {
    console.log(this.tag, "restoreOri", this.mOri);
    if ("PORTRAIT" === this.mOri) {
      this.toPortrait();
    } else if ("LANDSCAPE" === this.mOri) {
      this.toLandscape();
    }
  }

  toPortrait() {
    console.log(this.tag, "toPortrait");
    StatusBar.setHidden(false);
    this.mOri = "PORTRAIT";
    StatusBar.setHidden(false);
    CameraConfig.lockToPortrait();
  }

  toLandscape() {
    console.log(this.tag, "toLandscape");
    StatusBar.setHidden(true);
    this.mOri = "LANDSCAPE";
    StatusBar.setHidden(true);
    if (Platform.OS === "android") {
      Orientation.lockToLandscape();
    } else {
      Orientation.lockToLandscapeRight();
    }
  }

  render() {
    return null;
  }

  // navigation
  naviTo(aTgt, aParam = null) {
    this.mPauseL();
    if (NativePage[aTgt]) {
      let cfg = aParam ? aParam.cfg : null;
      console.log(this.tag, "go to native", aTgt, "with", cfg);
      NativePage[aTgt](cfg);
    } else if ("function" == typeof (aTgt)) {
      aTgt();
    } else if (this.constructor.name == aTgt) {
      this.props.navigation.push(aTgt, aParam);
    } else {
      this.props.navigation.navigate(aTgt, aParam);
    }
  }

  naviBack() {
    this.mPauseL();
    this.mRestorePreOrientation();
    let rootStack = getStack();
    const routes = rootStack._navigation.state.routes;
    if (routes.length > 1) {
      this.props.navigation.goBack();
    } else {
      Package.exit();
    }
  }


  checkStoragePerm() {
    this.mPermReq = true;
    return new Promise((resolve, reject) => {
      if (Platform.OS === "android") {
        PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
          .then((granted) => {
            if (granted === PermissionsAndroid.RESULTS.GRANTED) {
              this.mPermReq = false;
              resolve();
            } else {
              this.mPermReq = false;
              reject("camera_no_write_permission");
            }
          }).catch((error) => {
            this.mPermReq = false;
            reject("action_failed");
          });
      } else {
        System.permission.request("photos").then((res) => {
          this.mPermReq = false;
          resolve();
        }).catch((error) => {
          this.mPermReq = false;
          reject("unauthorized_album_permission");
        });
      }
    });
  }

  checkAudioPerm() {
    this.mPermReq = true;
    return new Promise((resolve, reject) => {
      System.permission.request(Permissions.RECORD_AUDIO)
        .then((res) => {
          this.mPermReq = false;
          if (res) {
            resolve();
          } else {
            throw new Error("android error");
          }
        })
        .catch((error) => {
          this.mPermReq = false;
          if (this.isAndroid()) {
            reject("camera_no_audio_permission");
          } else {
            reject("camera_no_audio_permission_ios");
          }
        });
    });
  }

  _clearTimeout(timer) {
    if (timer) {
      clearTimeout(timer);
    }
  }

  mRestorePreOrientation = () => {
    if (this.props.navigation.state.params.preOri) {
      this.mRestorePreOrientationSwitch = true;
      if (this.props.navigation.state.params.preOri == "landscape") {
        if (Platform.OS == 'android') {
          Orientation.lockToLandscape();
        } else {
          Orientation.lockToLandscapeRight();
        }
        StatusBar.setHidden(true);
      } else {
        CameraConfig.lockToPortrait();
        StatusBar.setHidden(false);
      }
    }
  }
}

export const BaseStyles = StyleSheet.create({
  row: {
    width: "100%",
    flexDirection: "row",
    flexWrap: 'nowrap',
    alignItems: "center",
    justifyContent: "space-between"
  },
  column: {
    width: "100%",
    flexWrap: 'nowrap',
    alignItems: "center",
    justifyContent: "space-between"
  },
  icon40: {
    width: 40,
    height: 40
  },

  icon30: {
    width: 30,
    height: 30
  },

  icon20: {
    width: 20,
    height: 20
  },
  icon22: {
    width: 22,
    height: 22
  },

  icon45: {
    width: 45,
    height: 45
  },

  pageRoot: {
    flex: 1,
    backgroundColor: "#ffffff"
  },

  text17: {
    fontSize: kIsCN ? 17 : 15,
    color: "black"
  },
  text16: {
    fontSize: kIsCN ? 16 : 14,
    color: "black"
  },

  text15: {
    fontSize: kIsCN ? 15 : 13,
    color: "black"
  },

  text14: {
    fontSize: kIsCN ? 14 : 12,
    color: "black"
  },

  text13: {
    fontSize: kIsCN ? 13 : 11,
    color: "black"
  },


  text12: {
    fontSize: kIsCN ? 12 : 10,
    color: "black"
  },

  text10: {
    fontSize: kIsCN ? 10 : 8,
    color: "black"
  },

  text22: {
    fontSize: kIsCN ? 22 : 20,
    color: "black"
  },

  text18: {
    fontSize: kIsCN ? 18 : 16,
    color: "black"
  },

  mainBg: {
    backgroundColor: "#F7F7F7"
  },

  evListItm: {
    height: 44
  }
});
