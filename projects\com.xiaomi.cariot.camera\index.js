import { Package } from 'miot';

import App from "./Main";
import CarIndex from "./Main/CarIndex";
import { Entrance } from "miot";
import { Alert } from "react-native";
// Alert.alert(`+++`,`+++${JSON.stringify(Package)}`)
switch (Package.entryInfo.mobileType) {
  case 'car':
    // 车机插件进入
    // Package.entry(App, () => {
    Package.entry(CarIndex, () => {
      console.disableYellowBox = true;
    });
    break;
  default:
    Package.entry(App, () => {
      console.disableYellowBox = true;
    });
    break;
}

