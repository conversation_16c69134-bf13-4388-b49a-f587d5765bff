import React from 'react';
import { ScrollView, View, BackHandler, FlatList, Platform, Text, StyleSheet, Image, TouchableWithoutFeedback, TouchableOpacity, ActivityIndicator, Dimensions } from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { DarkMode } from 'miot/Device';
import { NavigationBar } from 'mhui-rn';
import Video from "react-native-video";
import AlarmUtil from '../util/AlarmUtil';
import StorageKeys from '../StorageKeys';
import Toast from '../components/Toast';
import ImageButton from "miot/ui/ImageButton";
import CameraConfig from '../util/CameraConfig';
import { Service, Host, Device } from 'miot';
import LogUtil from '../util/LogUtil';
import VersionUtil from "../util/VersionUtil";
import { PackageEvent } from 'miot/Package';
import API from '../API';
const kWindowWidth = Math.min(Dimensions.get("screen").height, Dimensions.get("screen").width);// use this.winPortraitWidth instead

const TAG = "DailyStoryFirstEnter";
export default class DailyStoryFirstEnter extends React.Component {
  constructor(props, context) {
    super(props, context);
    this.video = null;
    this.state = {
      isPlaying: true,
      isLoading: false,
      alarmValues: {},
      editMode: false,
      isShowActiveStyle: true,
      listDatas: [],
      isChangeCategoryLoading: false,
      isVip: undefined
    };
    this.isReadOnlyShared = Device.isReadonlyShared;
    this.is022or051 = VersionUtil.is022Model(Device.model);
    this.path = "https://cnbj0.fds.api.xiaomi.com/dailystory-resource-online/preview3.2.mp4";
    if (Device.model == CameraConfig.Model_chuangmi_051a01 || Device.model == CameraConfig.Model_chuangmi_086ac1) {
      this.path = "https://cnbj1.fds.api.xiaomi.com/miot-camera-bucket/AI2-preview.mp4";
    }
    this.videoHeight = kWindowWidth * 0.5625;
  }
  componentDidMount() {
    this.props.navigation.setParams({
      title: LocalizedStrings['ss_daily_story'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => {
            this.props.navigation.goBack();
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    })

    console.log(TAG, "this.isReadOnlyShared", this.isReadOnlyShared);
    // 获取vip的状态
    StorageKeys.IS_VIP_STATUS.then((res) => {
      StorageKeys.IN_CLOSE_WINDOW.then((res1) => {
        this.setState({ isVip: res || res1 });
      })
      console.log("看看vip的状态：", this.state.isVip)
    }).catch(() => {
      this.setState({ isVip: false });
    });
    // 查看每日故事开关
    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {
        this._getSetting();
      }
    );
    this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {//// 原生页面关闭后回调
      this.autoPlay();
      LogUtil.logOnAll(TAG, "did resume ios");
      this._onResume();
    });
    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      LogUtil.logOnAll(TAG, "did resume");
      this._onResume();
    });
  }
  componentWillUnmount() {
    console.log("DailyStoryFirstEnter componentWillUnmount.....");
    this.willFocusSubscription && this.willFocusSubscription.remove();
    this.didResumeListener && this.didResumeListener.remove();
    this.willAppearListener && this.willAppearListener.remove();
  }
  autoPlay() { // 自动播放
    if (!this.isUserPause) {
      this.setState({ isPlaying: true });
    }
  }
  _onResume() {
    this._fetchVipStatus();
  }
  _fetchVipStatus() {
    console.log(TAG, "_fetchVipStatus start");
    // todo:需要框架端提供能够指定hostName前缀的api
    API.get("/miot/camera/app/v1/vip/status", "business.smartcamera")
      .then((result) => {
        if (result.code != 0) {
          return;
        }
        let data = result.data;
        if (data == null) {
          return;
        }
        console.log(TAG, "_fetchVipStatus", JSON.stringify(result));
        let vip = data["vip"];
        let status = data["status"];
        let inWindow = !data["closeWindow"]; // false表示在窗口内，true表示不在
        let endTime = data["endTime"];
        StorageKeys.IS_VIP_STATUS = vip;
        StorageKeys.IN_CLOSE_WINDOW = inWindow;
        StorageKeys.VIP_DETAIL = data;
        this.setState({ isVip: vip || inWindow });
      })
      .catch((err) => {
        console.log(err);
      });
  }
  // 通过后端获取开关信息
  _getSetting() {
    AlarmUtil.getAlarmConfig().then((res) => {
      if (res.code != 0) {
        console.log("getdailyStorySwitch:", JSON.stringify(-1));
        Toast.fail('c_get_fail');
        return;
      }
      this.setState({
        dailyStorySwitch: res.data.dailyStorySwitch,
      });
      console.log('先看看开关的状态:', res.data.dailyStorySwitch) // 开关为true
    }).catch((err) => {
      console.log("getdailyStorySwitch:", JSON.stringify(err));
      Toast.fail('c_get_fail', err);
      LogUtil.logOnAll("getdailyStorySwitch:", JSON.stringify(err));
    });
  }
  // 点击立即开启按钮 开启每日故事
  _onEnableValueChange(value) {
    console.log(TAG, 'this.isReadOnlyShared', this.isReadOnlyShared)
    // if ( !this.isReadOnlyShared ) {
    console.log(`_onEnableValueChange ${value}`);
    AlarmUtil.putDailyStorySwitch({
      open: value,
    }).then((res) => {
      this.setState({ dailyStorySwitch: res.code == 0 ? value : !value });
      if (res.code == 0) {
        this.setState({ isPlaying: false })
        // Toast.success('c_set_success');
        this.props.navigation.replace("DailyStoryList");
      } else {
        Toast.fail('c_set_fail');
      }
    }).catch((err) => {
      Toast.fail('c_set_fail', err);
    });
    // }   
  }
  // 切换播放
  togglePlay(isPlay) {
    console.log('toggle');
    if (Platform.OS == "ios" && this.state.progress == 0) {
      !this.destroyed && this.video && this.video.seek(0);
    }
    this.setState({ isPlaying: isPlay });
  }
  // 视频渲染
  renderVideo() {
    return (
      <View style={{ width: "100%", height: this.videoHeight, backgroundColor: "black" }}>
        <Video
          ref={(ref) => { this.video = ref; }}
          source={{ uri: this.path }}
          style={{ width: "100%", height: this.videoHeight }}
          onLoad={(response) => {
            console.log("===response=", JSON.stringify(response));
            let radio = response.naturalSize.height / response.naturalSize.width;
            this.videoHeight = kWindowWidth * radio;
            this.forceUpdate();
          }}
          paused={!this.state.isPlaying}
          repeat={false}
          resizeMode={'cover'}
          ignoreSilentSwitch={"ignore"}
          onEnd={() => {
            this.setState({ isPlaying: false });
            this.video.seek(0);
          }}
        />
        {this._renderPlayButton()}
      </View>
    )
  }
  // 立即开启每日故事
  _renderMessageDialog() {
    let is022or051 = this.is022or051 ? true : this.state.isVip;
    if (is022or051 && !this.isReadOnlyShared) {
      return (
        <TouchableOpacity
          style={{ height: 46, width: 312, backgroundColor: "#32BAC0", borderRadius: 23, position:"absolute", bottom:20 }}
          onPress={() => { this._onEnableValueChange(true) }}
        >
          <Text style={{ textAlign: "center", fontSize: 15,  color: "#ffffff",lineHeight: 46}}>{LocalizedStrings['auto_discovery_setting_now']}</Text>

        </TouchableOpacity>
      )
    }
  }
  // 购买云存储
  _renderCloudBuy() {
    if (!this.state.isVip && Device.isOwner) {
      return (
        <View style={{ display: "flex", flexDirection: "row", alignItems: "space-around",position:"absolute", bottom:60}}>
          <Text style={{ fontFamily: "MILanPro_MEDIUM--GB1-4", fontSize: 12 }}>{LocalizedStrings['c_cloudvip_need']}</Text>
          <TouchableOpacity
            onPress={() => {
              // 进入云存储购买页面 应该时已经封装在插件里面了 所以 直接调用即可  我猜
              this.setState({ isPlaying: false });
              // Service.miotcamera.showCloudStorage(true, false);// todo 不同model 要区分情况
              Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: "daily_story" });

              CameraConfig.isToUpdateVipStatue = true;              
            }}
          >
            <Text style={{ fontFamily: "MILanPro_MEDIUM--GB1-4", fontSize: 12, color: "#32BAC0", marginLeft: 5 }}>{LocalizedStrings['c_cloudvip_buy']}</Text>
          </TouchableOpacity>
        </View>
      )
    }
  }
  // 渲染播放暂停按钮
  _renderPlayButton() {
    const playIcons = [
      {
        source: require('../../Resources/Images/icon_camera_pause.png'),
        highlightedSource: null,
        onPress: () => { this.isUserPause = true; this.togglePlay(false); }
      },
      {
        source: require('../../Resources/Images/icon_camera_play.png'),
        highlightedSource: null,
        onPress: () => { this.isUserPause = false; this.togglePlay(true); } // 开始播放
      }
    ];
    let playIndex = this.state.isPlaying ? 0 : 1;
    return (
      <ImageButton
        style={{ width: 56, height: 56, position: "absolute", top: "50%", left: "50%", marginTop: -28, marginLeft: -28 }}
        onPress={playIcons[playIndex].onPress}
        source={playIcons[playIndex].source}
        highlightedSource={playIcons[playIndex].highlightedSource}
      ></ImageButton>
    );
  }
  render() {
    return (
      <View style={{ display: "flex", height: "100%", width: "100%", flex: 1, flexDirection: "column", backgroundColor: "#FFFFFF", alignItems: "center" }}>
        {this.renderVideo()}
        <Text style={{ color: "#999", fontSize: 12, marginTop: 20, paddingHorizontal: 24 }}>{LocalizedStrings['ss_daily_story_setting_tips']}</Text>
        {this._renderMessageDialog()}
        {this.is022or051 ? null : this._renderCloudBuy()}
      </View>
    )
  }
}