{"methodAllow": {"getHomeDevice": ["lumi.sensor_motion.v2", "lumi.sensor_magnet.v2", "lumi.lock.aq1", "lumi.lock.mcn002", "lumi.gateway.v3", "lumi.gateway.mitw01", "lumi.gateway.mihk01", "lumi.gateway.mieu01", "lumi.gateway.mgl03", "lumi.gateway.lmuk01", "lumi.gateway.aqhm01", "lumi.gateway.aqcn02", "lumi.acpartner.v3", "lumi.acpartner.v2", "lumi.acpartner.v1", "loock.lock.v5", "loock.lock.t1pro", "loock.lock.t1", "loock.lock.pfvl10", "loock.lock.fvl111", "loock.lock.t2v1", "loock.lock.t2pv1", "loock.lock.fcl201", "lumi.lock.mcn01", "lumi.lock.bmcn02", "lumi.lock.bmcn03", "lumi.lock.bmcn04", "lumi.lock.bmcn05", "lumi.lock.wbmcn1", "lumi.gateway.v2", "lumi.camera.aq1", "lumi.camera.gwagl01", "lumi.gateway.aqhm02", "yunlu.door.sd2105", "yunlu.door.sd2106", "oms.lock.dl01"], "getDevicesWithModel": ["chuangmi.plug.m3", "chunmi.ihcooker.cb", "deerma.humidifier.jsq3", "fgj.bed.rcctg4", "hhcc.plantmonitor.v1", "hmpace.bracelet.v3nfc", "hmpace.bracelet.v4", "jiqid.mistudy.v2", "k0918.toothbrush.t700", "leshow.heater.bs2", "ls123.headphone.t10", "lumi.acpartner.mcn02", "lumi.curtain.hmcn02", "lumi.curtain.hmcn04", "lumi.gateway.aqhm01", "lumi.gateway.aqhm02", "lumi.gateway.mgl03", "lumi.lock.bacn01", "lumi.lock.bmcn02", "lumi.lock.bmcn04", "lumi.lock.bmcn05", "lumi.lock.bzacn1", "lumi.lock.bzacn2", "lumi.lock.eicn02", "lumi.lock.mcn01", "lumi.lock.wbmcn1", "lumi.sensor_magnet.aq2", "miaomiaoce.sensor_ht.t1", "miot.heater.dnqty1", "mock.model.xiaomi.demo", "nhy.airrtc.v1", "nnleaf.light.strips", "nnleaf.light.ulp", "xiaomi.aircondition.c1", "xiaomi.aircondition.c10", "xiaomi.aircondition.c11", "xiaomi.aircondition.c12", "xiaomi.aircondition.c13", "xiaomi.aircondition.c14", "xiaomi.aircondition.c15", "xiaomi.aircondition.c16", "xiaomi.aircondition.c17", "xiaomi.aircondition.c18", "xiaomi.aircondition.c19", "xiaomi.aircondition.c20", "xiaomi.aircondition.c22", "xiaomi.aircondition.h2", "xiaomi.aircondition.mc1", "xiaomi.aircondition.mc2", "xiaomi.aircondition.mc3", "xiaomi.aircondition.mc4", "xiaomi.aircondition.mc5", "xiaomi.aircondition.mc6", "xiaomi.aircondition.mc7", "xiaomi.aircondition.mc8", "xiaomi.aircondition.mc9", "xiaomi.aircondition.mh1", "xiaomi.aircondition.mh2", "xiaomi.aircondition.mh3", "xiaomi.aircondition.mh4", "xiaomi.aircondition.mh6", "xiaomi.aircondition.mt0", "xiaomi.aircondition.mt1", "xiaomi.aircondition.mt2", "xiaomi.aircondition.mt3", "xiaomi.aircondition.mt4", "xiaomi.aircondition.mt5", "xiaomi.aircondition.mt6", "xiaomi.aircondition.mt7", "xiaomi.aircondition.mt8", "xiaomi.aircondition.t10", "xiaomi.aircondition.t11", "xiaomi.aircondition.t12", "xiaomi.aircondition.xft", "xiaomi.aircondition.dfb", "xiaomi.wifispeaker.l06a", "xiaomi.wifispeaker.l09a", "yeelink.wifispeaker.v1", "yunmi.waterpuri.lx6", "xiaomi.dev.lx2", "zhimi.heater.ma6"], "getHomeList": ["micar.", "xia<PERSON>."], "System.location.getLocation": ["chunmi.cooker.eh1"], "Host.local.getLocation": ["zhimi.airpurifier.ma2"], "callSpecificAPI": ["xiaomi.router", "xiaomi.wifispeaker"], "getServiceTokenWithSid": ["xiaomi.router", "xiaomi.wifispeaker"]}, "methodBlock": {}, "hostTokenCfgMap": {"api2.mina.mi.com": {"sid": "i.ai.mi.com", "cookieName": "serviceToken"}, "api.miwifi.com": {"sid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cookieName": "serviceToken"}}}