import React from 'react';
import EventGrid, { FooterH } from "./EventGrid";
import { View } from "react-native";
import EventCard, { CardHeight, CardMB } from "./EventCard";
import { BaseStyles } from "../BasePage";
import EventList, { LoadStatus } from "./EventList";

import { Order } from "../framework/EventLoaderInf";
import Separator from 'miot/ui/Separator';
import dayjs from 'dayjs';

const TAG = "EventListTurbo";

export default class EventListTurbo extends EventList {

  constructor(aProps) {
    super(aProps);
    this.state = Object.assign({}, this.state, { nextDateS: { [Order.Asc]: null, [Order.Desc]: null } });
  }

  async getData(date, event, isMore = false, aOrder = Order.Desc) {
    let data = await this.mLoader.getAllEvent(date, event, isMore, aOrder);
    return data;
  }
  
  
  async getEventList(date, event, isMore = false, aOrder = Order.Desc) {
    let keepRet = await super.getEventList(date, event, isMore, aOrder);
    if (keepRet) {
      let nextDateS = null;
      if (Order.Desc == aOrder) {
        if (!isMore) {
          let ascNext = new Date(dayjs(this.props.loaderArgs.startDate).add(1, "days").hour(0).minute(0).second(0).valueOf());
          
          nextDateS = { [aOrder]: this.state.nextDate, [Order.Asc]: ascNext };  
        } else {
          nextDateS = { [aOrder]: this.state.nextDate, [Order.Asc]: this.state.nextDateS[Order.Asc] };  
        }
      } else {
        nextDateS = { [aOrder]: this.state.nextDate, [Order.Desc]: this.state.nextDateS[Order.Desc] };
      }
      
      this.setState({ nextDateS });
      console.log(TAG, this.state.nextDateS, this.state.nextDate);
    }
  }

  
  onTopItemChange(aOldIdx, aNewIdx) {
    let ev = this.state.events;
    if (aNewIdx >= 0 && ev != null && aNewIdx < ev.length) {
      let date = new Date();
      date.setTime(ev[aNewIdx].createTime);
      // console.log(TAG, "onTopItemChange", date.getDate());
      if (this.props.topItemListener != null) {
        this.props.topItemListener(ev[aNewIdx]);
      }
    }
  }
  
  mRefresh = (aDateChange = false) => {
    if (aDateChange) {
      this.setState({ nextDateS: { [Order.Asc]: null, [Order.Desc]: null } });
      this.getEventList(this.props.loaderArgs.startDate, this.getEventFilter(), false, Order.Desc);
    } else {
      let nextAsc = this.state.nextDateS[Order.Asc]; 
      if (nextAsc != null) {
        this.getEventList(nextAsc, this.getEventFilter(), true, Order.Asc);  
      } else {
        this.getEventList(this.props.loaderArgs.startDate, this.getEventFilter(), false, Order.Asc);
      }
    }
    
  }
  
  mOnEnd = () => {
    console.log(TAG, "mOnEnd");
    if (LoadStatus.Finish === this.state.loadingStatus || LoadStatus.Loading === this.state.loadingStatus) {
      console.log(TAG, "onEndReached skip loading for status", this.state.loadingStatus);
    } else {
      this.getEventList(this.state.nextDate, this.props.loaderArgs.filter, true);
      this.setState({ loadingStatus: LoadStatus.Loading });
    }
  }
}
