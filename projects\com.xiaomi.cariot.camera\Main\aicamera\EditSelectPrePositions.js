import React from 'react';
import { ScrollView, View, BackHandler, FlatList, Platform, Text, StyleSheet, Image, TouchableOpacity, SafeAreaView, Dimensions, RefreshControl } from 'react-native';
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import Switch from 'miot/ui/Switch';
import Checkbox from 'miot/ui/Checkbox';
import CameraConfig, { IMG_DARKMODE_TINT } from '../util/CameraConfig';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlarmUtil from '../util/AlarmUtil';
import Toast from '../Toast';
import { DarkMode } from 'miot/Device';
import { ImageButton, InputDialog, NavigationBar } from 'mhui-rn';
import { MessageDialog } from 'miot/ui/Dialog';
import AutoDragSortableView from '../widget/AutoDragSortableView';
import { TouchableWithoutFeedback } from 'react-native';
import Host from 'miot/Host';
import { Base64 } from '../util/Base64';
import Util from '../util2/Util';
import LogUtil from '../util/LogUtil';

const { width } = Dimensions.get('window');

const itemSpace = 8;
const parentWidth = width * 0.97;
const childrenWidth = parentWidth / 2 - itemSpace * 2;
const childrenHeight = 42 * 4;

export default class EditSelectPrePositions extends React.Component {


  constructor(props, context) {
    super(props, context);
    this.state = {
      inputPrePositionName: false,
      prePositionNameTooLong: false,
      commentErr: "",
      showSmall2TipsDialog: false,
      loading: false
    };
    this.data = [];
    this.deleteData = [];
    this.renameItem = null;
    this.editedNames = {};
    this.isSelectPosition = false;
    this.selectPositions = [];
    this.sortItems = {};
  }

  componentDidMount() {
    let positionArray = JSON.parse(JSON.stringify(this.props.navigation.getParam("positionArray")));
    console.log("positionArray", positionArray);
    this.isSelectPosition = this.props.navigation.getParam("isSelectPosition");
    if (this.isSelectPosition) {
      this.selectPositions = this.props.navigation.getParam("selectPositions");
      console.log("this.isSelectPosition===", this.isSelectPosition);
      console.log("this.selectPositions===", this.selectPositions);
      positionArray.forEach((item) => {
        if (this.selectPositions.indexOf(item.idx) >= 0 || this.selectPositions.length <= 0) {
          item.select = true;
        }
      });
    }
    this.data = positionArray;
    // console.log(`positionArray = ${ JSON.stringify(positionArray) }`);
    this.props.navigation.setParams({
      backgroundColor: "#f5f5f5",
      title: LocalizedStrings['常看位置'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.CLOSE,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      right: [
        {
          key: NavigationBar.ICON.COMPLETE,
          onPress: () => { 
            if (this.isSelectPosition) {
              let positions = [];
              this.data.forEach((item) => {
                if (item.select) {
                  positions.push(item.idx);
                }
              });
              if (positions.length < 2) {
                this.setState({ showSmall2TipsDialog: true });
                return;
              }
              this.props.navigation.state.params.callBack(JSON.stringify(positions));
              this.props.navigation.goBack();
            } else {
              AlarmUtil.getPrePositions().then((res) => {
                this.addPreSetIndex = 1;
                console.log("EditSelectPrePositions", `get_preset_position = ${ JSON.stringify(res) }`);
                this.prePositions = JSON.parse(res[0].value);
                // [{"idx":1,"location":1,"name":"5aSf54ug","pos":"[45,7]"},{"idx":2,"location":2,"name":"aGg=","pos":"[96,23]"}]
                this.resultData = [];
                let cruisePositions = JSON.parse(this.cruiseValue.position);
                console.log("this.cruiseValue.position=", this.cruiseValue.position);
                this.resultPositions = [];
                this.prePositions.forEach((item) => {
                  let beDeleted = false;
                  for (let index in this.deleteData) {
                    let deItem = this.deleteData[index];
                    if (item.idx == deItem.idx && item.pos == deItem.pos) {
                      beDeleted = true;
                      break;
                    }
                  }
                  if (!beDeleted) {
                    if (this.editedNames[item.idx]) {
                      item.name = this.editedNames[item.idx];
                    }
                    this.resultData.push(item);

                    // 判断巡航点是不是被删除小于2个
                    if (cruisePositions.indexOf(item.idx) >= 0) {
                      console.log("resultPositions push=", item.idx);
                      this.resultPositions.push(item.idx);
                    }
                  }
                });
                console.log("this.deleteData.length=", this.deleteData.length);
                console.log("resultPositions.length=", this.resultPositions.length);
                if (this.cruiseValue.mode == 1 && this.deleteData.length > 0 && this.resultPositions.length < 2) {
                  this.setState({ showSmall2TipsDialog: true });
                  return;
                }
                this.resultData.sort(function(a, b) { return a.location - b.location; });
                for (let i in this.resultData) {
                  let item = this.resultData[i];
                  item.location = parseInt(i) + 1;
                  if (this.sortItems != {}) {
                    item.location = this.sortItems[item.idx];
                  }
                }
                this.props.navigation.state.params.callBack(JSON.stringify(this.resultData));
                this.props.navigation.goBack();
              }).catch((err) => {
                console.log("EditSelectPrePositions", `get_preset_position error = ${ err }`);
                this.props.navigation.state.params.callBack(JSON.stringify(this.data));
                this.props.navigation.goBack();
              });
            }
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {
        // this._getSetting();
      }
    );
    AlarmUtil.getCruiseConfig().then((res) => {
      console.log("getCruiseConfig res=", JSON.stringify(res));
      // {"freq":"00:10","start_time":"00:00","end_time":"23:59","repeat":127,"mode":1,"position":"[1,2,3,4]"}
      this.cruiseValue = JSON.parse(AlarmUtil.cruiseConfigure[0].value);
    }).catch((err) => {
      console.log("getCruiseConfig err=", JSON.stringify(err));
    });
  }

  componentWillUnmount() {

  }

  onRefreshHandle() {
    console.log("onRefreshHandle");
  }

  render() {
    return (<View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.header_title}>{this.isSelectPosition ? LocalizedStrings.select_position_str : LocalizedStrings.edite_position_str}</Text>
      </View>
      <SafeAreaView style={{ flex: 1, backgroundColor: '#f5f5f5', alignItems: "center" }}>
        <AutoDragSortableView
          dataSource={this.data}
          onRefreshHandle = {() => this.onRefreshHandle()}
          parentWidth={parentWidth}
          childrenWidth= {childrenWidth}
          marginChildrenBottom={itemSpace}
          marginChildrenRight={itemSpace}
          marginChildrenLeft = {itemSpace}
          marginChildrenTop = {itemSpace}
          childrenHeight={childrenHeight}
          fixedItems={this.isSelectPosition ? [0, 1, 2, 3, 4, 5, 6] : []}
          maxScale={1.05}
          
          onDataChange = {(data) => {
            console.log("before data:", data);
            let location = 1;
            this.sortItems = {};
            data.forEach((item) => {
              item.location = location;
              location += 1;
              this.sortItems[item.idx] = item.location;
            });
            console.log("after  data:", data);
            this.data = data;
          }}
          // keyExtractor={(item, index)=> item.txt} // FlatList作用一样，优化
          renderItem={(item, index) => {
            return this.renderItem(item, index);
          }}
          // renderHeaderView = {renderHeaderView}
          // headerViewHeight={headerViewHeight}
          // renderBottomView = {renderBottomView}
          // bottomViewHeight={bottomViewHeight}
        />
      </SafeAreaView>
      {this.renderPrePositionNameDialog()}
      {this.renderSmall2TipsDialog()}
    </View>);
  }

  renderSmall2TipsDialog() {
    return (
      <MessageDialog
        visible={this.state.showSmall2TipsDialog}
        message={this.isSelectPosition ? LocalizedStrings.can_not_set_cruise_tips : LocalizedStrings.can_not_del_cruise_tips}
        onDismiss={(_) => this.setState({ isAddCruisePoint: false })}
        messageStyle={{ textAlign: "center" }}
        buttons={[
          { text: LocalizedStrings.cancel },
          { text: LocalizedStrings.btn_confirm,
            callback: (result) => {
              if (this.isSelectPosition) {
                
                this.setState({ showSmall2TipsDialog: false });

              } else {

                this.cruiseValue.position = JSON.stringify(this.resultPositions);
                this.cruiseValue.mode = 0;
                AlarmUtil.putCruiseConfig(JSON.stringify(this.cruiseValue)).then((res) => {
                  LogUtil.logOnAll("renderSmall2TipsDialog putCruiseConfig==res=", JSON.stringify(res));
          
                }).catch((err) => {
                  LogUtil.logOnAll("renderSmall2TipsDialog putCruiseConfig==err=", JSON.stringify(err));
                });
                this.setState({ showSmall2TipsDialog: false });
                this.props.navigation.state.params.callBack(JSON.stringify(this.resultData));
                this.props.navigation.goBack();
              }
            }
          }
        ]}
      />
    );
  }

  renderItem(item, index) {
    let preSetPositionImg = "preSetPosition_";
    let imgPath = `${ Host.file.storageBasePath }/${ preSetPositionImg }${ item.idx }.jpg`;
    let itemName = Base64.decode(item.name);
    let backIcon = require('../../Resources/Images/icon_right_anchor_black.png');
    return (
      <View style={styles.item}>
        <View style={{ width: "100%", paddingStart: 12, display: "flex" }}>
          <Text style={styles.item_text}>
            {itemName !== "" ? Base64.decode(item.name) : LocalizedStrings["default_location"]}
          </Text>
          {this.isSelectPosition ? null : <TouchableOpacity onPress = {() => { 
          // this._ctrlPreSetPosition(this.PreSetADD, addPreSetIndex);
            this.renameItem = item;
            this.setState({ inputPrePositionName: true }); 
          }} style = {{ alignItems: "center", flexDirection: "row" }}>
            <Text style={{ color: '#999', fontSize: 12, marginStart: 7, fontWeight: 'bold', marginBottom: 1 }}>{LocalizedStrings["rename_tip"]}  </Text>
            <ImageButton
              style={{ width: 7, height: 11, position: "relative" }}
              source={backIcon}
            />
          </TouchableOpacity>}

          { this.isSelectPosition ? <Checkbox
            style={{ position: "absolute", width: 20, height: 20, borderRadius: 20, top: 6, right: 12 }}
            checked={item.select}
            onValueChange={(checked) => item.select = checked}
          /> : 
            <TouchableWithoutFeedback onPress = {() => { 
              let positions = [];
              this.data.forEach((item2) => {
                if (item2.idx != item.idx) {
                  positions.push(item2);
                } else {
                  this.deleteData.push(item2);
                }
              });
              this.data = positions;
              this.forceUpdate();
              console.log("==================reomved===", JSON.stringify(positions));
            }}>
              <Image style={{ position: "absolute", width: 23, height: 23, top: 10, right: 16 }}
                source = {require('../../Resources/Images/preset_position_del.png')}/>
            </TouchableWithoutFeedback>}
          
        </View>
        <Image style={styles.item_icon} source={{ uri: imgPath }}/>
      </View>
    );
  }

  isTextcommon(str) {
    let arrList = this.data;
    return arrList.some((res) => {
      return res.name === Base64.encode(str);
    });
  }

  renderPrePositionNameDialog() {
    return (
      <InputDialog
        visible={this.state.inputPrePositionName}
        title={LocalizedStrings["input_name_text"]}
        inputs={[{
          placeholder: LocalizedStrings["input_name_text"],
          defaultValue: '',
          textInputProps: {
            autoFocus: true
          },
          onChangeText: (result) => {
            let isEmoji = Util.containsEmoji(result);
            let length = result.length; 
            let isCommon = this.isTextcommon(result);
            if (isEmoji) {
              this.setState({ prePositionNameTooLong: true, commentErr: LocalizedStrings["Special_symbol_input_not_supported_temporarily"] }); 
            } else if (length > 6) {
              this.setState({ prePositionNameTooLong: true, commentErr: LocalizedStrings["input_name_too_long"] });
            } else if (isCommon && result !== "") {
              this.setState({ prePositionNameTooLong: true, commentErr: LocalizedStrings["the_name_already_exists"] });
            } else if (length <= 0) {
              this.setState({ prePositionNameTooLong: true, commentErr: LocalizedStrings["add_feature_empty_tips"] });
            } else {
              this.setState({ prePositionNameTooLong: false, commentErr: "error" });
            }
          },
          type: 'DELETE',
          isCorrect: !this.state.prePositionNameTooLong
        }]}
        // noInputDisButton={true}
        inputWarnText={this.state.commentErr}
        buttons={[
          {
            // style: { color: 'lightpink' },
            callback: () => this.setState({ inputPrePositionName: false, prePositionNameTooLong: false })
          },
          {
            text: LocalizedStrings["save_files"],
            disabled: this.state.prePositionNameTooLong,
            // style: { color: 'lightblue' },
            callback: (result) => {
              console.log(`结果`, result);
              if (result.textInputArray[0].length <= 0) {
                this.setState({ prePositionNameTooLong: true, commentErr: LocalizedStrings["add_feature_empty_tips"] });
                return;
              }
              let name = Base64.encode(result.textInputArray[0]);
              this.renameItem.name = name;
              this.editedNames[this.renameItem.idx] = name;
              console.log("editedNames:======", JSON.stringify(this.editedNames));
              this.setState({ inputPrePositionName: false, prePositionNameTooLong: false });
              this.forceUpdate();
              Toast._showToast(LocalizedStrings.updated_success);
            }
          }
        ]}
        canDismiss={false}
        // onDismiss={() => this.setState({ inputPrePositionName: false, prePositionNameTooLong: false })}
      />
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5'
  },
  header: {
    height: 48,
    justifyContent: 'center'
  },
  header_title: {
    color: '#555',
    fontSize: 30,
    marginStart: 30,
    fontWeight: '300'
  },
  item: {
    width: childrenWidth,
    height: childrenHeight,
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: '#fff',
    // backgroundColor: '#f39c12',
    borderRadius: 18
  },
  item_icon_swipe: {
    width: childrenWidth * 0.7,
    height: childrenWidth * 0.7,
    backgroundColor: '#fff',
    borderRadius: childrenWidth * 0.35,
    justifyContent: 'center',
    alignItems: 'center'
  },
  item_icon: {
    marginBottom: 6,
    width: childrenWidth * 0.78,
    height: childrenWidth * 0.48,
    borderRadius: 10,
    overflow: 'hidden'
  },
  item_text_swipe: {
    backgroundColor: '#fff',
    width: 56,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center'
  },
  item_text: {
    marginTop: 10,
    marginStart: 7,
    color: '#444',
    fontSize: 15,
    fontWeight: 'bold'
  },
  abottom_desc: {
    color: '#333',
    fontSize: 20,
    fontWeight: 'bold'
  }
});