diff --git a/node_modules/react-native-root-toast/Example/.flowconfig b/node_modules/react-native-root-toast/Example/.flowconfig
deleted file mode 100644
index c3edaf9..0000000
--- a/node_modules/react-native-root-toast/Example/.flowconfig
+++ /dev/null
@@ -1,65 +0,0 @@
-[ignore]
-
-# We fork some components by platform.
-.*/*.web.js
-.*/*.android.js
-
-# Some modules have their own node_modules with overlap
-.*/node_modules/node-haste/.*
-
-# Ugh
-.*/node_modules/babel.*
-.*/node_modules/babylon.*
-.*/node_modules/invariant.*
-
-# Ignore react and fbjs where there are overlaps, but don't ignore
-# anything that react-native relies on
-.*/node_modules/fbjs/lib/Map.js
-.*/node_modules/fbjs/lib/Promise.js
-.*/node_modules/fbjs/lib/fetch.js
-.*/node_modules/fbjs/lib/ExecutionEnvironment.js
-.*/node_modules/fbjs/lib/isEmpty.js
-.*/node_modules/fbjs/lib/crc32.js
-.*/node_modules/fbjs/lib/ErrorUtils.js
-
-# Flow has a built-in definition for the 'react' module which we prefer to use
-# over the currently-untyped source
-.*/node_modules/react/react.js
-.*/node_modules/react/lib/React.js
-.*/node_modules/react/lib/ReactDOM.js
-
-# Ignore commoner tests
-.*/node_modules/commoner/test/.*
-
-# See https://github.com/facebook/flow/issues/442
-.*/react-tools/node_modules/commoner/lib/reader.js
-
-# Ignore jest
-.*/node_modules/jest-cli/.*
-
-# Ignore Website
-.*/website/.*
-
-[include]
-
-[libs]
-node_modules/react-native/Libraries/react-native/react-native-interface.js
-
-[options]
-module.system=haste
-
-munge_underscores=true
-
-module.name_mapper='^image![a-zA-Z0-9$_-]+$' -> 'GlobalImageStub'
-module.name_mapper='^[./a-zA-Z0-9$_-]+\.\(bmp\|gif\|jpg\|jpeg\|png\|psd\|svg\|webp\|m4v\|mov\|mp4\|mpeg\|mpg\|webm\|aac\|aiff\|caf\|m4a\|mp3\|wav\|html\)$' -> 'RelativeImageStub'
-
-suppress_type=$FlowIssue
-suppress_type=$FlowFixMe
-suppress_type=$FixMe
-
-suppress_comment=\\(.\\|\n\\)*\\$FlowFixMe\\($\\|[^(]\\|(\\(>=0\\.\\(2[0-1]\\|1[0-9]\\|[0-9]\\).[0-9]\\)? *\\(site=[a-z,_]*react_native[a-z,_]*\\)?)\\)
-suppress_comment=\\(.\\|\n\\)*\\$FlowIssue\\((\\(>=0\\.\\(2[0-1]\\|1[0-9]\\|[0-9]\\).[0-9]\\)? *\\(site=[a-z,_]*react_native[a-z,_]*\\)?)\\)?:? #[0-9]+
-suppress_comment=\\(.\\|\n\\)*\\$FlowFixedInNextDeploy
-
-[version]
-0.21.0
diff --git a/node_modules/react-native-root-toast/Example/.watchmanconfig b/node_modules/react-native-root-toast/Example/.watchmanconfig
deleted file mode 100644
index 9e26dfe..0000000
--- a/node_modules/react-native-root-toast/Example/.watchmanconfig
+++ /dev/null
@@ -1 +0,0 @@
-{}
\ No newline at end of file
diff --git a/node_modules/react-native-root-toast/Example/Field.js b/node_modules/react-native-root-toast/Example/Field.js
deleted file mode 100644
index e08f121..0000000
--- a/node_modules/react-native-root-toast/Example/Field.js
+++ /dev/null
@@ -1,150 +0,0 @@
-import React, {
-    Component,
-    StyleSheet,
-    Text,
-    View,
-    TouchableHighlight,
-    TouchableOpacity,
-    Picker,
-    TextInput
-} from 'react-native';
-import Modal from 'react-native-root-modal';
-
-const styles = StyleSheet.create({
-    pickerModal: {
-        bottom: 0,
-        left: 0,
-        right: 0
-    },
-    picker: {
-        borderTopWidth: StyleSheet.hairlineWidth,
-        borderTopColor: '#ccc',
-        backgroundColor: '#fff'
-    },
-    hidePicker: {
-        position: 'absolute',
-        top: 0,
-        right: 0,
-        padding: 5
-    },
-    hidePickerText: {
-        fontSize: 12,
-        color: '#333'
-    },
-    field: {
-        overflow: 'hidden',
-        padding: 5,
-        alignItems: 'center',
-        justifyContent: 'center'
-    },
-    value: {
-        alignItems: 'center',
-        width: 200,
-        borderBottomWidth: 1,
-        borderBottomColor: '#ccc',
-        height: 30,
-        justifyContent: 'center'
-    },
-    input: {
-        width: 200,
-        height: 30,
-        textAlign: 'center'
-    },
-    disabledValue: {
-        borderBottomColor: 'transparent'
-    },
-    disabled: {
-        color: '#ccc'
-    }
-});
-
-class Field extends Component{
-    constructor() {
-        super(...arguments);
-        let keys = this.keys = Object.keys(this.props.options);
-        this.state = {
-            value: this.props.options[keys[0]],
-            text: `Toast.${this.props.name}s.${keys[0]}`,
-            picked: keys[0],
-            picker: false
-        };
-    }
-
-    openPicker = () => {
-        this.setState({
-            picker: true
-        });
-    };
-
-    closePicker = () => {
-        this.setState({
-            picker: false
-        });
-    };
-
-    pickerChange = value => {
-        let to = value ? this.props.options[value] : this.state.value;
-        this.setState({
-            value: to,
-            picked: value,
-            text: value ? `Toast.${this.props.name}s.${value}` : `Custom ${this.props.name}`
-        });
-        this.props.onChange(to);
-    };
-
-    inputChange = ({nativeEvent: {text}}) => {
-        this.setState({
-            value: text
-        });
-        this.props.onChange(+text);
-    };
-
-    render(){
-        return <View style={styles.field}>
-            <TouchableOpacity
-                onPress={this.openPicker}
-            >
-                <View style={styles.value}>
-                    <Text>{this.state.text}</Text>
-                </View>
-            </TouchableOpacity>
-            <View style={[styles.value, this.state.picked && styles.disabledValue]}>
-                <TextInput
-                    style={[styles.input, this.state.picked && styles.disabled]}
-                    keyboardType={'numeric'}
-                    value={(this.state.value || 0).toString()}
-                    onChange={this.inputChange}
-                    editable={!this.state.picked}
-                />
-            </View>
-            <Modal
-                style={styles.pickerModal}
-                visible={this.state.picker}
-            >
-                <Picker
-                    selectedValue={this.state.picked}
-                    onValueChange={this.pickerChange}
-                    style={styles.picker}
-                >
-                    {this.keys.map(key => <Picker.Item
-                        key={key}
-                        label={`Toast.${this.props.name}s.${key}`}
-                        value={key}
-                    />)}
-                    <Picker.Item
-                        label={`Custom ${this.props.name}`}
-                        value={false}
-                    />
-                </Picker>
-                <TouchableOpacity
-                    style={styles.hidePicker}
-                    onPress={this.closePicker}
-                >
-                    <Text style={styles.hidePickerText}>DONE</Text>
-                </TouchableOpacity>
-            </Modal>
-        </View>;
-    }
-}
-
-export default Field;
diff --git a/node_modules/react-native-root-toast/Example/android/app/build.gradle b/node_modules/react-native-root-toast/Example/android/app/build.gradle
deleted file mode 100644
index a235b9c..0000000
--- a/node_modules/react-native-root-toast/Example/android/app/build.gradle
+++ /dev/null
@@ -1,125 +0,0 @@
-apply plugin: "com.android.application"
-
-import com.android.build.OutputFile
-
-/**
- * The react.gradle file registers a task for each build variant (e.g. bundleDebugJsAndAssets
- * and bundleReleaseJsAndAssets).
- * These basically call `react-native bundle` with the correct arguments during the Android build
- * cycle. By default, bundleDebugJsAndAssets is skipped, as in debug/dev mode we prefer to load the
- * bundle directly from the development server. Below you can see all the possible configurations
- * and their defaults. If you decide to add a configuration block, make sure to add it before the
- * `apply from: "react.gradle"` line.
- *
- * project.ext.react = [
- *   // the name of the generated asset file containing your JS bundle
- *   bundleAssetName: "index.android.bundle",
- *
- *   // the entry file for bundle generation
- *   entryFile: "index.android.js",
- *
- *   // whether to bundle JS and assets in debug mode
- *   bundleInDebug: false,
- *
- *   // whether to bundle JS and assets in release mode
- *   bundleInRelease: true,
- *
- *   // whether to bundle JS and assets in another build variant (if configured).
- *   // See http://tools.android.com/tech-docs/new-build-system/user-guide#TOC-Build-Variants
- *   // The configuration property is in the format 'bundleIn${productFlavor}${buildType}'
- *   // bundleInFreeDebug: true,
- *   // bundleInPaidRelease: true,
- *   // bundleInBeta: true,
- *
- *   // the root of your project, i.e. where "package.json" lives
- *   root: "../../",
- *
- *   // where to put the JS bundle asset in debug mode
- *   jsBundleDirDebug: "$buildDir/intermediates/assets/debug",
- *
- *   // where to put the JS bundle asset in release mode
- *   jsBundleDirRelease: "$buildDir/intermediates/assets/release",
- *
- *   // where to put drawable resources / React Native assets, e.g. the ones you use via
- *   // require('./image.png')), in debug mode
- *   resourcesDirDebug: "$buildDir/intermediates/res/merged/debug",
- *
- *   // where to put drawable resources / React Native assets, e.g. the ones you use via
- *   // require('./image.png')), in release mode
- *   resourcesDirRelease: "$buildDir/intermediates/res/merged/release",
- *
- *   // by default the gradle tasks are skipped if none of the JS files or assets change; this means
- *   // that we don't look at files in android/ or ios/ to determine whether the tasks are up to
- *   // date; if you have any other folders that you want to ignore for performance reasons (gradle
- *   // indexes the entire tree), add them here. Alternatively, if you have JS files in android/
- *   // for example, you might want to remove it from here.
- *   inputExcludes: ["android/**", "ios/**"]
- * ]
- */
-
-apply from: "react.gradle"
-
-/**
- * Set this to true to create three separate APKs instead of one:
- *   - A universal APK that works on all devices
- *   - An APK that only works on ARM devices
- *   - An APK that only works on x86 devices
- * The advantage is the size of the APK is reduced by about 4MB.
- * Upload all the APKs to the Play Store and people will download
- * the correct one based on the CPU architecture of their device.
- */
-def enableSeparateBuildPerCPUArchitecture = false
-
-/**
- * Run Proguard to shrink the Java bytecode in release builds.
- */
-def enableProguardInReleaseBuilds = false
-
-android {
-    compileSdkVersion 23
-    buildToolsVersion "23.0.1"
-
-    defaultConfig {
-        applicationId "com.reactnativeroottoast"
-        minSdkVersion 16
-        targetSdkVersion 22
-        versionCode 1
-        versionName "1.0"
-        ndk {
-            abiFilters "armeabi-v7a", "x86"
-        }
-    }
-    splits {
-        abi {
-            enable enableSeparateBuildPerCPUArchitecture
-            universalApk false
-            reset()
-            include "armeabi-v7a", "x86"
-        }
-    }
-    buildTypes {
-        release {
-            minifyEnabled enableProguardInReleaseBuilds
-            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
-        }
-    }
-    // applicationVariants are e.g. debug, release
-    applicationVariants.all { variant ->
-        variant.outputs.each { output ->
-            // For each separate APK per architecture, set a unique version code as described here:
-            // http://tools.android.com/tech-docs/new-build-system/user-guide/apk-splits
-            def versionCodes = ["armeabi-v7a":1, "x86":2]
-            def abi = output.getFilter(OutputFile.ABI)
-            if (abi != null) {  // null for the universal-debug, universal-release variants
-                output.versionCodeOverride =
-                        versionCodes.get(abi) * 1048576 + defaultConfig.versionCode
-            }
-        }
-    }
-}
-
-dependencies {
-    compile fileTree(dir: "libs", include: ["*.jar"])
-    compile "com.android.support:appcompat-v7:23.0.1"
-    compile "com.facebook.react:react-native:0.20.+"
-}
diff --git a/node_modules/react-native-root-toast/Example/android/app/proguard-rules.pro b/node_modules/react-native-root-toast/Example/android/app/proguard-rules.pro
deleted file mode 100644
index 7d72e46..0000000
--- a/node_modules/react-native-root-toast/Example/android/app/proguard-rules.pro
+++ /dev/null
@@ -1,67 +0,0 @@
-# Add project specific ProGuard rules here.
-# By default, the flags in this file are appended to flags specified
-# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
-# You can edit the include path and order by changing the proguardFiles
-# directive in build.gradle.
-#
-# For more details, see
-#   http://developer.android.com/guide/developing/tools/proguard.html
-
-# Add any project specific keep options here:
-
-# If your project uses WebView with JS, uncomment the following
-# and specify the fully qualified class name to the JavaScript interface
-# class:
-#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
-#   public *;
-#}
-
-# Disabling obfuscation is useful if you collect stack traces from production crashes
-# (unless you are using a system that supports de-obfuscate the stack traces).
--dontobfuscate
-
-# React Native
-
-# Keep our interfaces so they can be used by other ProGuard rules.
-# See http://sourceforge.net/p/proguard/bugs/466/
--keep,allowobfuscation @interface com.facebook.proguard.annotations.DoNotStrip
--keep,allowobfuscation @interface com.facebook.proguard.annotations.KeepGettersAndSetters
-
-# Do not strip any method/class that is annotated with @DoNotStrip
--keep @com.facebook.proguard.annotations.DoNotStrip class *
--keepclassmembers class * {
-    @com.facebook.proguard.annotations.DoNotStrip *;
-}
-
--keepclassmembers @com.facebook.proguard.annotations.KeepGettersAndSetters class * {
-  void set*(***);
-  *** get*();
-}
-
--keep class * extends com.facebook.react.bridge.JavaScriptModule { *; }
--keep class * extends com.facebook.react.bridge.NativeModule { *; }
--keepclassmembers,includedescriptorclasses class * { native <methods>; }
--keepclassmembers class *  { @com.facebook.react.uimanager.UIProp <fields>; }
--keepclassmembers class *  { @com.facebook.react.uimanager.annotations.ReactProp <methods>; }
--keepclassmembers class *  { @com.facebook.react.uimanager.annotations.ReactPropGroup <methods>; }
-
--dontwarn com.facebook.react.**
-
-# okhttp
-
--keepattributes Signature
--keepattributes *Annotation*
--keep class com.squareup.okhttp.** { *; }
--keep interface com.squareup.okhttp.** { *; }
--dontwarn com.squareup.okhttp.**
-
-# okio
-
--keep class sun.misc.Unsafe { *; }
--dontwarn java.nio.file.*
--dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
--dontwarn okio.**
-
-# stetho
-
--dontwarn com.facebook.stetho.**
diff --git a/node_modules/react-native-root-toast/Example/android/app/react.gradle b/node_modules/react-native-root-toast/Example/android/app/react.gradle
deleted file mode 100644
index 4b43bf9..0000000
--- a/node_modules/react-native-root-toast/Example/android/app/react.gradle
+++ /dev/null
@@ -1,96 +0,0 @@
-import org.apache.tools.ant.taskdefs.condition.Os
-
-def config = project.hasProperty("react") ? project.react : [];
-
-def bundleAssetName = config.bundleAssetName ?: "index.android.bundle"
-def entryFile = config.entryFile ?: "index.android.js"
-
-// because elvis operator
-def elvisFile(thing) {
-    return thing ? file(thing) : null;
-}
-
-def reactRoot = elvisFile(config.root) ?: file("../../")
-def inputExcludes = config.inputExcludes ?: ["android/**", "ios/**"]
-
-void runBefore(String dependentTaskName, Task task) {
-    Task dependentTask = tasks.findByPath(dependentTaskName);
-    if (dependentTask != null) {
-        dependentTask.dependsOn task
-    }
-}
-
-gradle.projectsEvaluated {
-    // Grab all build types and product flavors
-    def buildTypes = android.buildTypes.collect { type -> type.name }
-    def productFlavors = android.productFlavors.collect { flavor -> flavor.name }
-
-    // When no product flavors defined, use empty
-    if (!productFlavors) productFlavors.add('')
-
-    productFlavors.each { productFlavorName ->
-        buildTypes.each { buildTypeName ->
-            // Create variant and source names
-            def sourceName = "${buildTypeName}"
-            def targetName = "${sourceName.capitalize()}"
-            if (productFlavorName) {
-                sourceName = "${productFlavorName}${targetName}"
-            }
-
-            // React js bundle directories
-            def jsBundleDirConfigName = "jsBundleDir${targetName}"
-            def jsBundleDir = elvisFile(config."$jsBundleDirConfigName") ?:
-                    file("$buildDir/intermediates/assets/${sourceName}")
-
-            def resourcesDirConfigName = "jsBundleDir${targetName}"
-            def resourcesDir = elvisFile(config."${resourcesDirConfigName}") ?:
-                    file("$buildDir/intermediates/res/merged/${sourceName}")
-            def jsBundleFile = file("$jsBundleDir/$bundleAssetName")
-
-            // Bundle task name for variant
-            def bundleJsAndAssetsTaskName = "bundle${targetName}JsAndAssets"
-
-            def currentBundleTask = tasks.create(
-                    name: bundleJsAndAssetsTaskName,
-                    type: Exec) {
-                group = "react"
-                description = "bundle JS and assets for ${targetName}."
-
-                // Create dirs if they are not there (e.g. the "clean" task just ran)
-                doFirst {
-                    jsBundleDir.mkdirs()
-                    resourcesDir.mkdirs()
-                }
-
-                // Set up inputs and outputs so gradle can cache the result
-                inputs.files fileTree(dir: reactRoot, excludes: inputExcludes)
-                outputs.dir jsBundleDir
-                outputs.dir resourcesDir
-
-                // Set up the call to the react-native cli
-                workingDir reactRoot
-
-                // Set up dev mode
-                def devEnabled = !targetName.toLowerCase().contains("release")
-                if (Os.isFamily(Os.FAMILY_WINDOWS)) {
-                    commandLine "cmd", "/c", "react-native", "bundle", "--platform", "android", "--dev", "${devEnabled}",
-                            "--entry-file", entryFile, "--bundle-output", jsBundleFile, "--assets-dest", resourcesDir
-                } else {
-                    commandLine "react-native", "bundle", "--platform", "android", "--dev", "${devEnabled}",
-                            "--entry-file", entryFile, "--bundle-output", jsBundleFile, "--assets-dest", resourcesDir
-                }
-
-                enabled config."bundleIn${targetName}" ?: targetName.toLowerCase().contains("release")
-            }
-
-            // Hook bundle${productFlavor}${buildType}JsAndAssets into the android build process
-            currentBundleTask.dependsOn("merge${targetName}Resources")
-            currentBundleTask.dependsOn("merge${targetName}Assets")
-
-            runBefore("processArmeabi-v7a${targetName}Resources", currentBundleTask)
-            runBefore("processX86${targetName}Resources", currentBundleTask)
-            runBefore("processUniversal${targetName}Resources", currentBundleTask)
-            runBefore("process${targetName}Resources", currentBundleTask)
-        }
-    }
-}
diff --git a/node_modules/react-native-root-toast/Example/android/app/src/main/AndroidManifest.xml b/node_modules/react-native-root-toast/Example/android/app/src/main/AndroidManifest.xml
deleted file mode 100644
index e72e5e8..0000000
--- a/node_modules/react-native-root-toast/Example/android/app/src/main/AndroidManifest.xml
+++ /dev/null
@@ -1,23 +0,0 @@
-<manifest xmlns:android="http://schemas.android.com/apk/res/android"
-    package="com.reactnativeroottoast">
-
-    <uses-permission android:name="android.permission.INTERNET" />
-
-    <application
-      android:allowBackup="true"
-      android:label="@string/app_name"
-      android:icon="@mipmap/ic_launcher"
-      android:theme="@style/AppTheme">
-      <activity
-        android:name=".MainActivity"
-        android:label="@string/app_name"
-        android:configChanges="keyboard|keyboardHidden|orientation|screenSize">
-        <intent-filter>
-            <action android:name="android.intent.action.MAIN" />
-            <category android:name="android.intent.category.LAUNCHER" />
-        </intent-filter>
-      </activity>
-      <activity android:name="com.facebook.react.devsupport.DevSettingsActivity" />
-    </application>
-
-</manifest>
diff --git a/node_modules/react-native-root-toast/Example/android/app/src/main/java/com/reactnativeroottoast/MainActivity.java b/node_modules/react-native-root-toast/Example/android/app/src/main/java/com/reactnativeroottoast/MainActivity.java
deleted file mode 100644
index aad4b7a..0000000
--- a/node_modules/react-native-root-toast/Example/android/app/src/main/java/com/reactnativeroottoast/MainActivity.java
+++ /dev/null
@@ -1,40 +0,0 @@
-package com.reactnativeroottoast;
-
-import com.facebook.react.ReactActivity;
-import com.facebook.react.ReactPackage;
-import com.facebook.react.shell.MainReactPackage;
-
-import java.util.Arrays;
-import java.util.List;
-
-public class MainActivity extends ReactActivity {
-
-    /**
-     * Returns the name of the main component registered from JavaScript.
-     * This is used to schedule rendering of the component.
-     */
-    @Override
-    protected String getMainComponentName() {
-        return "ReactNativeRootToast";
-    }
-
-    /**
-     * Returns whether dev mode should be enabled.
-     * This enables e.g. the dev menu.
-     */
-    @Override
-    protected boolean getUseDeveloperSupport() {
-        return BuildConfig.DEBUG;
-    }
-
-   /**
-   * A list of packages used by the app. If the app uses additional views
-   * or modules besides the default ones, add more packages here.
-   */
-    @Override
-    protected List<ReactPackage> getPackages() {
-      return Arrays.<ReactPackage>asList(
-        new MainReactPackage()
-      );
-    }
-}
diff --git a/node_modules/react-native-root-toast/Example/android/app/src/main/res/mipmap-hdpi/ic_launcher.png b/node_modules/react-native-root-toast/Example/android/app/src/main/res/mipmap-hdpi/ic_launcher.png
deleted file mode 100644
index cde69bc..0000000
Binary files a/node_modules/react-native-root-toast/Example/android/app/src/main/res/mipmap-hdpi/ic_launcher.png and /dev/null differ
diff --git a/node_modules/react-native-root-toast/Example/android/app/src/main/res/mipmap-mdpi/ic_launcher.png b/node_modules/react-native-root-toast/Example/android/app/src/main/res/mipmap-mdpi/ic_launcher.png
deleted file mode 100644
index c133a0c..0000000
Binary files a/node_modules/react-native-root-toast/Example/android/app/src/main/res/mipmap-mdpi/ic_launcher.png and /dev/null differ
diff --git a/node_modules/react-native-root-toast/Example/android/app/src/main/res/mipmap-xhdpi/ic_launcher.png b/node_modules/react-native-root-toast/Example/android/app/src/main/res/mipmap-xhdpi/ic_launcher.png
deleted file mode 100644
index bfa42f0..0000000
Binary files a/node_modules/react-native-root-toast/Example/android/app/src/main/res/mipmap-xhdpi/ic_launcher.png and /dev/null differ
diff --git a/node_modules/react-native-root-toast/Example/android/app/src/main/res/mipmap-xxhdpi/ic_launcher.png b/node_modules/react-native-root-toast/Example/android/app/src/main/res/mipmap-xxhdpi/ic_launcher.png
deleted file mode 100644
index 324e72c..0000000
Binary files a/node_modules/react-native-root-toast/Example/android/app/src/main/res/mipmap-xxhdpi/ic_launcher.png and /dev/null differ
diff --git a/node_modules/react-native-root-toast/Example/android/app/src/main/res/values/strings.xml b/node_modules/react-native-root-toast/Example/android/app/src/main/res/values/strings.xml
deleted file mode 100644
index 313b28a..0000000
--- a/node_modules/react-native-root-toast/Example/android/app/src/main/res/values/strings.xml
+++ /dev/null
@@ -1,3 +0,0 @@
-<resources>
-    <string name="app_name">ReactNativeRootToast</string>
-</resources>
diff --git a/node_modules/react-native-root-toast/Example/android/app/src/main/res/values/styles.xml b/node_modules/react-native-root-toast/Example/android/app/src/main/res/values/styles.xml
deleted file mode 100644
index 319eb0c..0000000
--- a/node_modules/react-native-root-toast/Example/android/app/src/main/res/values/styles.xml
+++ /dev/null
@@ -1,8 +0,0 @@
-<resources>
-
-    <!-- Base application theme. -->
-    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
-        <!-- Customize your theme here. -->
-    </style>
-
-</resources>
diff --git a/node_modules/react-native-root-toast/Example/android/build.gradle b/node_modules/react-native-root-toast/Example/android/build.gradle
deleted file mode 100644
index ccdfc4e..0000000
--- a/node_modules/react-native-root-toast/Example/android/build.gradle
+++ /dev/null
@@ -1,20 +0,0 @@
-// Top-level build file where you can add configuration options common to all sub-projects/modules.
-
-buildscript {
-    repositories {
-        jcenter()
-    }
-    dependencies {
-        classpath 'com.android.tools.build:gradle:1.3.1'
-
-        // NOTE: Do not place your application dependencies here; they belong
-        // in the individual module build.gradle files
-    }
-}
-
-allprojects {
-    repositories {
-        mavenLocal()
-        jcenter()
-    }
-}
diff --git a/node_modules/react-native-root-toast/Example/android/gradle.properties b/node_modules/react-native-root-toast/Example/android/gradle.properties
deleted file mode 100644
index 1fd964e..0000000
--- a/node_modules/react-native-root-toast/Example/android/gradle.properties
+++ /dev/null
@@ -1,20 +0,0 @@
-# Project-wide Gradle settings.
-
-# IDE (e.g. Android Studio) users:
-# Gradle settings configured through the IDE *will override*
-# any settings specified in this file.
-
-# For more details on how to configure your build environment visit
-# http://www.gradle.org/docs/current/userguide/build_environment.html
-
-# Specifies the JVM arguments used for the daemon process.
-# The setting is particularly useful for tweaking memory settings.
-# Default value: -Xmx10248m -XX:MaxPermSize=256m
-# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
-
-# When configured, Gradle will run in incubating parallel mode.
-# This option should only be used with decoupled projects. More details, visit
-# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
-# org.gradle.parallel=true
-
-android.useDeprecatedNdk=true
diff --git a/node_modules/react-native-root-toast/Example/android/gradle/wrapper/gradle-wrapper.jar b/node_modules/react-native-root-toast/Example/android/gradle/wrapper/gradle-wrapper.jar
deleted file mode 100644
index b5166da..0000000
Binary files a/node_modules/react-native-root-toast/Example/android/gradle/wrapper/gradle-wrapper.jar and /dev/null differ
diff --git a/node_modules/react-native-root-toast/Example/android/gradle/wrapper/gradle-wrapper.properties b/node_modules/react-native-root-toast/Example/android/gradle/wrapper/gradle-wrapper.properties
deleted file mode 100644
index b9fbfab..0000000
--- a/node_modules/react-native-root-toast/Example/android/gradle/wrapper/gradle-wrapper.properties
+++ /dev/null
@@ -1,5 +0,0 @@
-distributionBase=GRADLE_USER_HOME
-distributionPath=wrapper/dists
-zipStoreBase=GRADLE_USER_HOME
-zipStorePath=wrapper/dists
-distributionUrl=https\://services.gradle.org/distributions/gradle-2.4-all.zip
diff --git a/node_modules/react-native-root-toast/Example/android/gradlew b/node_modules/react-native-root-toast/Example/android/gradlew
deleted file mode 100644
index 91a7e26..0000000
--- a/node_modules/react-native-root-toast/Example/android/gradlew
+++ /dev/null
@@ -1,164 +0,0 @@
-#!/usr/bin/env bash
-
-##############################################################################
-##
-##  Gradle start up script for UN*X
-##
-##############################################################################
-
-# Add default JVM options here. You can also use JAVA_OPTS and GRADLE_OPTS to pass JVM options to this script.
-DEFAULT_JVM_OPTS=""
-
-APP_NAME="Gradle"
-APP_BASE_NAME=`basename "$0"`
-
-# Use the maximum available, or set MAX_FD != -1 to use that value.
-MAX_FD="maximum"
-
-warn ( ) {
-    echo "$*"
-}
-
-die ( ) {
-    echo
-    echo "$*"
-    echo
-    exit 1
-}
-
-# OS specific support (must be 'true' or 'false').
-cygwin=false
-msys=false
-darwin=false
-case "`uname`" in
-  CYGWIN* )
-    cygwin=true
-    ;;
-  Darwin* )
-    darwin=true
-    ;;
-  MINGW* )
-    msys=true
-    ;;
-esac
-
-# For Cygwin, ensure paths are in UNIX format before anything is touched.
-if $cygwin ; then
-    [ -n "$JAVA_HOME" ] && JAVA_HOME=`cygpath --unix "$JAVA_HOME"`
-fi
-
-# Attempt to set APP_HOME
-# Resolve links: $0 may be a link
-PRG="$0"
-# Need this for relative symlinks.
-while [ -h "$PRG" ] ; do
-    ls=`ls -ld "$PRG"`
-    link=`expr "$ls" : '.*-> \(.*\)$'`
-    if expr "$link" : '/.*' > /dev/null; then
-        PRG="$link"
-    else
-        PRG=`dirname "$PRG"`"/$link"
-    fi
-done
-SAVED="`pwd`"
-cd "`dirname \"$PRG\"`/" >&-
-APP_HOME="`pwd -P`"
-cd "$SAVED" >&-
-
-CLASSPATH=$APP_HOME/gradle/wrapper/gradle-wrapper.jar
-
-# Determine the Java command to use to start the JVM.
-if [ -n "$JAVA_HOME" ] ; then
-    if [ -x "$JAVA_HOME/jre/sh/java" ] ; then
-        # IBM's JDK on AIX uses strange locations for the executables
-        JAVACMD="$JAVA_HOME/jre/sh/java"
-    else
-        JAVACMD="$JAVA_HOME/bin/java"
-    fi
-    if [ ! -x "$JAVACMD" ] ; then
-        die "ERROR: JAVA_HOME is set to an invalid directory: $JAVA_HOME
-
-Please set the JAVA_HOME variable in your environment to match the
-location of your Java installation."
-    fi
-else
-    JAVACMD="java"
-    which java >/dev/null 2>&1 || die "ERROR: JAVA_HOME is not set and no 'java' command could be found in your PATH.
-
-Please set the JAVA_HOME variable in your environment to match the
-location of your Java installation."
-fi
-
-# Increase the maximum file descriptors if we can.
-if [ "$cygwin" = "false" -a "$darwin" = "false" ] ; then
-    MAX_FD_LIMIT=`ulimit -H -n`
-    if [ $? -eq 0 ] ; then
-        if [ "$MAX_FD" = "maximum" -o "$MAX_FD" = "max" ] ; then
-            MAX_FD="$MAX_FD_LIMIT"
-        fi
-        ulimit -n $MAX_FD
-        if [ $? -ne 0 ] ; then
-            warn "Could not set maximum file descriptor limit: $MAX_FD"
-        fi
-    else
-        warn "Could not query maximum file descriptor limit: $MAX_FD_LIMIT"
-    fi
-fi
-
-# For Darwin, add options to specify how the application appears in the dock
-if $darwin; then
-    GRADLE_OPTS="$GRADLE_OPTS \"-Xdock:name=$APP_NAME\" \"-Xdock:icon=$APP_HOME/media/gradle.icns\""
-fi
-
-# For Cygwin, switch paths to Windows format before running java
-if $cygwin ; then
-    APP_HOME=`cygpath --path --mixed "$APP_HOME"`
-    CLASSPATH=`cygpath --path --mixed "$CLASSPATH"`
-
-    # We build the pattern for arguments to be converted via cygpath
-    ROOTDIRSRAW=`find -L / -maxdepth 1 -mindepth 1 -type d 2>/dev/null`
-    SEP=""
-    for dir in $ROOTDIRSRAW ; do
-        ROOTDIRS="$ROOTDIRS$SEP$dir"
-        SEP="|"
-    done
-    OURCYGPATTERN="(^($ROOTDIRS))"
-    # Add a user-defined pattern to the cygpath arguments
-    if [ "$GRADLE_CYGPATTERN" != "" ] ; then
-        OURCYGPATTERN="$OURCYGPATTERN|($GRADLE_CYGPATTERN)"
-    fi
-    # Now convert the arguments - kludge to limit ourselves to /bin/sh
-    i=0
-    for arg in "$@" ; do
-        CHECK=`echo "$arg"|egrep -c "$OURCYGPATTERN" -`
-        CHECK2=`echo "$arg"|egrep -c "^-"`                                 ### Determine if an option
-
-        if [ $CHECK -ne 0 ] && [ $CHECK2 -eq 0 ] ; then                    ### Added a condition
-            eval `echo args$i`=`cygpath --path --ignore --mixed "$arg"`
-        else
-            eval `echo args$i`="\"$arg\""
-        fi
-        i=$((i+1))
-    done
-    case $i in
-        (0) set -- ;;
-        (1) set -- "$args0" ;;
-        (2) set -- "$args0" "$args1" ;;
-        (3) set -- "$args0" "$args1" "$args2" ;;
-        (4) set -- "$args0" "$args1" "$args2" "$args3" ;;
-        (5) set -- "$args0" "$args1" "$args2" "$args3" "$args4" ;;
-        (6) set -- "$args0" "$args1" "$args2" "$args3" "$args4" "$args5" ;;
-        (7) set -- "$args0" "$args1" "$args2" "$args3" "$args4" "$args5" "$args6" ;;
-        (8) set -- "$args0" "$args1" "$args2" "$args3" "$args4" "$args5" "$args6" "$args7" ;;
-        (9) set -- "$args0" "$args1" "$args2" "$args3" "$args4" "$args5" "$args6" "$args7" "$args8" ;;
-    esac
-fi
-
-# Split up the JVM_OPTS And GRADLE_OPTS values into an array, following the shell quoting and substitution rules
-function splitJvmOpts() {
-    JVM_OPTS=("$@")
-}
-eval splitJvmOpts $DEFAULT_JVM_OPTS $JAVA_OPTS $GRADLE_OPTS
-JVM_OPTS[${#JVM_OPTS[*]}]="-Dorg.gradle.appname=$APP_BASE_NAME"
-
-exec "$JAVACMD" "${JVM_OPTS[@]}" -classpath "$CLASSPATH" org.gradle.wrapper.GradleWrapperMain "$@"
diff --git a/node_modules/react-native-root-toast/Example/android/gradlew.bat b/node_modules/react-native-root-toast/Example/android/gradlew.bat
deleted file mode 100644
index 8a0b282..0000000
--- a/node_modules/react-native-root-toast/Example/android/gradlew.bat
+++ /dev/null
@@ -1,90 +0,0 @@
-@if "%DEBUG%" == "" @echo off
-@rem ##########################################################################
-@rem
-@rem  Gradle startup script for Windows
-@rem
-@rem ##########################################################################
-
-@rem Set local scope for the variables with windows NT shell
-if "%OS%"=="Windows_NT" setlocal
-
-@rem Add default JVM options here. You can also use JAVA_OPTS and GRADLE_OPTS to pass JVM options to this script.
-set DEFAULT_JVM_OPTS=
-
-set DIRNAME=%~dp0
-if "%DIRNAME%" == "" set DIRNAME=.
-set APP_BASE_NAME=%~n0
-set APP_HOME=%DIRNAME%
-
-@rem Find java.exe
-if defined JAVA_HOME goto findJavaFromJavaHome
-
-set JAVA_EXE=java.exe
-%JAVA_EXE% -version >NUL 2>&1
-if "%ERRORLEVEL%" == "0" goto init
-
-echo.
-echo ERROR: JAVA_HOME is not set and no 'java' command could be found in your PATH.
-echo.
-echo Please set the JAVA_HOME variable in your environment to match the
-echo location of your Java installation.
-
-goto fail
-
-:findJavaFromJavaHome
-set JAVA_HOME=%JAVA_HOME:"=%
-set JAVA_EXE=%JAVA_HOME%/bin/java.exe
-
-if exist "%JAVA_EXE%" goto init
-
-echo.
-echo ERROR: JAVA_HOME is set to an invalid directory: %JAVA_HOME%
-echo.
-echo Please set the JAVA_HOME variable in your environment to match the
-echo location of your Java installation.
-
-goto fail
-
-:init
-@rem Get command-line arguments, handling Windowz variants
-
-if not "%OS%" == "Windows_NT" goto win9xME_args
-if "%@eval[2+2]" == "4" goto 4NT_args
-
-:win9xME_args
-@rem Slurp the command line arguments.
-set CMD_LINE_ARGS=
-set _SKIP=2
-
-:win9xME_args_slurp
-if "x%~1" == "x" goto execute
-
-set CMD_LINE_ARGS=%*
-goto execute
-
-:4NT_args
-@rem Get arguments from the 4NT Shell from JP Software
-set CMD_LINE_ARGS=%$
-
-:execute
-@rem Setup the command line
-
-set CLASSPATH=%APP_HOME%\gradle\wrapper\gradle-wrapper.jar
-
-@rem Execute Gradle
-"%JAVA_EXE%" %DEFAULT_JVM_OPTS% %JAVA_OPTS% %GRADLE_OPTS% "-Dorg.gradle.appname=%APP_BASE_NAME%" -classpath "%CLASSPATH%" org.gradle.wrapper.GradleWrapperMain %CMD_LINE_ARGS%
-
-:end
-@rem End local scope for the variables with windows NT shell
-if "%ERRORLEVEL%"=="0" goto mainEnd
-
-:fail
-rem Set variable GRADLE_EXIT_CONSOLE if you need the _script_ return code instead of
-rem the _cmd.exe /c_ return code!
-if  not "" == "%GRADLE_EXIT_CONSOLE%" exit 1
-exit /b 1
-
-:mainEnd
-if "%OS%"=="Windows_NT" endlocal
-
-:omega
diff --git a/node_modules/react-native-root-toast/Example/android/settings.gradle b/node_modules/react-native-root-toast/Example/android/settings.gradle
deleted file mode 100644
index b0bbd58..0000000
--- a/node_modules/react-native-root-toast/Example/android/settings.gradle
+++ /dev/null
@@ -1,3 +0,0 @@
-rootProject.name = 'ReactNativeRootToast'
-
-include ':app'
diff --git a/node_modules/react-native-root-toast/Example/index.android.js b/node_modules/react-native-root-toast/Example/index.android.js
deleted file mode 100644
index ce89878..0000000
--- a/node_modules/react-native-root-toast/Example/index.android.js
+++ /dev/null
@@ -1 +0,0 @@
-import './main';
diff --git a/node_modules/react-native-root-toast/Example/index.ios.js b/node_modules/react-native-root-toast/Example/index.ios.js
deleted file mode 100644
index ce89878..0000000
--- a/node_modules/react-native-root-toast/Example/index.ios.js
+++ /dev/null
@@ -1 +0,0 @@
-import './main';
diff --git a/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast.xcodeproj/project.pbxproj b/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast.xcodeproj/project.pbxproj
deleted file mode 100644
index d6a26ed..0000000
--- a/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast.xcodeproj/project.pbxproj
+++ /dev/null
@@ -1,763 +0,0 @@
-// !$*UTF8*$!
-{
-	archiveVersion = 1;
-	classes = {
-	};
-	objectVersion = 46;
-	objects = {
-
-/* Begin PBXBuildFile section */
-		00C302E51ABCBA2D00DB3ED1 /* libRCTActionSheet.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302AC1ABCB8CE00DB3ED1 /* libRCTActionSheet.a */; };
-		00C302E71ABCBA2D00DB3ED1 /* libRCTGeolocation.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302BA1ABCB90400DB3ED1 /* libRCTGeolocation.a */; };
-		00C302E81ABCBA2D00DB3ED1 /* libRCTImage.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302C01ABCB91800DB3ED1 /* libRCTImage.a */; };
-		00C302E91ABCBA2D00DB3ED1 /* libRCTNetwork.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302DC1ABCB9D200DB3ED1 /* libRCTNetwork.a */; };
-		00C302EA1ABCBA2D00DB3ED1 /* libRCTVibration.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 00C302E41ABCB9EE00DB3ED1 /* libRCTVibration.a */; };
-		00E356F31AD99517003FC87E /* ReactNativeRootToastTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* ReactNativeRootToastTests.m */; };
-		133E29F31AD74F7200F7D852 /* libRCTLinking.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 78C398B91ACF4ADC00677621 /* libRCTLinking.a */; };
-		139105C61AF99C1200B5F7CC /* libRCTSettings.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 139105C11AF99BAD00B5F7CC /* libRCTSettings.a */; };
-		139FDEF61B0652A700C62182 /* libRCTWebSocket.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 139FDEF41B06529B00C62182 /* libRCTWebSocket.a */; };
-		13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
-		13B07FBD1A68108700A75B9A /* LaunchScreen.xib in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB11A68108700A75B9A /* LaunchScreen.xib */; };
-		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
-		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
-		146834051AC3E58100842450 /* libReact.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 146834041AC3E56700842450 /* libReact.a */; };
-		832341BD1AAA6AB300B99B32 /* libRCTText.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 832341B51AAA6A8300B99B32 /* libRCTText.a */; };
-/* End PBXBuildFile section */
-
-/* Begin PBXContainerItemProxy section */
-		00C302AB1ABCB8CE00DB3ED1 /* PBXContainerItemProxy */ = {
-			isa = PBXContainerItemProxy;
-			containerPortal = 00C302A71ABCB8CE00DB3ED1 /* RCTActionSheet.xcodeproj */;
-			proxyType = 2;
-			remoteGlobalIDString = 134814201AA4EA6300B7C361;
-			remoteInfo = RCTActionSheet;
-		};
-		00C302B91ABCB90400DB3ED1 /* PBXContainerItemProxy */ = {
-			isa = PBXContainerItemProxy;
-			containerPortal = 00C302B51ABCB90400DB3ED1 /* RCTGeolocation.xcodeproj */;
-			proxyType = 2;
-			remoteGlobalIDString = 134814201AA4EA6300B7C361;
-			remoteInfo = RCTGeolocation;
-		};
-		00C302BF1ABCB91800DB3ED1 /* PBXContainerItemProxy */ = {
-			isa = PBXContainerItemProxy;
-			containerPortal = 00C302BB1ABCB91800DB3ED1 /* RCTImage.xcodeproj */;
-			proxyType = 2;
-			remoteGlobalIDString = 58B5115D1A9E6B3D00147676;
-			remoteInfo = RCTImage;
-		};
-		00C302DB1ABCB9D200DB3ED1 /* PBXContainerItemProxy */ = {
-			isa = PBXContainerItemProxy;
-			containerPortal = 00C302D31ABCB9D200DB3ED1 /* RCTNetwork.xcodeproj */;
-			proxyType = 2;
-			remoteGlobalIDString = 58B511DB1A9E6C8500147676;
-			remoteInfo = RCTNetwork;
-		};
-		00C302E31ABCB9EE00DB3ED1 /* PBXContainerItemProxy */ = {
-			isa = PBXContainerItemProxy;
-			containerPortal = 00C302DF1ABCB9EE00DB3ED1 /* RCTVibration.xcodeproj */;
-			proxyType = 2;
-			remoteGlobalIDString = 832C81801AAF6DEF007FA2F7;
-			remoteInfo = RCTVibration;
-		};
-		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
-			isa = PBXContainerItemProxy;
-			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
-			proxyType = 1;
-			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
-			remoteInfo = ReactNativeRootToast;
-		};
-		139105C01AF99BAD00B5F7CC /* PBXContainerItemProxy */ = {
-			isa = PBXContainerItemProxy;
-			containerPortal = 139105B61AF99BAD00B5F7CC /* RCTSettings.xcodeproj */;
-			proxyType = 2;
-			remoteGlobalIDString = 134814201AA4EA6300B7C361;
-			remoteInfo = RCTSettings;
-		};
-		139FDEF31B06529B00C62182 /* PBXContainerItemProxy */ = {
-			isa = PBXContainerItemProxy;
-			containerPortal = 139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */;
-			proxyType = 2;
-			remoteGlobalIDString = 3C86DF461ADF2C930047B81A;
-			remoteInfo = RCTWebSocket;
-		};
-		146834031AC3E56700842450 /* PBXContainerItemProxy */ = {
-			isa = PBXContainerItemProxy;
-			containerPortal = 146833FF1AC3E56700842450 /* React.xcodeproj */;
-			proxyType = 2;
-			remoteGlobalIDString = 83CBBA2E1A601D0E00E9B192;
-			remoteInfo = React;
-		};
-		78C398B81ACF4ADC00677621 /* PBXContainerItemProxy */ = {
-			isa = PBXContainerItemProxy;
-			containerPortal = 78C398B01ACF4ADC00677621 /* RCTLinking.xcodeproj */;
-			proxyType = 2;
-			remoteGlobalIDString = 134814201AA4EA6300B7C361;
-			remoteInfo = RCTLinking;
-		};
-		832341B41AAA6A8300B99B32 /* PBXContainerItemProxy */ = {
-			isa = PBXContainerItemProxy;
-			containerPortal = 832341B01AAA6A8300B99B32 /* RCTText.xcodeproj */;
-			proxyType = 2;
-			remoteGlobalIDString = 58B5119B1A9E6C1200147676;
-			remoteInfo = RCTText;
-		};
-/* End PBXContainerItemProxy section */
-
-/* Begin PBXFileReference section */
-		008F07F21AC5B25A0029DE68 /* main.jsbundle */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; name = main.jsbundle; path = main.jsbundle; sourceTree = "<group>"; };
-		00C302A71ABCB8CE00DB3ED1 /* RCTActionSheet.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTActionSheet.xcodeproj; path = ../node_modules/react-native/Libraries/ActionSheetIOS/RCTActionSheet.xcodeproj; sourceTree = "<group>"; };
-		00C302B51ABCB90400DB3ED1 /* RCTGeolocation.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTGeolocation.xcodeproj; path = ../node_modules/react-native/Libraries/Geolocation/RCTGeolocation.xcodeproj; sourceTree = "<group>"; };
-		00C302BB1ABCB91800DB3ED1 /* RCTImage.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTImage.xcodeproj; path = ../node_modules/react-native/Libraries/Image/RCTImage.xcodeproj; sourceTree = "<group>"; };
-		00C302D31ABCB9D200DB3ED1 /* RCTNetwork.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTNetwork.xcodeproj; path = ../node_modules/react-native/Libraries/Network/RCTNetwork.xcodeproj; sourceTree = "<group>"; };
-		00C302DF1ABCB9EE00DB3ED1 /* RCTVibration.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTVibration.xcodeproj; path = ../node_modules/react-native/Libraries/Vibration/RCTVibration.xcodeproj; sourceTree = "<group>"; };
-		00E356EE1AD99517003FC87E /* ReactNativeRootToastTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = ReactNativeRootToastTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
-		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
-		00E356F21AD99517003FC87E /* ReactNativeRootToastTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ReactNativeRootToastTests.m; sourceTree = "<group>"; };
-		139105B61AF99BAD00B5F7CC /* RCTSettings.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTSettings.xcodeproj; path = ../node_modules/react-native/Libraries/Settings/RCTSettings.xcodeproj; sourceTree = "<group>"; };
-		139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTWebSocket.xcodeproj; path = ../node_modules/react-native/Libraries/WebSocket/RCTWebSocket.xcodeproj; sourceTree = "<group>"; };
-		13B07F961A680F5B00A75B9A /* ReactNativeRootToast.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = ReactNativeRootToast.app; sourceTree = BUILT_PRODUCTS_DIR; };
-		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = ReactNativeRootToast/AppDelegate.h; sourceTree = "<group>"; };
-		13B07FB01A68108700A75B9A /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AppDelegate.m; path = ReactNativeRootToast/AppDelegate.m; sourceTree = "<group>"; };
-		13B07FB21A68108700A75B9A /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/LaunchScreen.xib; sourceTree = "<group>"; };
-		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = ReactNativeRootToast/Images.xcassets; sourceTree = "<group>"; };
-		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = ReactNativeRootToast/Info.plist; sourceTree = "<group>"; };
-		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = ReactNativeRootToast/main.m; sourceTree = "<group>"; };
-		146833FF1AC3E56700842450 /* React.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = React.xcodeproj; path = ../node_modules/react-native/React/React.xcodeproj; sourceTree = "<group>"; };
-		78C398B01ACF4ADC00677621 /* RCTLinking.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTLinking.xcodeproj; path = ../node_modules/react-native/Libraries/LinkingIOS/RCTLinking.xcodeproj; sourceTree = "<group>"; };
-		832341B01AAA6A8300B99B32 /* RCTText.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTText.xcodeproj; path = ../node_modules/react-native/Libraries/Text/RCTText.xcodeproj; sourceTree = "<group>"; };
-/* End PBXFileReference section */
-
-/* Begin PBXFrameworksBuildPhase section */
-		00E356EB1AD99517003FC87E /* Frameworks */ = {
-			isa = PBXFrameworksBuildPhase;
-			buildActionMask = 2147483647;
-			files = (
-			);
-			runOnlyForDeploymentPostprocessing = 0;
-		};
-		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
-			isa = PBXFrameworksBuildPhase;
-			buildActionMask = 2147483647;
-			files = (
-				146834051AC3E58100842450 /* libReact.a in Frameworks */,
-				00C302E51ABCBA2D00DB3ED1 /* libRCTActionSheet.a in Frameworks */,
-				00C302E71ABCBA2D00DB3ED1 /* libRCTGeolocation.a in Frameworks */,
-				00C302E81ABCBA2D00DB3ED1 /* libRCTImage.a in Frameworks */,
-				133E29F31AD74F7200F7D852 /* libRCTLinking.a in Frameworks */,
-				00C302E91ABCBA2D00DB3ED1 /* libRCTNetwork.a in Frameworks */,
-				139105C61AF99C1200B5F7CC /* libRCTSettings.a in Frameworks */,
-				832341BD1AAA6AB300B99B32 /* libRCTText.a in Frameworks */,
-				00C302EA1ABCBA2D00DB3ED1 /* libRCTVibration.a in Frameworks */,
-				139FDEF61B0652A700C62182 /* libRCTWebSocket.a in Frameworks */,
-			);
-			runOnlyForDeploymentPostprocessing = 0;
-		};
-/* End PBXFrameworksBuildPhase section */
-
-/* Begin PBXGroup section */
-		00C302A81ABCB8CE00DB3ED1 /* Products */ = {
-			isa = PBXGroup;
-			children = (
-				00C302AC1ABCB8CE00DB3ED1 /* libRCTActionSheet.a */,
-			);
-			name = Products;
-			sourceTree = "<group>";
-		};
-		00C302B61ABCB90400DB3ED1 /* Products */ = {
-			isa = PBXGroup;
-			children = (
-				00C302BA1ABCB90400DB3ED1 /* libRCTGeolocation.a */,
-			);
-			name = Products;
-			sourceTree = "<group>";
-		};
-		00C302BC1ABCB91800DB3ED1 /* Products */ = {
-			isa = PBXGroup;
-			children = (
-				00C302C01ABCB91800DB3ED1 /* libRCTImage.a */,
-			);
-			name = Products;
-			sourceTree = "<group>";
-		};
-		00C302D41ABCB9D200DB3ED1 /* Products */ = {
-			isa = PBXGroup;
-			children = (
-				00C302DC1ABCB9D200DB3ED1 /* libRCTNetwork.a */,
-			);
-			name = Products;
-			sourceTree = "<group>";
-		};
-		00C302E01ABCB9EE00DB3ED1 /* Products */ = {
-			isa = PBXGroup;
-			children = (
-				00C302E41ABCB9EE00DB3ED1 /* libRCTVibration.a */,
-			);
-			name = Products;
-			sourceTree = "<group>";
-		};
-		00E356EF1AD99517003FC87E /* ReactNativeRootToastTests */ = {
-			isa = PBXGroup;
-			children = (
-				00E356F21AD99517003FC87E /* ReactNativeRootToastTests.m */,
-				00E356F01AD99517003FC87E /* Supporting Files */,
-			);
-			path = ReactNativeRootToastTests;
-			sourceTree = "<group>";
-		};
-		00E356F01AD99517003FC87E /* Supporting Files */ = {
-			isa = PBXGroup;
-			children = (
-				00E356F11AD99517003FC87E /* Info.plist */,
-			);
-			name = "Supporting Files";
-			sourceTree = "<group>";
-		};
-		139105B71AF99BAD00B5F7CC /* Products */ = {
-			isa = PBXGroup;
-			children = (
-				139105C11AF99BAD00B5F7CC /* libRCTSettings.a */,
-			);
-			name = Products;
-			sourceTree = "<group>";
-		};
-		139FDEE71B06529A00C62182 /* Products */ = {
-			isa = PBXGroup;
-			children = (
-				139FDEF41B06529B00C62182 /* libRCTWebSocket.a */,
-			);
-			name = Products;
-			sourceTree = "<group>";
-		};
-		13B07FAE1A68108700A75B9A /* ReactNativeRootToast */ = {
-			isa = PBXGroup;
-			children = (
-				008F07F21AC5B25A0029DE68 /* main.jsbundle */,
-				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
-				13B07FB01A68108700A75B9A /* AppDelegate.m */,
-				13B07FB51A68108700A75B9A /* Images.xcassets */,
-				13B07FB61A68108700A75B9A /* Info.plist */,
-				13B07FB11A68108700A75B9A /* LaunchScreen.xib */,
-				13B07FB71A68108700A75B9A /* main.m */,
-			);
-			name = ReactNativeRootToast;
-			sourceTree = "<group>";
-		};
-		146834001AC3E56700842450 /* Products */ = {
-			isa = PBXGroup;
-			children = (
-				146834041AC3E56700842450 /* libReact.a */,
-			);
-			name = Products;
-			sourceTree = "<group>";
-		};
-		78C398B11ACF4ADC00677621 /* Products */ = {
-			isa = PBXGroup;
-			children = (
-				78C398B91ACF4ADC00677621 /* libRCTLinking.a */,
-			);
-			name = Products;
-			sourceTree = "<group>";
-		};
-		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
-			isa = PBXGroup;
-			children = (
-				146833FF1AC3E56700842450 /* React.xcodeproj */,
-				00C302A71ABCB8CE00DB3ED1 /* RCTActionSheet.xcodeproj */,
-				00C302B51ABCB90400DB3ED1 /* RCTGeolocation.xcodeproj */,
-				00C302BB1ABCB91800DB3ED1 /* RCTImage.xcodeproj */,
-				78C398B01ACF4ADC00677621 /* RCTLinking.xcodeproj */,
-				00C302D31ABCB9D200DB3ED1 /* RCTNetwork.xcodeproj */,
-				139105B61AF99BAD00B5F7CC /* RCTSettings.xcodeproj */,
-				832341B01AAA6A8300B99B32 /* RCTText.xcodeproj */,
-				00C302DF1ABCB9EE00DB3ED1 /* RCTVibration.xcodeproj */,
-				139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */,
-			);
-			name = Libraries;
-			sourceTree = "<group>";
-		};
-		832341B11AAA6A8300B99B32 /* Products */ = {
-			isa = PBXGroup;
-			children = (
-				832341B51AAA6A8300B99B32 /* libRCTText.a */,
-			);
-			name = Products;
-			sourceTree = "<group>";
-		};
-		83CBB9F61A601CBA00E9B192 = {
-			isa = PBXGroup;
-			children = (
-				13B07FAE1A68108700A75B9A /* ReactNativeRootToast */,
-				832341AE1AAA6A7D00B99B32 /* Libraries */,
-				00E356EF1AD99517003FC87E /* ReactNativeRootToastTests */,
-				83CBBA001A601CBA00E9B192 /* Products */,
-			);
-			indentWidth = 2;
-			sourceTree = "<group>";
-			tabWidth = 2;
-		};
-		83CBBA001A601CBA00E9B192 /* Products */ = {
-			isa = PBXGroup;
-			children = (
-				13B07F961A680F5B00A75B9A /* ReactNativeRootToast.app */,
-				00E356EE1AD99517003FC87E /* ReactNativeRootToastTests.xctest */,
-			);
-			name = Products;
-			sourceTree = "<group>";
-		};
-/* End PBXGroup section */
-
-/* Begin PBXNativeTarget section */
-		00E356ED1AD99517003FC87E /* ReactNativeRootToastTests */ = {
-			isa = PBXNativeTarget;
-			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "ReactNativeRootToastTests" */;
-			buildPhases = (
-				00E356EA1AD99517003FC87E /* Sources */,
-				00E356EB1AD99517003FC87E /* Frameworks */,
-				00E356EC1AD99517003FC87E /* Resources */,
-			);
-			buildRules = (
-			);
-			dependencies = (
-				00E356F51AD99517003FC87E /* PBXTargetDependency */,
-			);
-			name = ReactNativeRootToastTests;
-			productName = ReactNativeRootToastTests;
-			productReference = 00E356EE1AD99517003FC87E /* ReactNativeRootToastTests.xctest */;
-			productType = "com.apple.product-type.bundle.unit-test";
-		};
-		13B07F861A680F5B00A75B9A /* ReactNativeRootToast */ = {
-			isa = PBXNativeTarget;
-			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "ReactNativeRootToast" */;
-			buildPhases = (
-				13B07F871A680F5B00A75B9A /* Sources */,
-				13B07F8C1A680F5B00A75B9A /* Frameworks */,
-				13B07F8E1A680F5B00A75B9A /* Resources */,
-				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
-			);
-			buildRules = (
-			);
-			dependencies = (
-			);
-			name = ReactNativeRootToast;
-			productName = "Hello World";
-			productReference = 13B07F961A680F5B00A75B9A /* ReactNativeRootToast.app */;
-			productType = "com.apple.product-type.application";
-		};
-/* End PBXNativeTarget section */
-
-/* Begin PBXProject section */
-		83CBB9F71A601CBA00E9B192 /* Project object */ = {
-			isa = PBXProject;
-			attributes = {
-				LastUpgradeCheck = 0610;
-				ORGANIZATIONNAME = Facebook;
-				TargetAttributes = {
-					00E356ED1AD99517003FC87E = {
-						CreatedOnToolsVersion = 6.2;
-						TestTargetID = 13B07F861A680F5B00A75B9A;
-					};
-				};
-			};
-			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "ReactNativeRootToast" */;
-			compatibilityVersion = "Xcode 3.2";
-			developmentRegion = English;
-			hasScannedForEncodings = 0;
-			knownRegions = (
-				en,
-				Base,
-			);
-			mainGroup = 83CBB9F61A601CBA00E9B192;
-			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
-			projectDirPath = "";
-			projectReferences = (
-				{
-					ProductGroup = 00C302A81ABCB8CE00DB3ED1 /* Products */;
-					ProjectRef = 00C302A71ABCB8CE00DB3ED1 /* RCTActionSheet.xcodeproj */;
-				},
-				{
-					ProductGroup = 00C302B61ABCB90400DB3ED1 /* Products */;
-					ProjectRef = 00C302B51ABCB90400DB3ED1 /* RCTGeolocation.xcodeproj */;
-				},
-				{
-					ProductGroup = 00C302BC1ABCB91800DB3ED1 /* Products */;
-					ProjectRef = 00C302BB1ABCB91800DB3ED1 /* RCTImage.xcodeproj */;
-				},
-				{
-					ProductGroup = 78C398B11ACF4ADC00677621 /* Products */;
-					ProjectRef = 78C398B01ACF4ADC00677621 /* RCTLinking.xcodeproj */;
-				},
-				{
-					ProductGroup = 00C302D41ABCB9D200DB3ED1 /* Products */;
-					ProjectRef = 00C302D31ABCB9D200DB3ED1 /* RCTNetwork.xcodeproj */;
-				},
-				{
-					ProductGroup = 139105B71AF99BAD00B5F7CC /* Products */;
-					ProjectRef = 139105B61AF99BAD00B5F7CC /* RCTSettings.xcodeproj */;
-				},
-				{
-					ProductGroup = 832341B11AAA6A8300B99B32 /* Products */;
-					ProjectRef = 832341B01AAA6A8300B99B32 /* RCTText.xcodeproj */;
-				},
-				{
-					ProductGroup = 00C302E01ABCB9EE00DB3ED1 /* Products */;
-					ProjectRef = 00C302DF1ABCB9EE00DB3ED1 /* RCTVibration.xcodeproj */;
-				},
-				{
-					ProductGroup = 139FDEE71B06529A00C62182 /* Products */;
-					ProjectRef = 139FDEE61B06529A00C62182 /* RCTWebSocket.xcodeproj */;
-				},
-				{
-					ProductGroup = 146834001AC3E56700842450 /* Products */;
-					ProjectRef = 146833FF1AC3E56700842450 /* React.xcodeproj */;
-				},
-			);
-			projectRoot = "";
-			targets = (
-				13B07F861A680F5B00A75B9A /* ReactNativeRootToast */,
-				00E356ED1AD99517003FC87E /* ReactNativeRootToastTests */,
-			);
-		};
-/* End PBXProject section */
-
-/* Begin PBXReferenceProxy section */
-		00C302AC1ABCB8CE00DB3ED1 /* libRCTActionSheet.a */ = {
-			isa = PBXReferenceProxy;
-			fileType = archive.ar;
-			path = libRCTActionSheet.a;
-			remoteRef = 00C302AB1ABCB8CE00DB3ED1 /* PBXContainerItemProxy */;
-			sourceTree = BUILT_PRODUCTS_DIR;
-		};
-		00C302BA1ABCB90400DB3ED1 /* libRCTGeolocation.a */ = {
-			isa = PBXReferenceProxy;
-			fileType = archive.ar;
-			path = libRCTGeolocation.a;
-			remoteRef = 00C302B91ABCB90400DB3ED1 /* PBXContainerItemProxy */;
-			sourceTree = BUILT_PRODUCTS_DIR;
-		};
-		00C302C01ABCB91800DB3ED1 /* libRCTImage.a */ = {
-			isa = PBXReferenceProxy;
-			fileType = archive.ar;
-			path = libRCTImage.a;
-			remoteRef = 00C302BF1ABCB91800DB3ED1 /* PBXContainerItemProxy */;
-			sourceTree = BUILT_PRODUCTS_DIR;
-		};
-		00C302DC1ABCB9D200DB3ED1 /* libRCTNetwork.a */ = {
-			isa = PBXReferenceProxy;
-			fileType = archive.ar;
-			path = libRCTNetwork.a;
-			remoteRef = 00C302DB1ABCB9D200DB3ED1 /* PBXContainerItemProxy */;
-			sourceTree = BUILT_PRODUCTS_DIR;
-		};
-		00C302E41ABCB9EE00DB3ED1 /* libRCTVibration.a */ = {
-			isa = PBXReferenceProxy;
-			fileType = archive.ar;
-			path = libRCTVibration.a;
-			remoteRef = 00C302E31ABCB9EE00DB3ED1 /* PBXContainerItemProxy */;
-			sourceTree = BUILT_PRODUCTS_DIR;
-		};
-		139105C11AF99BAD00B5F7CC /* libRCTSettings.a */ = {
-			isa = PBXReferenceProxy;
-			fileType = archive.ar;
-			path = libRCTSettings.a;
-			remoteRef = 139105C01AF99BAD00B5F7CC /* PBXContainerItemProxy */;
-			sourceTree = BUILT_PRODUCTS_DIR;
-		};
-		139FDEF41B06529B00C62182 /* libRCTWebSocket.a */ = {
-			isa = PBXReferenceProxy;
-			fileType = archive.ar;
-			path = libRCTWebSocket.a;
-			remoteRef = 139FDEF31B06529B00C62182 /* PBXContainerItemProxy */;
-			sourceTree = BUILT_PRODUCTS_DIR;
-		};
-		146834041AC3E56700842450 /* libReact.a */ = {
-			isa = PBXReferenceProxy;
-			fileType = archive.ar;
-			path = libReact.a;
-			remoteRef = 146834031AC3E56700842450 /* PBXContainerItemProxy */;
-			sourceTree = BUILT_PRODUCTS_DIR;
-		};
-		78C398B91ACF4ADC00677621 /* libRCTLinking.a */ = {
-			isa = PBXReferenceProxy;
-			fileType = archive.ar;
-			path = libRCTLinking.a;
-			remoteRef = 78C398B81ACF4ADC00677621 /* PBXContainerItemProxy */;
-			sourceTree = BUILT_PRODUCTS_DIR;
-		};
-		832341B51AAA6A8300B99B32 /* libRCTText.a */ = {
-			isa = PBXReferenceProxy;
-			fileType = archive.ar;
-			path = libRCTText.a;
-			remoteRef = 832341B41AAA6A8300B99B32 /* PBXContainerItemProxy */;
-			sourceTree = BUILT_PRODUCTS_DIR;
-		};
-/* End PBXReferenceProxy section */
-
-/* Begin PBXResourcesBuildPhase section */
-		00E356EC1AD99517003FC87E /* Resources */ = {
-			isa = PBXResourcesBuildPhase;
-			buildActionMask = 2147483647;
-			files = (
-			);
-			runOnlyForDeploymentPostprocessing = 0;
-		};
-		13B07F8E1A680F5B00A75B9A /* Resources */ = {
-			isa = PBXResourcesBuildPhase;
-			buildActionMask = 2147483647;
-			files = (
-				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
-				13B07FBD1A68108700A75B9A /* LaunchScreen.xib in Resources */,
-			);
-			runOnlyForDeploymentPostprocessing = 0;
-		};
-/* End PBXResourcesBuildPhase section */
-
-/* Begin PBXShellScriptBuildPhase section */
-		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
-			isa = PBXShellScriptBuildPhase;
-			buildActionMask = 2147483647;
-			files = (
-			);
-			inputPaths = (
-			);
-			name = "Bundle React Native code and images";
-			outputPaths = (
-			);
-			runOnlyForDeploymentPostprocessing = 0;
-			shellPath = /bin/sh;
-			shellScript = "export NODE_BINARY=node\n../node_modules/react-native/packager/react-native-xcode.sh";
-			showEnvVarsInLog = 1;
-		};
-/* End PBXShellScriptBuildPhase section */
-
-/* Begin PBXSourcesBuildPhase section */
-		00E356EA1AD99517003FC87E /* Sources */ = {
-			isa = PBXSourcesBuildPhase;
-			buildActionMask = 2147483647;
-			files = (
-				00E356F31AD99517003FC87E /* ReactNativeRootToastTests.m in Sources */,
-			);
-			runOnlyForDeploymentPostprocessing = 0;
-		};
-		13B07F871A680F5B00A75B9A /* Sources */ = {
-			isa = PBXSourcesBuildPhase;
-			buildActionMask = 2147483647;
-			files = (
-				13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */,
-				13B07FC11A68108700A75B9A /* main.m in Sources */,
-			);
-			runOnlyForDeploymentPostprocessing = 0;
-		};
-/* End PBXSourcesBuildPhase section */
-
-/* Begin PBXTargetDependency section */
-		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
-			isa = PBXTargetDependency;
-			target = 13B07F861A680F5B00A75B9A /* ReactNativeRootToast */;
-			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
-		};
-/* End PBXTargetDependency section */
-
-/* Begin PBXVariantGroup section */
-		13B07FB11A68108700A75B9A /* LaunchScreen.xib */ = {
-			isa = PBXVariantGroup;
-			children = (
-				13B07FB21A68108700A75B9A /* Base */,
-			);
-			name = LaunchScreen.xib;
-			path = ReactNativeRootToast;
-			sourceTree = "<group>";
-		};
-/* End PBXVariantGroup section */
-
-/* Begin XCBuildConfiguration section */
-		00E356F61AD99517003FC87E /* Debug */ = {
-			isa = XCBuildConfiguration;
-			buildSettings = {
-				BUNDLE_LOADER = "$(TEST_HOST)";
-				FRAMEWORK_SEARCH_PATHS = (
-					"$(SDKROOT)/Developer/Library/Frameworks",
-					"$(inherited)",
-				);
-				GCC_PREPROCESSOR_DEFINITIONS = (
-					"DEBUG=1",
-					"$(inherited)",
-				);
-				INFOPLIST_FILE = ReactNativeRootToastTests/Info.plist;
-				IPHONEOS_DEPLOYMENT_TARGET = 8.2;
-				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
-				PRODUCT_NAME = "$(TARGET_NAME)";
-				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/ReactNativeRootToast.app/ReactNativeRootToast";
-			};
-			name = Debug;
-		};
-		00E356F71AD99517003FC87E /* Release */ = {
-			isa = XCBuildConfiguration;
-			buildSettings = {
-				BUNDLE_LOADER = "$(TEST_HOST)";
-				COPY_PHASE_STRIP = NO;
-				FRAMEWORK_SEARCH_PATHS = (
-					"$(SDKROOT)/Developer/Library/Frameworks",
-					"$(inherited)",
-				);
-				INFOPLIST_FILE = ReactNativeRootToastTests/Info.plist;
-				IPHONEOS_DEPLOYMENT_TARGET = 8.2;
-				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
-				PRODUCT_NAME = "$(TARGET_NAME)";
-				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/ReactNativeRootToast.app/ReactNativeRootToast";
-			};
-			name = Release;
-		};
-		13B07F941A680F5B00A75B9A /* Debug */ = {
-			isa = XCBuildConfiguration;
-			buildSettings = {
-				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
-				DEAD_CODE_STRIPPING = NO;
-				HEADER_SEARCH_PATHS = (
-					"$(inherited)",
-					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
-					"$(SRCROOT)/../node_modules/react-native/React/**",
-				);
-				INFOPLIST_FILE = "ReactNativeRootToast/Info.plist";
-				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
-				OTHER_LDFLAGS = "-ObjC";
-				PRODUCT_NAME = ReactNativeRootToast;
-			};
-			name = Debug;
-		};
-		13B07F951A680F5B00A75B9A /* Release */ = {
-			isa = XCBuildConfiguration;
-			buildSettings = {
-				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
-				HEADER_SEARCH_PATHS = (
-					"$(inherited)",
-					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
-					"$(SRCROOT)/../node_modules/react-native/React/**",
-				);
-				INFOPLIST_FILE = "ReactNativeRootToast/Info.plist";
-				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
-				OTHER_LDFLAGS = "-ObjC";
-				PRODUCT_NAME = ReactNativeRootToast;
-			};
-			name = Release;
-		};
-		83CBBA201A601CBA00E9B192 /* Debug */ = {
-			isa = XCBuildConfiguration;
-			buildSettings = {
-				ALWAYS_SEARCH_USER_PATHS = NO;
-				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
-				CLANG_CXX_LIBRARY = "libc++";
-				CLANG_ENABLE_MODULES = YES;
-				CLANG_ENABLE_OBJC_ARC = YES;
-				CLANG_WARN_BOOL_CONVERSION = YES;
-				CLANG_WARN_CONSTANT_CONVERSION = YES;
-				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
-				CLANG_WARN_EMPTY_BODY = YES;
-				CLANG_WARN_ENUM_CONVERSION = YES;
-				CLANG_WARN_INT_CONVERSION = YES;
-				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
-				CLANG_WARN_UNREACHABLE_CODE = YES;
-				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
-				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
-				COPY_PHASE_STRIP = NO;
-				ENABLE_STRICT_OBJC_MSGSEND = YES;
-				GCC_C_LANGUAGE_STANDARD = gnu99;
-				GCC_DYNAMIC_NO_PIC = NO;
-				GCC_OPTIMIZATION_LEVEL = 0;
-				GCC_PREPROCESSOR_DEFINITIONS = (
-					"DEBUG=1",
-					"$(inherited)",
-				);
-				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
-				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
-				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
-				GCC_WARN_UNDECLARED_SELECTOR = YES;
-				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
-				GCC_WARN_UNUSED_FUNCTION = YES;
-				GCC_WARN_UNUSED_VARIABLE = YES;
-				HEADER_SEARCH_PATHS = (
-					"$(inherited)",
-					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
-					"$(SRCROOT)/../node_modules/react-native/React/**",
-				);
-				IPHONEOS_DEPLOYMENT_TARGET = 7.0;
-				MTL_ENABLE_DEBUG_INFO = YES;
-				ONLY_ACTIVE_ARCH = YES;
-				SDKROOT = iphoneos;
-			};
-			name = Debug;
-		};
-		83CBBA211A601CBA00E9B192 /* Release */ = {
-			isa = XCBuildConfiguration;
-			buildSettings = {
-				ALWAYS_SEARCH_USER_PATHS = NO;
-				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
-				CLANG_CXX_LIBRARY = "libc++";
-				CLANG_ENABLE_MODULES = YES;
-				CLANG_ENABLE_OBJC_ARC = YES;
-				CLANG_WARN_BOOL_CONVERSION = YES;
-				CLANG_WARN_CONSTANT_CONVERSION = YES;
-				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
-				CLANG_WARN_EMPTY_BODY = YES;
-				CLANG_WARN_ENUM_CONVERSION = YES;
-				CLANG_WARN_INT_CONVERSION = YES;
-				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
-				CLANG_WARN_UNREACHABLE_CODE = YES;
-				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
-				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
-				COPY_PHASE_STRIP = YES;
-				ENABLE_NS_ASSERTIONS = NO;
-				ENABLE_STRICT_OBJC_MSGSEND = YES;
-				GCC_C_LANGUAGE_STANDARD = gnu99;
-				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
-				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
-				GCC_WARN_UNDECLARED_SELECTOR = YES;
-				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
-				GCC_WARN_UNUSED_FUNCTION = YES;
-				GCC_WARN_UNUSED_VARIABLE = YES;
-				HEADER_SEARCH_PATHS = (
-					"$(inherited)",
-					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
-					"$(SRCROOT)/../node_modules/react-native/React/**",
-				);
-				IPHONEOS_DEPLOYMENT_TARGET = 7.0;
-				MTL_ENABLE_DEBUG_INFO = NO;
-				SDKROOT = iphoneos;
-				VALIDATE_PRODUCT = YES;
-			};
-			name = Release;
-		};
-/* End XCBuildConfiguration section */
-
-/* Begin XCConfigurationList section */
-		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "ReactNativeRootToastTests" */ = {
-			isa = XCConfigurationList;
-			buildConfigurations = (
-				00E356F61AD99517003FC87E /* Debug */,
-				00E356F71AD99517003FC87E /* Release */,
-			);
-			defaultConfigurationIsVisible = 0;
-			defaultConfigurationName = Release;
-		};
-		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "ReactNativeRootToast" */ = {
-			isa = XCConfigurationList;
-			buildConfigurations = (
-				13B07F941A680F5B00A75B9A /* Debug */,
-				13B07F951A680F5B00A75B9A /* Release */,
-			);
-			defaultConfigurationIsVisible = 0;
-			defaultConfigurationName = Release;
-		};
-		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "ReactNativeRootToast" */ = {
-			isa = XCConfigurationList;
-			buildConfigurations = (
-				83CBBA201A601CBA00E9B192 /* Debug */,
-				83CBBA211A601CBA00E9B192 /* Release */,
-			);
-			defaultConfigurationIsVisible = 0;
-			defaultConfigurationName = Release;
-		};
-/* End XCConfigurationList section */
-	};
-	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
-}
diff --git a/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast.xcodeproj/xcshareddata/xcschemes/ReactNativeRootToast.xcscheme b/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast.xcodeproj/xcshareddata/xcschemes/ReactNativeRootToast.xcscheme
deleted file mode 100644
index 0f29b0e..0000000
--- a/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast.xcodeproj/xcshareddata/xcschemes/ReactNativeRootToast.xcscheme
+++ /dev/null
@@ -1,112 +0,0 @@
-<?xml version="1.0" encoding="UTF-8"?>
-<Scheme
-   LastUpgradeVersion = "0620"
-   version = "1.3">
-   <BuildAction
-      parallelizeBuildables = "YES"
-      buildImplicitDependencies = "YES">
-      <BuildActionEntries>
-         <BuildActionEntry
-            buildForTesting = "YES"
-            buildForRunning = "YES"
-            buildForProfiling = "YES"
-            buildForArchiving = "YES"
-            buildForAnalyzing = "YES">
-            <BuildableReference
-               BuildableIdentifier = "primary"
-               BlueprintIdentifier = "13B07F861A680F5B00A75B9A"
-               BuildableName = "ReactNativeRootToast.app"
-               BlueprintName = "ReactNativeRootToast"
-               ReferencedContainer = "container:ReactNativeRootToast.xcodeproj">
-            </BuildableReference>
-         </BuildActionEntry>
-         <BuildActionEntry
-            buildForTesting = "YES"
-            buildForRunning = "YES"
-            buildForProfiling = "NO"
-            buildForArchiving = "NO"
-            buildForAnalyzing = "YES">
-            <BuildableReference
-               BuildableIdentifier = "primary"
-               BlueprintIdentifier = "00E356ED1AD99517003FC87E"
-               BuildableName = "ReactNativeRootToastTests.xctest"
-               BlueprintName = "ReactNativeRootToastTests"
-               ReferencedContainer = "container:ReactNativeRootToast.xcodeproj">
-            </BuildableReference>
-         </BuildActionEntry>
-      </BuildActionEntries>
-   </BuildAction>
-   <TestAction
-      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
-      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
-      shouldUseLaunchSchemeArgsEnv = "YES"
-      buildConfiguration = "Debug">
-      <Testables>
-         <TestableReference
-            skipped = "NO">
-            <BuildableReference
-               BuildableIdentifier = "primary"
-               BlueprintIdentifier = "00E356ED1AD99517003FC87E"
-               BuildableName = "ReactNativeRootToastTests.xctest"
-               BlueprintName = "ReactNativeRootToastTests"
-               ReferencedContainer = "container:ReactNativeRootToast.xcodeproj">
-            </BuildableReference>
-         </TestableReference>
-      </Testables>
-      <MacroExpansion>
-         <BuildableReference
-            BuildableIdentifier = "primary"
-            BlueprintIdentifier = "13B07F861A680F5B00A75B9A"
-            BuildableName = "ReactNativeRootToast.app"
-            BlueprintName = "ReactNativeRootToast"
-            ReferencedContainer = "container:ReactNativeRootToast.xcodeproj">
-         </BuildableReference>
-      </MacroExpansion>
-   </TestAction>
-   <LaunchAction
-      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
-      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
-      launchStyle = "0"
-      useCustomWorkingDirectory = "NO"
-      buildConfiguration = "Debug"
-      ignoresPersistentStateOnLaunch = "NO"
-      debugDocumentVersioning = "YES"
-      allowLocationSimulation = "YES">
-      <BuildableProductRunnable
-         runnableDebuggingMode = "0">
-         <BuildableReference
-            BuildableIdentifier = "primary"
-            BlueprintIdentifier = "13B07F861A680F5B00A75B9A"
-            BuildableName = "ReactNativeRootToast.app"
-            BlueprintName = "ReactNativeRootToast"
-            ReferencedContainer = "container:ReactNativeRootToast.xcodeproj">
-         </BuildableReference>
-      </BuildableProductRunnable>
-      <AdditionalOptions>
-      </AdditionalOptions>
-   </LaunchAction>
-   <ProfileAction
-      shouldUseLaunchSchemeArgsEnv = "YES"
-      savedToolIdentifier = ""
-      useCustomWorkingDirectory = "NO"
-      buildConfiguration = "Release"
-      debugDocumentVersioning = "YES">
-      <BuildableProductRunnable
-         runnableDebuggingMode = "0">
-         <BuildableReference
-            BuildableIdentifier = "primary"
-            BlueprintIdentifier = "13B07F861A680F5B00A75B9A"
-            BuildableName = "ReactNativeRootToast.app"
-            BlueprintName = "ReactNativeRootToast"
-            ReferencedContainer = "container:ReactNativeRootToast.xcodeproj">
-         </BuildableReference>
-      </BuildableProductRunnable>
-   </ProfileAction>
-   <AnalyzeAction
-      buildConfiguration = "Debug">
-   </AnalyzeAction>
-   <ArchiveAction
-      buildConfiguration = "Release"
-      revealArchiveInOrganizer = "YES">
-   </ArchiveAction>
-</Scheme>
diff --git a/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast/AppDelegate.h b/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast/AppDelegate.h
deleted file mode 100644
index a9654d5..0000000
--- a/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast/AppDelegate.h
+++ /dev/null
@@ -1,16 +0,0 @@
-/**
- * Copyright (c) 2015-present, Facebook, Inc.
- * All rights reserved.
- *
- * This source code is licensed under the BSD-style license found in the
- * LICENSE file in the root directory of this source tree. An additional grant
- * of patent rights can be found in the PATENTS file in the same directory.
- */
-
-#import <UIKit/UIKit.h>
-
-@interface AppDelegate : UIResponder <UIApplicationDelegate>
-
-@property (nonatomic, strong) UIWindow *window;
-
-@end
diff --git a/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast/AppDelegate.m b/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast/AppDelegate.m
deleted file mode 100644
index 1991339..0000000
--- a/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast/AppDelegate.m
+++ /dev/null
@@ -1,57 +0,0 @@
-/**
- * Copyright (c) 2015-present, Facebook, Inc.
- * All rights reserved.
- *
- * This source code is licensed under the BSD-style license found in the
- * LICENSE file in the root directory of this source tree. An additional grant
- * of patent rights can be found in the PATENTS file in the same directory.
- */
-
-#import "AppDelegate.h"
-
-#import "RCTRootView.h"
-
-@implementation AppDelegate
-
-- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
-{
-  NSURL *jsCodeLocation;
-
-  /**
-   * Loading JavaScript code - uncomment the one you want.
-   *
-   * OPTION 1
-   * Load from development server. Start the server from the repository root:
-   *
-   * $ npm start
-   *
-   * To run on device, change `localhost` to the IP address of your computer
-   * (you can get this by typing `ifconfig` into the terminal and selecting the
-   * `inet` value under `en0:`) and make sure your computer and iOS device are
-   * on the same Wi-Fi network.
-   */
-
-  jsCodeLocation = [NSURL URLWithString:@"http://localhost:8081/index.ios.bundle?platform=ios&dev=true"];
-
-  /**
-   * OPTION 2
-   * Load from pre-bundled file on disk. The static bundle is automatically
-   * generated by "Bundle React Native code and images" build step.
-   */
-
-//   jsCodeLocation = [[NSBundle mainBundle] URLForResource:@"main" withExtension:@"jsbundle"];
-
-  RCTRootView *rootView = [[RCTRootView alloc] initWithBundleURL:jsCodeLocation
-                                                      moduleName:@"ReactNativeRootToast"
-                                               initialProperties:nil
-                                                   launchOptions:launchOptions];
-
-  self.window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
-  UIViewController *rootViewController = [UIViewController new];
-  rootViewController.view = rootView;
-  self.window.rootViewController = rootViewController;
-  [self.window makeKeyAndVisible];
-  return YES;
-}
-
-@end
diff --git a/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast/Base.lproj/LaunchScreen.xib b/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast/Base.lproj/LaunchScreen.xib
deleted file mode 100644
index 1697957..0000000
--- a/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast/Base.lproj/LaunchScreen.xib
+++ /dev/null
@@ -1,42 +0,0 @@
-<?xml version="1.0" encoding="UTF-8" standalone="no"?>
-<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="7702" systemVersion="14D136" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES">
-    <dependencies>
-        <deployment identifier="iOS"/>
-        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="7701"/>
-        <capability name="Constraints with non-1.0 multipliers" minToolsVersion="5.1"/>
-    </dependencies>
-    <objects>
-        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
-        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
-        <view contentMode="scaleToFill" id="iN0-l3-epB">
-            <rect key="frame" x="0.0" y="0.0" width="480" height="480"/>
-            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
-            <subviews>
-                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Powered by React Native" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="9" translatesAutoresizingMaskIntoConstraints="NO" id="8ie-xW-0ye">
-                    <rect key="frame" x="20" y="439" width="441" height="21"/>
-                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
-                    <color key="textColor" cocoaTouchSystemColor="darkTextColor"/>
-                    <nil key="highlightedColor"/>
-                </label>
-                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="ReactNativeRootToast" textAlignment="center" lineBreakMode="middleTruncation" baselineAdjustment="alignBaselines" minimumFontSize="18" translatesAutoresizingMaskIntoConstraints="NO" id="kId-c2-rCX">
-                    <rect key="frame" x="20" y="140" width="441" height="43"/>
-                    <fontDescription key="fontDescription" type="boldSystem" pointSize="36"/>
-                    <color key="textColor" cocoaTouchSystemColor="darkTextColor"/>
-                    <nil key="highlightedColor"/>
-                </label>
-            </subviews>
-            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="calibratedWhite"/>
-            <constraints>
-                <constraint firstItem="kId-c2-rCX" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="bottom" multiplier="1/3" constant="1" id="5cJ-9S-tgC"/>
-                <constraint firstAttribute="centerX" secondItem="kId-c2-rCX" secondAttribute="centerX" id="Koa-jz-hwk"/>
-                <constraint firstAttribute="bottom" secondItem="8ie-xW-0ye" secondAttribute="bottom" constant="20" id="Kzo-t9-V3l"/>
-                <constraint firstItem="8ie-xW-0ye" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="20" symbolic="YES" id="MfP-vx-nX0"/>
-                <constraint firstAttribute="centerX" secondItem="8ie-xW-0ye" secondAttribute="centerX" id="ZEH-qu-HZ9"/>
-                <constraint firstItem="kId-c2-rCX" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="20" symbolic="YES" id="fvb-Df-36g"/>
-            </constraints>
-            <nil key="simulatedStatusBarMetrics"/>
-            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
-            <point key="canvasLocation" x="548" y="455"/>
-        </view>
-    </objects>
-</document>
diff --git a/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast/Images.xcassets/AppIcon.appiconset/Contents.json b/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast/Images.xcassets/AppIcon.appiconset/Contents.json
deleted file mode 100644
index 118c98f..0000000
--- a/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast/Images.xcassets/AppIcon.appiconset/Contents.json
+++ /dev/null
@@ -1,38 +0,0 @@
-{
-  "images" : [
-    {
-      "idiom" : "iphone",
-      "size" : "29x29",
-      "scale" : "2x"
-    },
-    {
-      "idiom" : "iphone",
-      "size" : "29x29",
-      "scale" : "3x"
-    },
-    {
-      "idiom" : "iphone",
-      "size" : "40x40",
-      "scale" : "2x"
-    },
-    {
-      "idiom" : "iphone",
-      "size" : "40x40",
-      "scale" : "3x"
-    },
-    {
-      "idiom" : "iphone",
-      "size" : "60x60",
-      "scale" : "2x"
-    },
-    {
-      "idiom" : "iphone",
-      "size" : "60x60",
-      "scale" : "3x"
-    }
-  ],
-  "info" : {
-    "version" : 1,
-    "author" : "xcode"
-  }
-}
\ No newline at end of file
diff --git a/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast/Info.plist b/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast/Info.plist
deleted file mode 100644
index 91963b2..0000000
--- a/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast/Info.plist
+++ /dev/null
@@ -1,48 +0,0 @@
-<?xml version="1.0" encoding="UTF-8"?>
-<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
-<plist version="1.0">
-<dict>
-	<key>CFBundleDevelopmentRegion</key>
-	<string>en</string>
-	<key>CFBundleExecutable</key>
-	<string>$(EXECUTABLE_NAME)</string>
-	<key>CFBundleIdentifier</key>
-	<string>org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)</string>
-	<key>CFBundleInfoDictionaryVersion</key>
-	<string>6.0</string>
-	<key>CFBundleName</key>
-	<string>$(PRODUCT_NAME)</string>
-	<key>CFBundlePackageType</key>
-	<string>APPL</string>
-	<key>CFBundleShortVersionString</key>
-	<string>1.0</string>
-	<key>CFBundleSignature</key>
-	<string>????</string>
-	<key>CFBundleVersion</key>
-	<string>1</string>
-	<key>LSRequiresIPhoneOS</key>
-	<true/>
-	<key>UILaunchStoryboardName</key>
-	<string>LaunchScreen</string>
-	<key>UIRequiredDeviceCapabilities</key>
-	<array>
-		<string>armv7</string>
-	</array>
-	<key>UISupportedInterfaceOrientations</key>
-	<array>
-		<string>UIInterfaceOrientationPortrait</string>
-		<string>UIInterfaceOrientationLandscapeLeft</string>
-		<string>UIInterfaceOrientationLandscapeRight</string>
-	</array>
-	<key>UIViewControllerBasedStatusBarAppearance</key>
-	<false/>
-	<key>NSLocationWhenInUseUsageDescription</key>
-	<string></string>
-  <key>NSAppTransportSecurity</key>
-  <dict>
-    <!--See http://ste.vn/2015/06/10/configuring-app-transport-security-ios-9-osx-10-11/ -->
-    <key>NSAllowsArbitraryLoads</key>
-    <true/>
-  </dict>
-</dict>
-</plist>
diff --git a/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast/main.m b/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast/main.m
deleted file mode 100644
index 3d767fc..0000000
--- a/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToast/main.m
+++ /dev/null
@@ -1,18 +0,0 @@
-/**
- * Copyright (c) 2015-present, Facebook, Inc.
- * All rights reserved.
- *
- * This source code is licensed under the BSD-style license found in the
- * LICENSE file in the root directory of this source tree. An additional grant
- * of patent rights can be found in the PATENTS file in the same directory.
- */
-
-#import <UIKit/UIKit.h>
-
-#import "AppDelegate.h"
-
-int main(int argc, char * argv[]) {
-  @autoreleasepool {
-    return UIApplicationMain(argc, argv, nil, NSStringFromClass([AppDelegate class]));
-  }
-}
diff --git a/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToastTests/Info.plist b/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToastTests/Info.plist
deleted file mode 100644
index 886825c..0000000
--- a/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToastTests/Info.plist
+++ /dev/null
@@ -1,24 +0,0 @@
-<?xml version="1.0" encoding="UTF-8"?>
-<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
-<plist version="1.0">
-<dict>
-	<key>CFBundleDevelopmentRegion</key>
-	<string>en</string>
-	<key>CFBundleExecutable</key>
-	<string>$(EXECUTABLE_NAME)</string>
-	<key>CFBundleIdentifier</key>
-	<string>org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)</string>
-	<key>CFBundleInfoDictionaryVersion</key>
-	<string>6.0</string>
-	<key>CFBundleName</key>
-	<string>$(PRODUCT_NAME)</string>
-	<key>CFBundlePackageType</key>
-	<string>BNDL</string>
-	<key>CFBundleShortVersionString</key>
-	<string>1.0</string>
-	<key>CFBundleSignature</key>
-	<string>????</string>
-	<key>CFBundleVersion</key>
-	<string>1</string>
-</dict>
-</plist>
diff --git a/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToastTests/ReactNativeRootToastTests.m b/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToastTests/ReactNativeRootToastTests.m
deleted file mode 100644
index 7f20e8a..0000000
--- a/node_modules/react-native-root-toast/Example/ios/ReactNativeRootToastTests/ReactNativeRootToastTests.m
+++ /dev/null
@@ -1,70 +0,0 @@
-/**
- * Copyright (c) 2015-present, Facebook, Inc.
- * All rights reserved.
- *
- * This source code is licensed under the BSD-style license found in the
- * LICENSE file in the root directory of this source tree. An additional grant
- * of patent rights can be found in the PATENTS file in the same directory.
- */
-
-#import <UIKit/UIKit.h>
-#import <XCTest/XCTest.h>
-
-#import "RCTLog.h"
-#import "RCTRootView.h"
-
-#define TIMEOUT_SECONDS 240
-#define TEXT_TO_LOOK_FOR @"Welcome to React Native!"
-
-@interface ReactNativeRootToastTests : XCTestCase
-
-@end
-
-@implementation ReactNativeRootToastTests
-
-- (BOOL)findSubviewInView:(UIView *)view matching:(BOOL(^)(UIView *view))test
-{
-  if (test(view)) {
-    return YES;
-  }
-  for (UIView *subview in [view subviews]) {
-    if ([self findSubviewInView:subview matching:test]) {
-      return YES;
-    }
-  }
-  return NO;
-}
-
-- (void)testRendersWelcomeScreen
-{
-  UIViewController *vc = [[[[UIApplication sharedApplication] delegate] window] rootViewController];
-  NSDate *date = [NSDate dateWithTimeIntervalSinceNow:TIMEOUT_SECONDS];
-  BOOL foundElement = NO;
-
-  __block NSString *redboxError = nil;
-  RCTSetLogFunction(^(RCTLogLevel level, RCTLogSource source, NSString *fileName, NSNumber *lineNumber, NSString *message) {
-    if (level >= RCTLogLevelError) {
-      redboxError = message;
-    }
-  });
-
-  while ([date timeIntervalSinceNow] > 0 && !foundElement && !redboxError) {
-    [[NSRunLoop mainRunLoop] runMode:NSDefaultRunLoopMode beforeDate:[NSDate dateWithTimeIntervalSinceNow:0.1]];
-    [[NSRunLoop mainRunLoop] runMode:NSRunLoopCommonModes beforeDate:[NSDate dateWithTimeIntervalSinceNow:0.1]];
-
-    foundElement = [self findSubviewInView:vc.view matching:^BOOL(UIView *view) {
-      if ([view.accessibilityLabel isEqualToString:TEXT_TO_LOOK_FOR]) {
-        return YES;
-      }
-      return NO;
-    }];
-  }
-
-  RCTSetLogFunction(RCTDefaultLogFunction);
-
-  XCTAssertNil(redboxError, @"RedBox error: %@", redboxError);
-  XCTAssertTrue(foundElement, @"Couldn't find element with text '%@' in %d seconds", TEXT_TO_LOOK_FOR, TIMEOUT_SECONDS);
-}
-
-
-@end
diff --git a/node_modules/react-native-root-toast/Example/main.js b/node_modules/react-native-root-toast/Example/main.js
deleted file mode 100644
index 9b9bd72..0000000
--- a/node_modules/react-native-root-toast/Example/main.js
+++ /dev/null
@@ -1,288 +0,0 @@
-/**
- * Sample React Native App
- * https://github.com/facebook/react-native
- */
-'use strict';
-import React, {
-    AppRegistry,
-    Component,
-    StyleSheet,
-    Text,
-    View,
-    TouchableHighlight,
-    TouchableOpacity,
-    Picker,
-    TextInput,
-    ScrollView,
-    Switch
-} from 'react-native';
-import Toast from 'react-native-root-toast';
-import Field from './Field';
-
-let {
-    durations,
-    positions
-} = Toast;
-
-const DURATIONS_KEYS = Object.keys(durations);
-const POSITIONS_KEYS = Object.keys(positions);
-const messages = [
-    'Mr. and Mrs. Dursley, of number four Privet Drive, were proud to say that they were perfectly normal, thank you very much.',
-    '“I am not worried, Harry,” said Dumbledore, his voice a little stronger despite the freezing water. “I am with you.”',
-    'You’re a wizard, Harry.',
-    'But you know, happiness can be found even in the darkest of times, if one only remembers to turn on the light.',
-    'Ah, music,” he said, wiping his eyes. “A magic beyond all we do here!”',
-    'I am what I am, an’ I’m not ashamed. ‘Never be ashamed,’ my ol’ dad used ter say, ‘there’s some who’ll hold it against you, but they’re not worth botherin’ with.',
-    'Never trust anything that can think for itself if you can’t see where it keeps its brain.',
-    'There are some things you can’t share without ending up liking each other, and knocking out a twelve-foot mountain troll is one of them.',
-    'It’s wingardium leviOsa, not leviosAH.',
-    'There is no need to call me Sir, Professor.',
-    '’I’m not going to be murdered,’ Harry said out loud.‘That’s the spirit, dear,’ said his mirror sleepily.',
-    'You sort of start thinking anything’s possible if you’ve got enough nerve.',
-    'It is our choices, Harry, that show what we truly are, far more than our abilities.',
-    'It takes a great deal of bravery to stand up to our enemies, but just as much to stand up to our friends.',
-    'Just because you have the emotional range of a teaspoon doesn’t mean we all have.',
-    '‘He is dead!’ Narcissa Malfoy called to the watchers.',
-    'Really Hagrid, if you are holding out for universal popularity, I’m afraid you will be in this cabin for a very long time',
-    'Chaos reigned.',
-    'Give her hell from us, Peeves!',
-    'Until the very end.',
-    'Oculus Reparo!',
-    '“After all this time?”“Always,” said Snape.'
-];
-const colors = {
-    default: null,
-    red: 'red',
-    blue: 'blue'
-};
-
-const styles = StyleSheet.create({
-    container: {
-        flex: 1,
-        backgroundColor: '#F5FCFF'
-    },
-    content: {
-        paddingTop: 40,
-        alignItems: 'center'
-    },
-    button: {
-        borderRadius: 3,
-        paddingVertical: 5,
-        paddingHorizontal: 10,
-        backgroundColor: 'green',
-        marginBottom: 10
-    },
-    buttonText: {
-        color: '#fff',
-        textAlign: 'center'
-    },
-    prop: {
-        alignItems: 'center',
-        justifyContent: 'center'
-    },
-    title: {
-        fontSize: 20,
-        fontWeight: 'bold',
-        color: 'skyblue',
-        borderBottomWidth: 1,
-        borderBottomColor: '#666'
-    },
-    fieldContainer: {
-        flexDirection: 'row',
-        alignItems: 'center',
-        marginBottom: 10
-    },
-    fieldText: {
-        marginRight: 5,
-        fontSize: 20,
-        fontWeight: 'bold',
-        color: 'skyblue'
-    },
-    input: {
-        width: 100,
-        height: 30,
-        lineHeight: 30,
-        fontWeight: 'bold',
-        color: '#333'
-    },
-    code: {
-        alignSelf: 'stretch',
-        backgroundColor: '#f0f0f0',
-        padding: 10,
-        height: 200
-    },
-    codeText: {
-        fontSize: 10
-    },
-    codeTittle: {
-        textAlign: 'center',
-        fontSize: 16,
-        fontWeight: 'bold',
-        marginBottom: 10
-    },
-    value: {
-        color: 'blue'
-    },
-    string: {
-        color: 'grey'
-    },
-    api: {
-        fontSize: 12,
-        textAlign: 'center',
-        marginRight: 10
-    }
-});
-
-class ReactNativeRootToast extends Component {
-    constructor() {
-        super(...arguments);
-        this.state = {
-            duration: durations[DURATIONS_KEYS[0]],
-            position: positions[POSITIONS_KEYS[0]],
-            shadow: true,
-            animation: true,
-            hideOnPress: true,
-            delay: 0,
-            message: messages[~~(messages.length * Math.random())],
-            backgroundColor: false,
-            shadowColor: false,
-            textColor: false
-        };
-    }
-
-    toast = null;
-
-    show = () => {
-        let message = messages[~~(messages.length * Math.random())];
-        this.toast && this.toast.destroy();
-        this.setState({
-            message
-        });
-        this.toast = Toast.show(message, {
-            duration: this.state.duration,
-            position: this.state.position,
-            shadow: this.state.shadow,
-            animation: this.state.animation,
-            hideOnPress: this.state.hideOnPress,
-            delay: this.state.delay,
-            backgroundColor: this.state.backgroundColor ? 'blue' : null,
-            shadowColor: this.state.shadowColor ? 'yellow' : null,
-            textColor: this.state.textColor ? 'purple' : null,
-            onPress: () => {
-              alert('You clicked me!')
-            },
-            onHidden: () => {
-                this.toast.destroy();
-                this.toast = null;
-            }
-        });
-    };
-
-    getApiCode = () => <Text>
-        {`Toast.show(
-    `}<Text style={styles.string}>'{this.state.message}'</Text>{`,
-    {
-        position:`} <Text style={styles.value}>{this.state.position}</Text>{`,
-        delay:`} <Text style={styles.value}>{this.state.delay}</Text>{`,
-        shadow:`} <Text style={styles.value}>{this.state.shadow.toString()}</Text>{`,
-        animation:`} <Text style={styles.value}>{this.state.animation.toString()}</Text>{`,
-        hideOnPress:`} <Text style={styles.value}>{this.state.hideOnPress.toString()}</Text>{`,
-        backgroundColor:`} <Text style={styles.value}>{this.state.backgroundColor.toString()}</Text>{`,
-        shadowColor:`} <Text style={styles.value}>{this.state.shadowColor.toString()}</Text>{`,
-        textColor:`} <Text style={styles.value}>{this.state.textColor.toString()}</Text>{`
-    }
-);`}
-    </Text>;
-
-    getSwitchList = () => {
-        return ['shadow', 'animation', 'hideOnPress', 'backgroundColor', 'shadowColor', 'textColor'].map(prop =>
-            <View style={styles.fieldContainer} key={prop}>
-                <Text style={styles.fieldText}>{prop}</Text>
-                <Switch
-                    onValueChange={value => this.setState({[prop]: value})}
-                    value={this.state[prop]}
-                />
-            </View>);
-    };
-
-    render() {
-        let code = this.getApiCode();
-        return (
-            <ScrollView
-                style={styles.container}
-                contentContainerStyle={styles.content}
-            >
-                <View style={styles.prop}>
-                    <Text style={styles.title}>duration</Text>
-                </View>
-                <Field
-                    name="duration"
-                    options={durations}
-                    onChange={duration => this.setState({duration})}
-                />
-                <View style={styles.prop}>
-                    <Text style={styles.title}>position</Text>
-                </View>
-                <Field
-                    name="position"
-                    options={positions}
-                    onChange={position => this.setState({position})}
-                />
-                <View style={styles.fieldContainer}>
-                    <Text style={styles.fieldText}>delay (ms)</Text>
-                    <TextInput
-                        style={styles.input}
-                        onChange={({nativeEvent: {text}}) => this.setState({delay: +text || 0})}
-                        value={(this.state.delay || 0).toString()}
-                        keyboardType={'decimal-pad'}
-                    />
-                </View>
-                {this.getSwitchList()}
-                <TouchableHighlight
-                    style={styles.button}
-                    underlayColor="green"
-                    onPress={this.show}
-                >
-                    <Text style={styles.buttonText}>Show Toast</Text>
-                </TouchableHighlight>
-                <View style={styles.code}>
-                    <Text style={styles.codeTittle}>CODE:</Text>
-                    <Text style={styles.codeText}>{code}</Text>
-                </View>
-            </ScrollView>
-        );
-    }
-}
-
-// You can also show a toast by using a <Toast /> inside render
-class Example extends Component{
-    constructor() {
-        super(...arguments);
-        this.state = {
-            visible: false
-        };
-    }
-
-    componentDidMount() {
-        setTimeout(() => this.setState({
-            visible: true
-        }), 2000); // show toast after 2s
-
-        setTimeout(() => this.setState({
-            visible: false
-        }), 5000); // hide toast after 5s
-    };
-
-    render() {
-        return <Toast
-            visible={this.state.visible}
-            position={50}
-            shadow={false}
-            animation={false}
-            hideOnPress={true}
-        >This is a message</Toast>;
-    }
-}
-
-
-AppRegistry.registerComponent('ReactNativeRootToast', () => ReactNativeRootToast);
diff --git a/node_modules/react-native-root-toast/Example/screen-shoots.gif b/node_modules/react-native-root-toast/Example/screen-shoots.gif
deleted file mode 100644
index a7fb665..0000000
Binary files a/node_modules/react-native-root-toast/Example/screen-shoots.gif and /dev/null differ
diff --git a/node_modules/react-native-root-toast/lib/Toast.js b/node_modules/react-native-root-toast/lib/Toast.js
index f765306..a191df3 100644
--- a/node_modules/react-native-root-toast/lib/Toast.js
+++ b/node_modules/react-native-root-toast/lib/Toast.js
@@ -13,7 +13,7 @@ class Toast extends Component {
     static positions = positions;
     static durations = durations;
 
-    static show = (message, options = {position: positions.BOTTOM, duration: durations.SHORT}) => {
+    static show = (message, options = {position: positions.BOTTOM,fadeOutDuration: durations.SHORT,fadeInDuration: durations.SHORT}) => {
         return new RootSiblings(<ToastContainer
             {...options}
             visible={true}
@@ -24,31 +24,32 @@ class Toast extends Component {
 
     static hide = toast => {
         if (toast instanceof RootSiblings) {
+            console.log('清除了hide')
             toast.destroy();
         } else {
             console.warn(`Toast.hide expected a \`RootSiblings\` instance as argument.\nBut got \`${typeof toast}\` instead.`);
         }
     };
 
-    _toast = null;
-
-    componentWillMount = () => {
-        this._toast = new RootSiblings(<ToastContainer
-            {...this.props}
-            duration={0}
-        />);
-    };
-
-    componentWillReceiveProps = nextProps => {
-        this._toast.update(<ToastContainer
-            {...nextProps}
-            duration={0}
-        />);
-    };
-
-    componentWillUnmount = () => {
-        this._toast.destroy();
-    };
+    // _toast = null;
+
+    // componentWillMount = () => {
+    //     this._toast = new RootSiblings(<ToastContainer
+    //         {...this.props}
+    //         duration={0}
+    //     />);
+    // };
+
+    // componentWillReceiveProps = nextProps => {
+    //     this._toast.update(<ToastContainer
+    //         {...nextProps}
+    //         duration={0}
+    //     />);
+    // };
+
+    // componentWillUnmount = () => {
+    //     this._toast.destroy();
+    // };
 
     render() {
         return null;
diff --git a/node_modules/react-native-root-toast/lib/ToastContainer.js b/node_modules/react-native-root-toast/lib/ToastContainer.js
index a645d4d..23c663c 100644
--- a/node_modules/react-native-root-toast/lib/ToastContainer.js
+++ b/node_modules/react-native-root-toast/lib/ToastContainer.js
@@ -11,7 +11,8 @@ import {
     Dimensions,
     TouchableWithoutFeedback,
     Easing,
-    Keyboard
+    Keyboard,
+    AccessibilityInfo
 } from 'react-native';
 const TOAST_MAX_WIDTH = 0.8;
 const TOAST_ANIMATION_DURATION = 200;
@@ -21,12 +22,12 @@ const positions = {
     BOTTOM: -20,
     CENTER: 0
 };
-
+// 改动1
 const durations = {
     LONG: 3500,
     SHORT: 2000
 };
-
+let _hideTimeout = null,_showTimeout = null;
 let styles = StyleSheet.create({
     defaultStyle: {
         position: 'absolute',
@@ -81,7 +82,9 @@ class ToastContainer extends Component {
         onHide: PropTypes.func,
         onHidden: PropTypes.func,
         onShow: PropTypes.func,
-        onShown: PropTypes.func
+        onShown: PropTypes.func,
+        fadeInDuration: PropTypes.number,
+        fadeOutDuration: PropTypes.number,
     };
 
     static defaultProps = {
@@ -93,7 +96,9 @@ class ToastContainer extends Component {
         opacity: 0.8,
         delay: 0,
         hideOnPress: true,
-        keyboardAvoiding: true
+        keyboardAvoiding: true,
+        fadeInDuration: 500,
+        fadeOutDuration: 500,
     };
 
     constructor() {
@@ -114,16 +119,16 @@ class ToastContainer extends Component {
             Keyboard.addListener('keyboardDidChangeFrame', this._keyboardDidChangeFrame);
         }
         if (this.state.visible) {
-            this._showTimeout = setTimeout(() => this._show(), this.props.delay);
+            _showTimeout = setTimeout(() => this._show());
         }
     };
 
     componentDidUpdate = prevProps => {
         if (this.props.visible !== prevProps.visible) {
             if (this.props.visible) {
-                clearTimeout(this._showTimeout);
-                clearTimeout(this._hideTimeout);
-                this._showTimeout = setTimeout(() => this._show(), this.props.delay);
+                clearTimeout(_showTimeout);
+                clearTimeout(_hideTimeout);
+                _showTimeout = setTimeout(() => this._show(), this.props.fadeInDuration);
             } else {
                 this._hide();
             }
@@ -142,8 +147,7 @@ class ToastContainer extends Component {
 
     _animating = false;
     _root = null;
-    _hideTimeout = null;
-    _showTimeout = null;
+    
     _keyboardHeight = 0;
 
     _windowChanged = ({ window }) => {
@@ -160,25 +164,31 @@ class ToastContainer extends Component {
     };
 
     _show = () => {
-        clearTimeout(this._showTimeout);
+        console.log('执行了show',_showTimeout)
+        clearTimeout(_showTimeout);
+        clearTimeout(_hideTimeout);
+
         if (!this._animating) {
-            clearTimeout(this._hideTimeout);
+            clearTimeout(_hideTimeout);
             this._animating = true;
-            this._root.setNativeProps({
-                pointerEvents: 'auto'
-            });
+            if (this._root) {
+                this._root.setNativeProps({
+                    pointerEvents: 'auto'
+                });
+            }
             this.props.onShow && this.props.onShow(this.props.siblingManager);
             Animated.timing(this.state.opacity, {
                 toValue: this.props.opacity,
-                duration: this.props.animation ? TOAST_ANIMATION_DURATION : 0,
+                duration: parseFloat(this.props.duration),// 改动2
                 easing: Easing.out(Easing.ease),
                 useNativeDriver: true
             }).start(({finished}) => {
                 if (finished) {
                     this._animating = !finished;
                     this.props.onShown && this.props.onShown(this.props.siblingManager);
-                    if (this.props.duration > 0) {
-                        this._hideTimeout = setTimeout(() => this._hide(), this.props.duration);
+                    if (this.props.duration >= 0) {
+                        //ai设置是这里 的延时 和之前的重合
+                        _hideTimeout = setTimeout(() => this._hide());
                     }
                 }
             });
@@ -186,8 +196,9 @@ class ToastContainer extends Component {
     };
 
     _hide = () => {
-        clearTimeout(this._showTimeout);
-        clearTimeout(this._hideTimeout);
+        console.log('执行了close',_showTimeout,this.props.fadeOutDuration)
+        clearTimeout(_showTimeout);
+        clearTimeout(_hideTimeout);
         if (!this._animating) {
             if (this._root) {
                 this._root.setNativeProps({
@@ -201,7 +212,7 @@ class ToastContainer extends Component {
             
             Animated.timing(this.state.opacity, {
                 toValue: 0,
-                duration: this.props.animation ? TOAST_ANIMATION_DURATION : 0,
+                duration: parseFloat(this.props.duration), // 改动3
                 easing: Easing.in(Easing.ease),
                 useNativeDriver: true
             }).start(({finished}) => {
@@ -255,11 +266,14 @@ class ToastContainer extends Component {
                     pointerEvents="none"
                     ref={ele => this._root = ele}
                 >
-                    <Text style={[
-                        styles.textStyle,
-                        props.textStyle,
-                        props.textColor && {color: props.textColor}
-                    ]}>
+                    <Text 
+                        style={[
+                            styles.textStyle,
+                            props.textStyle,
+                            props.textColor && { color: props.textColor }
+                        ]}
+                        accessibilityLiveRegion={"assertive"}
+                    >
                         {this.props.children}
                     </Text>
                 </Animated.View>
