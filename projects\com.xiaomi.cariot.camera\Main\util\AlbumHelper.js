import { findNodeHandle, NativeModules, Platform, PermissionsAndroid } from 'react-native';
import { Host, Device, Service, System } from "miot";
export const SNAPSHOT_IMG_PATH = "snapshot.jpg";// 二级目录ios不支持直接显示出来
export const SNAPSHOT_SETTING_IMG_PATH = "snapshot_setting.jpg";// 无翻转截图
export const SNAPSHOT_FLIP_IMG_PATH = "snapshot_setting_flip.jpg"; // 180翻转截图
export const SNAPSHOT_LOADING_BG = "snapshot_loading_bg.jpg";
export const SNAPSHOT_CAR_FREEZE = "snapshot_car_freeze_bg.jpg";

import native from "miot/native";
import RPC from './RPC';
import VersionUtil from './VersionUtil';
import { CAMERA_CONTROL_SEPC_PARAMS } from '../Constants';
import StorageKeys from '../StorageKeys';
import dayjs from 'dayjs';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
export default class AlbumHelper {

  constructor() {
    this.nativeAlbumName = `Xiaomi/local/${ Device.deviceID }`;
  }
  // 这里命名方式有待改进
  static getFileName(isVideo, timestamp = new Date().getTime()) {
    
    let fileName = (isVideo ? "VIDEO_" : "IMAGE_") + this.formatDateForFile(timestamp, !isVideo) + (isVideo ? ".mp4" : ".jpg");
    return fileName;
  }
  static getDailyTitleName(prefix, timestamp = new Date().getTime()) {
    let dateTime = new Date();
    dateTime.setTime(timestamp);
    // let fileName = `${ prefix + this.formatDate(dateTime, false) }.mp4`;
    let fileName = `${ this.DSformatDate(dateTime) + prefix  }`;
    return fileName;
  }

  static getDailyFileName(prefix, timestamp = new Date().getTime()) {
    
    // let fileName = `${ prefix + this.formatDateForFile(timestamp, false) }.mp4`;
    let fileName = `${ prefix + timestamp }.mp4`;
    return fileName;
  }

  static getDownloadTargetPathName(isVideo, timestamp = new Date().getTime(), needSuffix = false) {
    // let timestampInSec = Number.parseInt(timestamp / 1000);
    // let format = "YYYY-MM-DD_HH-mm-ss";
    // let str = dayjs.unix(timestampInSec).format(format);
    // if (needSuffix) {
    //   str = `${str}_${Math.floor(Math.random()*100)}`;
    // }
    // let fileName = (isVideo ? "VIDEO_" : "IMAGE_") + str + (isVideo ? ".mp4" : ".jpg");
    let fileName = (isVideo ? "VIDEO_" : "IMAGE_") + timestamp + (isVideo ? ".mp4" : ".jpg");
    return fileName;
  }

  static snapShot(vidoeRenderView) {
    return new Promise((resolve, reject) => {
      let path = this.getDownloadTargetPathName(false);
      vidoeRenderView.snapShot(`${ Host.file.storageBasePath }/${ path }`)
        .then((_) => {
          // 得到文件路径，并显示出来。
          return this.saveToAlbum(path, false);// 不是视频
        })
        .then((path) => {
          resolve(path);
        })
        .catch((e) => {
          reject(e);

        });

    });
  }

  static snapshotForCarFreeze(videoRenderView, isCloud = false) {
    return new Promise((resolve, reject) => {
      if (videoRenderView != null) {
        if (isCloud) {
          let reactNativeVideo = findNodeHandle(videoRenderView);
          Service.miotcamera.reactNativeVideoScreenShot(reactNativeVideo, `${ Host.file.storageBasePath }/${ SNAPSHOT_CAR_FREEZE }`)
            .then((_) => {
              console.log("reactNativeVideoScreenShot success");
              resolve(SNAPSHOT_CAR_FREEZE);
            })
            .catch((e) => {
              console.log("reactNativeVideoScreenShot error",e);
              reject(e);
            });
        } else {
          videoRenderView.snapShot(`${ Host.file.storageBasePath }/${ SNAPSHOT_CAR_FREEZE }`)
            .then((result) => { // 截图完毕。不转存
              console.log("live snapshotForCarFreeze");
              resolve(SNAPSHOT_CAR_FREEZE);
            }).catch((e) => {
              console.log("snapshotForCarFreeze", e);
              reject();
            });
        }
      } else {
        reject();
      }

    });
  }

  static reactNativeSnapShot(vidoeRenderView) {
    return new Promise((resolve, reject) => {
      // let path = this.getFileName(false);
      let path = this.getDownloadTargetPathName(false);
      console.log("landing10");
      let reactNativeVideo = findNodeHandle(vidoeRenderView);
      Service.miotcamera.reactNativeVideoScreenShot(reactNativeVideo, `${ Host.file.storageBasePath }/${ path }`)
        .then((_) => {
          // 得到文件路径，并显示出来。
          console.log("landing11");
          return this.saveToAlbum(path, false);// 不是视频
        })
        .then((path) => {

          console.log("landing12");
          resolve(path);
        })
        .catch((e) => {

          console.log("landing13");
          reject(e);
        });

    });
  }

  static saveToAlbum(path, isVideo) {
    console.log("why!, saveToAlbum");

    if (isVideo) {
      return new Promise((resolve, reject) => {
        Host.file.saveVideoToPhotosDidAlbum(path)
          .then((result) => {
            resolve(path);
          })
          .catch((error) => {
            reject(error);
          });
      });
    } else {
      return new Promise((resolve, reject) => {        
        Host.file.saveImageToPhotosDidAlbum(path)
          .then((result) => {
            resolve(path);
          })
          .catch((error) => {
            reject(error);
          });
      });
    }
  }

  static saveToNativeAlbum(path, isVideo) {
    if (isVideo) {
      return new Promise((resolve, reject) => {
        this.saveVideoToPhotosDidAlbum(path)
          .then((result) => {
            resolve(path);
          })
          .catch((error) => {
            reject(error);
          });
      });
    } else {
      return new Promise((resolve, reject) => {        
        this.saveImageToPhotosDidAlbum(path)
          .then((result) => {
            resolve(path);
          })
          .catch((error) => {
            reject(error);
          });
      });
    }
  }

  static getCachedImage() {
    return new Promise((resolve, reject) => {
      StorageKeys.IS_IMAGE_FLIP.then((result) => {
        if (result === true) {
          resolve(SNAPSHOT_FLIP_IMG_PATH);
        } else {
          resolve(SNAPSHOT_SETTING_IMG_PATH);
        }
      })
        .catch(() => {
          resolve(SNAPSHOT_SETTING_IMG_PATH);
        });
    });
  }

  static getNativeAlbumName() {
    if (!this.nativeAlbumName) {
      if (Platform.OS == "android") {
        this.nativeAlbumName = `Xiaomi/local/${ Device.deviceID }`;
      } else {
        this.nativeAlbumName = Device.deviceID;
      }
    }

    console.log(`why!, getNativeAlbumName ret: ${ this.nativeAlbumName }`);
    return this.nativeAlbumName;
  }

  static fetchDeviceAlbumName() {
    if (Platform.OS == "android") {
      this.nativeAlbumName = `Xiaomi/local/${ Device.deviceID }`;
    } else {
      // TODO: should be replaced in miotcamera in 10051
      // Service.miotcamera.getAlbumName()
      this.getAlbumNameForDevice(Device.deviceID)
        .then((res) => {
          console.log(`why!, getAlbumNameForDevice res: ${ res }`);
          this.nativeAlbumName = res;
        })
        .catch((e) => {
        // reject(e);
        });
    }
  }

  static getAlbumNameForDevice(did) {
    // @native :=> promise
    return new Promise((resolve, reject) => {
      NativeModules.MHCameraSDK.getAlbumName(did, (success, result) => {
        if (success) {
          resolve(result);
        } else {
          reject(result);
        }
      });
    });
    // @native end
  }

  static saveImageToPhotosDidAlbum(fileName, customDirName = null) {
    // @native :=> Promise.resolve(false)
    console.log("why!, saveImageToPhotosDidAlbum 000");

    return new Promise((resolve, reject) => {
      native.MIOTFile.saveImageToPhotosDidAlbum(this.getNativeAlbumName(), fileName, (isSuccess, result) => {
        console.log("why!, saveImageToPhotosDidAlbum 333");
        if (isSuccess) {
          resolve(true);
        } else {
          reject(result);
        }
      });
    });
    // @native end
  }

  static saveVideoToPhotosDidAlbum(fileName, customDirName = null) {
    // @native :=> Promise.resolve(false)
    return new Promise((resolve, reject) => {
      native.MIOTFile.saveVideoToPhotosDidAlbum(this.getNativeAlbumName(), fileName, (isSuccess, result) => {
        if (isSuccess) {
          resolve(true);
        } else {
          reject(result);
        }
      });
    });
    // @native end
  }

  static formatDateForFile(date, needMillis) {
    let timestampInSec = Number.parseInt(date / 1000);
    let format = LocalizedStrings["yyyymmdd"].replace(/[/]/g, "_") + " HH:mm:ss";
    let str = dayjs.unix(timestampInSec).format(format);
    return str;
  }
  static formatDate(date, needMillis) {
    let timestampInSec = Number.parseInt(date / 1000);
    let format = LocalizedStrings["yyyymmdd"] + " HH:mm:ss";
    let str = dayjs.unix(timestampInSec).format(format);
    return str;
  }

  // 单独修改每日故事时间函数
  static DSformatDate(date, needMillis) {
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    let day = date.getDate();

    let timeStr = `${ year }.${ month > 9 ? month : `0${ month }` }.${ day > 9 ? day : `0${ day }` }-`;
      
    return timeStr;

  }

  static showLastAlbumMediaFile() {
    let albumName = this.getNativeAlbumName();
    let data = { albumName: albumName };
    NativeModules.MHCameraSDK.showLastAlbumMediaFile(Device.deviceID, JSON.stringify(data));
  }
  
  static getAlbumFiles() {
    // 读取原生插件目录里的内容 和 RN插件里的内容
    return new Promise((resolve, reject) => {
      let albumName = this.getNativeAlbumName();
      let getNativeAlbum = Platform.OS === "android" || albumName != Device.deviceID || (VersionUtil.judgeIsV1(Device.model) && !VersionUtil.isFirmwareSupportCloud(Device.model));
      Promise.all( getNativeAlbum ? [Host.file.getAllSourceFromPhotosDidAlbum(), this.getAllSourceFromPhotosDidAlbum().catch((error) => { return "getAllSourceFromPhotosDidAlbum error:" + JSON.stringify(error); })] : [Host.file.getAllSourceFromPhotosDidAlbum()])
        .then((values) => {
          let mergeAlbumArray = values[0].data;
          if (values.length != 1) {
            let didAlbumArray = values[0].data;
            if (typeof(values[1]) != "string") {//不是捕获到了异常
              let nativeAlbumArray = values[1].data;
              mergeAlbumArray = didAlbumArray.concat(nativeAlbumArray);
            } else {
              console.log("从原生插件抓数据出错了");
            }
          }
          if (mergeAlbumArray == null || mergeAlbumArray.length <= 0) {
            reject(values);
            return;
          }
          mergeAlbumArray.sort((a, b) => {
            return b.modificationDate - a.modificationDate;
          });
          let videoArray = [];
          for (let i = 0; i < mergeAlbumArray.length; i++) {
            let data = mergeAlbumArray[i];
            data.isSelected = false;
            if (data.mediaType == 2) {
              videoArray.push(data);
            }
          }
          Promise.all(videoArray.map((item) => {
            return Host.file.fetchLocalVideoFilePathFromDidAlbumByUrl(item.url);
          }))
            .then((result1) => {
              for (let i = 0; i < result1.length; i++) {
                let videoPath = result1[i];
                videoArray[i].path = videoPath.data;// 改变了值
              }
              resolve(mergeAlbumArray);
            })
            .catch((error) => {
              resolve(mergeAlbumArray);
            });
        })
        .catch((err) => {
          reject(err);
        });
    });
  }

  static getNativeAlbumFiles() {
    console.log("why!, getNativeAlbumFiles");
    return new Promise((resolve, reject) => {
      this.getAllSourceFromPhotosDidAlbum()
        .then((result) => {
          if (result == null || result.data == null || !(result.data instanceof Array) || result.data.length <= 0) {
            reject(result);
            return;
          }
          let dataArray = result.data;
          let videoArray = [];
          dataArray.sort((a, b) => { return b.modificationDate - a.modificationDate; });// 排序  

          for (let i = 0; i < dataArray.length; i++) {
            let data = dataArray[i];
            /**
             * {'url':<'miotph://XXXXXX'(ios) 'file://XXXXXX' (android)>,
     *      'mediaType' : <number>, // 0 : unknowntype, 1: image, 2:video, 3: audio(10037暂不支持)
     *      'pixelWidth' :<number>, // width信息，0 代表unknown
     *      'pixelHeight' :<number>, // height 0 代表unknown
     *      'creationDate' :<number>, // 创建时间信息，unix时间戳
     *      'modificationDate' : <number>, // 修改时间信息， unix时间戳
     *      'duration' : <number>, // 持续时间 信息 图片文件返回0
     *      }
             */
            data.isSelected = false;
            if (data.mediaType == 2) {
              videoArray.push(data);
            }
          }


          Promise.all(videoArray.map((item) => {
            return Host.file.fetchLocalVideoFilePathFromDidAlbumByUrl(item.url);
          }))
            .then((result1) => {
              for (let i = 0; i < result1.length; i++) {
                let videoPath = result1[i];
                videoArray[i].path = videoPath.data;// 改变了值
              }
              resolve(dataArray);
            })
            .catch((error) => {
              resolve(dataArray);
            });
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  static getAllSourceFromPhotosDidAlbum(customDirName = null) {
    // @native :=> Promise.resolve(false)
    return new Promise((resolve, reject) => {
      native.MIOTFile.getAllSourceFromPhotosDidAlbum(this.getNativeAlbumName(), (isSuccess, result) => {
        if (isSuccess) {
          resolve(result);
        } else {
          reject(result);
        }
      });
    });
    // @native end
  }

  static deleteAlbumFilesByUrl(urls) {
    return new Promise((resolve, reject) => {
      Host.file.deleteAssetsFromAlbumByUrls(urls)
        .then((result) => {
          resolve(result);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  static getAlbumFileName(url) {
    let name = "";
    if (url.lastIndexOf("/") != -1) {
      name = url.substring(url.lastIndexOf("/") + 1);
    } else if (url.lastIndexOf(":") != -1) {
      name = url.substring(url.lastIndexOf(":") + 1);
    }
    return name;
  }

  /**
   * vidoeRenderView videoview
   * 仅仅截图到本地，不copy到相册里。
   */
  static justSnapshot(vidoeRenderView) {
    let path = SNAPSHOT_IMG_PATH;
    return new Promise((resolve, reject) => {
      vidoeRenderView.snapShot(`${ Host.file.storageBasePath }/${ path }`)
        .then((result) => { // 截图完毕。不转存
          resolve(result);
        })

        .catch((e) => {
          console.log(e);
        });
    });
    
  }

  /**
   * vidoeRenderView videoview
   * 仅仅截图到本地，不copy到相册里。
   */
  static snapshotForSetting(vidoeRenderView, localFlip, customPath = "") {
    console.log(`why!, snapshotForSetting customPath=${ customPath }`);
    return new Promise((resolve, reject) => {
      if (vidoeRenderView != null) {
        let path = "aaa.jpg";
        vidoeRenderView.snapShot(`${ Host.file.storageBasePath }/${ path }`)
          .then((result) => { // 截图完毕。不转存
            // todo update  image翻转的问题
            if (VersionUtil.isUsingSpec(Device.model)) {
              Service.spec.getPropertiesValue([CAMERA_CONTROL_SEPC_PARAMS[3]])
                .then((res) => {
                  if (res[0].code == 0) {
                    let remoteFlip = res[0].value == 180;
                    this._onGetImageRotateSuccess(remoteFlip, path, customPath, resolve);
                  } else {
                    this._onGetImageRotateFail(localFlip, path, customPath, resolve);
                  }
                })
                .catch((err) => {
                  this._onGetImageRotateFail(localFlip, path, customPath, resolve);
                });
            } else {
              RPC.callMethod("get_prop", ['flip'])
                .then((res) => {
                  let remoteFlip = res.result[0] == 'on';
                  this._onGetImageRotateSuccess(remoteFlip, path, customPath, resolve);
                })
                .catch((error) => {
                  this._onGetImageRotateFail(localFlip, path, customPath, resolve);
                });
            }
            
          });
        
      }
    
    }); 
  }

  static _onGetImageRotateSuccess(remoteFlip, path, customPath, resolve) {
    let isFlipOn = remoteFlip;
    let targetPath = SNAPSHOT_SETTING_IMG_PATH;
    if (isFlipOn) {
      targetPath = SNAPSHOT_FLIP_IMG_PATH;
    }
    if (customPath != "") {
      targetPath = customPath;
    }
    VersionUtil.settingsImgPath = targetPath;
    Host.file.isFileExists(targetPath)
      .then((result) => {
        if (result) {
          Host.file.deleteFile(targetPath)
            .then(() => {
              Host.file.copyFile({ srcPath: path, dstPath: targetPath });
              resolve(result);
            })
            .catch(() => {
              Host.file.copyFile({ srcPath: path, dstPath: targetPath });
              resolve(result);
            });
        } else {
          Host.file.copyFile({ srcPath: path, dstPath: targetPath });
          resolve(result);
        }
      })
      .catch(() => {
        Host.file.deleteFile(targetPath)
          .then(() => {
            Host.file.copyFile({ srcPath: path, dstPath: targetPath });
            resolve();
          })
          .catch(() => {
            Host.file.copyFile({ srcPath: path, dstPath: targetPath });
            resolve();
          });
      });
  }

  static _onGetImageRotateFail(localFlip, path, customPath, resolve) {
    let isFlip = localFlip;
    let targetPath = SNAPSHOT_SETTING_IMG_PATH;
    if (isFlip) {
      targetPath = SNAPSHOT_FLIP_IMG_PATH;
    }
    if (customPath != "") {
      targetPath = customPath;
    }
    VersionUtil.settingsImgPath = targetPath;
    Host.file.deleteFile(targetPath)
      .then(() => {
        Host.file.copyFile({ srcPath: path, dstPath: targetPath });
        resolve();
      })
      .catch(() => {
        Host.file.copyFile({ srcPath: path, dstPath: targetPath });
        resolve();
      });
  }

  static getSnapshotName() {
    if (Platform.OS === "ios") {
      return SNAPSHOT_IMG_PATH;
    } else {
      // android add ts to hack the cache issue
      let timestamp = new Date().getTime();
      return `${ SNAPSHOT_IMG_PATH }?timestamp=${ timestamp }`;
    }
  }

  static getSnapshot() {
    let name = AlbumHelper.getSnapshotName();
    if (Platform.OS === "ios") {
      return { uri: `${ Host.file.storageBasePath }/${ name }`, cache: 'reload' };
    } else {
      // android add ts to hack the cache issue
      return { uri: `${ Host.file.storageBasePath }/${ name }` };
    }
  }

  static checkPermission() {
    return new Promise((resolve, reject) => {
      if (Platform.OS == "android") {
        PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
          .then((granted) => {
            if (granted === PermissionsAndroid.RESULTS.GRANTED) {
              resolve(PermissionsAndroid.RESULTS.GRANTED);
            } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
              reject(PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN);
            } else {
              reject(PermissionsAndroid.RESULTS.DENIED);
            }
          }).catch((error) => {
            reject(PermissionsAndroid.RESULTS.DENIED);
          });
      } else {
        // no ios's photos const use hardcode
        System.permission.request("photos").then((res) => {
          resolve(PermissionsAndroid.RESULTS.GRANTED);
        }).catch((error) => {
          // Toast.success("camera_no_write_permission");
          reject(PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN);// ios强制弹框
        });
      }
    });
    
  }

  
}
