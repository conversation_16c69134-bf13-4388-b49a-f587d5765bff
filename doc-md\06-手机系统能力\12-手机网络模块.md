<a name="module_miot/system"></a>

## miot/system
手机网络相关接口

**Export**: public  
**Doc_name**: 手机网络模块  
**Doc_index**: 12  
**Doc_directory**: system  
**Example**  
```js
import {System} from "miot"
...
System.network.getGatewayIpAddress().then()
...
```

* [miot/system](#module_miot/system)
    * [~INetwork](#module_miot/system..INetwork)
        * [.getGatewayIpAddress()](#module_miot/system..INetwork+getGatewayIpAddress) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
        * [.getWifiBroadcastAddress()](#module_miot/system..INetwork+getWifiBroadcastAddress) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>


* * *

<a name="module_miot/system..INetwork"></a>

### miot/system~INetwork
**Kind**: inner interface of [<code>miot/system</code>](#module_miot/system)  

* [~INetwork](#module_miot/system..INetwork)
    * [.getGatewayIpAddress()](#module_miot/system..INetwork+getGatewayIpAddress) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
    * [.getWifiBroadcastAddress()](#module_miot/system..INetwork+getWifiBroadcastAddress) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>


* * *

<a name="module_miot/system..INetwork+getGatewayIpAddress"></a>

#### iNetwork.getGatewayIpAddress() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
获取手机当前连接的路由器IP地址

**Kind**: instance method of [<code>INetwork</code>](#module_miot/system..INetwork)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code> - result:
成功时：{"code":0, "data":xxx},data.ipAddress:手机当前连接的路由器IP地址
失败时：{"code":-1, "message":"xxx" }  
**Since**: 10045  
**Example**  
```js
System.network.getGatewayIpAddress().then((res) => {
    if (res && res.data) {
      alert(`getGatewayIpAddress success,ipAddress:${ res.data.ipAddress }`);
    } else {
      alert(`getGatewayIpAddress fail,${ JSON.stringify(res) }`);
    }
  }).catch((error) => {
    alert(`getGatewayIpAddress fail,${ JSON.stringify(error) }`);
  });
```

* * *

<a name="module_miot/system..INetwork+getWifiBroadcastAddress"></a>

#### iNetwork.getWifiBroadcastAddress() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
获取当前wifi的广播地址

**Kind**: instance method of [<code>INetwork</code>](#module_miot/system..INetwork)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code> - result:
成功时：{"code":0, "data":{"address":xxx.xxx.xxx}}
失败时：{"code":-1, "message":"xxx" }  
**Since**: 10047  
**Example**  
```js
System.network.getGatewayIpAddress().then( res =>{
 alert(JSON.stringify(res));
}).catch(err =>{
 alert(JSON.stringify(err));
})
```

* * *

