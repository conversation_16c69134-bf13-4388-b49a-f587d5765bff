<a name="module_miot/device/bluetooth"></a>

## miot/device/bluetooth
蓝牙锁操作类
蓝牙锁的开发，详见：https://iot.mi.com/new/doc/extension-development/topics/bluetooth-lock
本文件主要提供了蓝牙锁的开关锁，蓝牙锁密钥的分享，获取一次性开锁密钥，锁相关数据加解密等功能

**Export**: public  
**Doc_name**: 蓝牙锁模块  
**Doc_index**: 3  
**Doc_directory**: bluetooth  
**Example**  
```js
import {Bluetooth} from 'miot/device/bluetooth'

...
 Bluetooth.createBluetoothLE(...).connect(...).then(device => {
        device.securityLock().toggle(0,5000)
       .then(lock => {console.log('toggle success')})
       .catch(err => {console.log('toggle failed'})
    })
 Bluetooth.createBluetoothLE(...).securityLock().encryptMessage('message')
    .then(msg => {console.log('encrypted message is ', msg)})
    .catch(err => {console.log('encrypted message failed, ', err})

    Bluetooth.createBluetoothLE(...).securityLock().encryptMessage('decryptedMessage')
    .then(msg => {console.log('decrypt message is ', msg)})
    .catch(err => {console.log('decrypt message failed, ', err})
...
```

* [miot/device/bluetooth](#module_miot/device/bluetooth)
    * [module.exports](#exp_module_miot/device/bluetooth--module.exports) ⏏
        * [.toggle(cmd, timeout)](#module_miot/device/bluetooth--module.exports+toggle) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;IBluetoothLock&gt;</code>
        * [.isShareKeyValid()](#module_miot/device/bluetooth--module.exports+isShareKeyValid) ⇒ <code>Promise</code>
        * ~~[.getOneTimePassword(interval, digits)](#module_miot/device/bluetooth--module.exports+getOneTimePassword) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;int&gt;&gt;</code>~~
        * [.initOneTimePasswordContext(param)](#module_miot/device/bluetooth--module.exports+initOneTimePasswordContext) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
        * [.getOneTimePasswordV2()](#module_miot/device/bluetooth--module.exports+getOneTimePasswordV2) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
        * [.encryptMessage(message)](#module_miot/device/bluetooth--module.exports+encryptMessage) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;string&gt;</code>
        * [.decryptMessage(encrypted)](#module_miot/device/bluetooth--module.exports+decryptMessage) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;string&gt;</code>
        * [.encryptMessageWithToken(data)](#module_miot/device/bluetooth--module.exports+encryptMessageWithToken) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
        * [.decryptMessageWithToken(data)](#module_miot/device/bluetooth--module.exports+decryptMessageWithToken) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>


* * *

<a name="exp_module_miot/device/bluetooth--module.exports"></a>

### module.exports ⏏
**Kind**: Exported interface  

* * *

<a name="module_miot/device/bluetooth--module.exports+toggle"></a>

#### module.exports.toggle(cmd, timeout) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;IBluetoothLock&gt;</code>
支持小米加密芯片的蓝牙设备，开关蓝牙锁

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device/bluetooth--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;IBluetoothLock&gt;</code> - resolve：hex string
     reject：{code: xxx, message: xxx} 1：设备正在切换中 2：加密失败 3：找不到服务 4：超时  

| Param | Type | Description |
| --- | --- | --- |
| cmd | <code>int</code> | 操作命令可传入 0 ，1 ，2三个 int 值，分别代表 开锁，上锁，反锁 |
| timeout | <code>int</code> | 毫秒（iOS 是秒） 蓝牙未响应的超时时间 |

**Example**  
```js
import {Bluetooth} from 'miot'
...
Bluetooth.createBluetoothLE(...).connect(...).then(device => {
 device.securityLock.toggle(0,5000)
     .then(lock => {console.log('toggle success')})
     .catch(err => {console.log('toggle failed'})
})
...
```

* * *

<a name="module_miot/device/bluetooth--module.exports+isShareKeyValid"></a>

#### module.exports.isShareKeyValid() ⇒ <code>Promise</code>
支持小米加密芯片的蓝牙设备，在被分享的设备中，调用此方法，可判断分享的电子钥匙是否有效。**设备owner调用此方法会走reject**

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device/bluetooth--module.exports)  
**Returns**: <code>Promise</code> - resolve：null
     reject：null  
**Example**  
```js
import {Bluetooth} from 'miot'
...
Bluetooth.createBluetoothLE(...).securityLock.isShareKeyValid()
 .then(lock => {console.log('ShareKey is valid')})
 .catch(err => {console.log('ShareKey isn't valid'})
...
```

* * *

<a name="module_miot/device/bluetooth--module.exports+getOneTimePassword"></a>

#### ~~module.exports.getOneTimePassword(interval, digits) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;int&gt;&gt;</code>~~
***Deprecated***

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device/bluetooth--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;int&gt;&gt;</code> - resolve：int[8],意思是生成8个一次性密码，每个密码的长度等于digits。比如 [123456,234567,....]
     reject：{code: xxx, message:xxx} 1:设备owner才可调用  2:参数不正确  3:生成的密码长度不对  4:网络错误  

| Param | Type | Description |
| --- | --- | --- |
| interval | <code>int</code> | 时间间隔，单位为分钟，类型为 number，传入 10 到 60 的整数（建议用整数10，20，30，40，50，60） |
| digits | <code>int</code> | 密码位数，类型为 number，传入 6 到 8 的整数 |

**Example**  
```js
import {Bluetooth} from 'miot'
...
Bluetooth.createBluetoothLE(...).securityLock.getOneTimePassword(30,6)
 .then(pwd => {console.log('one time password is ', pwd)})
 .catch(err => {console.log('get one time password failed, ', err})
...
```

* * *

<a name="module_miot/device/bluetooth--module.exports+initOneTimePasswordContext"></a>

#### module.exports.initOneTimePasswordContext(param) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device/bluetooth--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code> - resolve：{},初始化成功不会返回数据
     reject：{code: xxx, message:xxx} 失败原因  
**Since**: 10075
配合[getOneTimePasswordV2](getOneTimePasswordV2)方法使用，在生成一次性密码之前需要先初始化环境
支持小米加密芯片的蓝牙设备，初始化获取一次性密码的环境。 **设备owner调用此方法才有效**  

| Param | Type | Description |
| --- | --- | --- |
| param | <code>Object</code> | param.interval {int} 密码生效间隔,单位为分钟，假设输入 interval 为 30，则会从当日 0 点开始计算，每 30 分钟为一个刷新间隔。生成的密码在当前刷新间隔及下一个刷新间隔内有效。 如当日 10:19 生成，则该组密码在 10:00 ~ 10:30（当前刷新间隔） 以及 10:30 ~ 11:00 (下一个刷新间隔) 有效。 注意设备上获取当前时间（UTC，精度为秒）的准确性由设备保证，否则会有计算误差。 param.digits {int} 密码位数，传入 6 到 8 的整数 |

**Example**  
```js
import {Bluetooth} from 'miot'
...
const { securityLock } = Bluetooth.createBluetoothLE(...);
securityLock.initOneTimePasswordContext( {interval:30,digits:6} ).then(() => {
   console.log('one time password context init success!')}
   securityLock.getOneTimePasswordV2().then((res)=>{
     console.log(`one time password is ${res}`)
   }).catch(err=>{
     console.log(`one time password failed ${err}`)
   })
 )
 .catch(err => {
   console.log('one time password context init failed: ', err
 })
...
```

* * *

<a name="module_miot/device/bluetooth--module.exports+getOneTimePasswordV2"></a>

#### module.exports.getOneTimePasswordV2() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device/bluetooth--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code> - resolve：{code:0,data:{ pwd: 123456 }}
     reject：{code: xxx, message:xxx} 失败原因  
**Since**: 10075
获取一次性密码，有别于getOneTimePassword，这个方法只会返回一个当前有效的一次性密码，而不是一个数组
注意，调用此方法之前请保证调用过initOneTimePasswordContext进行初始化  

* * *

<a name="module_miot/device/bluetooth--module.exports+encryptMessage"></a>

#### module.exports.encryptMessage(message) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;string&gt;</code>
支持小米加密芯片的蓝牙设备，使用此方法将明文加密为密文后，可发送给设备。然后小米加密芯片会解密，设备端可以直接拿到解密后的数据。

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device/bluetooth--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;string&gt;</code> - resolve: 加密后的string
     reject：{code: xxx, message: xxx} 1:必须是16进制字符串  2:设备未绑定  3:加密出错  

| Param | Type | Description |
| --- | --- | --- |
| message | <code>string</code> | 明文 |

**Example**  
```js
import {Bluetooth} from 'miot'
...
Bluetooth.createBluetoothLE(...).securityLock.encryptMessage('message')
 .then(msg => {console.log('encrypted message is ', msg)})
 .catch(err => {console.log('encrypted message failed, ', err})
...
```

* * *

<a name="module_miot/device/bluetooth--module.exports+decryptMessage"></a>

#### module.exports.decryptMessage(encrypted) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;string&gt;</code>
支持小米加密芯片的蓝牙设备，使用此方法将密文解密为明文

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device/bluetooth--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;string&gt;</code> - resolve：解密后的string
     reject：{code: xxx, message: xxx}  1:必须是16进制字符串  2:设备未绑定 3:解密出错  

| Param | Type | Description |
| --- | --- | --- |
| encrypted | <code>string</code> | 密文 |

**Example**  
```js
import {Bluetooth} from 'miot'
...
Bluetooth.createBluetoothLE(...).securityLock.decryptMessage('decryptedMessage')
 .then(msg => {console.log('decrypt message is ', msg)})
 .catch(err => {console.log('decrypt message failed, ', err})
...
```

* * *

<a name="module_miot/device/bluetooth--module.exports+encryptMessageWithToken"></a>

#### module.exports.encryptMessageWithToken(data) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
使用设备的token加密指定数据

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device/bluetooth--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - resolve：{"result": :"encripted string"} result字段即为加密后的string
     reject：{code: xxx, message: xxx} 1:必须16进制字符串  2:获取device token 失败  3:加密失败  
**Since**: 10004  

| Param | Type | Description |
| --- | --- | --- |
| data | <code>string</code> | Hex Data String |


* * *

<a name="module_miot/device/bluetooth--module.exports+decryptMessageWithToken"></a>

#### module.exports.decryptMessageWithToken(data) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
使用设备的token解密指定数据

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device/bluetooth--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - resolve：{"result": :"encripted string"} result字段即为解密后的string
     reject：{code: xxx, message: xxx} 1:必须16进制字符串  2:获取device token 失败  3:解密失败  
**Since**: 10004  

| Param | Type | Description |
| --- | --- | --- |
| data | <code>strng</code> | Hex Data String |


* * *

