import DynamicColor from './DynamicColor';
import { dynamicStyleSheet } from './DynamicStyleSheet';

export const Constants = {

  // 组件间上下边距
  DEFAULT_MARGIN_TOP_BOTTOM: 36,

  // 组件内文字下边距
  DEFAULT_TEXT_MARGIN_BOTTOM: 24
  
};

// 插件窗口参数
export const PluginWindow = {
  Small: {
    Width: 864,
    Height: 864,
    MarginLeft_Right: 56
  },
  Medium: {
    Width: 1200,
    Height: 904,
    MarginLeft_Right: 72
  },
  Large: {
    Width: 1960,
    Height: 1077,
    MarginLeft_Right: 72
  }
};

// 圆角分级
export const Radius = {
  // 窗口级
  WindowLevel: 32,
  // 面板级
  PanelLevel: 24,
  // 组件级
  WidgetLevel: 16
};

// 抽取字体style
export const Font = {
  Size: {
    _24: 24,
    _26: 26,
    _28: 28,
    _30: 30,
    _32: 32,
    _36: 36,
    _48: 48
  },
  Color: {
    Light: {
      TextPrimary: '#121B2E',
      TextSecondary: '#121B2E9E',
      TextHighlight: '#0080FF',
      TextWarning: '#D66700',
      TextError: '#E44338'
    },
    Dark: {
      TextPrimary: '#FFFFFFE5',
      TextSecondary: '#FFFFFF7A',
      TextHighlight: '#0073E5',
      TextWarning: '#FDA117',
      TextError: '#FB4C3C'
    }
  }
};

export const Color = {
  Border: '#FFFFFFCC',
  ShadeCover: '#00000033',
  Light: {
    AccentPrimary: '#0A6CFF',
    AccentOnPrimary: '#FFFFFF',
    AccentSecondary: '#F0F5FFCC',
    SurfaceDialog: '#D6DDEB',
    Shade: '#00000066',
    DividLine: '#121B2E29',
    SliderDisabled: '#80ADE0'
  },
  Dark: {
    AccentPrimary: '#0060F0',
    AccentOnPrimary: '#FFFFFFE5',
    AccentSecondary: '#C1CCE22B',
    SurfaceDialog: '#25272D',
    Shade: '#000000B2',
    SliderDisabled: '#183E66'
  }
};

export const styles = dynamicStyleSheet({
  smallContainerStyle: {
    width: PluginWindow.Small.Width,
    height: PluginWindow.Small.Height,
    paddingLeft: PluginWindow.Small.MarginLeft_Right,
    paddingRight: PluginWindow.Small.MarginLeft_Right,
    backgroundColor: new DynamicColor(Color.Light.SurfaceDialog, Color.Dark.SurfaceDialog)
  },
  mediumContainerStyle: {
    width: PluginWindow.Medium.Width,
    height: PluginWindow.Small.Height,
    paddingLeft: PluginWindow.Medium.MarginLeft_Right,
    paddingRight: PluginWindow.Medium.MarginLeft_Right,
    backgroundColor: new DynamicColor(Color.Light.SurfaceDialog, Color.Dark.SurfaceDialog)
  },
  largeContainerStyle: {
    width: PluginWindow.Large.Width,
    height: PluginWindow.Large.Height,
    paddingLeft: PluginWindow.Medium.MarginLeft_Right,
    paddingRight: PluginWindow.Medium.MarginLeft_Right,
    backgroundColor: new DynamicColor(Color.Light.SurfaceDialog, Color.Dark.SurfaceDialog)
  },

  buttonTextStyle: {
    color: new DynamicColor(Font.Color.Light.TextPrimary, Font.Color.Dark.TextPrimary),
    fontSize: Font.Size._26,
    textAlignVertical: 'center'
  },
  buttonHightLightTextStyle: {
    color: new DynamicColor(Color.Light.AccentOnPrimary, Font.Color.Dark.TextPrimary),
    fontSize: Font.Size._26,
    textAlignVertical: 'center'
  },
  titleTextStyle: {
    color: new DynamicColor(Font.Color.Light.TextPrimary, Font.Color.Dark.TextPrimary),
    fontSize: Font.Size._28
  },
  subTitleTextStyle: {
    color: new DynamicColor(Font.Color.Light.TextSecondary, Font.Color.Dark.TextSecondary),
    fontSize: Font.Size._24
  },
  warningTextStyle: {
    color: new DynamicColor(Font.Color.Light.TextWarning, Font.Color.Dark.TextWarning)
  },
  errorTextStyle: {
    color: new DynamicColor(Font.Color.Light.TextError, Font.Color.Dark.TextError)
  },
  itemTextStyle: {
    color: new DynamicColor(Font.Color.Light.TextSecondary, Font.Color.Dark.TextSecondary),
    fontSize: Font.Size._24
  },
  itemHighlightTextStyle: {
    color: new DynamicColor(Font.Color.Light.TextPrimary, Font.Color.Dark.TextPrimary),
    fontSize: Font.Size._24
  },
  borderStyle: {
    backgroundColor: new DynamicColor(Color.Light.AccentSecondary, Color.Dark.AccentSecondary),
    borderRadius: Radius.WidgetLevel
  },
  sliderThumbStyle: {
    alignItems: 'center',
    backgroundColor: new DynamicColor(Color.Light.AccentPrimary, Color.Dark.AccentPrimary),
    width: 154,
    borderRadius: 36
  },
  sliderDisableThumbStyle: {
    backgroundColor: new DynamicColor(Color.Light.SliderDisabled, Color.Dark.SliderDisabled)
  },
  sliderThumbTextStyle: {
    height: 72,
    textAlignVertical: 'center',
    color: new DynamicColor(Font.Color.Light.TextPrimary, Font.Color.Dark.TextPrimary),
    fontSize: Font.Size._28
  },
  dividersStyle: {
    borderBottomColor: new DynamicColor(Color.Light.DividLine, Color.Dark.AccentSecondary),
    borderBottomWidth: 1
  },
  itemHighlightStyle: {
    backgroundColor: new DynamicColor(Color.Light.AccentPrimary, Color.Dark.AccentPrimary)
  },
  itemSecondaryStyle: {
    backgroundColor: new DynamicColor(Color.Light.AccentSecondary, Color.Dark.AccentSecondary)
  },
  dialogStyle: {
    width: 864,
    height: 864,
    alignItems: 'center',
    backgroundColor: new DynamicColor(Color.Light.SurfaceDialog, Color.Dark.SurfaceDialog),
    paddingLeft: 56,
    paddingRight: 56,
    paddingBottom: 56,
    borderRadius: Radius.WindowLevel,
    marginTop: 84
  },
  modalStyle: {
    backgroundColor: new DynamicColor(Color.Light.Shade, Color.Dark.Shade)
  },

  loadingPageContainer: {
    backgroundColor: new DynamicColor(Color.Light.SurfaceDialog, Color.Dark.SurfaceDialog),
    alignItems: 'center',
    justifyContent: 'center'
  },
  loadingFailedTextStyle: {
    color: new DynamicColor(Font.Color.Light.TextSecondary, Font.Color.Dark.TextSecondary),
    fontSize: Font.Size._28
  },
  retryTextStyle: {
    color: new DynamicColor(Font.Color.Light.TextPrimary, Font.Color.Dark.TextPrimary),
    fontSize: Font.Size._28
  },
  retryBtnStyle: {
    backgroundColor: new DynamicColor(Color.Light.AccentSecondary, Color.Dark.AccentSecondary)
  },

  progressStyle: {
    width: 56,
    height: 56,
    color: new DynamicColor(Font.Color.Light.TextSecondary, Font.Color.Dark.TextSecondary)
  },
  buttonBaseStyle: { 
    alignItems: 'center', 
    justifyContent: 'center',
    borderRadius: Radius.WidgetLevel 
  },
  dshItemBackgroundStyle: {
    backgroundColor: new DynamicColor(Color.Light.AccentSecondary, Color.ShadeCover)
  },
  containerStyle: {
    backgroundColor: new DynamicColor(Color.Light.SurfaceDialog, Color.Dark.SurfaceDialog)
  }
});

export const Opacity = {
  // 正常
  Normal: 1,
  // 置灰
  Disabled: 0.35
};
