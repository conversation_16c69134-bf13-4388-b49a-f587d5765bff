'use strict';

import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import Separator from 'miot/ui/Separator';
import React from 'react';
import { ScrollView, View, Text, Image, StyleSheet, Dimensions} from 'react-native';
import { DescriptionConstants } from '../Constants';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from '../setting/SettingStyles';

import { Card, NavigationBar, Radio, Switch } from 'mhui-rn';

import SingleRadioView from "../ui/SingleRadioView";
import { HLSettingStyles } from "../aicamera/HLSettingStyles";
import BaseSettingPage from "../BaseSettingPage";
import AlarmUtilV2, {
  PIID_AI_EXTENSION_HOUR_SWITCH,
  PIID_AI_EXTENSION_HOUR_TIME,
  SIID_AI_EXTENSION
} from "../util/AlarmUtilV2";
import Toast from "../components/Toast";
const { width: screenWidth, height: screenHeight } = Dimensions.get("screen");

export default class OnTimeSetting extends BaseSettingPage {

  constructor(props, context) {
    super(props, context);
    this.state = {
      clockState: -1,
      timeValue: 261888
    };
  }

  getTitle(): string {
    return LocalizedStrings['on_time_alarm_time'];
  }
  hasRightButton(): boolean {
    return true;
  }

  getTitleBackgroundColor(): string {
    return "#F6F6F6";
  }

  renderSettingContent() {

    return (
      <View style={ [styles.container] }>
          <View style={ stylesClock.clockContainer } key={ 103 }>
            <View style={ { height: 15 } }/>

            <SingleRadioView
              title={ LocalizedStrings['alarm_time_all'] }
              value={ this.state.clockState == 0 }
              radioValue={ 0 }
              subtitleStyle={{fontWeight: 'bold'}}
              onCheckChange={ (value) => {
                this.setState({ clockState: 0 });
              } }
              onSubClick={ () => {
                console.log("我点击了啊");
              } }/>
            <View style={ { height: 15 } }/>

            <SingleRadioView
              title={ LocalizedStrings['sps_custom'] }
              subtitle={ LocalizedStrings['on_time_alarm_time_select'] }
              value={ this.state.clockState == 1 }
              subValue={""}
              radioValue={ 1 }
              onCheckChange={ (value) => {
                this.setState({ clockState: 1 });
              } }
              onSubClick={ () => {
                console.log("我点击了啊");
                this.props.navigation.navigate('OnTimeSelect', { timeValue: this.state.timeValue, callback: (value) => this.timeSelectBack(value)});
              } }/>
            <View style={ { height: 15 } }/>
          </View>

      </View>
    );
  }

  timeSelectBack(value){
    console.log("=======time",value);
    if (value) {
      this.setState({
        timeValue: value,
      })
    }

  }

  rightPress() {
    let value;
    if (this.state.clockState == 0) {
      value = -1;
    } else {
      value = this.state.timeValue;
    }
    let params = [{ sname: SIID_AI_EXTENSION, pname: PIID_AI_EXTENSION_HOUR_TIME, value: value }];
    AlarmUtilV2.setSpecPValue(params).then((res) => {
      if (res[0].code == 0) {
        Toast.fail("c_set_success");
        this.props.navigation.goBack();
      } else {
        Toast.fail("c_set_fail");
      }
    }).catch((err) => {
      Toast.fail("c_set_fail",err);
    });
  }

  componentDidMount() {
    super.componentDidMount();
    this.getSetting();

  }

  getSetting() {
    let params = [{ sname: SIID_AI_EXTENSION, pname: PIID_AI_EXTENSION_HOUR_TIME }];
    AlarmUtilV2.getSpecPValue(params).then((res) => {
      if (res[0].code == 0) {
        let timeValue = res[0].value;
        this.setState({
          clockState: timeValue == -1 ? 0 : 1,
          timeValue: timeValue == -1 ? 261888 : timeValue,
        })
      } else {
        Toast.fail("c_get_fail");
      }
    }).catch((err) => {
      Toast.fail("c_get_fail",err);
    });
  }
}
const stylesClock = StyleSheet.create({
  clockContainer: {
    backgroundColor: '#F6F6F6',
    height: "100%"
  },
  itemContainer2: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 15,
    borderRadius: 10,
    alignItems: 'center',
    display: "flex",
    flexDirection: 'column'
  },
  itemContainer: {
    marginTop: 15,
    marginBottom: 15,
    paddingHorizontal: 15,
    alignItems: 'center',
    display: "flex",
    flexDirection: 'row'
  },
  itemSubContainer: {
    marginBottom: 15,
    paddingHorizontal: 15,
    alignItems: 'center',
    display: "flex",
    flexDirection: 'row'
  },

  checkTitle: {
    flex: 1,
    height: 40,
    justifyContent: 'center'
  },
  checkSubTitle: {
    flex: 1,
    height: 30,
    justifyContent: 'center'
  },
  rightArrow: {
    width: 15,
    height: 15
  },

  title:{
    color: "#000000",
    fontSize: 16,
    fontWeight: "400"
  },
  separatorLine:{
    color: "#979797",
    fontSize: 16,
    fontWeight: "400",
    marginHorizontal: 10
  },
  subtitle:{
    color: "#979797",
    fontSize: 16,
    fontWeight: "400",
    marginRight: 10
  }
});
