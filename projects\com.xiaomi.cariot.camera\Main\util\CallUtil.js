import { Device, Service } from "miot";
import DeviceSettingUtil from "./DeviceSettingUtil";
import API from '../API';
import StorageKeys from "../StorageKeys";
import Toast from "../Toast";
import LogUtil from "./LogUtil";

export const CLICK_SETTING = "CLICK_SETTING";
export const CALL_TYPE = {
  SINGLE: 0,
  DOUBLE: 1,
  LONG: 2,
  GESTURE: 3
};
/**
 * @Author: byh
 * @Date: 2023/12/11
 * @explanation: 通话相关设置类
 *
 *********************************************************/
export default class CallUtil {

  // 设备端需要的东西都塞到这个里面
  static callSettingKey = "call_setting";
  // 按键通话 前缀
  static clickCallPrefixKey = "clickCall_";
  static singleClickSuffixKey = "single";
  static doubleClickSuffixKey = "double";
  static longClickSuffixKey = "long";

  // 手势通话 前缀
  static gestureCallKey = "gestureCall_";
  static gestureCallKeySuffix = "gestureCall_";

  /**
   * 判断是否第一次绑定，没有相关按键联系人
   */
  static judgeToAddOwnerToSingleClickCall() {
    return new Promise((resolve, reject) => {
      // 每次进来，判断是否需要弹出授权提醒框
      DeviceSettingUtil.getDeviceSettingByKey(DeviceSettingUtil.clickCallSetting).then(response => {
        if (response.code === 0 && JSON.stringify(response.result.settings) === '{}') {
          // 没有设置联系人
          if (!Device.isOwner) {
            // 非主账号 不添加单击按键联系人
            resolve(false);
            return;
          }
          // 换取openID
          // 没有一个设置项，设置一个单击呼叫为当前主账号
          // 需要先load用户信息，否则可能拿不到用户信息，比如昵称等
          let uid = parseInt(Service.account.ID);
          CallUtil.getOpenIdByUid(uid).then((openId) => {
            this.upLoadFirstContact(openId);
          }).catch(error => {
            this.upLoadFirstContact("");
          });
          resolve(false);
        } else {
          // 设置了联系人
          Service.account.load();
          this.shouldShowWarningDialog(response.result.settings, resolve, reject);
          // this.isShareUserInUse(response.result.settings);
          // 是否需要更新联系人头像
          this.checkUserInfo(response.result.settings);
        }
      }).catch(error => {
        console.log("=======error: ", error);
        resolve(false);
      });
    });

  }

  /**
   * @Author: byh
   * @Date: 2024/8/12
   * @explanation:
   * @param type 0首页  1联系人列表页
   *********************************************************/
  static checkIsWarningToShowDialog(type = 0, shouldCheck = false) {
    return new Promise((resolve, reject) => {
      // 每次进来，判断是否需要弹出授权提醒框
      DeviceSettingUtil.getDeviceSettingByPrefix(DeviceSettingUtil.callPrefixKey).then(response => {
        // DeviceSettingUtil.getDeviceSettingByKey(DeviceSettingUtil.clickCallSetting).then(response => {
        if (response.code === 0 && JSON.stringify(response.result.settings) !== '{}') {
          // 设置了联系人
          Service.account.load();
          this.shouldShowWarningDialog(response.result.settings, type, resolve, reject);
          if (shouldCheck) {
            this.isShareUserInUse(response.result.settings);
            // 是否需要更新联系人头像
            this.checkUserInfo(response.result.settings);
          }

        }
      }).catch(error => {
        console.log("=======error: ", error);
        resolve(false);
      });
    });

  }

  /**
   * 每次进来，判断是否需要弹出授权提醒框
   * 1、联系人是否在设置的联系人中
   * 2、未弹出过
   */
  static async shouldShowWarningDialog(settings, type, resolve, reject) {
    let data = JSON.parse(settings.call_setting);
    let isShow = false;
    const res = type === 0 ? await StorageKeys.WX_WARNING_DIALOG : await StorageKeys.WX_WARNING_LIST_DIALOG;
    typeof (res) === 'boolean' ? isShow = res : isShow = false;
    let shouldShow = !isShow;
    console.log("===================isShow:", typeof (res), isShow, shouldShow, res);
    let hasSet = false;
    let hasSetWx = false;
    let uid = Service.account.ID;

    Object.keys(data).map((key) => {
      if (key.indexOf('key') != -1) {
        let contact = data[key];
        if (uid == contact.mijia) {
          hasSet = true;
          if (contact.wx) {
            hasSetWx = true;
          }
        }
      }
    });

    // 1、未展示过  2、已设置为呼叫联系人 3、当前用户wx openId不存在
    // if (!shouldShow && hasSet && !hasSetWx) {
    if (shouldShow && hasSet) {
      resolve(true);
    } else {
      resolve(false);
    }
  }

  static async checkUserInfo(settings) {

    let shouldUpdate = false;
    let callIconSetting = {};
    let callIconSettingForUpdate = {};
    // 取头像
    let needUpData = JSON.parse(settings.call_setting);
    LogUtil.logOnAll("call setting data:", needUpData,settings);
    Promise.all(Object.keys(needUpData).map(async(key) => {
      if (key.indexOf('key') != -1) {
        let iconKey = `call_${key}`;
        let iconNeedUpdate = settings[iconKey];
        iconNeedUpdate = JSON.parse(iconNeedUpdate);
        let keyData = needUpData[key];
        let userinfo = await this.getAccountInfoById(keyData.mijia);
        console.log("+++++++Call update++++++",iconNeedUpdate,userinfo,typeof (iconNeedUpdate),typeof (userinfo));
        if (userinfo != undefined && iconNeedUpdate.icon != userinfo.avatarURL) {
          shouldUpdate = true;
          iconNeedUpdate.icon = userinfo.avatarURL;
          callIconSetting[`call_${key}`] = JSON.stringify(iconNeedUpdate);
          callIconSettingForUpdate[`call_${key}`] = iconNeedUpdate;
        }
      }
    })).then((res) => {
      console.log("=============================success",shouldUpdate,res,callIconSetting);
      if (shouldUpdate) {
        //更新头像数据到服务器
        DeviceSettingUtil.setDeviceSettingArray(callIconSetting).then((res) =>{
          // 下发头像数据
          let iconKeys = Object.keys(callIconSettingForUpdate);
          if (iconKeys.length < 6) {
            callIconSettingForUpdate['method'] = "1";
            let paramsIcon = JSON.stringify(callIconSettingForUpdate);
            console.log("update device setting:",paramsIcon);
            CallUtil.updateSettingToDevice([paramsIcon]);
          } else {
            let groupOne = {};
            iconKeys.slice(0, 5).map(key => {
              groupOne[key] = callIconSettingForUpdate[key];
            });
            groupOne['method'] = "1";
            let groupTwo = {}
            iconKeys.slice(5).map(key => {
              groupTwo[key] = callIconSettingForUpdate[key];
            });
            groupTwo['method'] = "1";
            // 分两次下发
            let paramsGroupIconOne = JSON.stringify(groupOne);
            CallUtil.updateSettingToDevice([paramsGroupIconOne]);
            let paramsGroupIconTwo = JSON.stringify(groupTwo);
            CallUtil.updateSettingToDevice([paramsGroupIconTwo]);
          }
        }).catch((error) => {
          console.log("=========error",error);
        });

        // let paramsStr = JSON.stringify(needUpData);
        // DeviceSettingUtil.setDeviceSetting(`${ DeviceSettingUtil.clickCallSetting }`, paramsStr).then((res) => {
        //   // 成功后，
        //   CallUtil.updateSettingToDevice([paramsStr]);
        // }).catch(error => {
        //   console.log("update share user contact error");
        // });
      }
    }).catch((err) => {
      console.log("========++++++++++++++++++++++++=err",err);
    });
    console.log("========++++++++++++++++++++++++=",shouldUpdate);

  }

  /**
   * @Author: byh
   * @Date: 2024/2/27
   * @explanation:
   * 判断分享用户是否还在分享列表中
   * 1、在    不做其他处理
   * 2、不在   删除该联系人
   *********************************************************/
  static async isShareUserInUse(settings) {
    // 非主账号不做处理
    if (!Device.isOwner) {
      return;
    }
    // 主账号
    let needUpData = JSON.parse(settings.call_setting);
    // 获取分享列表
    Service.callSmartHomeAPI('/share/get_share_user', { did: Device.deviceID, pid: Device.pd_id })
      .then((res) => {
        console.log("res=====", res);
        let shareList = res.list;
        let newArray = shareList.filter((item) => item.status == 1);

        return newArray;
      }).then(async(value) => {
      console.log("=====================[[[[[[[]]]]]]]", value);
      return await Device.getHomeMemberList({}).then((res) => {
        console.log("++++++", res);
        let resData = [];
        if (res.code == 0) {
          resData = res.data;
        }
        return { share: value, member: resData };
      }).catch((error) => {
        console.log("++++++err", error);
        return { share: value, member: [] };
      });
    }).then((contacts) => {
        console.log("================================contacts",contacts);
        // 处理删除联系人操作
      let keys = Object.keys(needUpData);
      let shareData = contacts.share;
      let memberDara = contacts.member;
      let shouldUpdate = false;
      let iconSettingDelete = [];
      keys.forEach((key) => {
        if (key.indexOf('key') != -1) {

          let singleData = needUpData[key];
          // 共享用户 家人
          let shareIndex = shareData.findIndex((v) => v.userid == singleData.mijia);
          let memberIndex = memberDara.findIndex((v) => v.uid == singleData.mijia);
          if (shareIndex == -1 && memberIndex == -1) {
            // 家庭成员与分享用户中都没有  删除此联系人
            shouldUpdate = true;
            delete needUpData[key];
            iconSettingDelete.push(`call_${key}`);
          }
        }
      });
      console.log("======+=======++++++++=delete",shouldUpdate,iconSettingDelete,needUpData);
      if (shouldUpdate) {
        let paramsStr = JSON.stringify(needUpData);
        DeviceSettingUtil.setDeviceSetting(`${ DeviceSettingUtil.clickCallSetting }`, paramsStr).then((res) => {
          // 成功后，
          needUpData['method'] = "0";
          let updateParams = JSON.stringify(needUpData);
          CallUtil.updateSettingToDevice([updateParams]);
          // 成功后删除头像
          DeviceSettingUtil.delDeviceSetting(iconSettingDelete).then((res) => {});
        }).catch(error => {
          console.log("update share user contact error");
        });
      }
      }).catch((e) => console.log("'''''", e));
  }

  /**
   * @Author: byh
   * @Date: 2024/2/27
   * @explanation:
   * 需求更改-设置单击呼叫、手势呼叫都为主账号
   *********************************************************/
  static upLoadFirstContact(openId) {
    Service.account.load().then(account => {
      let value = {
        switch: { mijia: 1, wx: 0, hand: 0 },
        key1: {
          mijia: parseInt(Service.account.ID),
          nickname: Service.account.nickName,
          callName: Service.account.nickName,
          icon: Service.account.avatarURL,
          wx: openId ? openId : '',
          wx_switch: false
        }
        // hand1: {
        //   mijia: parseInt(Service.account.ID),
        //   nickname: Service.account.nickName,
        //   callName: Service.account.nickName,
        //   icon: Service.account.avatarURL,
        //   wx: openId ? openId : ''
        // }
      };
      DeviceSettingUtil.setDeviceSetting(`${ DeviceSettingUtil.clickCallSetting }`, JSON.stringify(value)).then((res) => {
        CallUtil.updateSettingToDevice([JSON.stringify(value)]);
      });
    });
  }

  /**
   * 获取wx openid
   * @param uid 用户ID换取
   */
  static getOpenIdByUid(uid) {
    return new Promise((resolve, reject) => {
      Service.callSmartHomeAPI("/wx_call/get_openid_by_uid", { uid: uid })
        .then((result) => {
          console.log(`getOpenIdByUid:response:${ result }`);
          resolve(result);
        }).catch(error => {
        console.log(`getOpenIdByUid:err:`, error);
        reject(error);
      });
    });
  }

  static async getOpenIdByUidV2(uid) {
    Service.callSmartHomeAPI("/wx_call/get_openid_by_uid", { uid: uid })
      .then((result) => {
        console.log(`getOpenIdByUid:response:${ result }`);
        return result;
      }).catch(error => {
      console.log(`getOpenIdByUid:err:`, error);
      return "";
    });
  }

  /**
   * 同步设置的按键联系人信息到设备
   * @param params 设置的按键设置用户
   */
  static updateSettingToDevice(params) {
    return new Promise((resolve, reject) => {
      let shouldRetry = true;
      let resendParams = params;
      Device.getDeviceWifi().callMethod('_sync.update_call_setting', params).then((res) => {
        LogUtil.logOnAll('updateSettingToDevice success ', res);
        resolve(res);
      }).catch((error) => {
        LogUtil.logOnAll('updateSettingToDevice error ', error);
        if (shouldRetry) {
          shouldRetry = false;
          // 不再关注此次下发后的结果
          Device.getDeviceWifi().callMethod('_sync.update_call_setting', resendParams).then((res)=>{}).catch((error)=>{});
        }
        reject(error);
      });
    })

  }

  static getAccountInfoById(uid) {
    return new Promise((resolve, reject) => {
      Service.account.getAccountInfoById(`${ uid }`).then(res => {
        resolve(res);
        console.log("getAccountInfoById", res);
      }).catch(error => {
        console.log("getAccountInfoById err", error);
        reject(undefined);
      });
    });
  }
}