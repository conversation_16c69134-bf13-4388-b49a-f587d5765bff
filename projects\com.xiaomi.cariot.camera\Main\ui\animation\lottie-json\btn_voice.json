{"v": "5.5.2", "fr": 60, "ip": 0, "op": 60, "w": 150, "h": 150, "nm": "F/首页/语音P1", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 2, "ty": 4, "nm": "路径 7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [63, 76.173, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 61, "s": [100, 3, 100], "e": [100, 100, 100]}, {"t": 74}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.242], [0, 3.943]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.242], [0, 3.943]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.909], [0, 0.609]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.909], [0, 0.609]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.242], [0, 3.943]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.242], [0, 3.943]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.909], [0, 0.609]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.909], [0, 0.609]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 179, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.242], [0, 3.943]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 194, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.242], [0, 3.943]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.909], [0, 0.609]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 209, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.909], [0, 0.609]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 224, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 239, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.242], [0, 3.943]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 254, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.242], [0, 3.943]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.909], [0, 0.609]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 269, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.909], [0, 0.609]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 284, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 298, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.242], [0, 3.943]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 313, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.242], [0, 3.943]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.909], [0, 0.609]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 328, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.909], [0, 0.609]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 343, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 357, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.242], [0, 3.943]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 372, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.242], [0, 3.943]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.909], [0, 0.609]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 387, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.909], [0, 0.609]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 402, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 416, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.242], [0, 3.943]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 431, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.242], [0, 3.943]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.909], [0, 0.609]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 446, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.909], [0, 0.609]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 461, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 476, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.242], [0, 3.943]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 491, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.242], [0, 3.943]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.909], [0, 0.609]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 506, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.909], [0, 0.609]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.576], [0, 2.276]], "c": false}]}, {"t": 521}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = valueAtTime($bm_mod(time, key(numKeys).time));"}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 6", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 61, "op": 2150, "st": 48, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "路径 9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [87, 75.173, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 61, "s": [100, 3, 100], "e": [100, 100, 100]}, {"t": 74}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.529], [0, 2.531]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.862], [0, 0.864]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.862], [0, 0.864]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.529], [0, 2.531]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.529], [0, 2.531]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.862], [0, 0.864]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.862], [0, 0.864]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.529], [0, 2.531]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.529], [0, 2.531]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 195, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.862], [0, 0.864]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 210, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.862], [0, 0.864]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 225, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.529], [0, 2.531]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 239, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.529], [0, 2.531]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 254, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.862], [0, 0.864]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 269, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.862], [0, 0.864]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 284, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.529], [0, 2.531]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 298, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.529], [0, 2.531]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 313, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.862], [0, 0.864]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 328, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.862], [0, 0.864]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 343, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.529], [0, 2.531]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 357, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.529], [0, 2.531]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 372, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.862], [0, 0.864]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 387, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.862], [0, 0.864]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 402, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.529], [0, 2.531]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 416, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.529], [0, 2.531]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 431, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.862], [0, 0.864]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 446, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.862], [0, 0.864]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 461, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.529], [0, 2.531]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 476, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -2.529], [0, 2.531]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 491, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.862], [0, 0.864]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 506, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -0.862], [0, 0.864]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -3.362], [0, 3.364]], "c": false}]}, {"t": 521}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = valueAtTime($bm_mod(time, key(numKeys).time));"}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 6", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 61, "op": 2150, "st": 48, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "路径 10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [75, 76.173, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 195, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 210, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 225, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 239, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 254, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 269, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 284, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 298, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 313, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 328, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 343, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 357, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 372, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 387, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 402, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 416, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 431, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 446, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 461, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 476, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 491, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 506, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -4.534], [0, 4.109]], "c": false}], "e": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -1.201], [0, 0.776]], "c": false}]}, {"t": 521}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径 6", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 61, "op": 2163, "st": 61, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "椭圆形", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [75, 75, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 0, "s": [18, 19], "e": [18, 19]}, {"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 21, "s": [18, 19], "e": [21.2, 21.2]}, {"t": 36}], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [24], "e": [0]}, {"t": 36}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [76], "e": [100]}, {"t": 36}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 13, "s": [0.20000001496, 0.20000001496, 0.20000001496, 1], "e": [0.196078446332, 0.729411764706, 0.752941236309, 1]}, {"t": 21}], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 21, "s": [2.8], "e": [1.8]}, {"t": 36}], "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆形", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 811, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "形状图层 3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 28, "s": [100], "e": [0]}, {"t": 33}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.06], "y": [0]}, "t": 0, "s": [75.004], "e": [75.004]}, {"i": {"x": [0.945], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [75.004], "e": [75.254]}, {"i": {"x": [0.971], "y": [1]}, "o": {"x": [0.029], "y": [0]}, "t": 21, "s": [75.254], "e": [75.004]}, {"t": 42}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.424], "y": [1]}, "o": {"x": [0.45], "y": [0]}, "t": 0, "s": [69.221], "e": [43.182]}, {"i": {"x": [0.679], "y": [0.822]}, "o": {"x": [0.466], "y": [0]}, "t": 10, "s": [43.182], "e": [65.109]}, {"i": {"x": [0.629], "y": [0.868]}, "o": {"x": [0.31], "y": [0.68]}, "t": 21, "s": [65.109], "e": [75.721]}, {"t": 42}], "ix": 4}}, "a": {"a": 0, "k": [0.346, -9.993, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 21, "s": [30, 46], "e": [6, 32]}, {"t": 29}], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 20, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 13, "s": [0.20000000298, 0.20000000298, 0.20000000298, 1], "e": [0.196078446332, 0.729411764706, 0.752941236309, 1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 21, "s": [0.196078446332, 0.729411764706, 0.752941236309, 1], "e": [0.196078446332, 0.729411764706, 0.752941236309, 1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [0.196078446332, 0.729411764706, 0.752941236309, 1], "e": [1, 1, 1, 1]}, {"t": 37}], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0.346, -9.993], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 42, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "形状图层 5", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [74.75, 75.273, 0], "ix": 2}, "a": {"a": 0, "k": [-35.227, 3.023, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 1, "k": [{"i": {"x": [0.419, 0.419], "y": [1, 1]}, "o": {"x": [0.55, 0.55], "y": [0, 0]}, "t": 26, "s": [70, 70], "e": [0, 0]}, {"t": 56}], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-35.227, 3.023], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 2", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 33, "op": 2100, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "形状图层 4", "tt": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [75, 75, 0], "ix": 2}, "a": {"a": 0, "k": [-35.249, 3.001, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [67, 67], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078446332, 0.729411764706, 0.752941236309, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-35.249, 3.001], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 3, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078446332, 0.729411764706, 0.752941236309, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 33, "op": 2100, "st": 0, "bm": 0}], "markers": []}