'use strict';

import { ListItemWithSwitch } from 'miot/ui/ListItem';
import Separator from 'miot/ui/Separator';
import React from 'react';
import { ScrollView, View, Text, StyleSheet, Image, Dimensions } from 'react-native';
import { Device, Host, DarkMode, Service } from 'miot';
import CameraConfig from '../util/CameraConfig';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { ListItem } from 'miot/ui/ListItem';
import Toast from '../components/Toast';
import AlarmUtil from '../util/AlarmUtil';
import AlarmUtilV2, {
  PIID_FACE_SWITCH, PIID_MOTION_DETECTION,
  SIID_AI_DETECTION, SIID_MOTION_DETECTION
} from '../util/AlarmUtilV2';
import TrackUtil from '../util/TrackUtil';
import VersionUtil from '../util/VersionUtil';
import NavigationBar from "miot/ui/NavigationBar";
import { MessageDialog } from 'miot/ui/Dialog';

import Util from '../util2/Util';
import VipUtil from "../util/VipUtil";
import LogUtil from "../util/LogUtil";
import { Event } from '../config/base/CfgConst';
import StorageKeys from "../StorageKeys";
import { LoadingDialog } from 'miot/ui/Dialog';
import API from '../API';
import BaseSettingPage from "../BaseSettingPage";

const viewHeight = (Dimensions.get('window').width - 48) * 9 / 16;

export default class AIFaceSettingV2 extends BaseSettingPage {

  constructor(props, context) {
    super(props, context);
    this.state = {
      faceSwitch: false,
      enableFaceManager: false,
      babyPush: false,
      isVip: CameraConfig.isVip,
      facePush: false,
      pedestrianDetectionPushSwitch: false,
      showGBFDialog: false,
      showBugDialog: false,
      cloudSwitch: false,
      cloudSwitchDialog: false
    };
    this.isInternational = CameraConfig.getInternationalServerStatus();
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.fetchVipData();
      }
    );
  }

  getTitle(): string {
    return LocalizedStrings['camera_face'];
  }

  componentDidMount() {
    super.componentDidMount();
    // this.props.navigation.setParams({
    //   // show:true
    //   title: LocalizedStrings['camera_face'],
    //   type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
    //   left: [
    //     {
    //       key: NavigationBar.ICON.BACK,
    //       onPress: () => {
    //         this.props.navigation.goBack();
    //       }
    //     }
    //   ],
    //   titleStyle: {
    //     fontSize: 18,
    //     color: '#333333',
    //     fontWeight: 500
    //   }
    // });

    if (VersionUtil.isAiCameraModel(Device.model)) {
      let params = [{ "sname": SIID_AI_DETECTION, "pname": PIID_FACE_SWITCH }]
      AlarmUtilV2.getSpecPValue(params).then((res) => {
        if (res[0].code == 0) {
        } else {
          Toast.fail('c_get_fail');
          return;
        }
        this.setState({
          faceSwitch: res[0].value
        });
      }).catch((err) => {
        Toast.fail('c_get_fail', err);
      });
    } else {
      AlarmUtil.getAlarmConfig().then((res) => {
        if (res.code == 0) {
          this.setState({ faceSwitch: res.data.faceSwitch });
        } else {
          Toast.fail('c_get_fail');
          return;
        }
      }).catch((err) => {
        Toast.fail('c_get_fail', err);
      });
    }

  }

  fetchVipData() {
    VipUtil.getVipStatus().then((res) => {
      console.log("======", res);
      this.setState({ isVip: res.isVip });
    }).catch((err) => {
      Toast.fail("c_get_fail");
      LogUtil.logOnAll("fetchVipStatus err=", JSON.stringify(err));
    });
  }

  getCloudSwitch() {
    let params = [{ "sname": SIID_MOTION_DETECTION, "pname": PIID_MOTION_DETECTION }]
    AlarmUtilV2.getSpecPValue(params).then((res) => {
      if (res[0].code == 0) {
        this.setState({
          cloudSwitch: res[0].value
        });
      }
    }).catch((err) => {
    });
  }

  _onFaceSwitchValueChange(value) {
    console.log("=======",value);
    if (VersionUtil.isAiCameraModel(Device.model)) {
      if (value === false) {
        Toast.loading('c_setting');
        AlarmUtilV2.putFaceSwitch(false).then((res) => {
          if (res[0].code != 0) {
            this.setState({ faceSwitch: !value });
            Toast.fail('c_get_fail');
            return;
          } else if (!value) {
            // AlarmUtilV2.setAISettingEventClose(Event.Face);
          }
          Toast.success('c_set_success');
        }).catch((err) => {

          this.setState({ faceSwitch: !value });
          Toast.fail('c_get_fail', err);
        });
      } else {
        if (!this._satisfyPreCondition()) {
          return;
        }
        this.setState({ showGBFDialog: true });
      }
    } else {
      if (value) {
        if (!this._satisfyPreCondition()) {
          return;
        }
      }
      this.openFaceSwitch(value);
    }
  }

  openFaceSwitch(value) {
    API.post('/miot/camera/app/v1/put/faceSwitch', 'business.smartcamera', {
      open: value
    }).then((res) => {
      this.setState({ faceSwitch: res.code == 0 ? value : !value });
      if (res.code == 0) {
        Toast.success('c_set_success');
      } else {
        Toast.fail('c_set_fail');
      }
    }).catch((err) => {
      console.log("===+=+==+========",err);
      this.setState({ faceSwitch: !value });
      if (typeof err === 'object' && err.message == "canceled") {
        // android hock 的人脸弹框点击了取消
        console.log("++++++++++++++++++++")
        Toast.fail('c_set_fail', err);
      } else if (typeof err === 'object' && err.code == 0) {
        // ios hock 的人脸弹框点击了取消
        console.log("++++++++++++++++++++")
        Toast.fail('c_set_fail', err);
      } else {
        Toast.fail('c_set_fail', err);
      }
    });
  }

  _satisfyPreCondition() {
    if (!this.state.isVip) {
      this.setState({ showBugDialog: true });
      return false;
    }

    return true;
  }

  _renderBugDialog() {
    return (
      <MessageDialog
        visible={this.state.showBugDialog}
        title={LocalizedStrings['not_bug_cloud']}
        message={LocalizedStrings['used_for_bug_cloud']}
        canDismiss={false}
        messageStyle={{ textAlign: "center" }}
        buttons={[
          {
            text: LocalizedStrings["offline_divice_ok"],
            // style: { color: 'lightpink' },
            callback: (_) => {
              this.setState({ showBugDialog: false, faceSwitch: false });
            }
          },
          {
            text: LocalizedStrings["c_cloudvip_buy"],
            // style: { color: 'lightblue' },
            callback: (_) => {
              Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: "face_guide" });

              this.setState({ showBugDialog: false, faceSwitch: false });

            }
          }
        ]}
        onDismiss={(_) => {
        }}
      />
    );
  }

  _renderGBFDialog() {
    return (
      <MessageDialog
        visible={this.state.showGBFDialog}
        title={LocalizedStrings['face_service_tips']}
        message={LocalizedStrings['face_service_tips_message']}
        canDismiss={false}
        buttons={[
          {
            text: LocalizedStrings["license_negative_btn_face"],
            callback: (_) => {
              AlarmUtilV2.setFacePrivacyConfirmation(AlarmUtilV2.FACE_CANCEL);
              this.setState({ showGBFDialog: false, faceSwitch: false });
            }
          },
          {
            text: LocalizedStrings["license_positive_btn_face"],
            callback: (_) => {
              AlarmUtilV2.setFacePrivacyConfirmation(AlarmUtilV2.FACE_ACCEPT).then(() => {
                this.setState({ faceSwitch: true });

              });
              this.setState({ showGBFDialog: false });
            }
          }
        ]}
        onDismiss={(_) => {
        }}
      />
    );
  }

  _renderCloudOpenDialog() {
    return (
      <MessageDialog
        visible={this.state.cloudSwitchDialog}
        title={LocalizedStrings['cloud_not_open']}
        message={LocalizedStrings['open_cloud_alarm_content']}
        canDismiss={false}
        buttons={[
          {
            text: LocalizedStrings["offline_divice_ok"],
            callback: (_) => {
              this.setState({ cloudSwitchDialog: false });
            }
          },
          {
            text: LocalizedStrings["open"],
            callback: (_) => {
              this.setState({ cloudSwitchDialog: false });
              //直接设置开启云存按钮开关
              let params = [{ "sname": SIID_MOTION_DETECTION, "pname": PIID_MOTION_DETECTION, value: true }];
              AlarmUtilV2.setSpecPValue(params).then((result) => {
                if (result[0].code === 0) {
                  // Toast.success('c_set_success');
                  StorageKeys.FREE_CLOUD = true;
                  this.setState({ cloudSwitch: true });
                } else {
                  Toast.fail('c_set_fail');
                }
              }).catch((err) => {
                Toast.fail('c_set_fail', err);
              });
              // this.props.navigation.push('SurvelillanceSettingV2', { vip: this.state.isVip });
            }
          }
        ]}
        onDismiss={(_) => {
        }}
      />
    );
  }


  componentWillUnmount() {

  }

  renderSettingContent() {
    let faceRecognize = (
      <ListItemWithSwitch
        showSeparator={false}
        title={LocalizedStrings['as_facial_recognized']}
        value={this.state.faceSwitch}
        onValueChange={(value) => this._onFaceSwitchValueChange(value)}
        titleStyle={{ fontWeight: 'bold' }}

      />
    );

    let faceManage = (
      <ListItem title={LocalizedStrings['camera_face_manage']}
        showSeparator={false}
        onPress={() => {
          if (!Device.isOwner) {
            Toast.fail('face_deny_tips');
            return;
          }
          TrackUtil.reportClickEvent("Camera_Face_ClickNum"); // Camera_Face_ClickNum
          if (VersionUtil.isAiCameraModel(Device.model)) {
            this.props.navigation.navigate('FaceManager2');
          } else {
            this.state.isVip ? this.props.navigation.navigate('FaceManager', { isVip: this.state.isVip }) : this.props.navigation.navigate('NoVipFaceManager');
          }
        }
        }
        titleStyle={{ fontWeight: 'bold' }}

      />
    );
    // if (this.isInternational) {
    //   faceManage = null;// 海外隐藏人脸管理入口
    // }


    return (
      <View style={styles.container}>
        {/* <Separator/> */}
        {/*<ScrollView showsVerticalScrollIndicator={false}>*/}
          <View style={{ flexDirection: 'column', marginHorizontal: 24 }}>
            <View style={{ alignItems: "center" }}>
              <Image style={{ width: '100%', height: viewHeight, marginBottom: 20, borderRadius: 9 }}
                source={require('../../Resources/Images/faceRecognition/ai_pic_face.webp')} />
            </View>
            <Text style={styles.desc_title}>{LocalizedStrings['algorithm_desc']}</Text>
            <Text style={styles.desc_subtitle}>{LocalizedStrings['ai_face_desc']}</Text>
            <Text style={styles.desc_subtitle}>{LocalizedStrings['ai_face_desc_sub']}</Text>
          </View>
          <View style={styles.whiteblank} />
          {/* <View style={[styles.blank, { borderTopWidth: 0 }]}/> */}
          <View style={styles.featureSetting}>
            {this.state.isVip ? faceRecognize : null}
            {faceManage}
            {/*{faceRecognize}*/}
            {/*{this.state.faceSwitch ? faceManage : null}*/}
          </View>


        {/*</ScrollView>*/}
        {this._renderBugDialog()}
        {this._renderGBFDialog()}
        {this._renderCloudOpenDialog()}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Util.isDark() ? "#xm000000" : 'white',
    flex: 1
  },
  dnnText: {
    textAlign: 'center',
    fontSize: 13,
    fontFamily: "MI Lan Pro",
    fontWeight: "400",
    lineHeight: 20,
    color: "#4D4D4D"
  },
  featureSetting: {
    // marginTop: 8,
    backgroundColor: Util.isDark() ? "#xm000000" : 'white',
  },
  faceDesc: {
    borderBottomColor: "#eee",
    borderBottomWidth: 1,
    flexDirection: 'row',
    alignItems: "center",
    marginLeft: 20,
    height: 60
  },
  desc_title: {
    color: "#000000",
    fontSize: 18,
    fontFamily: "MI Lan Pro",
    fontWeight: "bold"
  },
  desc_subtitle: {
    color: "rgba(0, 0, 0, 0.6)",
    fontSize: 14,
    marginTop: 10,
    fontFamily: "MI Lan Pro",
    lineHeight: 21,
    fontWeight: "300"
  },
  whiteblank: {
    height: 0.5,
    marginHorizontal: 20,
    backgroundColor: "#e5e5e5",
    marginBottom: 20,
    marginTop: 20

  }
});
