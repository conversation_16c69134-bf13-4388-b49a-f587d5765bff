'use strict';

import { ListItemWithSwitch } from 'miot/ui/ListItem';
import Separator from 'miot/ui/Separator';
import React from 'react';
import { ScrollView, View, Text, StyleSheet, Image, Dimensions } from 'react-native';
import { Device, Host, DarkMode } from 'miot';
import CameraConfig from '../util/CameraConfig';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { ListItem } from 'miot/ui/ListItem';
import StorageKeys from '../StorageKeys';
import API from '../API';
import Toast from '../components/Toast';
import AlarmUtil from '../util/AlarmUtil';
import TrackUtil from '../util/TrackUtil';
import VersionUtil from '../util/VersionUtil';
import { CAMERA_ALARM, CAMERA_ALARM_051 } from '../Constants';
import NavigationBar from "miot/ui/NavigationBar";
import { MessageDialog } from 'miot/ui/Dialog';

import Util from '../util2/Util';
const kWindowWidth = Math.min(Dimensions.get("screen").height, Dimensions.get("screen").width);

export default class AIFaceSetting extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
      faceSwitch: false,
      enableFaceManager: false,
      babyPush: false,
      isVip: false,
      facePush: false,
      pedestrianDetectionPushSwitch: false,
      showGBFDialog: false
    };
    this.isInternational = CameraConfig.getInternationalServerStatus();
    this.is051 = CameraConfig.Model_chuangmi_051a01 == Device.model;
    this.isBabyCrySpec = /** Device.model === CameraConfig.Model_chuangmi_022 || CameraConfig.isCamera039(Device.model)* */ CameraConfig.isXiaomiCamera(Device.model) || this.is051;// 022走云端，039、049识别成功率低 改成了云端；目前只有c01a02的宝宝哭声开关走spec，051也走spec
    this.shouldDisableBabyCry = false;
  }



  componentDidMount() {
    this.props.navigation.setParams({
      // show:true
      title: LocalizedStrings['camera_face'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });

    this.forceBabyCry = CameraConfig.isSupportNonVipBabyCry(Device.model);



    if (VersionUtil.isAiCameraModel(Device.model)) {
      AlarmUtil.getAiSwitch022(2).then((res) => {
        console.log("getAiSwitch022 res=", JSON.stringify(res));
        this.setState({
          faceSwitch: res[0].value
        });
      }).catch((err) => {
        console.log("getAiSwitch022 err=", JSON.stringify(err));
      });
    }

    AlarmUtil.getAlarmConfig().then((res) => {
      if (res.code == 0) {
      } else {
        Toast.fail('c_get_fail');
        return;
      }
      if (!this.is051) {
        this.setState({
          faceSwitch: res.data.faceSwitch
        });
      }
    }).catch((err) => {
      Toast.fail('c_get_fail', err);
    });

    StorageKeys.IS_VIP_STATUS.then((vipStatus) => {
      StorageKeys.IN_CLOSE_WINDOW.then((windowStatus) => {
        if (vipStatus || windowStatus) {
          this.setState({ enableFaceManager: true, isVip: vipStatus });
        }
      });
    }).catch((err) => {
    });
  }

  _onFaceSwitchValueChange(value) {
    Toast.loading('c_setting');
    if (VersionUtil.isAiCameraModel(Device.model)) {
      if (value === false) {
        AlarmUtil.setFacePrivacyConfirmation(AlarmUtil.FACE_CANCEL).then(() => {
          this.setState({ faceSwitch: false });
        }).catch(() => {
          this.setState({ faceSwitch: true });
        });
      } else {
        this.setState({ showGBFDialog: true });
      }
      // AlarmUtil.putFaceSwitch022(value).then(() => {
      //   Toast.success('c_set_success');
      // }).catch((err) => {
      //   this.setState({ faceSwitch: !value });
      //   console.log("putFaceSwitch022 err=", JSON.stringify(err));
      //   Toast.fail('c_set_fail');
      // })
    } else {
      API.post('/miot/camera/app/v1/put/faceSwitch', 'business.smartcamera', {
        open: value
      }).then((res) => {
        this.setState({ faceSwitch: res.code == 0 ? value : !value });
        if (res.code == 0) {
          Toast.success('c_set_success');
        } else {
          Toast.fail('c_set_fail');
        }
      }).catch((err) => {
        this.setState({ faceSwitch: !value });
        Toast.fail('c_set_fail', err);
      });
    }
  }

  _renderGBFDialog() {
    if (VersionUtil.isAiCameraModel(Device.model)) {
      return (
        <MessageDialog
          visible={this.state.showGBFDialog}
          title={LocalizedStrings['face_service_tips']}
          message={LocalizedStrings['face_service_tips_message']}
          canDismiss={false}
          buttons={[
            {
              text: LocalizedStrings["license_negative_btn_face"],
              // style: { color: 'lightpink' },
              callback: (_) => {
                AlarmUtil.setFacePrivacyConfirmation(AlarmUtil.FACE_CANCEL);
                this.setState({ showGBFDialog: false, faceSwitch: false });
              }
            },
            {
              text: LocalizedStrings["license_positive_btn_face"],
              // style: { color: 'lightblue' },
              callback: (_) => {
                AlarmUtil.setFacePrivacyConfirmation(AlarmUtil.FACE_ACCEPT).then(() => {
                  this.setState({ faceSwitch: true });
                });
                this.setState({ showGBFDialog: false });
              }
            }
          ]}
          onDismiss={(_) => {
          }}
        />
      );
    }
  }



  componentWillUnmount() {
    if (this.shouldDisableBabyCry) {
      CameraConfig.force026c02BabyCryEnable(false);
    }
  }
  render() {
    let faceRecognize = (
      <ListItemWithSwitch
        showSeparator={false}
        title={LocalizedStrings['as_facial_recognized']}
        value={this.state.faceSwitch}
        onValueChange={(value) => this._onFaceSwitchValueChange(value)}
        titleStyle={{ fontWeight: 'bold' }}

      />
    );

    let faceManage = (
      <ListItem title={LocalizedStrings['camera_face_manage']}
        showSeparator={false}
        onPress={() => {
          if (!Device.isOwner) {
            Toast.fail('face_deny_tips');
            return;
          }
          TrackUtil.reportClickEvent("Camera_Face_ClickNum"); // Camera_Face_ClickNum
          // if (VersionUtil.Model_chuangmi_051a01 == Device.model) { // todo  react native canvas的canvas在米家pad模式下被缩放了，导致绘制出来的内容全部产生了偏移，不同设备不一样，处理起来很麻烦，暂时android pad跳原生。
          //   Service.miotcamera.showFaceRecognize(this.state.isVip || VersionUtil.isAiCameraModel(Device.model));
          //   return;
          // }
          if (VersionUtil.isAiCameraModel(Device.model)) {
            this.props.navigation.navigate('FaceManager2');
          } else {
            this.state.isVip ? this.props.navigation.navigate('FaceManager') : this.props.navigation.navigate('NoVipFaceManager');
          }
          
          // Service.miotcamera.showFaceRecognize(this.state.isVip);

        }
        }
        titleStyle={{ fontWeight: 'bold' }}

      />
    );
    if (this.isInternational) {
      faceManage = null;// 海外隐藏人脸管理入口
    }




   
   
    return (
      <View style={styles.container}>
        {/* <Separator/> */}
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={{ flexDirection: 'column', alignItems: "center", paddingBottom: 30, marginHorizontal: 20 }}>
            <Image style={{ width: '100%', height: 200, margin: 20, borderRadius: 9 }} source={require('../../Resources/Images/faceRecognition/picFace.webp')}>

            </Image>
            <View>
              <Text style={styles.dnnText}>
                {LocalizedStrings["face_manager_first_tips"]}
              </Text>
            </View>

          </View>
          {/* <View style={[styles.blank, { borderTopWidth: 0 }]}/> */}
          <View style={styles.featureSetting}>
            {this.state.isVip || VersionUtil.isAiCameraModel(Device.model) ? faceRecognize : null}
            {faceManage}
          </View>
          <View >
            <View style={styles.faceDesc} >
              <Image
                source={require('../../Resources/Images/icon_face_manager_first.png')}
                style={{ width: 25, height: 25, tintColor: "dark" == DarkMode.getColorScheme() ? '#DDDDDDFF' : "" }}
              >

              </Image>
              <View style={{ width: kWindowWidth - 65 }}>
                <Text style={{ fontSize: 15, textAlign: 'left', marginLeft: 10 }}>
                  {LocalizedStrings["face_manager_first_tips1"]}
                </Text>
              </View>


            </View>
            <View style={styles.faceDesc} >
              <Image
                source={require('../../Resources/Images/icon_face_manager_first_mark.png')}
                style={{ width: 25, height: 25, tintColor: "dark" == DarkMode.getColorScheme() ? '#DDDDDDFF' : "" }}
              >

              </Image>
              <View style={{ width: kWindowWidth - 65 }}>
                <Text style={{ fontSize: 15, textAlign: 'left', marginLeft: 10 }}>
                  {LocalizedStrings["face_manager_first_tips2"]}
                </Text>
              </View>
            </View>
            <View style={styles.faceDesc} >
              <Image
                source={require('../../Resources/Images/icon_face_manager_first_share.png')}
                style={{ width: 25, height: 25, tintColor: "dark" == DarkMode.getColorScheme() ? '#DDDDDDFF' : "" }}
              >

              </Image>
              <View style={{ width: kWindowWidth - 65 }}>
                <Text style={{ fontSize: 15, textAlign: 'left', marginLeft: 10 }}>
                  {LocalizedStrings["face_manager_first_tips3"]}
                </Text>
              </View>
            </View>
            <View style={{ marginHorizontal: 24, marginVertical: 20 }}>
              <Text style={{ fontSize: 11, color: '#999999', lineHeight: 18 }}>
                {LocalizedStrings["low_power_agreement"]}
              </Text>
            </View>




          </View>
          {/* <View style={styles.whiteblank}
          /> */}
        </ScrollView>
        {this._renderGBFDialog()}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Util.isDark() ? "#xm000000" : 'white',
    flex: 1
  },
  dnnText: {
    textAlign: 'center',
    fontSize: 13,
    fontFamily: "MI Lan Pro",
    fontWeight: "400",
    lineHeight: 20,
    color: "#4D4D4D"
  },
  featureSetting: {
    // marginTop: 8,
    backgroundColor: Util.isDark() ? "#xm000000" : 'white',
    borderBottomColor: "#eee",
    borderBottomWidth: 1
    // marginHorizontal: 24
  },
  faceDesc: {
    borderBottomColor: "#eee",
    borderBottomWidth: 1,
    flexDirection: 'row', 
    alignItems: "center", 
    marginLeft: 20, 
    height: 60
  }
});
