diff --git a/node_modules/react-native-image-zoom-viewer/built/image-viewer.component.d.ts b/node_modules/react-native-image-zoom-viewer/built/image-viewer.component.d.ts
index 52d1ab9..fe935a5 100644
--- a/node_modules/react-native-image-zoom-viewer/built/image-viewer.component.d.ts
+++ b/node_modules/react-native-image-zoom-viewer/built/image-viewer.component.d.ts
@@ -7,7 +7,7 @@ export default class ImageViewer extends React.Component<Props, State> {
     private standardPositionX;
     private positionXNumber;
     private positionX;
-    private width;
+    public width;
     private height;
     private styles;
     private hasLayout;
diff --git a/node_modules/react-native-image-zoom-viewer/built/image-viewer.component.js b/node_modules/react-native-image-zoom-viewer/built/image-viewer.component.js
index 2b45f06..18a5c5a 100644
--- a/node_modules/react-native-image-zoom-viewer/built/image-viewer.component.js
+++ b/node_modules/react-native-image-zoom-viewer/built/image-viewer.component.js
@@ -9,7 +9,7 @@ var __extends = (this && this.__extends) || (function () {
         d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
     };
 })();
-var __assign = (this && this.__assign) || Object.assign || function(t) {
+var __assign = (this && this.__assign) || Object.assign || function (t) {
     for (var s, i = 1, n = arguments.length; i < n; i++) {
         s = arguments[i];
         for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
@@ -72,19 +72,26 @@ var ImageViewer = /** @class */ (function (_super) {
          */
         _this.handleHorizontalOuterRangeOffset = function (offsetX) {
             if (offsetX === void 0) { offsetX = 0; }
-            _this.positionXNumber = _this.standardPositionX + offsetX;
+            //_this.standardPositionX第一张是规定值-392.7272644042969 应该是屏幕宽度
+            //offsetX往左是正值 往右是负值
+            _this.positionXNumber = _this.standardPositionX + offsetX;//水平移动的距离
             _this.positionX.setValue(_this.positionXNumber);
-            var offsetXRTL = !react_native_1.I18nManager.isRTL ? offsetX : -offsetX;
+
+            var offsetXRTL = !react_native_1.I18nManager.isRTL ? offsetX : -offsetX;//这个值和下面的horizontalWholeOuterCounter是一个值 就是水平移动的距离
+            
             if (offsetXRTL < 0) {
+                //说明往左滑动，当前的选择的是数组中的一个或者最后一个，然后要加载的就是后面那一张
                 if (_this.state.currentShowIndex || 0 < _this.props.imageUrls.length - 1) {
                     _this.loadImage((_this.state.currentShowIndex || 0) + 1);
                 }
             }
             else if (offsetXRTL > 0) {
+                //说明往右活动，要加载的是上一张要减一
                 if (_this.state.currentShowIndex || 0 > 0) {
                     _this.loadImage((_this.state.currentShowIndex || 0) - 1);
                 }
             }
+            // return offsetXRTL 
         };
         /**
          * 手势结束，但是没有取消浏览大图
@@ -92,12 +99,16 @@ var ImageViewer = /** @class */ (function (_super) {
         _this.handleResponderRelease = function (vx) {
             if (vx === void 0) { vx = 0; }
             var vxRTL = react_native_1.I18nManager.isRTL ? -vx : vx;
+            //这里_this.positionXNumber - _this.standardPositionX是移动的距离 
+            //这里_this.props.flipThreshold是规定的一个值
+            //这修改了滑动过去的比例
             var isLeftMove = react_native_1.I18nManager.isRTL
-                ? _this.positionXNumber - _this.standardPositionX < -(_this.props.flipThreshold || 0)
-                : _this.positionXNumber - _this.standardPositionX > (_this.props.flipThreshold || 0);
+                ? _this.positionXNumber - _this.standardPositionX < -(this.imageWidth / 2 || 0)
+                : _this.positionXNumber - _this.standardPositionX > (this.imageWidth / 2 || 0);
             var isRightMove = react_native_1.I18nManager.isRTL
-                ? _this.positionXNumber - _this.standardPositionX > (_this.props.flipThreshold || 0)
-                : _this.positionXNumber - _this.standardPositionX < -(_this.props.flipThreshold || 0);
+                ? _this.positionXNumber - _this.standardPositionX > (this.imageWidth / 2 || 0)
+                : _this.positionXNumber - _this.standardPositionX < -(this.imageWidth / 2 || 0);
+            //这里和下面都是滑动过去 没有这里不影响滑动
             if (vxRTL > 0.7) {
                 // 上一张
                 _this.goBack.call(_this);
@@ -115,6 +126,7 @@ var ImageViewer = /** @class */ (function (_super) {
                 }
                 return;
             }
+            // 这里可以让他不滑过去然后看数据 
             if (isLeftMove) {
                 // 上一张
                 _this.goBack.call(_this);
@@ -143,18 +155,22 @@ var ImageViewer = /** @class */ (function (_super) {
                 ? _this.standardPositionX + _this.width
                 : _this.standardPositionX - _this.width;
             _this.standardPositionX = _this.positionXNumber;
+
             react_native_1.Animated.timing(_this.positionX, {
                 toValue: _this.positionXNumber,
-                duration: _this.props.pageAnimateTime
+                duration: 200,
+                easing: react_native_1.Easing.linear
             }).start();
             var nextIndex = (_this.state.currentShowIndex || 0) - 1;
-            _this.setState({
-                currentShowIndex: nextIndex
-            }, function () {
-                if (_this.props.onChange) {
-                    _this.props.onChange(_this.state.currentShowIndex);
-                }
-            });
+            setTimeout(() => {
+                _this.setState({
+                    currentShowIndex: nextIndex
+                }, function () {
+                    if (_this.props.onChange) {
+                        _this.props.onChange(_this.state.currentShowIndex);
+                    }
+                });
+            }, 200);
         };
         /**
          * 到下一张
@@ -171,16 +187,31 @@ var ImageViewer = /** @class */ (function (_super) {
             _this.standardPositionX = _this.positionXNumber;
             react_native_1.Animated.timing(_this.positionX, {
                 toValue: _this.positionXNumber,
-                duration: _this.props.pageAnimateTime
+                duration: 200,
+                easing: react_native_1.Easing.linear
             }).start();
             var nextIndex = (_this.state.currentShowIndex || 0) + 1;
-            _this.setState({
-                currentShowIndex: nextIndex
-            }, function () {
-                if (_this.props.onChange) {
-                    _this.props.onChange(_this.state.currentShowIndex);
-                }
-            });
+            setTimeout(() => {
+                _this.setState(
+                    {
+                        currentShowIndex: nextIndex
+                    },
+                    function () {
+                        if (_this.props.onChange) {
+                            _this.props.onChange(_this.state.currentShowIndex);
+                        }
+                    });
+            }, 200)
+            // _this.setState(
+            // {
+            //     currentShowIndex: nextIndex
+            // }, 
+            // function () {
+            //     if (_this.props.onChange) {
+            //         _this.props.onChange(_this.state.currentShowIndex);
+            //     }
+            // }
+            // );
         };
         /**
          * 长按
@@ -223,7 +254,7 @@ var ImageViewer = /** @class */ (function (_super) {
          * 完成布局
          */
         _this.handleLayout = function (event) {
-            if (event.nativeEvent.layout.width !== _this.width) {
+            if (event.nativeEvent.layout.width !== _this.width || event.nativeEvent.layout.height !== _this.height) {
                 _this.hasLayout = true;
                 _this.width = event.nativeEvent.layout.width;
                 _this.height = event.nativeEvent.layout.height;
@@ -414,7 +445,7 @@ var ImageViewer = /** @class */ (function (_super) {
         var screenHeight = this.height;
         var ImageElements = this.props.imageUrls.map(function (image, index) {
             if ((_this.state.currentShowIndex || 0) > index + 1 || (_this.state.currentShowIndex || 0) < index - 1) {
-                return <react_native_1.View key={index} style={{ width: screenWidth, height: screenHeight }}/>;
+                return <react_native_1.View key={index} style={{ width: screenWidth, height: screenHeight }} />;
             }
             if (!_this.handleLongPressWithIndex.has(index)) {
                 _this.handleLongPressWithIndex.set(index, _this.handleLongPress.bind(_this, image));
@@ -423,31 +454,41 @@ var ImageViewer = /** @class */ (function (_super) {
             var height = _this.state.imageSizes[index] && _this.state.imageSizes[index].height;
             var imageInfo = _this.state.imageSizes[index];
             if (!imageInfo || !imageInfo.status) {
-                return <react_native_1.View key={index} style={{ width: screenWidth, height: screenHeight }}/>;
+                return <react_native_1.View key={index} style={{ width: screenWidth, height: screenHeight }} />;
             }
-            // 如果宽大于屏幕宽度,整体缩放到宽度是屏幕宽度
-            if (width > screenWidth) {
-                var widthPixel = screenWidth / width;
+            // 调整为宽度或者高度占满屏幕
+            var widthPixel = screenWidth / width;
+            var HeightPixel = screenHeight / height;
+            if (widthPixel < HeightPixel) {
                 width *= widthPixel;
                 height *= widthPixel;
-            }
-            // 如果此时高度还大于屏幕高度,整体缩放到高度是屏幕高度
-            if (height > screenHeight) {
-                var HeightPixel = screenHeight / height;
+            } else {
                 width *= HeightPixel;
                 height *= HeightPixel;
             }
+            // 如果宽大于屏幕宽度,整体缩放到宽度是屏幕宽度
+            // if (width > screenWidth) {
+            //     var widthPixel = screenWidth / width;
+            //     width *= widthPixel;
+            //     height *= widthPixel;
+            // }
+            // 如果此时高度还大于屏幕高度,整体缩放到高度是屏幕高度
+            // if (height > screenHeight) {
+            //     var HeightPixel = screenHeight / height;
+            //     width *= HeightPixel;
+            //     height *= HeightPixel;
+            // }
             var Wrapper = function (_a) {
                 var children = _a.children, others = __rest(_a, ["children"]);
                 return (<react_native_image_pan_zoom_1.default cropWidth={_this.width} cropHeight={_this.height} maxOverflow={_this.props.maxOverflow} horizontalOuterRangeOffset={_this.handleHorizontalOuterRangeOffset} responderRelease={_this.handleResponderRelease} onMove={_this.props.onMove} onLongPress={_this.handleLongPressWithIndex.get(index)} onClick={_this.handleClick} onDoubleClick={_this.handleDoubleClick} enableSwipeDown={_this.props.enableSwipeDown} swipeDownThreshold={_this.props.swipeDownThreshold} onSwipeDown={_this.handleSwipeDown} pinchToZoom={_this.props.enableImageZoom} enableDoubleClickZoom={_this.props.enableImageZoom} doubleClickInterval={_this.props.doubleClickInterval} {...others}>
-          {children}
-        </react_native_image_pan_zoom_1.default>);
+                    {children}
+                </react_native_image_pan_zoom_1.default>);
             };
             switch (imageInfo.status) {
                 case 'loading':
                     return (<Wrapper key={index} style={__assign({}, _this.styles.modalContainer, _this.styles.loadingContainer)} imageWidth={screenWidth} imageHeight={screenHeight}>
-              <react_native_1.View style={_this.styles.loadingContainer}>{_this.props.loadingRender()}</react_native_1.View>
-            </Wrapper>);
+                        <react_native_1.View style={_this.styles.loadingContainer}>{_this.props.loadingRender()}</react_native_1.View>
+                    </Wrapper>);
                 case 'success':
                     if (!image.props) {
                         image.props = {};
@@ -455,8 +496,10 @@ var ImageViewer = /** @class */ (function (_super) {
                     if (!image.props.style) {
                         image.props.style = {};
                     }
-                    image.props.style = __assign({}, _this.styles.imageStyle, image.props.style, { width: width,
-                        height: height });
+                    image.props.style = __assign({}, _this.styles.imageStyle, image.props.style, {
+                        width: width,
+                        height: height
+                    });
                     if (typeof image.props.source === 'number') {
                         // source = require(..), doing nothing
                     }
@@ -470,56 +513,56 @@ var ImageViewer = /** @class */ (function (_super) {
                         _this.preloadImage(_this.state.currentShowIndex || 0);
                     }
                     return (<react_native_image_pan_zoom_1.default key={index} ref={function (el) { return (_this.imageRefs[index] = el); }} cropWidth={_this.width} cropHeight={_this.height} maxOverflow={_this.props.maxOverflow} horizontalOuterRangeOffset={_this.handleHorizontalOuterRangeOffset} responderRelease={_this.handleResponderRelease} onMove={_this.props.onMove} onLongPress={_this.handleLongPressWithIndex.get(index)} onClick={_this.handleClick} onDoubleClick={_this.handleDoubleClick} imageWidth={width} imageHeight={height} enableSwipeDown={_this.props.enableSwipeDown} swipeDownThreshold={_this.props.swipeDownThreshold} onSwipeDown={_this.handleSwipeDown} panToMove={!_this.state.isShowMenu} pinchToZoom={_this.props.enableImageZoom && !_this.state.isShowMenu} enableDoubleClickZoom={_this.props.enableImageZoom && !_this.state.isShowMenu} doubleClickInterval={_this.props.doubleClickInterval} minScale={_this.props.minScale} maxScale={_this.props.maxScale}>
-              {_this.props.renderImage(image.props)}
-            </react_native_image_pan_zoom_1.default>);
+                        {_this.props.renderImage(image.props)}
+                    </react_native_image_pan_zoom_1.default>);
                 case 'fail':
                     return (<Wrapper key={index} style={_this.styles.modalContainer} imageWidth={_this.props.failImageSource ? _this.props.failImageSource.width : screenWidth} imageHeight={_this.props.failImageSource ? _this.props.failImageSource.height : screenHeight}>
-              {_this.props.failImageSource &&
-                        _this.props.renderImage({
-                            source: {
-                                uri: _this.props.failImageSource.url
-                            },
-                            style: {
-                                width: _this.props.failImageSource.width,
-                                height: _this.props.failImageSource.height
-                            }
-                        })}
-            </Wrapper>);
+                        {_this.props.failImageSource &&
+                            _this.props.renderImage({
+                                source: {
+                                    uri: _this.props.failImageSource.url
+                                },
+                                style: {
+                                    width: _this.props.failImageSource.width,
+                                    height: _this.props.failImageSource.height
+                                }
+                            })}
+                    </Wrapper>);
             }
         });
         return (<react_native_1.Animated.View style={{ zIndex: 9 }}>
-        <react_native_1.Animated.View style={__assign({}, this.styles.container, { opacity: this.fadeAnim })}>
-          {this.props.renderHeader(this.state.currentShowIndex)}
+            <react_native_1.Animated.View style={__assign({}, this.styles.container, { opacity: this.fadeAnim })}>
+                {this.props.renderHeader(this.state.currentShowIndex)}
 
-          <react_native_1.View style={this.styles.arrowLeftContainer}>
-            <react_native_1.TouchableWithoutFeedback onPress={this.goBack}>
-              <react_native_1.View>{this.props.renderArrowLeft()}</react_native_1.View>
-            </react_native_1.TouchableWithoutFeedback>
-          </react_native_1.View>
+                <react_native_1.View style={this.styles.arrowLeftContainer}>
+                    <react_native_1.TouchableWithoutFeedback onPress={this.goBack}>
+                        <react_native_1.View>{this.props.renderArrowLeft()}</react_native_1.View>
+                    </react_native_1.TouchableWithoutFeedback>
+                </react_native_1.View>
 
-          <react_native_1.View style={this.styles.arrowRightContainer}>
-            <react_native_1.TouchableWithoutFeedback onPress={this.goNext}>
-              <react_native_1.View>{this.props.renderArrowRight()}</react_native_1.View>
-            </react_native_1.TouchableWithoutFeedback>
-          </react_native_1.View>
+                <react_native_1.View style={this.styles.arrowRightContainer}>
+                    <react_native_1.TouchableWithoutFeedback onPress={this.goNext}>
+                        <react_native_1.View>{this.props.renderArrowRight()}</react_native_1.View>
+                    </react_native_1.TouchableWithoutFeedback>
+                </react_native_1.View>
 
-          <react_native_1.Animated.View style={__assign({}, this.styles.moveBox, { transform: [{ translateX: this.positionX }], width: this.width * this.props.imageUrls.length })}>
-            {ImageElements}
-          </react_native_1.Animated.View>
-          {this.props.renderIndicator((this.state.currentShowIndex || 0) + 1, this.props.imageUrls.length)}
+                <react_native_1.Animated.View style={__assign({}, this.styles.moveBox, { transform: [{ translateX: this.positionX }], width: this.width * this.props.imageUrls.length })}>
+                    {ImageElements}
+                </react_native_1.Animated.View>
+                {this.props.renderIndicator((this.state.currentShowIndex || 0) + 1, this.props.imageUrls.length)}
 
-          {this.props.imageUrls[this.state.currentShowIndex || 0] &&
-            this.props.imageUrls[this.state.currentShowIndex || 0].originSizeKb &&
-            this.props.imageUrls[this.state.currentShowIndex || 0].originUrl && (<react_native_1.View style={this.styles.watchOrigin}>
-                <react_native_1.TouchableOpacity style={this.styles.watchOriginTouchable}>
-                  <react_native_1.Text style={this.styles.watchOriginText}>查看原图(2M)</react_native_1.Text>
-                </react_native_1.TouchableOpacity>
-              </react_native_1.View>)}
-          <react_native_1.View style={[{ bottom: 0, position: 'absolute', zIndex: 9 }, this.props.footerContainerStyle]}>
-            {this.props.renderFooter(this.state.currentShowIndex || 0)}
-          </react_native_1.View>
-        </react_native_1.Animated.View>
-      </react_native_1.Animated.View>);
+                {this.props.imageUrls[this.state.currentShowIndex || 0] &&
+                    this.props.imageUrls[this.state.currentShowIndex || 0].originSizeKb &&
+                    this.props.imageUrls[this.state.currentShowIndex || 0].originUrl && (<react_native_1.View style={this.styles.watchOrigin}>
+                        <react_native_1.TouchableOpacity style={this.styles.watchOriginTouchable}>
+                            <react_native_1.Text style={this.styles.watchOriginText}>查看原图(2M)</react_native_1.Text>
+                        </react_native_1.TouchableOpacity>
+                    </react_native_1.View>)}
+                <react_native_1.View style={[{ bottom: 0, position: 'absolute', zIndex: 9 }, this.props.footerContainerStyle]}>
+                    {this.props.renderFooter(this.state.currentShowIndex || 0)}
+                </react_native_1.View>
+            </react_native_1.Animated.View>
+        </react_native_1.Animated.View>);
     };
     ImageViewer.prototype.getMenu = function () {
         if (!this.state.isShowMenu) {
@@ -527,30 +570,30 @@ var ImageViewer = /** @class */ (function (_super) {
         }
         if (this.props.menus) {
             return (<react_native_1.View style={this.styles.menuContainer}>
-          {this.props.menus({ cancel: this.handleLeaveMenu, saveToLocal: this.saveToLocal })}
-        </react_native_1.View>);
+                {this.props.menus({ cancel: this.handleLeaveMenu, saveToLocal: this.saveToLocal })}
+            </react_native_1.View>);
         }
         return (<react_native_1.View style={this.styles.menuContainer}>
-        <react_native_1.View style={this.styles.menuShadow}/>
-        <react_native_1.View style={this.styles.menuContent}>
-          <react_native_1.TouchableHighlight underlayColor="#F2F2F2" onPress={this.saveToLocal} style={this.styles.operateContainer}>
-            <react_native_1.Text style={this.styles.operateText}>{this.props.menuContext.saveToLocal}</react_native_1.Text>
-          </react_native_1.TouchableHighlight>
-          <react_native_1.TouchableHighlight underlayColor="#F2F2F2" onPress={this.handleLeaveMenu} style={this.styles.operateContainer}>
-            <react_native_1.Text style={this.styles.operateText}>{this.props.menuContext.cancel}</react_native_1.Text>
-          </react_native_1.TouchableHighlight>
-        </react_native_1.View>
-      </react_native_1.View>);
+            <react_native_1.View style={this.styles.menuShadow} />
+            <react_native_1.View style={this.styles.menuContent}>
+                <react_native_1.TouchableHighlight underlayColor="#F2F2F2" onPress={this.saveToLocal} style={this.styles.operateContainer}>
+                    <react_native_1.Text style={this.styles.operateText}>{this.props.menuContext.saveToLocal}</react_native_1.Text>
+                </react_native_1.TouchableHighlight>
+                <react_native_1.TouchableHighlight underlayColor="#F2F2F2" onPress={this.handleLeaveMenu} style={this.styles.operateContainer}>
+                    <react_native_1.Text style={this.styles.operateText}>{this.props.menuContext.cancel}</react_native_1.Text>
+                </react_native_1.TouchableHighlight>
+            </react_native_1.View>
+        </react_native_1.View>);
     };
     ImageViewer.prototype.render = function () {
         var childs = null;
         childs = (<react_native_1.View>
-        {this.getContent()}
-        {this.getMenu()}
-      </react_native_1.View>);
+            {this.getContent()}
+            {this.getMenu()}
+        </react_native_1.View>);
         return (<react_native_1.View onLayout={this.handleLayout} style={__assign({ flex: 1, overflow: 'hidden' }, this.props.style)}>
-        {childs}
-      </react_native_1.View>);
+            {childs}
+        </react_native_1.View>);
     };
     ImageViewer.defaultProps = new image_viewer_type_1.Props();
     return ImageViewer;
diff --git a/node_modules/react-native-image-zoom-viewer/src/image-viewer.component.tsx b/node_modules/react-native-image-zoom-viewer/src/image-viewer.component.tsx
index 7b41c04..c84283c 100644
--- a/node_modules/react-native-image-zoom-viewer/src/image-viewer.component.tsx
+++ b/node_modules/react-native-image-zoom-viewer/src/image-viewer.component.tsx
@@ -247,6 +247,7 @@ export default class ImageViewer extends React.Component<Props, State> {
   /**
    * 手势结束，但是没有取消浏览大图
    */
+
   public handleResponderRelease = (vx: number = 0) => {
     const vxRTL = I18nManager.isRTL ? -vx : vx;
     const isLeftMove = I18nManager.isRTL
@@ -255,17 +256,16 @@ export default class ImageViewer extends React.Component<Props, State> {
     const isRightMove = I18nManager.isRTL
       ? this.positionXNumber - this.standardPositionX > (this.props.flipThreshold || 0)
       : this.positionXNumber - this.standardPositionX < -(this.props.flipThreshold || 0);
-
-    if (vxRTL > 0.7) {
+    //
+    if (vxRTL > 1.0) {
       // 上一张
       this.goBack.call(this);
-
       // 这里可能没有触发溢出滚动，为了防止图片不被加载，调用加载图片
       if (this.state.currentShowIndex || 0 > 0) {
         this.loadImage((this.state.currentShowIndex || 0) - 1);
       }
       return;
-    } else if (vxRTL < -0.7) {
+    } else if (vxRTL < -1.0) {
       // 下一张
       this.goNext.call(this);
       if (this.state.currentShowIndex || 0 < this.props.imageUrls.length - 1) {
@@ -411,7 +411,7 @@ export default class ImageViewer extends React.Component<Props, State> {
    * 完成布局
    */
   public handleLayout = (event: any) => {
-    if (event.nativeEvent.layout.width !== this.width) {
+    if (event.nativeEvent.layout.width !== this.width || event.nativeEvent.layout.height !== this.height) {
       this.hasLayout = true;
 
       this.width = event.nativeEvent.layout.width;
@@ -448,20 +448,29 @@ export default class ImageViewer extends React.Component<Props, State> {
       if (!imageInfo || !imageInfo.status) {
         return <View key={index} style={{ width: screenWidth, height: screenHeight }} />;
       }
-
-      // 如果宽大于屏幕宽度,整体缩放到宽度是屏幕宽度
-      if (width > screenWidth) {
-        const widthPixel = screenWidth / width;
+      // 调整为宽度或者高度占满屏幕
+      const widthPixel = screenWidth / width;
+      const HeightPixel = screenHeight / height;
+      if (widthPixel < HeightPixel) {
         width *= widthPixel;
         height *= widthPixel;
-      }
-
-      // 如果此时高度还大于屏幕高度,整体缩放到高度是屏幕高度
-      if (height > screenHeight) {
-        const HeightPixel = screenHeight / height;
+      } else {
         width *= HeightPixel;
         height *= HeightPixel;
       }
+      // 如果宽大于屏幕宽度,整体缩放到宽度是屏幕宽度
+      // if (width > screenWidth) {
+      //   const widthPixel = screenWidth / width;
+      //   width *= widthPixel;
+      //   height *= widthPixel;
+      // }
+
+      // 如果此时高度还大于屏幕高度,整体缩放到高度是屏幕高度
+      // if (height > screenHeight) {
+      //   const HeightPixel = screenHeight / height;
+      //   width *= HeightPixel;
+      //   height *= HeightPixel;
+      // }
 
       const Wrapper = ({ children, ...others }: any) => (
         <ImageZoom
