import React from 'react';
import PropTypes from 'prop-types';
import { View, Text, StyleSheet, Image, TouchableWithoutFeedback, TouchableOpacity, TouchableWithoutFeedbackBase } from 'react-native';
import Checkbox from 'miot/ui/Checkbox';
import Switch from 'miot/ui/Switch';
import Radio from 'miot/ui/Radio';
import { ImageButton } from 'mhui-rn';
import { Styles } from "mhui-rn/dist/resources";
import { FontSecondary } from "mhui-rn/dist/constants/font";
import { getAccessibilityConfig } from "mhui-rn/dist/utils/accessibility-helper";
export default class ListItemWithIcon extends React.Component {
  static propTypes = {
    style: PropTypes.object,
    onPress: PropTypes.func,
    onCheckChange: PropTypes.func,
    onRadioChange: PropTypes.func,
    title: PropTypes.string,
    sub_title: PropTypes.string,
    isChecked: PropTypes.bool,
    isSingle: PropTypes.bool,
    isSwitch: PropTypes.bool,
    hideArrow: PropTypes.bool,
    id: PropTypes.string,
    icon: PropTypes.object,
    iconStyle: PropTypes.object,
    value: PropTypes.string,
    valueNumberOfLines: PropTypes.number,
    iconTag: PropTypes.object,
    titleStyle: PropTypes.object,
    subTitleStyle: PropTypes.object,
  };

  static defaultProps = {
    style: {},
    isSingle: false,
    isChecked: false,
    value:'',
    icon: require("../../Resources/Images/icon_right_anchor_black.png")
  };

  constructor(props, context) {
    super(props, context);
    this.backIcon = require('../../Resources/Images/icon_right_anchor_black.png');
  }

  render() {
    const valueStyle = {
      marginRight: 7,
      textAlignVertical: 'center',
      textAlign: 'right'
    };
    let valueLine = this.props.valueNumberOfLines == undefined ? 2 : this.props.valueNumberOfLines;
    return (
      <TouchableWithoutFeedback
        onPress={() => { 
          if (this.props.onPress) {
            this.props.onPress(this.props.id); 
          }
        }}>
        <View style={[{ display: "flex", flexDirection: "column", justifyContent: "center", height: 80, backgroundColor: "#ffffff" }, this.props.style ]}>
          <View style={{ display: "flex", flexDirection: "row", alignItems: "center", paddingRight: 20, width: "100%" }}>
            <Image
              style={[{ width: 28, height: 28, marginStart: 20 },this.props.iconStyle]}
              source={this.props.icon}
            />
            <View style={{ display: "flex", flexDirection: "row", flexGrow: 1, paddingLeft: 20, paddingRight: 15, flex: 1 ,alignItems: "center"}}>
              <View style={{ display: "flex", flexDirection: "column"}}>
                <Text style={[{ fontSize: 16, fontWeight: 'bold' }, this.props.titleStyle]}>{this.props.title}</Text>
                {this.props.sub_title ? <Text style={[{ color: "#999999", fontSize: 11 }, this.props.subTitleStyle]}>{this.props.sub_title}</Text> : null }
              </View>
              {this.props.iconTag?
                <Image
                  style={{ width: 13, height: 11, marginLeft: 5}}
                  source={this.props.iconTag}
                />:null
              }
            </View>
            <View style={{
              width: 10
            }} />
            <View style={[{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'flex-start'
            }, { maxWidth:'30%' }]}>
              {this.props.value ? <Text numberOfLines={valueLine}  ellipsizeMode="tail" style={[Styles.common.subtitle, {
                color: 'rgba(0, 0, 0, 0.40)',
                ...FontSecondary
              }, valueStyle]} {...getAccessibilityConfig({
                accessible: false
              })}>
                {this.props.value}
              </Text> : null}
            </View>
            { this.props.isSwitch ?
              <Switch
                value={this.props.isChecked}
                disabled={false}
                onValueChange={(checked) => {
                  if (this.props.onPress) {
                    this.props.onPress(checked); 
                  }
                }}
              /> :
              this.props.hideArrow ? null :
              <ImageButton
                style={{ width: 7, height: 12, position: "relative" }}
                source={this.backIcon}
              />}
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  }
}